package com.caidaocloud.hr.service.employee.application.familyInfo.service;

import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.familyInfo.entity.FamilyInfoDo;
import com.caidaocloud.hr.service.employee.domain.familyInfo.service.FamilyInfoDomainService;
import com.caidaocloud.hr.service.dto.FamilyInfoDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.familyInfo.FamilyInfoVo;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FamilyInfoService {
    @Resource
    private FamilyInfoDomainService familyInfoDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public String saveOrUpdate(FamilyInfoDto dto) {
        FamilyInfoDo data = ObjectConverter.convert(dto, FamilyInfoDo.class);

        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return familyInfoDomainService.save(data);
    }

    public FamilyInfoDo selectById(String empId) {
        return familyInfoDomainService.selectById(empId);
    }

    public FamilyInfoVo getDetail(String empId) {
        FamilyInfoDo data = selectById(empId);
        if(null == data){
            return new FamilyInfoVo();
        }

        FamilyInfoVo vo = ObjectConverter.convert(data, FamilyInfoVo.class);
        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
        vo.setExt(ext);
        return vo;
    }
}
