package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/19
 */
@Data
@ApiModel("续签意向书dto")
public class ContinueLetterDto {
	@ApiModelProperty("bid")
	private String bid;
	@ApiModelProperty("续签意向模板id")
	private String templateId;

	@ApiModelProperty("合同id")
	private String contractId;

	@ApiModelProperty("合同签署人")
	private EmpSimple owner;

	@ApiModelProperty("入职日期")
	private Long hireDate;

	@ApiModelProperty("员工状态")
	private EnumSimple empStatus;

	@ApiModelProperty("所属组织Id")
	private String organize;

	@ApiModelProperty("所属组织名称")
	private String organizeTxt;

	@ApiModelProperty("关联的职务ID")
	private String job;

	@ApiModelProperty("关联的职务名称")
	private String jobTxt;

	@ApiModelProperty("岗位ID")
	private String post;

	@ApiModelProperty("岗位名称")
	private String postTxt;


	@ApiModelProperty("合同公司Id")
	private String company;

	@ApiModelProperty("所属公司名称")
	private String companyTxt;

	@ApiModelProperty("合同编号")
	private String contractNo;


	@ApiModelProperty("合同期（月）")
	private Integer contractPeriod;

	@ApiModelProperty("合同设置Bid")
	private String contractTypeSet;

	@ApiModelProperty("合同设置名称（合同类型名称/合同名称）")
	private String contractTypeSetTxt;

	@ApiModelProperty("合同开始日期")
	private Long startDate;

	@ApiModelProperty("合同结束日期")
	private Long endDate;

	@ApiModelProperty("附件")
	private Attachment attachment;

	@ApiModelProperty("续签意向确认时间")
	private Long confirmedTime;

	@ApiModelProperty("续签意向状态")
	private EnumSimple continueStatus;

	@ApiModelProperty("续签意向反馈")
	private DictSimple feedback;
}
