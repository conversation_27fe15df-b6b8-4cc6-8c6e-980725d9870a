package com.caidaocloud.hr.service.contract.application.cron;

import com.caidaocloud.hr.service.contract.application.service.EmpContractTypeSetRelService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpReportLineService;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class EmpContractTypeSetRelTaskService {
    @Resource
    EmpContractTypeSetRelService empContractTypeSetRelService;
    @Resource
    private EmpReportLineService empReportLineService;
    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    // 合同适用条件跑批
    @XxlJob("empContractRelJobHandler")
    public ReturnT<String> empContractRelJobHandler() {
        XxlJobHelper.log("XxlJob empContractRelJobHandler start");
        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());

        for (String tenantId : tenantList) {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            // 系统跑批userid默认为0
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            empContractTypeSetRelService.initEmpContractRel(null);
            SecurityUserUtil.removeSecurityUserInfo();
        }

        log.info("cronTask[Template package]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empContractRelJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 每日2点 主岗信息/兼岗信息 同步 上级汇报线；
     *
     * @return
     */
    @XxlJob("empHrReportInputDaily")
    public ReturnT<String> empHrReportInputDaily() {
        XxlJobHelper.log("XxlJob empHrReportInputDaily start");
        Long dataTime = DateUtil.getMidnightTimestamp();
        // 租户获取
        for (String tenantId : tenantList) {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            // 系统跑批userid默认为0
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            empReportLineService.reportLineRefresh(dataTime);
            SecurityUserUtil.removeSecurityUserInfo();
        }
        XxlJobHelper.log("XxlJob empHrReportInputDaily end");
        return ReturnT.SUCCESS;
    }

}
