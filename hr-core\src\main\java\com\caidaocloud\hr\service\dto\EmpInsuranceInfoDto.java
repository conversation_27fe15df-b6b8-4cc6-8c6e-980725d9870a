package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class EmpInsuranceInfoDto {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("缴纳单位bid")
    private String unitBid;

    @ApiModelProperty("缴纳单位")
    private String unitName;

    @ApiModelProperty("社保生效日期")
    private Long socialEffectiveDate;

    @ApiModelProperty("社保缴纳地")
    private Address socialSecurity;

    @ApiModelProperty("社保账号")
    private String socialAccount;

    @ApiModelProperty("社保方案id")
    private Integer socialPlanId;

    @ApiModelProperty("基数")
    private BigDecimal socialBase;

    @ApiModelProperty("公积金生效日期")
    private Long providentEffectiveDate;

    @ApiModelProperty("公积金缴纳地")
    private Address providentFund;

    @ApiModelProperty("公积金账号")
    private String providentAccount;

    @ApiModelProperty("公积金方案id")
    private Integer providentPlanId;

    @ApiModelProperty("公积金基数")
    private BigDecimal providentBase;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
