package com.caidaocloud.hr.service.contract.infrastructure.config.workflow;

import com.caidaocloud.workflow.annotation.WfCallback;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;

/**
 * 流程回调
 *
 * <AUTHOR>
 * @date 2024/2/21
 **/
public class WfFunctionCallback {
    @WfCallback(name = "合同续签意向确认回调", code = "CONTINUECONTRACT-Callback",
            address = "/api/hr/contract/v1/continue/callback",
            serviceId = "caidaocloud-hr-service",
            callbackType = WfCallbackTypeEnum.RELATIVE_PATH,
            timeType = WfCallbackTimeTypeEnum.NOW,
            funcCode = {"CONTINUECONTRACT"})
    public void registerContinueContractCallback() {
    }
}