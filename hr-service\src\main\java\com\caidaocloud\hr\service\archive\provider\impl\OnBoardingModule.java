package com.caidaocloud.hr.service.archive.provider.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.archive.provider.IModuleProvider;
import com.caidaocloud.hr.service.archive.provider.feign.OnBoardingProviderFeign;
import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 5:35 下午
 */
@Slf4j
@Service
public class OnBoardingModule implements IModuleProvider {

    @Resource
    private OnBoardingProviderFeign providerFeign;

    @Override
    public String module() {
        return ArchiveModule.ONBOARDING.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessLine, String businessId) {
        Result<List<ArchiveData>> archiveResult = providerFeign.fetchArchive(businessLine, businessId);
        return archiveResult.isSuccess() ? archiveResult.getData() : Lists.newArrayList();
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        Result<List<ArchiveData>> archiveResult = providerFeign.fetchPageArchive(page);
        return archiveResult.isSuccess() ? archiveResult.getData() : Lists.newArrayList();
    }

    @Override
    public void dispatchArchiveDelEvent(String businessLine, String subBusinessLine, String businessId, ArchiveFile archiveFile) {
        throw new UnsupportedOperationException("不支持删除操作");
    }
}
