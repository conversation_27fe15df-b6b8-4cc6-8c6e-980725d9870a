package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.hr.service.common.application.dto.TenantDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaintenanceFeignFallBack implements MaintenanceFeignClient {
    @Override
    public Result<List<TenantDto>> tenantList() {
        return Result.fail();
    }
}
