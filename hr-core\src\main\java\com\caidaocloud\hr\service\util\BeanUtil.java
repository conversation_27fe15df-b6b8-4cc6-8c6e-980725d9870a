package com.caidaocloud.hr.service.util;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sets;
import org.springframework.beans.BeanUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class BeanUtil {

    // 把JavaBean转化为map
    public static Map<String, Object> obj2map(Object bean) {
        try {
            return bean2map(bean);
        } catch (Exception e){
            throw new ServerException("obj2map err");
        }
    }

    public static Map<String, Object> bean2map(Object bean) throws Exception{
        Map<String, Object> map = new HashMap<>();
        //获取JavaBean的描述器
        BeanInfo b = Introspector.getBeanInfo(bean.getClass(), Object.class);
        //获取属性描述器
        PropertyDescriptor[] pds = b.getPropertyDescriptors();
        //对属性迭代
        for (PropertyDescriptor pd : pds) {
            //属性名称
            String propertyName = pd.getName();
            //属性值,用getter方法获取
            Method m = pd.getReadMethod();
            Object properValue = m.invoke(bean);//用对象执行getter方法获得属性值

            //把属性名-属性值 存到Map中
            map.put(propertyName, properValue);
        }
        return map;
    }

    public static Object getFieldValue(Object bean, String fieldKey) throws Exception {
        return bean2map(bean).getOrDefault(fieldKey, null);
    }

    public static <T> T map2obj(Map<String, Object> map, Class<T> clz) {
        try {
            return map2bean(map, clz);
        } catch (Exception e){
            e.printStackTrace();
            throw new ServerException("map2obj err");
        }
    }

    // 把Map转化为JavaBean
    public static <T> T map2bean(Map<String, Object> map, Class<T> clz) throws Exception{
        //创建一个需要转换为的类型的对象
        T obj = clz.newInstance();
        //从Map中获取和属性名称一样的值，把值设置给对象(setter方法)

        //得到属性的描述器
        BeanInfo b = Introspector.getBeanInfo(clz,Object.class);
        PropertyDescriptor[] pds = b.getPropertyDescriptors();
        for (PropertyDescriptor pd : pds) {
            //得到属性的setter方法
            Method setter = pd.getWriteMethod();
            //得到key名字和属性名字相同的value设置给属性
            setter.invoke(obj, map.get(pd.getName()));
        }
        return obj;
    }

    // todo "DictSimple", Sets.set("text", "value", "code"),
    private static final Map<String, Set<String>> COMPONENT_PROP = Maps.map(
            "DictSimple", Sets.set("value", "code"),
            "EmpSimple", Sets.set("empId", "workno", "name", "enName"),
            "Attachment", Sets.set("names", "urls"),
            "JobGradeRange", Sets.set("isRange",
                    "channel",
                    "startGrade",
                    "endGrade",
                    "startLevel",
                    "endLevel"
            ));
    /**
     *
     * @param clz
     * @param prop
     * @return
     * @throws Exception
     */
    public static <T> List<String> getPropName(Class<T> clz,char infix, String... prop) {
        List<String> p = Lists.list();
        Set<String> propSet = Sets.set(prop);
        //得到属性的描述器
        PropertyDescriptor[] pds = BeanUtils.getPropertyDescriptors(clz);
        for (PropertyDescriptor pd : pds) {
            if (!propSet.contains(pd.getName())) {
                continue;
            }

            Set<String> componentPropSet;
            if (ComponentPropertyValue.class.isAssignableFrom(pd.getPropertyType())
                    && (componentPropSet=COMPONENT_PROP.get(pd.getPropertyType().getSimpleName()))!=null) {
                StringBuilder sb = new StringBuilder(pd.getName()).append(infix);
                if ("DictSimple".equals(pd.getPropertyType().getSimpleName())) {
                    sb.append("dict").append(infix);
                }
                int idx = sb.length();
                for (Field field : pd.getPropertyType().getDeclaredFields()) {
                    if (!componentPropSet.contains(field.getName())) {
                        continue;
                    }
                    p.add(sb.append(field.getName()).toString());
                    sb.delete(idx, sb.length());
                }
                continue;
            }
            p.add(pd.getName());
        }
        return p;
    }

    /**
     *
     * @param clz
     * @param prop
     * @return
     * @throws Exception
     */
    public static <T> List<String> getPropName(Class<T> clz, String... prop) {
        return getPropName(clz, '$', prop);
    }

    public static <T> T buildData(DataSimple data, Class<T> clz) {
        NestPropertyValue lcProp = data.getProperties();
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, PropertyValue> entry : lcProp.entrySet()) {
            PropertyValue value = entry.getValue();
            if (value == null) {
                continue;
            }
            if (value instanceof ComponentPropertyValue) {
                map.put(entry.getKey(), value);
            } else {
                map.put(entry.getKey(), ((SimplePropertyValue) value).getValue());
            }
        }
        T obj = JsonEnhanceUtil.toObject(map, clz);
        com.caidaocloud.util.BeanUtil.copyWithNoValue(data, obj);
        return obj;
    }

    public static <T, S> T copyProperties(S source, T target, List<String> props) throws Exception {
        return copyProperties(source, target, props, false);
    }

    public static <T, S> T copyProperties(S source, T target, List<String> props, boolean isCopyNoTxt) throws Exception {
        return copyProperties(source, target, props, isCopyNoTxt, null);
    }

    public static <T, S> T copyProperties(S source, T target, List<String> props, boolean isCopyNoTxt, List<String> fixedProps) throws Exception {
        if(null == props || props.isEmpty()){
            return target;
        }
        Map<String, String> map = props.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
        if(null != fixedProps && !fixedProps.isEmpty()){
            fixedProps.forEach(prop -> map.put(prop, prop));
        }
        addCopyFields(isCopyNoTxt, map);
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();
        Field [] sourceFields = sourceClass.getDeclaredFields();
        for (Field sourceField : sourceFields) {
            String fieldName = sourceField.getName();
            if(!map.containsKey(fieldName)){
                continue;
            }
            Field targetField = null;
            try {
                targetField = targetClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 目标对象不存在该属性，忽略
                continue;
            }
            if(null == targetField){
                continue;
            }

            sourceField.setAccessible(true);
            targetField.setAccessible(true);
            Object value = sourceField.get(source);
            targetField.set(target, value);
        }
        return target;
    }

    public static void addCopyFields(boolean isCopyNoTxt, Map<String, String> map) {
        if(!isCopyNoTxt){
            return;
        }

        Map<String, String> txtMap = new HashMap<>();
        map.forEach((k, v) -> {
            if(StringUtil.isBlank(k)){
                return;
            }
            String kTxt = k.length() > 3 ? k.substring(0, k.length() - 3) : k;
            if(k.endsWith("Txt") && !map.containsKey(kTxt)){
                txtMap.put(kTxt, kTxt);
            }
        });
        map.putAll(txtMap);
    }
}
