package com.caidaocloud.hr.service.enums.system;

/**
 * @Authot CI29616
 * @Date 2023/6/5 15:53
 * @Version 1.0
 **/
public enum PostTypeEnum {

    NOT_EFFECTIVE("2", "未生效"),
    IN_EFFECT("0", "生效中"),
    EXPIRED("1", "已失效"),
    VOIDED("3", "已作废"),
    CANCELED("4", "已取消");

    PostTypeEnum(String code, String name
    ) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
