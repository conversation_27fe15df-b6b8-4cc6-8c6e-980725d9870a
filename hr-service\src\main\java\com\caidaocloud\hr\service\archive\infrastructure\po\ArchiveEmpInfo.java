package com.caidaocloud.hr.service.archive.infrastructure.po;

import lombok.Data;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.enums.DataType;

import java.io.Serializable;

/**
 * 归档empInfo
 * created by: FoAng
 * create time: 6/6/2024 10:43 上午
 */
@Data
public class ArchiveEmpInfo implements Serializable {

    @ESMapping(datatype = DataType.keyword_type)
    private String empId;

    @ESMapping(datatype = DataType.keyword_type)
    private String workno;

    @ESMapping(datatype = DataType.keyword_type)
    private String name;

    @ESMapping(datatype = DataType.keyword_type)
    private String enName;

    @ESMapping(datatype = DataType.keyword_type)
    private String deptDesc;
}
