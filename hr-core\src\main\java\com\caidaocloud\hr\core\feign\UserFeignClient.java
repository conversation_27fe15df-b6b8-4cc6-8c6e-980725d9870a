package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.core.dto.SyncOnBoarDingDto;
import com.caidaocloud.hr.core.dto.UserBaseInfoDto;
import com.caidaocloud.hr.core.dto.UserSyncDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient( value = "caidaocloud-user-service", fallback = UserFeignFallBack.class,
        configuration = FeignConfiguration.class, contextId = "hrUserFeign")
public interface UserFeignClient {

    /**
     * 生成或更新账号
     * @param dtoList
     * @return
     */
    @PostMapping("/api/user/base/v2/batchSyncOnBoarDing")
    Result batchSyncOnBoarDing(@RequestBody List<SyncOnBoarDingDto> dtoList);

    /**
     * 账号变成正式员工账号
     * @param dtoList
     * @return
     */
    @PostMapping("/api/user/base/v2/batchSyncOfficial")
    Result batchSyncOfficial(@RequestBody List<SyncOnBoarDingDto> dtoList);

    /**
     * 删除员工账号
     * @return
     */
    @PostMapping("/api/user/base/v2/deleteByEmpId")
    Result<?> deleteUserInfo(@RequestParam String empId, @RequestParam String tenantId);

    /**
     * 查询用户信息
     * @return
     */
    @GetMapping("/api/user/base/v2/getUserById")
    public Result<UserBaseInfoDto> getUserById(@RequestParam("userId") Long userId);

    /**
     * 停用 用户；
     * @param empId
     * @return
     */
    @GetMapping("/api/user/base/v2/stop")
    Result stopUser(@RequestParam("empId") String empId);

    /**
     * 用户中心同步
     * @param dto
     * @return
     */
    @PostMapping("/api/user/base/v2/sync")
    public Result<Boolean> syncUserInfo(@RequestBody UserSyncDto dto);

    @PostMapping("/api/user/base/v2/getUserByIds")
    public Result<List<UserBaseInfoDto>> getUserByIds(@RequestBody List<Long> userIds);
}
