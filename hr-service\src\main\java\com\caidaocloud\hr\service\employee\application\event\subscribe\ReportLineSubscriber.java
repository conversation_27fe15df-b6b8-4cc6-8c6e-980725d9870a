package com.caidaocloud.hr.service.employee.application.event.subscribe;

import com.caidaocloud.hr.service.employee.application.common.constant.Constant;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpReportLineService;
import com.caidaocloud.hr.service.employee.application.event.dto.ReportLineMsgDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ReportLineSubscriber {
    @Autowired
    private EmpReportLineService empReportLineService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "hr.reportline.refresh", durable = "true"),
                    exchange = @Exchange(value = Constant.HR_DIRECT_EXCHANGE, type = ExchangeTypes.DIRECT),
                    key = {Constant.REPORT_LINE_REFRESH_ROUTING}
            )
    )
    public void refresh(String message) {
        if (StringUtils.isBlank(message)) {
            log.info("refresh message is null");
            return;
        }
        ReportLineMsgDto reportLineMsgDto = FastjsonUtil.toObject(message, ReportLineMsgDto.class);
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(reportLineMsgDto.getTenantId());
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            empReportLineService.reportLineRefresh(reportLineMsgDto.getEmpId(), reportLineMsgDto.getDataTime());
        } catch (Exception e) {
            log.error("occur error", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}