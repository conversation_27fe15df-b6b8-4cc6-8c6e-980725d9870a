package com.caidaocloud.hr.service.confirmation.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConfirmationRecordSearchDto extends BasePage {
    private String keywords;
    @ApiModelProperty("所属组织")
    private String organize;
    @ApiModelProperty("审批状态")
    private String approvalStatus;
    @ApiModelProperty("是否显示已撤销")
    private boolean showRevoke;
    @ApiModelProperty("转正日期开始区间")
    private Long startConfirmationDate;
    @ApiModelProperty("转正日期结束区间")
    private Long endConfirmationDate;
    @ApiModelProperty("员工")
    private String empId;
    @ApiModelProperty("转正配置ID")
    private String defId;
    private String[] approvalStatusInclude;

    /**
     * 停用使用：获取 审批通过未来生效日期
     * 未来转正日期
     */
    @ApiModelProperty("未来转正日期")
    private String confirmationFutureDate;

    @ApiModelProperty("新岗位")
    private String newPost;

    @ApiModelProperty("新组织")
    private String newOrganize;
}

