package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.hr.service.HRApplication;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = HRApplication.class)
public class EmpDirectSuperiorUpdateTaskTest {

    @Resource
    private EmpDirectSuperiorUpdateTask empDirectSuperiorUpdateTask;

    @Resource
    private EmpWorkInfoService empWorkInfoService;

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Resource
    private OrgService orgService;

    @Before
    public void initUserInfo() {
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        securityUserInfo.setTenantId("11");
        securityUserInfo.setUserId(0L);
        securityUserInfo.setEmpId(0L);
        securityUserInfo.setIsAdmin(false);
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
    }

    /**
     * 测试定时任务的完整执行
     */
    @Test
    public void testUpdateEmpDirectSuperior() {
        System.out.println("开始测试员工直接上级更新定时任务...");
        
        try {
            empDirectSuperiorUpdateTask.updateEmpDirectSuperior();
            System.out.println("定时任务执行完成");
        } catch (Exception e) {
            System.err.println("定时任务执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查找组织负责人的逻辑
     */
    @Test
    public void testFindOrgLeader() {
        System.out.println("开始测试查找组织负责人逻辑...");
        
        long currentTime = System.currentTimeMillis();
        
        // 获取一些员工进行测试
        List<EmpWorkInfoDo> empList = empWorkInfoService.getAllEmpList();
        if (empList.isEmpty()) {
            System.out.println("没有找到员工数据");
            return;
        }
        
        // 取前5个员工进行测试
        int testCount = Math.min(5, empList.size());
        for (int i = 0; i < testCount; i++) {
            EmpWorkInfoDo emp = empList.get(i);
            
            System.out.println("测试员工: " + emp.getEmpId() + " - " + emp.getName());
            System.out.println("  所属组织: " + emp.getOrganize() + " - " + emp.getOrganizeTxt());
            System.out.println("  当前直接上级: " + 
                (emp.getLeadEmpId() != null ? emp.getLeadEmpId().getEmpId() + " - " + emp.getLeadEmpId().getText() : "无"));
            
            // 查找组织信息
            if (emp.getOrganize() != null) {
                try {
                    OrgDo orgDo = orgService.getOrgById(emp.getOrganize(), currentTime);
                    if (orgDo != null) {
                        System.out.println("  组织负责人: " + 
                            (orgDo.getLeaderEmp() != null ? orgDo.getLeaderEmp().getEmpId() + " - " + orgDo.getLeaderEmp().getText() : "无"));
                        
                        // 如果有上级组织，也显示上级组织信息
                        if (orgDo.getPid() != null && orgDo.getPid().getPid() != null) {
                            OrgDo parentOrg = orgService.getOrgById(orgDo.getPid().getPid(), currentTime);
                            if (parentOrg != null) {
                                System.out.println("  上级组织: " + parentOrg.getBid() + " - " + parentOrg.getName());
                                System.out.println("  上级组织负责人: " + 
                                    (parentOrg.getLeaderEmp() != null ? parentOrg.getLeaderEmp().getEmpId() + " - " + parentOrg.getLeaderEmp().getText() : "无"));
                            }
                        }
                    } else {
                        System.out.println("  组织信息未找到");
                    }
                } catch (Exception e) {
                    System.err.println("  查询组织信息失败: " + e.getMessage());
                }
            }
            
            System.out.println("  ---");
        }
    }

    /**
     * 测试单个员工的直接上级更新
     */
    @Test
    public void testSingleEmpUpdate() {
        System.out.println("开始测试单个员工直接上级更新...");
        
        long currentTime = System.currentTimeMillis();
        
        // 获取一个员工进行测试
        List<EmpWorkInfoDo> empList = empWorkInfoService.getAllEmpList();
        if (empList.isEmpty()) {
            System.out.println("没有找到员工数据");
            return;
        }
        
        EmpWorkInfoDo testEmp = empList.get(0);
        System.out.println("测试员工: " + testEmp.getEmpId() + " - " + testEmp.getName());
        
        // 显示更新前的信息
        System.out.println("更新前直接上级: " + 
            (testEmp.getLeadEmpId() != null ? testEmp.getLeadEmpId().getEmpId() + " - " + testEmp.getLeadEmpId().getText() : "无"));
        
        // 查找应该设置的直接上级
        if (testEmp.getOrganize() != null) {
            try {
                OrgDo orgDo = orgService.getOrgById(testEmp.getOrganize(), currentTime);
                if (orgDo != null && orgDo.getLeaderEmp() != null) {
                    EmpSimple orgLeader = orgDo.getLeaderEmp();
                    
                    // 如果组织负责人是员工本人，查找上级组织
                    if (testEmp.getEmpId().equals(orgLeader.getEmpId())) {
                        System.out.println("组织负责人是员工本人，查找上级组织...");
                        if (orgDo.getPid() != null && orgDo.getPid().getPid() != null) {
                            OrgDo parentOrg = orgService.getOrgById(orgDo.getPid().getPid(), currentTime);
                            if (parentOrg != null && parentOrg.getLeaderEmp() != null) {
                                orgLeader = parentOrg.getLeaderEmp();
                                System.out.println("找到上级组织负责人: " + orgLeader.getEmpId() + " - " + orgLeader.getText());
                            }
                        }
                    }
                    
                    System.out.println("应设置的直接上级: " + orgLeader.getEmpId() + " - " + orgLeader.getText());
                    
                    // 模拟更新（这里只是显示，不实际更新）
                    System.out.println("模拟更新完成");
                } else {
                    System.out.println("组织没有负责人");
                }
            } catch (Exception e) {
                System.err.println("查询组织信息失败: " + e.getMessage());
            }
        }
    }

    /**
     * 测试组织层级结构
     */
    @Test
    public void testOrgHierarchy() {
        System.out.println("开始测试组织层级结构...");
        
        long currentTime = System.currentTimeMillis();
        
        // 获取一些员工的组织信息
        List<EmpWorkInfoDo> empList = empWorkInfoService.getAllEmpList();
        if (empList.isEmpty()) {
            System.out.println("没有找到员工数据");
            return;
        }
        
        // 收集所有组织ID
        empList.stream()
            .filter(emp -> emp.getOrganize() != null)
            .map(EmpWorkInfoDo::getOrganize)
            .distinct()
            .limit(5) // 只测试前5个组织
            .forEach(orgId -> {
                try {
                    System.out.println("组织ID: " + orgId);
                    printOrgHierarchy(orgId, currentTime, 0);
                    System.out.println("---");
                } catch (Exception e) {
                    System.err.println("查询组织层级失败: " + e.getMessage());
                }
            });
    }

    private void printOrgHierarchy(String orgId, long currentTime, int level) {
        if (orgId == null || level > 5) { // 防止无限递归
            return;
        }
        
        String indent = "  ".repeat(level);
        
        try {
            OrgDo orgDo = orgService.getOrgById(orgId, currentTime);
            if (orgDo != null) {
                System.out.println(indent + "组织: " + orgDo.getName() + " (ID: " + orgDo.getBid() + ")");
                System.out.println(indent + "负责人: " + 
                    (orgDo.getLeaderEmp() != null ? orgDo.getLeaderEmp().getText() + " (ID: " + orgDo.getLeaderEmp().getEmpId() + ")" : "无"));
                
                // 递归显示上级组织
                if (orgDo.getPid() != null && orgDo.getPid().getPid() != null) {
                    System.out.println(indent + "上级组织:");
                    printOrgHierarchy(orgDo.getPid().getPid(), currentTime, level + 1);
                }
            } else {
                System.out.println(indent + "组织未找到: " + orgId);
            }
        } catch (Exception e) {
            System.err.println(indent + "查询组织失败: " + e.getMessage());
        }
    }
}
