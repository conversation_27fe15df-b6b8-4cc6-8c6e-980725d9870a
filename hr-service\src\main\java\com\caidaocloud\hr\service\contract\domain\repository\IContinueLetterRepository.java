package com.caidaocloud.hr.service.contract.domain.repository;

import java.io.InputStream;
import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.CompletedContractImportPo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

public interface IContinueLetterRepository  extends BaseRepository<ContinueLetterDo> {

    List<ContinueLetterDo> getArchiveData(String identifier, BasePage page);
}
