package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class WorkExperienceDto {
    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("开始日期")
    private Long startDate;

    @ApiModelProperty("结束日期")
    private Long endDate;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("证明人")
    private String witness;

    @ApiModelProperty("证明人联系方式")
    private String witnessContact;

    @ApiModelProperty("离职原因")
    private String reason;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("岗位")
    private String post;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
