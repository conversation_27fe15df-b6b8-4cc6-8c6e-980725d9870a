package com.caidaocloud.hr.service.contract.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("选人组件员工搜索结果")
@Accessors(chain = true)
public class ContractEmpSearchUserVo {
    @ApiModelProperty("员工信息")
    private List<ContractEmpTreeNodeVo> userList;
    @ApiModelProperty("员工数量")
    private Integer userCount = 0;
}
