package com.caidaocloud.hr.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户账号同步
 * created by: FoAng
 * create time: 9/6/2023 1:55 下午
 */
@Data
public class UserSyncDto implements Serializable {

    @ApiModelProperty("操作类型：INSERT,UPDATE,DELETE")
    private String op;
    @ApiModelProperty("操作类型取值")
    private String optValue;

    // 账号信息
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录")
    private String accountLoginPrefix;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("注册手机号")
    private String mobNum;
    @ApiModelProperty("注册邮箱")
    private String email;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("盐值")
    private String salt;
    @ApiModelProperty("手势密码")
    private String gesture;
    @ApiModelProperty("注册方式")
    private String regType;

    // 用户信息
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("用户id是否必填")
    private boolean requiredUserId = true;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("性别")
    private Integer sex;
    @ApiModelProperty("头像")
    private String headPortrait;
    @ApiModelProperty("用户账号状态：-1 无效 1 正常 2 停用 3 锁定")
    private Integer status;
    @ApiModelProperty("是否为默认用户")
    private Boolean ifDefault;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("集团公司ID")
    private Long corpId;
    @ApiModelProperty("扩展信息")
    private String extInfo;
    @ApiModelProperty("创建人")
    private Long createBy;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("修改人")
    private Long updateBy;
    @ApiModelProperty("修改时间")
    private Long updateTime;
    @ApiModelProperty("删除状态 0 未删除 1 已删除")
    private Integer deleted;
}
