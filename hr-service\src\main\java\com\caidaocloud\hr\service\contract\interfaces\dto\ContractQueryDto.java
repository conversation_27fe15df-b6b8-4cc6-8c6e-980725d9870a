package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("合同管理查询DTO")
public class ContractQueryDto extends BaseJoinPage {
    @ApiModelProperty("任职组织")
    private String organize;
    @ApiModelProperty("工号或姓名关键字搜索")
    private String keyword;
    @ApiModelProperty("工号或姓名关键字搜索")
    private String keywords;
    @ApiModelProperty("审批状态 0:审批中 1:已通过 2:已拒绝")
    private String status;
    @ApiModelProperty("合同到期天数")
    private Integer contractDays;
    @ApiModelProperty("合同状态(不等于)")
    private ContractStatusEnum contractStatus;
    @ApiModelProperty("合同状态(不等于)复数")
    private List<ContractStatusEnum> contractNeStatusList;
    @ApiModelProperty("合同状态(等于)")
    private List<ContractStatusEnum> contractStatusEnumList;

    private String empId;

    @ApiModelProperty("合同结束日期")
    private List<Long> endDates;

    public String getKeyword() {
        return StringUtil.isEmpty(keyword) ? keywords : keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }


}
