package com.caidaocloud.hr.service.contract.application.enums;

import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;

/**
 * 合同状态
 */
public enum ContractStatusEnum {
    // 未生效
    IN_EFFECTIVE("0", String.valueOf(MsgCodeConstant.INACTIVE)),
    // 生效中
    EFFECTIVE("1", String.valueOf(MsgCodeConstant.ACTIVE)),
    // 已失效
    INVALID("2", String.valueOf(MsgCodeConstant.CLOSED)),
    // 已解除
    RELEASED("3", String.valueOf(MsgCodeConstant.CANCELLED)),
    // 已终止
    TERMINATED("4", String.valueOf(MsgCodeConstant.TERMINATED)),
    // 作废
    CANCEL("5", String.valueOf(MsgCodeConstant.INVALID));

    private String index;
    private String name;

    ContractStatusEnum(String index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(String index) {
        for (ContractStatusEnum c : ContractStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return LangUtil.getMsg(Integer.parseInt(c.getName()));
            }
        }
        return null;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
