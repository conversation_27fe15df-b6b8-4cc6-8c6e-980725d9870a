package com.caidaocloud.hr.service.confirmation.interfaces.dto;

import java.util.List;
import java.util.Map;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 转正申请表单dto
 */
@Data
@ApiModel("转正申请内容")
public class ConfirmationImportDto {
    private String formValueId;

    @ApiModelProperty("表单主键")
    private String id;

    @ApiModelProperty("申请员工")
    private EmpSimple emp;

    @ApiModelProperty("转正配置ID")
    private String defId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("表单数据")
    private Map<String, Object> formData;

    @ApiModelProperty("转正数据")
    private List<ImportDataItem> data;

    @Data
    public static class ImportDataItem{
        @ApiModelProperty("字段类型")
        private String type;

        @ApiModelProperty("字段code")
        private String property;

        @ApiModelProperty("异动value")
        private Object value;

        @ApiModelProperty("是否异动变更")
        private boolean enable;

        @ApiModelProperty("数据类型")
        private PropertyDataType propertyDataType;
    }
}
