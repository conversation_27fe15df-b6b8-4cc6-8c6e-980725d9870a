package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;
import lombok.var;
import org.springframework.beans.BeanUtils;

/**
 * 合同签订（新签，改签，续签）
 *
 * <AUTHOR>
 */
@Data
public class ContractSignQueryDto {

    /**
     * 任职信息，包含当前合同
     */
    private EmpWorkInfoVo empWorkInfoVo;

    /**
     * 当前合同信息
     */
    private ContractDo currentContractDo;

    /**
     * 上一份合同信息
     */
    private ContractDo lastContractDo;

    @Data
    public static class ContractAmendment {
        private EnumSimple signType;
        private String companyTxt;
        private String registerAddress;
        private DictSimple contractType;
        private Integer contractPeriod;
        private Long signDate;
        private Long startDate;
        private Long endDate;
        private String workplaceTxt;
        private EnumSimple workHour;
        private String remark;
        private Attachment attachFile;
        private String lastContractNo;
        private String lastCompanyTxt;
        private String lastRegisterAddress;
        private String lastContractTypeSetTxt;
        private Integer lastContractPeriod;
        private Long lastSignDate;
        private Long lastStartDate;
        private Long lastEndDate;
        private Integer lastSignTime;
        private Integer lastProbationPeriod;
        private Long lastProbationPeriodEndDate;
        private EnumSimple lastWorkHour;
    }

    public ContractAmendment convertContractAmendment() {
        var contractAmendment = new ContractAmendment();
        if (this.getCurrentContractDo() != null) {
            BeanUtils.copyProperties(this.getCurrentContractDo(), contractAmendment);
        }
        if (this.getLastContractDo() != null) {
            contractAmendment.setLastContractNo(this.getLastContractDo().getContractNo());
            contractAmendment.setLastCompanyTxt(this.getLastContractDo().getCompanyTxt());
            contractAmendment.setLastRegisterAddress(this.getLastContractDo().getRegisterAddress());
            contractAmendment.setLastContractTypeSetTxt(this.getLastContractDo().getContractTypeSetTxt());
            contractAmendment.setLastContractPeriod(this.getLastContractDo().getContractPeriod());
            contractAmendment.setLastSignDate(this.getLastContractDo().getSignDate());
            contractAmendment.setLastStartDate(this.getLastContractDo().getStartDate());
            contractAmendment.setLastEndDate(this.getLastContractDo().getEndDate());
            contractAmendment.setLastSignTime(this.getLastContractDo().getSignTime());
            contractAmendment.setLastProbationPeriod(this.getLastContractDo().getProbationPeriod());
            contractAmendment.setLastProbationPeriodEndDate(this.getLastContractDo().getProbationPeriodEndDate());
            contractAmendment.setLastWorkHour(this.getLastContractDo().getWorkHour());
        }
        return contractAmendment;
    }
}
