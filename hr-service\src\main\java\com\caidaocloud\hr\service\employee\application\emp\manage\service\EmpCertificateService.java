package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.EmpCertificateDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpCertificateDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpCertificateDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EmpCertificateService {
    @Resource
    private EmpCertificateDomainService empCertificateDomainService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    public List<EmpCertificateDo> getEmpCertificateList(String empId) {
        return empCertificateDomainService.getEmpCertificateList(empId);
    }

    public EmpCertificateDo getDetail(String bid) {
        return empCertificateDomainService.getDetail(bid);
    }

    public String save(EmpCertificateDto dto) {
        EmpCertificateDo data = ObjectConverter.convert(dto, EmpCertificateDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empCertificateDomainService.save(data);
    }

    private void doConvert(EmpCertificateDto source, EmpCertificateDo target) {
        // 熟练程度
        if (StringUtil.isNotEmpty(source.getProficiency())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getProficiency());
            target.setProficiency(dictSimple);
        }
    }

    public void update(EmpCertificateDto dto) {
        EmpCertificateDo data = ObjectConverter.convert(dto, EmpCertificateDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empCertificateDomainService.update(data);
    }

    public void delete(String bid) {
        empCertificateDomainService.delete(bid);
    }
}
