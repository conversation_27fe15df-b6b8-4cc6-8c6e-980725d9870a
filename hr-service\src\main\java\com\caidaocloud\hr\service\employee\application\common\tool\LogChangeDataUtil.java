package com.caidaocloud.hr.service.employee.application.common.tool;

import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 日志记录 字段变化
 */
public class LogChangeDataUtil {

    private static final List<String> ignoreList = Arrays.asList("post", "organize", "job", "benchPost", "company", "workplace");

    public static String getChangeInfo(DataSimple old, DataSimple data) {
        StringBuilder dataBuilder = new StringBuilder();
        MetadataService metadataService = SpringUtil.getBean(MetadataService.class);
        List<MetadataPropertyVo> allProperties = metadataService.getMetadataPropertyAll(data.getIdentifier());
        Map<String, String> propertyNameMap = allProperties.stream().collect(Collectors.toMap(MetadataPropertyVo::getProperty, MetadataPropertyVo::getName));
        if (data.getProperties() != null) {
            for (Map.Entry<String, PropertyValue> entry : data.getProperties().entrySet()) {
                PropertyValue value = entry.getValue();
                if (value == null || entry.getKey() == null) {
                    continue;
                }
                if (entry.getKey().endsWith("Id") && !"leadEmpId".equals(entry.getKey())) {
                    continue;
                }
                if (ignoreList.contains(entry.getKey())) {
                    continue;
                }
                if (old == null || old.getProperties() == null) {
                    String simpleValue = getSimpleValue(value);
                    if (StringUtils.isNotEmpty(simpleValue.trim())) {
                        dataBuilder.append(propertyNameMap.get(entry.getKey()))
                                .append("：")
                                .append("空 ");
                        dataBuilder.append("修改为");
                        dataBuilder.append(propertyNameMap.get(entry.getKey()))
                                .append("：")
                                .append(simpleValue)
                                .append(",");
                    }
                }else {
                    PropertyValue oldValue = old.getProperties().get(entry.getKey());
                    if (!value.equals(oldValue)) {
                        dataBuilder.append(propertyNameMap.get(entry.getKey()))
                                .append("：")
                                .append(getSimpleValue(oldValue));
                        dataBuilder.append("修改为");
                        dataBuilder.append(propertyNameMap.get(entry.getKey()))
                                .append("：")
                                .append(getSimpleValue(value)).append(",");
                    }
                }
            }
            if (dataBuilder.length() > 0) {
                dataBuilder.deleteCharAt(dataBuilder.length() - 1);
            }
        }
        String result = dataBuilder.toString();
        return StringUtils.isNotEmpty(result) ? result : "信息操作成功";
    }

    private static String getSimpleValue(PropertyValue value) {
        String result = null;
        if (value instanceof SimplePropertyValue) {
            SimplePropertyValue propertyValue = ((SimplePropertyValue) value);
            result = propertyValue.getValue();
            //时间转化
            if (PropertyDataType.Timestamp.equals(propertyValue.getType()) && result != null) {
                result = DateUtil.format(Long.parseLong(propertyValue.getValue()), "yyyy-MM-dd");
            }
        } else if (value instanceof EnumSimple) {
            result = ((EnumSimple) value).getText();
        } else if (value instanceof DictSimple) {
            result = ((DictSimple) value).getText();
        } else if (value instanceof Address) {
            Address address = (Address) value;
            if (Stream.of(address.getProvinceTxt(), address.getCityTxt(), address.getAreaTxt()).anyMatch(StringUtil::isNotEmpty)) {
                result = address.getProvinceTxt() + address.getCityTxt() + address.getAreaTxt();
            }
        } else if (value instanceof JobGradeRange) {
            result = ((JobGradeRange) value).getStartGradeName();
        } else if (value instanceof EmpSimple) {
            EmpSimple empSimple = ((EmpSimple) value);
            result = empSimple.getName() + "(" + empSimple.getWorkno() + ")";
        } else if (value instanceof PhoneSimple) {
            result = ((PhoneSimple) value).getValue();
        }
        return result == null ? "空" : result;
    }
}
