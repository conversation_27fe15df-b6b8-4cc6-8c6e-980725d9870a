package com.caidaocloud.hr.service.dto.emp;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgReportExtendDto {
    @ApiModelProperty("ID")
    private String bid;

    @ApiModelProperty("架构类型")
    private DictSimple schemaType;

    @ApiModelProperty("所属组织ID")
    private String orgId;

    @ApiModelProperty("所属组织名称")
    private String orgName;

    @ApiModelProperty("别名")
    private String alias;

    @ApiModelProperty("组织类型")
    private DictSimple orgType;

    @ApiModelProperty("成本中心ID")
    private String costCenterId;

    @ApiModelProperty("成本中心名字")
    private String costCenterName;

    @ApiModelProperty("责任人")
    private EmpSimple leaderEmp;

    @ApiModelProperty("责任人（岗位）")
    private String leaderPost;
}
