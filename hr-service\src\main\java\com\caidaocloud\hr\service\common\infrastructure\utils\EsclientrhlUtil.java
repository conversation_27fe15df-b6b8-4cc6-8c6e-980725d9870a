package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import lombok.var;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.util.Tools;

import java.util.List;

@Component
public class EsclientrhlUtil {
    @Autowired
    private ElasticsearchIndex elasticsearchIndex;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    public <T> void saveOrUpdate(List<T> list) throws Exception {
        if (list == null || list.size() == 0) {
            return;
        }
        T t = list.get(0);
        var metaData = elasticsearchIndex.getMetaData(t.getClass());
        val indexname = metaData.getIndexname();
        val indextype = metaData.getIndextype();
        var request = new BulkRequest();
        request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        for (int i = 0; i < list.size(); i++) {
            T tt = list.get(i);
            val id = Tools.getESId(tt);
            val sourceJsonStr = FastjsonUtil.toJsonStr(tt);
            request.add(new IndexRequest(indexname, indextype, id).source(sourceJsonStr, XContentType.JSON));
        }
        var bulkResponse = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        if (bulkResponse.hasFailures()) {
            throw new ElasticsearchException(bulkResponse.buildFailureMessage());
        }
    }
}