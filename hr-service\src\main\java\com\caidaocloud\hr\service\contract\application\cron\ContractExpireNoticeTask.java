package com.caidaocloud.hr.service.contract.application.cron;

import java.util.List;

import com.caidaocloud.hr.service.contract.application.service.ContractExpireNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 合同到期提醒定时任务
 * <AUTHOR>
 * @date 2023/3/7
 */
@Slf4j
@Service
public class ContractExpireNoticeTask {
	@Value("${caidaocloud.tenant:}")
	private List<String> tenantList;

	@Autowired
	private ContractExpireNoticeService contractExpireNoticeService;

	@XxlJob("ContractExpireNoticeJobHandler")
	public ReturnT<String> contractExpireNoticeJobHandler() {
		XxlJobHelper.log("XxlJob contractExpireNoticeJobHandler start");
		log.info("cronTask[Contract expire notice]------------------------start execution,time {}", System.currentTimeMillis());

		for (String tenantId : tenantList) {
			try {
				SecurityUserInfo userInfo = new SecurityUserInfo();
				userInfo.setTenantId(tenantId);
				userInfo.setUserId(0L);
				userInfo.setEmpId(0L);
				SecurityUserUtil.setSecurityUserInfo(userInfo);

				long currentTimestamp = DateUtil.getCurrentTimestamp();
				contractExpireNoticeService.expireNotify(currentTimestamp);
			}
			catch (Exception e) {
				log.error("合同到期提醒异常，tenantId={}",tenantId, e);
			}
			finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		}


		log.info("cronTask[Contract expire notice]------------------------end execution,time {}", System.currentTimeMillis());
		XxlJobHelper.log("XxlJob contractExpireJobHandler end");
		return ReturnT.SUCCESS;
	}
}
