package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.convert.ConversionService;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class DataSimpleUtil {
    public static <T extends DataSimple> void copyNewValue(T existObj, T newObj, String... ignoreFields) {
        if (existObj == null || newObj == null) {
            return;
        }
        val propertyDescriptorMap = Arrays.stream(BeanUtils.getPropertyDescriptors(existObj.getClass())).collect(Collectors.toMap(e -> e.getName(), Function.identity()));
        Set<String> ignoreSet = ArrayUtils.isEmpty(ignoreFields) ? null : Sets.newHashSet(ignoreFields);
        var existObjProperties = existObj.getProperties();
        var iterator = newObj.getProperties().entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            var key = entry.getKey();
            if (!existObjProperties.containsKey(key) || (Objects.nonNull(ignoreSet) && ignoreSet.contains(key))) {
                continue;
            }
            var newValue = getStrValue(entry.getValue());
            var oldValue = getStrValue(existObjProperties.get(key));
            if (!Objects.equals(newValue, oldValue)) {
                existObjProperties.add(key, newValue);
                setValueToEntity(key, existObj, propertyDescriptorMap);
            }
        }
    }

    @SneakyThrows
    private static <T extends DataSimple> void setValueToEntity(String key, T entity, Map<String, PropertyDescriptor> propertyDescriptorMap) {
        if (!propertyDescriptorMap.containsKey(key)) {
            return;
        }
        PropertyValue propertyValue = entity.getProperties().get(key);
        PropertyDescriptor propertyDescriptor = propertyDescriptorMap.get(key);
        if (PropertyValue.class.isAssignableFrom(propertyDescriptor.getPropertyType())) {
            propertyDescriptor.getWriteMethod().invoke(entity, FastjsonUtil.convertObject(propertyValue, Class.forName(propertyDescriptor.getPropertyType().getName())));
            return;
        }
        Object value = SpringUtil.getBean(ConversionService.class).convert(getStrValue(propertyValue), propertyDescriptor.getPropertyType());
        propertyDescriptor.getWriteMethod().invoke(entity, value);
    }

    @SneakyThrows
    private static <T extends DataSimple> T copyDataSimple(T obj) {
        T target = (T) obj.getClass().newInstance();
        PropertyDescriptor[] parentDescriptors = BeanUtils.getPropertyDescriptors(DataSimple.class);
        for (PropertyDescriptor descriptor : parentDescriptors) {
            Method readMethod = descriptor.getReadMethod();
            Method writeMethod = descriptor.getWriteMethod();
            if (readMethod != null && writeMethod != null) {
                Object value = readMethod.invoke(obj);
                writeMethod.invoke(target, value);
            }
        }
        return target;
    }

    @SneakyThrows
    public static void copyOldValueWhenNull(DataSimple existObj, DataSimple newObj, String... ignoreFields) {
        if (existObj == null || newObj == null) {
            return;
        }
        val propertyDescriptorMap = Arrays.stream(BeanUtils.getPropertyDescriptors(newObj.getClass())).collect(Collectors.toMap(e -> e.getName(), Function.identity()));
        Set<String> ignoreSet = ArrayUtils.isEmpty(ignoreFields) ? null : Sets.newHashSet(ignoreFields);
        var newObjProperties = newObj.getProperties();
        var iterator = existObj.getProperties().entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            var key = entry.getKey();
            var value = entry.getValue();
            if (Objects.nonNull(ignoreSet) && ignoreSet.contains(key)) {
                continue;
            }
            if (StringUtils.isNotBlank(getStrValue(value)) &&
                    (!newObjProperties.containsKey(key) || StringUtils.isBlank(getStrValue(newObjProperties.get(key))))) {
                PropertyDescriptor propertyDescriptor = propertyDescriptorMap.get(key);
                if (Objects.nonNull(propertyDescriptor) && Objects.nonNull(propertyDescriptor.getReadMethod().invoke(newObj))) {
                    continue;
                }
                newObj.getProperties().add(key, value);
                setValueToEntity(key, newObj, propertyDescriptorMap);
            }
        }
    }

    public static String getStrValue(PropertyValue value) {
        if (value == null) {
            return "";
        }
        String valueStr = null;
        if (value instanceof DictSimple) {
            DictSimple dictSimple = (DictSimple) value;
            valueStr = dictSimple.getValue();
        } else if (value instanceof EnumSimple) {
            EnumSimple enumSimple = (EnumSimple) value;
            valueStr = enumSimple.getValue();
        } else if (value instanceof ProvinceCity) {
            ProvinceCity provinceCity = (ProvinceCity) value;
            if (!Objects.isNull(provinceCity.getProvinceName()) && !Objects.isNull(provinceCity.getCityName())) {
                valueStr = String.format("%s/%s", provinceCity.getProvinceName(), provinceCity.getCityName());
            } else if (!Objects.isNull(provinceCity.getProvinceName())) {
                valueStr = provinceCity.getProvinceName();
            } else if (!Objects.isNull(provinceCity.getCityName())) {
                valueStr = provinceCity.getCityName();
            }
        } else if (value instanceof JobGradeRange) {
            JobGradeRange jobGradeRange = (JobGradeRange) value;
            var channelName = jobGradeRange.getChannelName();
            if (StringUtils.isNotBlank(jobGradeRange.getStartGradeName()) && StringUtils.isNotBlank(jobGradeRange.getEndGradeName())) {
                valueStr = String.format("%s/%s-%s", channelName, jobGradeRange.getStartGradeName(), jobGradeRange.getEndGradeName());
            } else if (StringUtils.isNotBlank(jobGradeRange.getStartGradeName())) {
                valueStr = String.format("%s/%s", channelName, jobGradeRange.getStartGradeName());
            } else {
                valueStr = "";
            }
        } else if (value instanceof EmpSimple) {
            EmpSimple empSimple = (EmpSimple) value;
            if (StringUtils.isNotBlank(empSimple.getName())) {
                valueStr = empSimple.getName().replaceAll("\\t", "").replaceAll("\t", "");
            } else if (StringUtils.isNotBlank(empSimple.getEnName())) {
                valueStr = empSimple.getEnName().replaceAll("\\t", "").replaceAll("\t", "");
            } else {
                valueStr = "";
            }
        } else if (value instanceof PhoneSimple) {
            PhoneSimple phoneSimple = (PhoneSimple) value;
            valueStr = phoneSimple.getValue();
        } else if (value instanceof Address) {
            Address address = (Address) value;
            valueStr = address.doText();
        } else if (value instanceof SimplePropertyValue) {
            SimplePropertyValue simplePropertyValue = (SimplePropertyValue) value;
            valueStr = simplePropertyValue.getValue();
        } else {
            valueStr = String.valueOf(value);
        }
        return valueStr;
    }

    @SneakyThrows
    public static <T extends DataSimple> void initDataSimpleEntity(T entity) {
        if (entity == null) {
            return;
        }
        val propertyDescriptors = BeanUtils.getPropertyDescriptors(entity.getClass());
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            if (PropertyValue.class.isAssignableFrom(propertyDescriptor.getPropertyType())) {
                entity.getProperties().put(propertyDescriptor.getName(), (PropertyValue) propertyDescriptor.getReadMethod().invoke(entity));
            } else {
                Object value = propertyDescriptor.getReadMethod().invoke(entity);
                entity.getProperties().put(propertyDescriptor.getName(), new SimplePropertyValue(Objects.isNull(value) ? "" : String.valueOf(value)));
            }
        }
    }
}