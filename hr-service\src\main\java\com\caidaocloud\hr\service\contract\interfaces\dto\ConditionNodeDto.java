package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.hr.service.contract.application.enums.ConditionNodeRelation;
import com.caidaocloud.hr.service.contract.application.enums.ConditionNodeType;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
@Slf4j
public class ConditionNodeDto {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("分组类型")
    private ConditionNodeType type;
    @ApiModelProperty("分组关系")
    private ConditionNodeRelation relation;
    @ApiModelProperty("子分组")
    private List<ConditionNodeDto> children = Lists.list();
    @ApiModelProperty("分组表达式")
    private ConditionExpDto condition;

    public List<Long> filterEmpIds(List<Long> empIds) {
        if(ConditionNodeType.group.equals(type)){
            List<Long> result = children.get(0).filterEmpIds(empIds);
            for(int i = 1; i < children.size();i++){
                if(ConditionNodeRelation.and.equals(relation)){
                    result = (List<Long>)CollectionUtils.intersection(result, children.get(i).filterEmpIds(empIds));
                }else{
                    result = (List<Long>)CollectionUtils.union(result, children.get(i).filterEmpIds(empIds));
                }
            }
            return result;
        }else{
            List<Long> filterEmpIds = condition.filterEmpIds(empIds);
            return filterEmpIds;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConditionNodeDto that = (ConditionNodeDto) o;
        return relation == that.relation && Objects.equals(children, that.children) && Objects.equals(condition, that.condition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(relation, children, condition);
    }

    public boolean isIncomplete() {
        return condition==null || condition.isIncomplete();
    }
}