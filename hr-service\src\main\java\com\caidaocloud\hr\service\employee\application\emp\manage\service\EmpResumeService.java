package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.common.application.dto.TemporaryDto;
import com.caidaocloud.hr.service.common.application.feign.HrPaasFeignClient;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.common.infrastructure.utils.PatternMatchUtil;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumeConfigDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumePropDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeEmpMergeConfig;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeFormConfig;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.transfer.application.feign.AuthFeignClient;
import com.caidaocloud.hr.service.transfer.application.service.TransferDynamicService;
import com.caidaocloud.hr.service.transfer.interfaces.dto.TransferQueryDto;
import com.caidaocloud.hr.service.util.ObjVarValueUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.oss.file.LocalMultipartFile;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.ByteArrayPictureRenderData;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.UrlPictureRenderData;
import com.deepoove.poi.data.style.PictureStyle;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Pair;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpResumeService {
    @Resource
    private OssService ossService;
    @Resource
    private TransferDynamicService transferDynamicService;
    @Resource
    private AuthFeignClient authFeignClient;
    @Resource
    private HrPaasFeignClient paasFeignClient;
    @NacosValue("${caidaocloud.oss.resumeTemp:true}")
    private boolean resumeTemp = true;

    public Map<String, Object> getEmpResumeData(String empId, Long dataTime) {
        EmpResumeConfigDto config = getResumeConfig();
        return getEmpResumeData(empId, dataTime, config);
    }

    public Map<String, Object> getEmpResumeData(String empId, Long dataTime, EmpResumeConfigDto config) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("nowDate", DateUtil.formatDate(System.currentTimeMillis()));
        if (null == config || null == config.getConfigs() || config.getConfigs().isEmpty()) {
            return dataMap;
        }

        config.getConfigs().forEach(item -> {
            List<DataSimple> dataList = loadDataSimpleList(item, empId, dataTime, config);
            if (null == dataList || dataList.isEmpty()) {
                dataMap.put(item.getAlias(), item.isMultiple() ? getEmptyDataList() : new HashMap<>());
                return;
            }
            if (!item.isMultiple()) {
                dataList.subList(1, dataList.size()).clear();
            }
            convertData(item, dataMap, dataList);
        });
        return dataMap;
    }

    private List<DataSimple> loadDataSimpleList(EmpResumePropDto item, String empId, Long dataTime, EmpResumeConfigDto config) {
        switch (item.getIdentifier()) {
            case "EmpCurrentContract":
                List<ContractDo> reList = SpringUtil.getBean(ContractService.class).getEmpCurrentContract(empId);
                ContractDo ret = reList.stream()
                        .filter(cd -> {
                            cd.calcStatus();
                            return null != cd.getStartDate() && cd.getStartDate() <= dataTime
                                    && null != cd.getEndDate() && dataTime <= cd.getEndDate()
                                    && ContractStatusEnum.EFFECTIVE.getIndex().equals(cd.getContractStatus().getValue());
                        })
                        .findFirst().orElse(null == reList || reList.isEmpty() ? new ContractDo() : reList.get(0));
                if (null != ret) {
                    ret.convertContractStatus();
                    ret.getProperties().put("contractStatus", ret.getContractStatus());
                }
                return Lists.newArrayList(ret);
            default:
                List<DataSimple> dataList = DataQuery.identifier(item.getIdentifier()).decrypt()
                        .specifyLanguage().queryInvisible().limit(500, 1)
                        .filter(DataFilter.eq("empId", empId)
                                .andNe("deleted", Boolean.TRUE.toString())
                                .andEq("tenantId", config.getTenantId()), DataSimple.class, dataTime)
                        .getItems();
                return dataList;
        }
    }

    private List<Map> getEmptyDataList() {
        List<Map> list = new ArrayList<>();
        list.add(new HashMap());
        return list;
    }

    private void convertData(EmpResumePropDto config, Map<String, Object> dataMap, List<DataSimple> dataList) {
        boolean multiple = config.isMultiple();
        String alias = config.getAlias();
        List<Map> itemList = new ArrayList<>();
        dataList.forEach(item -> {
            Map<String, Object> data = new HashMap<>();
            item.getProperties().forEach((k, pValue) -> {
                k = multiple ? k : alias + "$" + k;
                if (pValue instanceof SimplePropertyValue) {
                    SimplePropertyValue spv = (SimplePropertyValue) pValue;
                    if (null == spv) {
                        return;
                    }
                    if (PropertyDataType.Timestamp.equals(spv.getType())) {
                        data.put(k, StringUtil.isEmpty(spv.getValue()) ? "" : DateUtil.formatDate(Long.valueOf(spv.getValue())));
                    } else {
                        data.put(k, emptyTxt(spv.getValue()));
                    }
                } else if (pValue instanceof EnumSimple) {
                    data.put(k, emptyTxt(((EnumSimple) pValue).getText()));
                } else if (pValue instanceof Attachment) {
                    List<String> urls = ((Attachment) pValue).getUrls();
                    data.put(k, null != urls && !urls.isEmpty() ? getPictureRenderData(urls.get(0), config) : null);
                } else if (pValue instanceof Address) {
                    data.put(k, ((Address) pValue).doText());
                } else if (pValue instanceof PhoneSimple) {
                    data.put(k, emptyTxt(((PhoneSimple) pValue).getValue()));
                } else if (pValue instanceof DictSimple) {
                    data.put(k, emptyTxt(((DictSimple) pValue).getText()));
                }
            });
            postMethod(config, data);
            if (!multiple) {
                dataMap.putAll(data);
            } else {
                itemList.add(data);
            }
        });
        if (multiple) {
            dataMap.put(alias, itemList);
        }
    }

    private PictureRenderData getPictureRenderData(String url, EmpResumePropDto config) {
        if (StringUtil.isEmpty(url)) {
            return null;
        }

        InputStream inputStream = null;
        try {
            PictureStyle ps = new PictureStyle();
            ps.setWidth(config.getWidth());
            ps.setHeight(config.getHeight());
            if (url.startsWith("http")) {
                return new UrlPictureRenderData(url, PictureType.suggestFileType(url));
            }

            inputStream = ossService.getInputStream(url);
            if (null == inputStream) {
                return null;
            }
            byte[] bytes = IOUtils.toByteArray(inputStream);
            ByteArrayPictureRenderData bap = new ByteArrayPictureRenderData(bytes, PictureType.suggestFileType(url));
            bap.setPictureStyle(ps);
            return bap;
        } catch (Exception e) {
            log.error("read pic byte by oss err,errMsg={}", e.getMessage(), e);
            return null;
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                }
            }
        }
    }

    private void postMethod(EmpResumePropDto config, Map<String, Object> data) {
        if (StringUtil.isEmpty(config.getPostMethod())) {
            return;
        }
        switch (config.getPostMethod()) {
            case "calcWorkAge":
                setNewWorkAage(data);
                break;
        }
    }

    private void setNewWorkAage(Map<String, Object> data) {
        Object fwd = data.get("firstWorkDate"), waa = data.get("workAgeAdjust");
        Long firstWorkDate = null == fwd ? 0 : Long.valueOf(fwd.toString());
        Float workAgeAdjust = null == waa ? 0f : Float.valueOf(waa.toString());
        BigDecimal workAge = ObjVarValueUtil.calcWorkAge(firstWorkDate, workAgeAdjust);
        data.put("workAge", workAge);
    }

    private String emptyTxt(String txt) {
        return null == txt ? "" : txt;
    }

    public EmpResumeConfigDto getResumeConfig() {
        String key = getCacheRuleKey();
        String cacheKey = String.format("kv_%s", key);
        String value = SpringUtil.getBean(CacheService.class).getValue(cacheKey);
        if (StringUtil.isNotEmpty(value)) {
            EmpResumeConfigDto empResumeConfigDto = EmpResumeConfigDto.convert(value);
            if (StringUtils.isBlank(empResumeConfigDto.getTenantId())) {
                empResumeConfigDto.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
            }
            return empResumeConfigDto;
        }
        return getResumeRule(key);
    }

    public EmpResumeConfigDto getResumeRule(String key) {
        Result<String> result = SpringUtil.getBean(ITenantFeignClient.class).getKv(key);
        EmpResumeConfigDto ruleDto = null;
        if (null == result || !result.isSuccess() || null == (ruleDto = EmpResumeConfigDto.convert(result.getData()))) {
            return new EmpResumeConfigDto();
        }
        if (StringUtils.isBlank(ruleDto.getTenantId())) {
            ruleDto.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        return ruleDto;
    }

    private String getCacheRuleKey() {
        return HrConstant.EMP_RESUME_RULE + UserContext.getTenantId();
    }

    public void saveConfigTemplate(EmpResumeConfigDto data) {
        EmpResumeConfigDto config = getResumeConfig();
        if (null == config) {
            save(data);
            return;
        }

        config.setTemplate(data.getTemplate());
        save(config);
    }

    private void initConfig(EmpResumeConfigDto config) {
        String configStr = "[{\"alias\":\"a\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkInfo\",\"multiple\":false,\"timeline\":true,\"width\":120},{\"alias\":\"b\",\"height\":120,\"identifier\":\"entity.hr.EmpPrivateInfo\",\"multiple\":false,\"timeline\":false,\"width\":120},{\"alias\":\"edus\",\"height\":120,\"identifier\":\"entity.hr.EmpEduExp\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"ewes\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkExperience\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"c\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkOverview\",\"multiple\":false,\"postMethod\":\"calcWorkAge\",\"timeline\":false,\"width\":120},{\"alias\":\"d\",\"height\":120,\"identifier\":\"EmpCurrentContract\",\"multiple\":false,\"timeline\":false,\"width\":120}]";
        List<EmpResumePropDto> empResumePropDtos = FastjsonUtil.toList(configStr, EmpResumePropDto.class);
        config.setConfigs(empResumePropDtos);
    }

    public void save(EmpResumeConfigDto data) {
        initConfig(data);
        data.setId(0L);
        KvDto kvDto = new KvDto();
        kvDto.setProperty(getCacheRuleKey());
        kvDto.setContent(FastjsonUtil.toJson(data));
        Result<KvDto> result = SpringUtil.getBean(ITenantFeignClient.class).saveKv(kvDto);
        if (null != result && result.isSuccess()) {
            String key = getCacheRuleKey();
            String cacheKey = String.format("kv_%s", key);
            SpringUtil.getBean(CacheService.class).remove(cacheKey);
            SpringUtil.getBean(CacheService.class).cacheValue(cacheKey, FastjsonUtil.toJson(data));
        }
    }

    public void preview(String empId, Long dataTime, HttpServletResponse response) {
        doEmpResume(empId, dataTime, response, null, null, 0);
    }

    public UploadResult doEmpResume(String empId, Long dataTime, HttpServletResponse response,
                                    String filePath, String template, int fileType) {
        EmpResumeConfigDto erc = getResumeConfig();
        if (StringUtil.isEmpty(erc.getTemplate()) && StringUtil.isEmpty(template)) {
            log.warn("emp resume template file is not found.");
            throw new ServerException(LangUtil.getLangMsg("caidao.resume.error.check_no_template"));
        }

        if (null == erc.getConfigs() || erc.getConfigs().isEmpty()) {
            log.warn("emp resume template the binding field is empty.");
            throw new ServerException(LangUtil.getLangMsg("caidao.resume.error.check_no_unbound"));
        }

        Map<String, Object> dataMap = getEmpResumeData(empId, dataTime, erc);
        ConfigureBuilder cb = Configure.builder().useSpringEL(false);
        for (EmpResumePropDto conf : erc.getConfigs()) {
            if (conf.isMultiple()) {
                cb = cb.bind(conf.getAlias(), new LoopRowTableRenderPolicy());
            }
        }
        val transferDataMap = dymaicOfTransfer(empId);
        if (MapUtils.isNotEmpty(transferDataMap)) {
            for (Map.Entry<String, Object> entry : transferDataMap.entrySet()) {
                cb = cb.bind(entry.getKey(), new LoopRowTableRenderPolicy());
            }
            dataMap.putAll(transferDataMap);
        }

        // 处理mergeConfigs配置的数据组装逻辑
        if (erc.getMergeConfigs() != null && MapUtils.isNotEmpty(erc.getMergeConfigs())) {
            Map<String, Object> mergedDataMap = processMergeConfigs(transferDataMap, erc.getMergeConfigs());
            if (MapUtils.isNotEmpty(mergedDataMap)) {
                for (Map.Entry<String, Object> entry : mergedDataMap.entrySet()) {
                    cb = cb.bind(entry.getKey(), new LoopRowTableRenderPolicy());
                }
                dataMap.putAll(mergedDataMap);
            }
        }

        Map<String, Object> formDataMap = handleFormData(empId);
        if (MapUtils.isNotEmpty(formDataMap)) {
            for (Map.Entry<String, Object> entry : formDataMap.entrySet()) {
                cb = cb.bind(entry.getKey(), new LoopRowTableRenderPolicy());
            }
            dataMap.putAll(formDataMap);
            if (MapUtils.isNotEmpty(erc.getFormConfigs())) {
                for (Map.Entry<String, ? extends ResumeFormConfig> entry : erc.getFormConfigs().entrySet()) {
                    AbsFormResumeService.process(cb, dataMap, entry.getKey(), formDataMap, entry.getValue());
                }
            }
        }
        Configure config = cb.build();
        InputStream is = null;
        OutputStream out = null;
        XWPFTemplate xwpfTemplate = null;
        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            is = StringUtil.isEmpty(template) ?
                    SpringUtil.getBean(OssService.class).getInputStream(erc.getTemplate())
                    : new FileInputStream(template);
            xwpfTemplate = XWPFTemplate.compile(is, config).render(dataMap);
            if (null != response) {
                String fileName = URLEncoder.encode(1 == fileType ? "员工履历.pdf" : "员工履历.docx", "UTF-8");
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                out = response.getOutputStream();
            } else if (2 == fileType) {

            } else {
                out = new FileOutputStream(filePath);
            }
            if (1 == fileType) {
                word2pdf(xwpfTemplate.getXWPFDocument(), out);
                out.flush();
            } else if (0 == fileType) {
                xwpfTemplate.writeAndClose(out);
            } else if (2 == fileType) {
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                xwpfTemplate.writeAndClose(os);
                LocalMultipartFile file = new LocalMultipartFile("员工履历.docx", os.toByteArray(),
                        "application/docx;charset=UTF-8");
                file.setOriginalFilename("员工履历.docx");
                return SpringUtil.getBean(OssService.class).upload(getBucketName(erc.getTenantId()), file);
            }
        } catch (Exception e) {
            log.error("create resume word err. errMsg={}", e.getMessage(), e);
        } finally {
            if (null != xwpfTemplate) {
                try {
                    xwpfTemplate.close();
                } catch (Exception e) {
                    log.error("close xwpfTemplate stream err. errMsg={}", e.getMessage(), e);
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (Exception e) {
                    log.error("close out stream err. errMsg={}", e.getMessage(), e);
                }
            }
            if (null != is) {
                try {
                    is.close();
                } catch (Exception e) {
                    log.error("close is stream err. errMsg={}", e.getMessage(), e);
                }
            }
        }
        return null;
    }

    private InputStream getOssInputStream(String filePath) {
        InputStream is = null;
        InputStream inputStream = null;
        try {
            is = SpringUtil.getBean(OssService.class).getInputStream(filePath);
            byte[] bytes = IOUtils.toByteArray(is);
            inputStream = new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            log.error("oss input stream to ByteArrayInputStream err. errMsg={}", e.getMessage(), e);
        } finally {
            if (null != is) {
                try {
                    is.close();
                } catch (Exception e) {
                    log.error("close oss input stream err. errMsg={}", e.getMessage(), e);
                }
            }
        }
        return inputStream;
    }

    private void word2pdf(XWPFDocument document, OutputStream out) throws Exception {
        PdfOptions options = PdfOptions.create();
        PdfConverter.getInstance().convert(document, out, options);
    }

    public void pdf(String empId, Long dataTime, HttpServletResponse response) {
        doEmpResume(empId, dataTime, response, null, null, 1);
    }

    public UploadResult wordFile(String empId, Long dataTime, String template) {
        UploadResult ur = doEmpResume(empId, dataTime, null, null, template, 2);
        if (null == ur || StringUtil.isEmpty(ur.getObjectPath()) || !resumeTemp) {
            return ur;
        }
        try {
            DataSimple ossFileData = new DataSimple();
            ossFileData.setDeleted(false);
            ossFileData.getProperties().add("filePath", ur.getObjectPath());
            ossFileData.getProperties().add("fileName", ur.getFileName());
            ossFileData.getProperties().add("temporary", "true");
            ossFileData.getProperties().add("fileId", "hr_resume");
            ossFileData.getProperties().add("fileType", "PDF");
            ossFileData.setCreateTime(System.currentTimeMillis());
            ossFileData.setUpdateTime(ossFileData.getCreateTime());
            SecurityUserInfo sui = SecurityUserUtil.getSecurityUserInfo();
            ossFileData.setTenantId(sui.getTenantId());
            ossFileData.setCreateBy("" + sui.getUserId());
            ossFileData.setUpdateBy(ossFileData.getCreateBy());
            DataInsert.identifier("entity.esign.EsignOssFile").insert(ossFileData);
        } catch (Exception e) {
            log.error("insert oss temp file record err,errMsg={}", e.getMessage(), e);
        }
        return ur;
    }

    public String getBucketName(String tenantId) {
        return String.format("%s%s", "resume", tenantId);
    }

    private Map<String, Object> dymaicOfTransfer(String empId) {
        Map<String, Object> data = Maps.newHashMap();
        Result<List<String>> transferCodeResult = authFeignClient.getResourceCodeBySubjectId(SecurityUserUtil.getSecurityUserInfo().getUserId(), "transfer");
        if (CollectionUtils.isEmpty(transferCodeResult.getData())) {
            return data;
        }
        List<String> transferTypeIdList = transferCodeResult.getData().stream().filter(e -> StringUtils.isNotBlank(e))
                .distinct().map(e -> e.split("_")[1])
                .collect(Collectors.toList());
        val transferQueryDto = new TransferQueryDto();
        transferQueryDto.setPageSize(100);
        transferQueryDto.setEmpId(empId);
        for (String transferTypeId : transferTypeIdList) {
            transferQueryDto.setTypeId(transferTypeId);
            var temporary = new TemporaryDto<Pair<Map<String, List<MetadataPropertyDto>>, List<String>>>();
            val result = transferDynamicService.page(transferQueryDto, temporary);
            var configMap = temporary.getTemporaryEntity().first();
            Map<String, Map<String, DynamicPropertyDto>> dynamicConfig = getDynamicConfig(temporary.getTemporaryEntity().second().stream().map(e -> String.format("TRANSFER_%s", e)).collect(Collectors.toList()));
            List<Map<String, Object>> dataList = result.getItems().stream().map(e -> {
                String typeId = String.valueOf(e.get("typeId"));
                var iterator = e.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Object> next = iterator.next();
                    String key = next.getKey();
                    if (!key.contains("@")) {
                        continue;
                    }
                    String[] split = key.split("@");
                    Object value = next.getValue();
                    if (configMap != null && configMap.containsKey(split[1]) && Objects.nonNull(value)) {
                        Optional<MetadataPropertyDto> optional = configMap.get(split[1]).stream().filter(en -> split[0].equals(en.getProperty())).findFirst();
                        if (optional.isPresent() && (PropertyDataType.Timestamp.equals(optional.get().getDataType()) || PropertyDataType.Timestamp_Array.equals(optional.get().getDataType()))) {
                            MetadataPropertyDto metadataProperty = optional.get();
                            value = handleTimeStamp(value, metadataProperty.getDataType(), metadataProperty.getFormat());
                        }
                    } else if (dynamicConfig.containsKey(typeId) && dynamicConfig.get(typeId).containsKey(key)) {
                        DynamicPropertyDto dynamicProperty = dynamicConfig.get(typeId).get(key);
                        value = handleTimeStamp(value, dynamicProperty.getDataType(), dynamicProperty.getFormat());
                    }
                    iterator.remove();
                    String newKey = key.replace("@", "$").replace(".", "_");
                    e.put(newKey, value);
                    iterator = e.entrySet().iterator();
                }
                return e;
            }).collect(Collectors.toList());
            data.put(String.format("TRANSFER_%s", transferTypeId), dataList);
        }
        return data;
    }

    private Map<String, Map<String, DynamicPropertyDto>> getDynamicConfig(List<String> changeDefList) {
        if (CollectionUtils.isEmpty(changeDefList)) {
            return Maps.newHashMap();
        }
        Result<List<DynamicColumnConfigDto>> result = paasFeignClient.dynamicTablesSet(changeDefList);
        List<DynamicColumnConfigDto> data = result.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, DynamicPropertyDto>> resultMap = data.stream().collect(Collectors.toMap(e -> StringUtils.substringAfter(e.getCode(), "_"), e -> {
            return e.getProperties().stream().collect(Collectors.toMap(en -> en.getProperty(), Function.identity()));
        }));
        return resultMap;
    }

    private Map<String, Object> handleFormData(String empId) {
        Map<String, Object> data = Maps.newHashMap();
        var result = paasFeignClient.pageDetail("page.detail.employee");
        if (MapUtils.isEmpty(result.getData())) {
            return data;
        }
        Map<String, Object> map = FastjsonUtil.convertObject(result.getData().get("standardPageConfig"), Map.class);
        if (!map.containsKey("childList") && Objects.isNull(map.get("childList"))) {
            return data;
        }
        List<String> formIdentityList = ((JSONArray) map.get("childList")).stream().filter(e -> {
            JSONObject jsonObject = (JSONObject) e;
            boolean b1 = String.valueOf(jsonObject.get("key")).contains("form");
            boolean b2 = false;
            if (!Objects.isNull(jsonObject.get("props"))) {
                JSONObject jsonObject1 = (JSONObject) jsonObject.get("props");
                b2 = BooleanUtils.toBooleanDefaultIfNull(jsonObject1.get("show") != null ? Boolean.valueOf(jsonObject1.get("show").toString()) : null, false);
            }
            return b1 && b2;
        }).map(e -> {
            val identity = String.valueOf(((JSONObject) e).get("key"));
            val formId = StringUtils.substringAfterLast(identity, ".");
            JSONObject jsonObject = (JSONObject) e;
            for (Object child : ((JSONArray) jsonObject.get("childList"))) {
                JSONObject jsonObj = (JSONObject) child;
                var property = String.valueOf(jsonObj.get("key"));
                var metaProperty = FastjsonUtil.convertObject(jsonObj.get("props"), MetadataPropertyDto.class);
                metaProperty.setProperty(property);
            }
            return identity;
        }).collect(Collectors.toList());
        for (String formIdentiy : formIdentityList) {
            var formId = StringUtils.substringAfterLast(formIdentiy, ".");
            Result<PageResult<Map<String, Object>>> formPage = paasFeignClient.formPage(formId, 1, 100, empId, true);
            formIdentiy = formIdentiy.replace(".", "_");
            data.put(formIdentiy, Objects.isNull(formPage) || Objects.isNull(formPage.getData()) || Objects.isNull(formPage.getData().getItems()) ? Lists.newArrayList() : formPage.getData().getItems());
        }
        return data;
    }

    private Object handleTimeStamp(Object value, PropertyDataType dataType, String format) {
        if (Objects.isNull(value) || StringUtils.isBlank(value.toString()) || dataType == null) {
            return value;
        }
        format = StringUtils.isBlank(format) ? "yyyy-MM-dd" : format;
        switch (dataType) {
            case Timestamp:
                boolean isNumber = PatternMatchUtil.isInteger(value.toString());
                if (!isNumber) {
                    break;
                }
                value = DateUtil.format(Long.valueOf(value.toString()), format);
                break;
            case Timestamp_Array:
                StringBuilder stringBuilder = new StringBuilder();
                List<String> lists = FastjsonUtil.toList(String.valueOf(value), String.class);
                for (int i = 0; i < lists.size(); i++) {
                    String valueStr = lists.get(i);
                    if (StringUtils.isBlank(valueStr)) {
                        continue;
                    }
                    stringBuilder.append(DateUtil.format(Long.valueOf(valueStr), format));
                    if (lists.size() > 1 && i != lists.size() - 1) {
                        stringBuilder.append(" - ");
                    }
                }
                value = stringBuilder.toString();
                break;
            default:
                break;
        }
        return value;
    }

    /**
     * 基于mergeConfigs配置处理数据组装逻辑
     *
     * @param transferDataMap 原始数据映射
     * @param mergeConfigs    合并配置约束
     * @return 处理后的数据映射
     */
    private Map<String, Object> processMergeConfigs(Map<String, Object> transferDataMap,
                                                    Map<String, ResumeEmpMergeConfig> mergeConfigs) {
        Map<String, Object> resultMap = new HashMap<>();
        if (MapUtils.isEmpty(mergeConfigs) || MapUtils.isEmpty(transferDataMap)) {
            return resultMap;
        }
        // 遍历一层属性名
        for (Map.Entry<String, ResumeEmpMergeConfig> levelOneEntry : mergeConfigs.entrySet()) {
            String levelOneKey = levelOneEntry.getKey(); // 一层属性名
            val config = levelOneEntry.getValue();
            if (Objects.isNull(config) || MapUtils.isEmpty(config.getConfig())) {
                continue;
            }
            Map<String, Object> levelOneResult = new HashMap<>();
            // 遍历二层key
            for (Map.Entry<String, List<Map<String, String>>> levelTwoEntry : config.getConfig().entrySet()) {
                String levelTwoKey = levelTwoEntry.getKey(); // 二层key，要set到集合的key
                List<Map<String, String>> levelThreeMappings = levelTwoEntry.getValue(); // 三层映射配置列表

                if (CollectionUtils.isEmpty(levelThreeMappings)) {
                    continue;
                }
                // 从transferDataMap获取对应的数据集合
                Object dataFromTransferMap = transferDataMap.get(levelTwoKey);
                if (dataFromTransferMap == null) {
                    continue;
                }

                List<Map<String, Object>> sourceDataList = new ArrayList<>();
                if (dataFromTransferMap instanceof List) {
                    sourceDataList = (List<Map<String, Object>>) dataFromTransferMap;
                } else if (dataFromTransferMap instanceof Map) {
                    sourceDataList.add((Map<String, Object>) dataFromTransferMap);
                }

                if (CollectionUtils.isEmpty(sourceDataList)) {
                    continue;
                }

                // 处理三层映射配置，聚合到一个集合中
                List<Map<String, Object>> aggregatedList = new ArrayList<>();

                for (Map<String, Object> sourceData : sourceDataList) {
                    for (Map<String, String> mappingConfig : levelThreeMappings) {
                        Map<String, Object> mappedData = new HashMap<>();

                        // 基于四层做数据转换映射
                        for (Map.Entry<String, String> mappingEntry : mappingConfig.entrySet()) {
                            String targetKey = mappingEntry.getKey(); // 目标key（左边）
                            String sourceKey = mappingEntry.getValue(); // 源key（右边）
                            Object value = null;
                            // 四层映射中如目标key(右边)带有"txt:"字样，则最终值以"txt:"后面的值为准
                            if (sourceKey.startsWith("txt:")) {
                                value = sourceKey.substring(4); // 去掉"txt:"前缀
                            } else {
                                value = sourceData.get(sourceKey);
                            }
                            // 四层转换映射时候属性还是以key为准，value为具体集合中获取的数据
                            mappedData.put(targetKey, value != null ? value : "");
                        }
                        aggregatedList.add(mappedData);
                    }
                }

                // 将3层集合的结果，再基于2层的key放入一个map中
                levelOneResult.put(levelTwoKey, aggregatedList);
            }

            // 将处理后的结果转换为 List<Map> 并进行排序
            List<Map<String, Object>> sortedResultList = convertAndSortMergeResult(levelOneResult, config);
            if (!CollectionUtils.isEmpty(config.getNotBlankFiledList()) && !CollectionUtils.isEmpty(sortedResultList)) {
                sortedResultList.stream().filter(e -> {
                    boolean flag = true;
                    for (String field : config.getNotBlankFiledList()) {
                        Object val = e.get(field);
                        if (Objects.isNull(val) || StringUtils.isBlank(val.toString())) {
                            flag = false;
                            break;
                        }
                    }
                    return flag;
                });
            }
            // 将排序后的结果按一层属性名放入最终结果中
            resultMap.put(levelOneKey, Objects.nonNull(sortedResultList) ? sortedResultList : Lists.newArrayList());
        }
        return resultMap;
    }

    /**
     * 将聚合结果转换为List<Map>并进行排序
     *
     * @param levelOneResult 聚合结果
     * @param config         配置信息
     * @return 排序后的List<Map>
     */
    private List<Map<String, Object>> convertAndSortMergeResult(Map<String, Object> levelOneResult, ResumeEmpMergeConfig config) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 将聚合后的结果转换成一个List<Map>
        for (Map.Entry<String, Object> entry : levelOneResult.entrySet()) {
            if (entry.getValue() instanceof List) {
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                resultList.addAll(dataList);
            }
        }
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        // 根据mainSortField配置处理排序字段
        if (config.getMainSortField() != null && StringUtils.isNotEmpty(config.getMainSortField().getField())) {
            addSort(resultList, config);
            if (Objects.nonNull(config.getSecondSortList()) &&
                    StringUtils.isNotEmpty(config.getSecondSortList().getField()) &&
                    CollectionUtils.isNotEmpty(config.getSecondSortList().getSortList())) {
                // 二次排序
                performSecondarySort(resultList, config.getSecondSortList());
            } else {
                // 根据sort字段排序
                resultList.sort((map1, map2) -> {
                    Object sort1 = map1.get("sort");
                    Object sort2 = map2.get("sort");
                    if (sort1 == null && sort2 == null) {
                        return 0;
                    }
                    if (sort1 == null) {
                        return -1;
                    }
                    if (sort2 == null) {
                        return 1;
                    }
                    if (sort1 instanceof Comparable && sort2 instanceof Comparable) {
                        return -((Comparable) sort1).compareTo(sort2);
                    }
                    return -sort1.toString().compareTo(sort2.toString());
                });
            }
        }
        return resultList;
    }

    /**
     * 增加sort字段
     *
     * @param resultList
     * @param config
     */
    private void addSort(List<Map<String, Object>> resultList, ResumeEmpMergeConfig config) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        String sortField = config.getMainSortField().getField();
        String dataType = config.getMainSortField().getDataType();
        // 为每个Map添加sort字段
        for (Map<String, Object> dataMap : resultList) {
            Object fieldValue = dataMap.get(sortField);
            if (fieldValue != null && "DATE_TXT".equals(dataType)) {
                // 将日期字符串转换为long类型时间戳
                try {
                    long timestamp = convertDateToTimestamp(fieldValue.toString());
                    dataMap.put("sort", timestamp);
                } catch (Exception e) {
                    log.warn("Failed to convert date string to timestamp: {}", fieldValue, e);
                    dataMap.put("sort", 0L);
                }
            } else {
                // 其他数据类型，直接使用原值作为sort
                dataMap.put("sort", fieldValue != null ? fieldValue : "");
            }
        }
    }

    /**
     * 执行二次排序
     *
     * @param resultList 待排序的列表
     * @param secondSort 二次排序配置
     */
    private void performSecondarySort(List<Map<String, Object>> resultList, ResumeEmpMergeConfig.SortFieldVal secondSort) {
        String secondSortField = secondSort.getField();
        if (StringUtils.isBlank(secondSortField)) {
            log.warn("secondSortField is empty");
            return;
        }
        List<ResumeEmpMergeConfig.FieldValSort> sortList = secondSort.getSortList();
        // 创建值到排序顺序的映射
        Map<String, Integer> valueToSortOrder = new HashMap<>();
        for (ResumeEmpMergeConfig.FieldValSort fieldValSort : sortList) {
            valueToSortOrder.put(fieldValSort.getVal(), fieldValSort.getSort());
        }
        Map<Object, List<Map<String, Object>>> group = resultList.stream().collect(Collectors.groupingBy(e -> e.get("sort")));
        //二次排序字段排序
        group.forEach((k, v) -> {
            if (CollectionUtils.isEmpty(v) || v.size() == 1) {
                return;
            }
            v.sort((map1, map2) -> {
                Object value1 = map1.get(secondSortField);
                Object value2 = map2.get(secondSortField);
                String val1 = value1 != null ? value1.toString() : "";
                String val2 = value2 != null ? value2.toString() : "";
                Integer order1 = valueToSortOrder.get(val1);
                Integer order2 = valueToSortOrder.get(val2);
                if (order1 != null && order2 != null) {
                    return order1.compareTo(order2);
                } else if (order1 != null) {
                    return -1;
                } else if (order2 != null) {
                    return 1;
                } else {
                    return val1.compareTo(val2);
                }
            });
        });
        resultList.clear();
        group.keySet().stream().sorted((v1, v2) -> -v1.toString().compareTo(v2.toString())).forEach(e -> {
            List<Map<String, Object>> list = group.getOrDefault(e, null);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            resultList.addAll(list);
        });
    }

    /**
     * 将日期字符串转换为时间戳
     *
     * @param dateString 日期字符串，格式如 "2025-06-03"
     * @return 时间戳
     */
    private long convertDateToTimestamp(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return 0L;
        }

        try {
            // 尝试解析多种日期格式
            java.time.format.DateTimeFormatter[] formatters = {
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                    java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                    java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")
            };

            for (java.time.format.DateTimeFormatter formatter : formatters) {
                try {
                    if (dateString.contains(":")) {
                        // 包含时间部分
                        java.time.LocalDateTime localDateTime = java.time.LocalDateTime.parse(dateString, formatter);
                        return localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                    } else {
                        // 只有日期部分
                        java.time.LocalDate localDate = java.time.LocalDate.parse(dateString, formatter);
                        return localDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                    }
                } catch (Exception e) {
                    // 尝试下一个格式
                    continue;
                }
            }

            // 如果都失败了，尝试直接解析为长整型（可能已经是时间戳）
            return Long.parseLong(dateString);
        } catch (Exception e) {
            log.warn("Failed to parse date string: {}", dateString, e);
            return 0L;
        }
    }
}
