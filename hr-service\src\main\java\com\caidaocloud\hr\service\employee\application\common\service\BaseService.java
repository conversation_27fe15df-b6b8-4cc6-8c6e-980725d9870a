package com.caidaocloud.hr.service.employee.application.common.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;

public interface BaseService<T, D extends BasePage> extends BaseDomainService<T, D> {
    default T dto2do(Object data){
        return null;
    }

    /**
     * 保存或更新data对象
     */
    T saveOrUpdateObj(Object data);
}
