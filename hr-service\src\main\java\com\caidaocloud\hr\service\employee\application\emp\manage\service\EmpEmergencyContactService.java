package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.EmpEmergencyContactDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEmergencyContactDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpEmergencyContactDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmpEmergencyContactService {
    @Resource
    private EmpEmergencyContactDomainService empEmergencyContactDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public String save(EmpEmergencyContactDto dto) {
        EmpEmergencyContactDo data = ObjectConverter.convert(dto, EmpEmergencyContactDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empEmergencyContactDomainService.save(data);
    }

    public void update(EmpEmergencyContactDto dto) {
        EmpEmergencyContactDo data = ObjectConverter.convert(dto, EmpEmergencyContactDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empEmergencyContactDomainService.update(data);
    }

    private void doConverter(EmpEmergencyContactDto source, EmpEmergencyContactDo target) {
        if (StringUtils.isNotEmpty(source.getRelationType())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getRelationType());
            target.setRelationType(enumSimple);
        }
    }

    public void deleteById(EmpEmergencyContactDo data) {
        empEmergencyContactDomainService.deleteById(data);
    }

    public EmpEmergencyContactDo getById(String bid) {
        return empEmergencyContactDomainService.selectById(bid);
    }

    public List<EmpEmergencyContactDo> getList(String empId) {
        return empEmergencyContactDomainService.selectList(empId);
    }
}
