package com.caidaocloud.hr.service.handler;

import com.caidaocloud.hr.core.feign.ArchivePaasFeign;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 26/6/2024 5:15 下午
 */
@Component
@AllArgsConstructor
public class ProofResolveHandler implements ITypeResolveHandler{

    private ArchivePaasFeign archivePaasFeign;

    @Override
    public List<String> getTypes(String simpleValue) {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        String proofKey = String.format("SIGN_PROOF_%s", securityUserInfo.getTenantId());
        Result<String> result = archivePaasFeign.getKv(proofKey);
        if (result != null && result.isSuccess() && StringUtil.isNotEmpty(result.getData())) {
            List<Map> mapsList = FastjsonUtil.toList(result.getData(), Map.class);
            return mapsList.stream().map(it -> it.getOrDefault("name", ""))
                    .filter(StringUtil::isNotEmpty).map(Object::toString).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
