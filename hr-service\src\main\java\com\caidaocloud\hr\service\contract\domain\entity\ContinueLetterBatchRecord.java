package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.hr.service.contract.application.enums.ContinueLetterBatchType;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/4/25
 */
@Data
@AllArgsConstructor
public class ContinueLetterBatchRecord {
	/**
	 * 合同id
	 */
	private String contractId;
	/**
	 * 批量发起异常类型
	 */
	private ContinueLetterBatchType type;
}
