package com.caidaocloud.hr.service.contract.domain.service;

import cn.hutool.core.lang.mutable.MutablePair;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractConfigDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.entity.EmpContractTypeSetRelDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractConfigRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.ConditionTreeDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractTypeSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.EmpContractTypeSetRelQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpContractTypeSetRelDomainService extends BaseDomainServiceImpl<EmpContractTypeSetRelDo, EmpContractTypeSetRelQueryDto> {
    @Resource
    private EmpContractTypeSetRelDo empContractTypeSetRelDo;
    @Resource
    ContractTypeSetDo contractTypeSetDo;
    @Resource
    ContractSetConditionDo contractSetConditionDo;
    @Resource
    private Locker locker;
    @Resource
    private CacheService cacheService;
    @Resource
    private IContinueContractConfigRepository continueContractConfigRepository;

    private final static String CONTRACT_TYPE_SET_REFRESH_LOCK_KEY = "contract_type_set_refresh_lock_%s",
            CONTRACT_TYPE_SET_PROGRESS = "contract_type_set_progress_%s_%s";

    private final int REL_SIZE = 100;

    @Override
    public BaseDomainDo<EmpContractTypeSetRelDo> getDoService() {
        return empContractTypeSetRelDo;
    }


    public List<EmpContractTypeSetRelDo> getEmpContractTypeList(String empId) {
        return empContractTypeSetRelDo.getEmpContractTypeList(empId);
    }

    @Override
    public void batchSave(List<EmpContractTypeSetRelDo> data) {
        data.forEach(d -> {
            setRequiredField(d, true);
            d.setDataEndTime(DateUtil.MAX_TIMESTAMP);
        });
        empContractTypeSetRelDo.batchSave(data);
    }

    /**
     * 填充系统必填字段
     */
    @Override
    protected void setRequiredField(EmpContractTypeSetRelDo data, boolean saveOrUpdate) {
        UserInfo userInfo = UserContext.preCheckUser();
        final String userId = userInfo.getUserId().toString();
        long currentTimeMillis = System.currentTimeMillis();
        data.setIdentifier(getDoService().getDoIdentifier());
        data.setDeleted(Boolean.FALSE);

        if (!saveOrUpdate) {
            // 修改
            data.setUpdateBy(userId);
            data.setTenantId(userInfo.getTenantId());
            if (data.getUpdateTime() == 0) {
                data.setUpdateTime(currentTimeMillis);
            }
            return;
        }

        data.setCreateBy(userId);
        // 新增时，修改人信息默认为新增人信息
        data.setUpdateBy(userId);
        if (data.getCreateTime() != 0) {
            return;
        }

        data.setCreateTime(currentTimeMillis);
        data.setUpdateTime(currentTimeMillis);
    }

    public void doEmpContractRel(String progress) {
        String tenantId = UserContext.getTenantId();
        // 加锁
        Lock lock = locker.getLock(String.format(CONTRACT_TYPE_SET_REFRESH_LOCK_KEY, tenantId));
        boolean locked;
        try {
            locked = lock.tryLock(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            resetProgress(tenantId, progress);
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
        }

        if (!locked) {
            // 获取锁失败
            log.info("tryLock EmpContractRel fail,tenantId={}", tenantId);
            resetProgress(tenantId, progress);
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
        }

        // ContractTypeSetDo ctsd = null;
        boolean cacheProgress = StringUtil.isEmpty(progress);
        String progressKey = String.format(CONTRACT_TYPE_SET_PROGRESS, tenantId, progress);
        ImportExcelProcessVo processObj = null;

        int completed = 0, pageNo = 0;
        PageResult<ContractTypeSetDo> pageResult;
        ContractTypeSetQueryDto pageInfo = new ContractTypeSetQueryDto();
        Map<String, List<ContractTypeSetDo>> groupByMap = null;
        List<ContractTypeSetDo> values = null;
        List<Long> empIdList = null;

        try {
            pageInfo.setPageSize(REL_SIZE);
            do {
                pageInfo.setPageNo(++pageNo);
                pageInfo.setStatus("0");
                pageResult = contractTypeSetDo.getPage(pageInfo);
                int total = pageResult.getTotal();

                groupByMap = pageResult.getItems().stream().collect(Collectors.groupingBy(ContractTypeSetDo::getEmpTypeKey));

                for (Map.Entry<String, List<ContractTypeSetDo>> entry : groupByMap.entrySet()) {
                    values = entry.getValue();
                    empIdList = new ArrayList<>(5000);
                    for (ContractTypeSetDo ctsd : values) {
                        initEmpContractRel(ctsd, empIdList);

                        if (cacheProgress) {
                            continue;
                        }

                        completed++;
                        // 更新进度条
                        processObj = new ImportExcelProcessVo(progress, total, completed, total - completed, completed, 0);
                        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 300);
                    }
                    empIdList.clear();
                }
            } while (pageNo * REL_SIZE < pageResult.getTotal());
        } catch (Exception e) {
            log.error("doEmpContractRel err,{}", e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    private void resetProgress(String tenantId, String progress) {
        if (StringUtil.isEmpty(progress)) {
            return;
        }

        String progressKey = String.format(CONTRACT_TYPE_SET_PROGRESS, tenantId, progress);
        ImportExcelProcessVo processObj = new ImportExcelProcessVo(progress, 100, 100, 0, 100, 0);
        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 300);
    }

    public void initEmpContractRel(String bid, String progress, List<Long> empIdList) {
        String tenantId = UserContext.getTenantId();
        // 加锁
        Lock lock = locker.getLock(String.format(CONTRACT_TYPE_SET_REFRESH_LOCK_KEY, tenantId));
        boolean locked;
        try {
            locked = lock.tryLock(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
        }

        if (!locked) {
            // 获取锁失败
            log.info("tryLock EmpContractRel fail,tenantId={}", tenantId);
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
        }

        initEmpContractRel(contractTypeSetDo.getByBid(bid), empIdList);
        lock.unlock();

        // 更新进度条
        ImportExcelProcessVo processObj = new ImportExcelProcessVo(progress, 1, 1, 0, 1, 0);
        String progressKey = String.format(CONTRACT_TYPE_SET_PROGRESS, UserContext.getTenantId(), progress);
        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 300);
    }

    public void initEmpContractRel(ContractTypeSetDo typeSetData, List<Long> empIdList) {
        log.info("creating rel of ContractTypeSetDo={}", FastjsonUtil.toJson(typeSetData));
        String tsBid = typeSetData.getBid();
        try {
            if (typeSetData.isDeleted() || !StatusEnum.isEnable(typeSetData.getStatus())) {
                return;
            }
            ConditionTreeDto conditionTreeDto = contractSetConditionDo.getConditionTree(tsBid).orElse(new ConditionTreeDto());
            log.info("-----Get conditionTreeDto={},empIdList={}",
                    FastjsonUtil.toJson(conditionTreeDto),
                    FastjsonUtil.toJson(empIdList));
            // 获取符合条件员工id
            MutablePair<List<Long>, List<Long>> pair = conditionTreeDto.filterEmpIds(typeSetData, empIdList);
            List<Long> filterEmpIds = pair.getKey();
            if (CollectionUtils.isNotEmpty(pair.getValue())) {
                List<Long> otherEmpIds = getUnTrackContractEmpIds(pair.getValue());
                filterEmpIds.addAll(otherEmpIds);
            }
            log.info("-----Delete history ContractTypeSet Condition. bid={}, pair={}", tsBid, FastjsonUtil.toJson(pair));
            // 删除历史匹配条件
            deleteByTypeSetBid(tsBid);
            log.info("-------UnTrackContract filterEmpIds={}", FastjsonUtil.toJson(filterEmpIds));
            List<EmpContractTypeSetRelDo> typeSetRelList = Sequences.sequence(filterEmpIds)
                    .map(id -> new EmpContractTypeSetRelDo()
                            .setEmpId(String.valueOf(id))
                            .setContractTypeSet(tsBid)
                            .setContractTypeSetTxt(typeSetData.getContractType().getText())).toList();
            EmpContractTypeSetRelDo next;
            List<EmpContractTypeSetRelDo> subList = new ArrayList<>(2000);
            Iterator<EmpContractTypeSetRelDo> iterator = typeSetRelList.iterator();
            int i = 1;
            while (iterator.hasNext()) {
                next = iterator.next();
                subList.add(next);
                iterator.remove();
                if (i >= 2000) {
                    batchSave(subList);
                    subList.clear();
                    i = 1;
                }
                i++;
            }
            if (subList.size() > 0) {
                batchSave(subList);
            }
        } catch (Exception e) {
            log.error("failed to create rel of contract '{}' ", tsBid, e);
        }
    }

    private List<Long> getUnTrackContractEmpIds(List<Long> filterIds) {
        DataQuery dataQuery = DataQuery.identifier("entity.hr.EmpWorkInfo");
        // 排除已离职的员工
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andIn("empId", filterIds.stream().map(String::valueOf).collect(Collectors.toList()))
                .andNe("empStatus", "1")
                .andNe("deleted", Boolean.TRUE.toString()).andEq("lastContract.bid", null);
        int pageNo = 1;
        List<Long> otherIds = Lists.newArrayList();
        do {
            dataQuery.limit(5000, pageNo);
            PageResult<EmpWorkInfoDo> pageResult = dataQuery.filter(dataFilter, EmpWorkInfoDo.class);
            if (null == pageResult || null == pageResult.getItems()) {
                break;
            }
            otherIds.addAll(pageResult.getItems().stream().map(it -> Long.valueOf(it.getEmpId())).collect(Collectors.toList()));
            if (pageResult.getTotal() / 5000 + (pageResult.getTotal() % 5000 > 0 ? 1 : 0) <= pageNo) {
                break;
            }
            pageNo++;
        } while (true);
        return otherIds;
    }

    public void deleteByTypeSetBid(String bid) {
        empContractTypeSetRelDo.deleteByTypeSetBid(bid);
    }

    public Optional<ContinueContractConfigDo> getContinueContractConfig() {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        val list = continueContractConfigRepository.selectList(ContinueContractConfigDo.IDENTIFIER, securityUserInfo.getTenantId());
        return CollectionUtils.isEmpty(list) ? Optional.empty() : Optional.of(list.get(0));
    }
}