package com.caidaocloud.hr.service.confirmation.application;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationApplyApprovalDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationTodoEmpDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationTodoEmpExcelDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationTodoQueryDto;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationEsService;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationRecordService;
import com.caidaocloud.hr.service.confirmation.application.service.EmpConfirmationService;
import com.caidaocloud.hr.service.confirmation.domain.entity.*;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationConfigStatus;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationApplyDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRevokeDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationTemplateDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationApplyVo;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationTemplateDataVo;
import com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetVo;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryChangeDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpSalaryChangeDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.temination.application.FormService;
import com.caidaocloud.hr.service.temination.application.dto.FormDataDto;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskApproveDTO;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskRevokeDTO;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.util.DataSimpleUtil;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.transfer.infrastructure.utils.DataSimpleUtil.nullOrEmpty;

/**
 * <AUTHOR> Zhou
 * @date 2023/6/7
 */
@Service
@Slf4j
public class ConfirmationService {
    @Resource
    private ConfirmationEsService confirmationEsService;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private WfOperateFeignClient wfOperateFeignClient;
    @Resource
    private EmpSalaryChangeDomainService empSalaryChangeDomainService;
    @Autowired
    private FormService formService;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private EmpConfirmationService empConfirmationService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private CompanyDomainService companyDomainService;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private WorkplaceService workplaceService;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private ConfirmationRecordService recordService;
    @Resource
    private FormFeignClient formFeignClient;
	@Resource
	private ArchiveEventProducer archiveEventProducer;

	@Value("${postTxt.showCode:enabled}")
	private String postTxtShowCode;

	public DataSimple getConfirmationDataByBusinessId(String businessId) {
		return confirmationEsService.getConfirmationApply(StringUtils.substringBefore(businessId, "_"));
	}

	@Deprecated
	public ConfirmationApply getConfirmationApplyByBusinessId(String businessId) {
		// 2023/6/7
		return null;
	}

	public void approve(ConfirmationApplyApprovalDto dto) {
		updateWorkflowApply(dto);
		approveWorkflow(dto, WfTaskActionEnum.APPROVE);
	}

	public void refuse(ConfirmationApplyApprovalDto dto) {
		updateWorkflowApply(dto);
		approveWorkflow(dto, WfTaskActionEnum.REFUSE);
	}

	private void updateWorkflowApply(ConfirmationApplyApprovalDto dto) {
		val old = confirmationEsService.getConfirmationApply(dto.getId());
		NestPropertyValue properties = old.getProperties();
		// put 存储转正信息
		putConfirmationProp(properties, dto.getConfirmations().stream()
				.filter(it->dto.getWritableFields().contains("other."+it.getProperty())).collect(Collectors.toList()));
		// put 存储任职信息
		putWorkProps(properties, dto.getWork().stream()
				.filter(it->dto.getWritableFields().contains("work."+it.getProperty())).collect(Collectors.toList()));
		// put 存储薪资信息
		putSalaryProps(properties, dto.getSalary().stream()
				.filter(it->dto.getWritableFields().contains("salary."+it.getProperty())).collect(Collectors.toList()));
		confirmationEsService.updateConfirmationApply(old);
		// 保存表单数据
		if(null != old.getProperties().get("formDefId")){
			updateFormData(((SimplePropertyValue)old.getProperties().get("formDefId")).getValue(),
					((SimplePropertyValue)old.getProperties().get("formValueId")).getValue(), dto.getFormData(),
					dto.getWritableFields());
		}
		//saveFormData(applyDo, applyDto.getFormData(), applyDto);
	}

	private void approveWorkflow(ConfirmationApplyApprovalDto dto, WfTaskActionEnum choice) {
		WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
		wfApproveTaskDto.setChoice(choice);
		wfApproveTaskDto.setComment(dto.getComment());
		wfApproveTaskDto.setTaskId(dto.getTaskId());
		try {
			Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
			if (!result.isSuccess()) {
				PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
				throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
			}
		} catch (Exception e) {
			log.error("approveWorkflow err,{}", e.getMessage(), e);
			throw new ServerException(e.getMessage());
		}
	}

    public PageResult<ConfirmationTodoEmpDto> page(ConfirmationTodoQueryDto query) {
		val searchInTransitEmpIds =new ConfirmationRecordSearchDto();
		searchInTransitEmpIds.setApprovalStatus(WfProcessStatusEnum.IN_PROCESS.value);
		searchInTransitEmpIds.setPageSize(10000);
		searchInTransitEmpIds.setPageNo(1);
		List<String> inTransitEmpIds = recordService.page(searchInTransitEmpIds).getItems()
				.stream().map(it->it.getEmpId()).collect(Collectors.toList());
		DataFilter filter = query.toDataFilter();
		if(CollectionUtils.isNotEmpty(inTransitEmpIds)){
			filter = filter.andNotIn("empId", inTransitEmpIds);
		}
		val emps = DataQuery.identifier("entity.hr.EmpWorkInfo")
				.limit(query.getPageSize(), query.getPageNo()).filterProperties(filter,
				Lists.list("workno", "name", "hireDate", "probationPeriodEndDate", "organize", "organizeTxt",
						"post", "postTxt", "empType.dict.value",
						"leadEmpId.empId", "leadEmpId.name", "leadEmpId.workno", "empId"),
				System.currentTimeMillis());
		return new PageResult(emps.getItems().stream().map(emp->{
			ConfirmationTodoEmpDto confirmationTodoEmp = new ConfirmationTodoEmpDto();
			if(StringUtils.isNotEmpty(emp.get("empType.dict.value"))){
				confirmationTodoEmp.setEmpType(DictSimple.doDictSimple(emp.get("empType.dict.value")));
			}
			confirmationTodoEmp.setEmpId(emp.get("empId"));
			confirmationTodoEmp.setWorkno(emp.get("workno"));
			confirmationTodoEmp.setName(emp.get("name"));
			if(StringUtils.isNotEmpty(emp.get("hireDate"))){
				confirmationTodoEmp.setHireDate(Long.parseLong(emp.get("hireDate")));
			}
			val probationPeriodEndDate = emp.get("probationPeriodEndDate");
			if(StringUtils.isNotEmpty(probationPeriodEndDate)){
				confirmationTodoEmp.setProbationPeriodEndDate(Long.valueOf(probationPeriodEndDate));
				val probationPeriodDay = (Long.valueOf(probationPeriodEndDate)
						- Long.valueOf(com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.getMidnightTimestamp()))/1000/3600/24;
				confirmationTodoEmp.setProbationPeriodDay(probationPeriodDay);
			}
			confirmationTodoEmp.setOrganize(emp.get("organize"));
			confirmationTodoEmp.setOrganizeTxt(emp.get("organizeTxt"));
			confirmationTodoEmp.setPost(emp.get("post"));
			String postTxt = emp.get("postTxt");
			if(!"enabled".equals(postTxtShowCode)){
				if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
					postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
				}
			}
			confirmationTodoEmp.setPostTxt(postTxt);
			confirmationTodoEmp.setLeaderEmpId(emp.get("leadEmpId.empId"));
			confirmationTodoEmp.setLeaderEmpName(emp.get("leadEmpId.name"));
			confirmationTodoEmp.setLeaderEmpWorkno(emp.get("leadEmpId.workno"));
			return confirmationTodoEmp;
		}).collect(Collectors.toList()), emps.getPageNo(), emps.getPageSize(), emps.getTotal());
    }

    public String saveApply(ConfirmationApplyDto applyDto) {
		// 校验申请人
		applyDto.checkApplyEmp();
		// 校验是否已经有审批中的转正申请
		checkApply(applyDto);
		// 获取配置
		ConfirmationConfig config = getApplyConfig(applyDto.getDefId());
		// dto 转 do
		ConfirmationApply applyDo = convertApplyDo(applyDto, config);
		NestPropertyValue properties = applyDo.getProperties();
		// 记录员工展示数据
		putEmpDisplayWorkInfos(properties, applyDto);
		// put 存储转正信息
		putConfirmationProp(properties, applyDto.getConfirmations());
		// put 存储任职信息
		putWorkProps(properties, applyDto.getWork());
		// put 存储薪资信息
		putSalaryProps(properties, applyDto.getSalary());
		// 保存表单数据
		saveFormData(applyDo, applyDto.getFormData(), applyDto);
		// 默认的审批状态为审批中
		EnumSimple workflowStatus = new EnumSimple();
		workflowStatus.setValue(WfProcessStatusEnum.IN_PROCESS.value);
		properties.add("other$approvalStatus", workflowStatus);
		properties.add("formDefId", applyDo.getFormId());
		properties.add("formValueId", applyDo.getFormValueId());
		properties.add("other$remark", applyDto.getRemark());
		properties.add("defId", applyDto.getDefId());
		try{
			confirmationEsService.saveConfirmationApply(applyDo);
			String approvalStatus = doWorkFlow(config, applyDo, properties, applyDto.getEmp());
			DataSimple dbApplyDo = confirmationEsService.getConfirmationApply(applyDo.getId());
			if (dbApplyDo != null && !dbApplyDo.getProperties().isEmpty()) {
				if (dbApplyDo.getProperties().get("other$approvalStatus") == null || StringUtils.isBlank(((EnumSimple) dbApplyDo.getProperties().get("other$approvalStatus")).getValue()) || !"IN_PROCESS".equals(approvalStatus)) {
					dbApplyDo.getProperties().add("other$approvalStatus", approvalStatus);
				}
			}
			confirmationEsService.updateConfirmationApply(dbApplyDo);
		} catch (Exception e){
			log.error("open confirmation Workflow err,{}", e.getMessage(), e);
			confirmationEsService.deleteConfirmationApply(applyDo.getId());
			if(e instanceof ServerException){
				throw e;
			}
		}
		return applyDo.getId();
    }

	private String doWorkFlow(ConfirmationConfig config, ConfirmationApply applyDo, NestPropertyValue props,EmpSimple emp){
		Optional<EnabledConfirmationPropInfo> first = config.getEnabledConfirmationProps().stream()
				.filter(ecp -> ecp.getEnabled().equals(EnabledConfirmationPropInfo.EnabledConfirmationProp.WORKFLOW_ENABLED))
				.findFirst();
		if(first.isPresent()){
			SimplePropertyValue workflowEnabled = (SimplePropertyValue) applyDo.getProperties().get("other$workflowEnabled");
			if(null == workflowEnabled || !Boolean.valueOf(workflowEnabled.getValue())){
				// 转正申请页面未开启工作流，则直接返回审批通过
				empConfirmationService.updateEmpInfoWithConfirmationApply(applyDo);
				return WfProcessStatusEnum.APPROVE.value;
			}
		}
		WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
		workflowDto.setFuncCode("CONFIRMATION-" + config.getBid());
		workflowDto.setApplicantId(applyDo.getEmp().getEmpId());
		workflowDto.setBusinessId(applyDo.getId());
		workflowDto.setApplicantName(applyDo.getEmp().getName());
		// 业务单据事件时间
		SimplePropertyValue effectiveDate = (SimplePropertyValue) applyDo.getProperties().get("other$effectiveDate");
		workflowDto.setEventTime(null == effectiveDate || StringUtil.isEmpty(effectiveDate.getValue()) ? System.currentTimeMillis() : Long.parseLong(effectiveDate.getValue()));
		Result<?> wfResult = null;
		try {
			wfResult = iWfRegisterFeign.begin(workflowDto);
		} catch (Exception e) {
			log.error("beginWorkflow err,{}", e.getMessage(), e);
		}

		if (null == wfResult || !wfResult.isSuccess()) {
			Object msg = wfResult.getData();
			WorkFlowUtil.beginCallback(msg);
			throw new ServerException(msg.toString());
		}

		WorkFlowUtil.beginCallback(wfResult.getData());
		return WfProcessStatusEnum.IN_PROCESS.value;
	}

	private void putSalaryProps(NestPropertyValue properties, List<ConfirmationChangeField> salarys) {
		if(null == salarys || salarys.isEmpty()){
			return;
		}

		salarys.forEach(ccf -> {
			putProperties(properties, ccf, "salary");
			properties.add("salary" + "$" + ccf.getProperty() + "_enable", "" + ccf.isEnable());
		});
	}

	private void putWorkProps(NestPropertyValue properties, List<ConfirmationChangeField> works) {
		if(null == works || works.isEmpty()){
			return;
		}

		works.forEach(ccf -> {
			putProperties(properties, ccf, "work");
			properties.add("work" + "$" + ccf.getProperty() + "_enable", "" + ccf.isEnable());
			putSysWorkProps(ccf.getProperty(), "old_work$" + ccf.getProperty() + "Txt", properties, ccf.getBefore());
			putSysWorkProps(ccf.getProperty(), "work$" + ccf.getProperty() + "Txt", properties, ccf.getAfter());
		});
	}

	private void putSysWorkProps(String property, String saveProperty, NestPropertyValue props, Object after){
		String value = null;
		if(after instanceof SimplePropertyValue){
			SimplePropertyValue simpleValue = (SimplePropertyValue) after;
			value = simpleValue.getValue();
		} else if(after instanceof String){
			value = (String) after;
		}

		if(StringUtil.isEmpty(value)){
			return;
		}

		if("company".equals(property)){
			CompanyDo companyData = companyDomainService.selectById(value);
			props.add(saveProperty, null == companyData || null == companyData.getBid()
					? "" : companyData.getCompanyName());
		} else if("job".equals(property)){
			JobDo jobData = jobDomainService.selectById(value);
			props.add(saveProperty, null == jobData || null == jobData.getBid()
					? "" : jobData.getName());
		} else if("post".equals(property) || "leaderPost".equals(property)){
			PostDo data = postDomainService.selectById(value, System.currentTimeMillis());
			props.add(saveProperty, null == data || null == data.getBid()
					? "" : data.getName());
		} else if("organize".equals(property) || "leaderOrganize".equals(property)){
			OrgDo data = orgDomainService.selectById(value, System.currentTimeMillis());
			props.add(saveProperty, null == data || null == data.getBid()
					? "" : data.getName());
		} else if("contractTypeSet".equals(property)){
			ContractTypeSetVo data = contractTypeSetService.getDetail(value);
			props.add(saveProperty, null == data || null == data.getBid()
					? "" : data.getContractTypeTxt());
		} else if("workplace".equals(property)){
			WorkplaceDo data = workplaceService.getWorkplaceById(value);
			props.add(saveProperty, null == data || null == data.getBid()
					? "" : data.getName());
		}
	}

	private void putConfirmationProp(NestPropertyValue properties, List<ConfirmationChangeField> confirmations) {
		if(null == confirmations || confirmations.isEmpty()){
			doProbationPeriodEndDate(properties);
			return;
		}

		confirmations.forEach(ccf -> {
			putProperties(properties, ccf, "other");
		});
		doProbationPeriodEndDate(properties);
	}

	private void doProbationPeriodEndDate(NestPropertyValue properties){
		// 如果试用期截止日期为空，则默认取员工身上的试用期截止日期
		SimplePropertyValue pped = (SimplePropertyValue) properties.get("main$probationPeriodEndDate");
		if(null == pped || StringUtil.isEmpty(pped.getValue())){
			return;
		}

		SimplePropertyValue opped = (SimplePropertyValue) properties.get("other$probationPeriodEndDate");
		if(null != opped && StringUtil.isNotEmpty(opped.getValue())){
			return;
		}

		properties.add("other$probationPeriodEndDate", pped.getValue());
	}

	public void putProperties(NestPropertyValue properties, ConfirmationChangeField ccf, String model){
		String property = model + "$" + ccf.getProperty(), afterTxt, beforeTxt;
		Object before = ccf.getBefore(), after = ccf.getAfter();
		switch (ccf.getPropertyDataType()){
			case Dict:
				DictSimple dictSimple = convertDict(after);
				properties.add(property, dictSimple);
				if(ccf.isEnable()){
					dictSimple = convertDict(before);
					properties.add("old_" + property, dictSimple);
				}
				break;
			case Boolean:
				boolean boolValue = after instanceof String ? Boolean.valueOf((String) after) : (Boolean) after;
				afterTxt = boolValue ? "是" : "否";
				properties.add(property, String.valueOf(boolValue));
				properties.add(property + "Txt", afterTxt);
				if(ccf.isEnable()){
					boolValue = before instanceof String ? Boolean.valueOf((String) before) : (Boolean) before;
					beforeTxt = boolValue ? "是" : "否";
					properties.add("old_" + property, String.valueOf(boolValue));
					properties.add("old_" + property + "Txt", beforeTxt);
				}
				break;
			case String:
				properties.add(property, nullOrEmpty(ccf.getAfter()));
				if(ccf.isEnable()){
					properties.add("old_" + property, nullOrEmpty(ccf.getBefore()));
				}
				break;
			case Enum:
				EnumSimple enumSimple = null == after ? new EnumSimple() : FastjsonUtil.convertObject(after, EnumSimple.class);
				properties.add(property, enumSimple);
				if(ccf.isEnable()){
					if (null == before) {
						enumSimple = new EnumSimple();
					} else if (before instanceof String && !StringUtils.startsWith(String.valueOf(before), "{")){
						enumSimple = new EnumSimple();
						enumSimple.setValue(String.valueOf(before));
					} else {
						enumSimple = FastjsonUtil.convertObject(before, EnumSimple.class);
					}
					properties.add("old_" + property, enumSimple);
				}
				break;
			case Attachment:
				Attachment attachment = null == after ? new Attachment() : FastjsonUtil.toObject(FastjsonUtil.toJson(after), Attachment.class);
				properties.add(property, attachment);
				break;
			case Address:
				Address addr = null == after ? new Address() : FastjsonUtil.convertObject(after, Address.class);
				properties.add(property, addr);
				if(ccf.isEnable()){
					addr = null == before ? new Address() : FastjsonUtil.convertObject(before, Address.class);
					properties.add("old_" + property, addr);
				}
				break;
			case Timestamp:
				properties.add(property, nullOrEmpty(ccf.getAfter()));
				afterTxt = null != ccf.getAfter() ?
						DateUtil.formatDate(new Date(Long.valueOf(ccf.getAfter().toString()))) : null;
				properties.add(property + "Txt", nullOrEmpty(afterTxt));
				if(ccf.isEnable()){
					properties.add("old_" + property, nullOrEmpty(ccf.getBefore()));
					beforeTxt = null != ccf.getBefore() ?
							DateUtil.formatDate(new Date((Long) ccf.getBefore())) : null;
					properties.add("old_" + property + "Txt", nullOrEmpty(beforeTxt));
				}
				break;
			case Emp:
				EmpSimple leaderEmp = null == after ? new EmpSimple() : FastjsonUtil.convertObject(after, EmpSimple.class);
				properties.add(property, leaderEmp);
				if(ccf.isEnable()){
					leaderEmp = null == before ? new EmpSimple() : FastjsonUtil.convertObject(before, EmpSimple.class);
					properties.add("old_" + property, leaderEmp);
				}
				break;
			case Job_Grade_Range:
				JobGradeRange jpr = null == after ? new JobGradeRange() : FastjsonUtil.convertObject(after, JobGradeRange.class);
				properties.add(property, jpr);
				if(ccf.isEnable()){
					jpr = null == before ? new JobGradeRange() : FastjsonUtil.convertObject(before, JobGradeRange.class);
					properties.add("old_" + property, jpr);
				}
				break;
			default:
				SimplePropertyValue spv = new SimplePropertyValue(Objects.toString(null == after ? "" : after));
				properties.add(property, spv);
				if(ccf.isEnable()){
					spv = new SimplePropertyValue(Objects.toString(null == before ? "" : before));
					properties.add("old_" + property, spv);
				}
				break;
		}
	}

	private DictSimple convertDict(Object value){
		if(null == value){
			return new DictSimple();
		}

		if(value instanceof String){
			return DictSimple.doDictSimple((String) value);
		}

		return FastjsonUtil.convertObject(value, DictSimple.class);
	}

	private ConfirmationApply convertApplyDo(ConfirmationApplyDto applyDto, ConfirmationConfig config) {
		ConfirmationApply applyDo = new ConfirmationApply();
		applyDo.setId(null == applyDto.getId() ? SnowUtil.nextId() : applyDto.getId());
		applyDo.setFormValueId(applyDto.getFormValueId());
		applyDo.setFormId(config.getFormDefId());
		applyDo.setDefId(applyDto.getDefId());
		BeanUtil.copyProperties(applyDto, applyDo);
		return applyDo;
	}

	private void putEmpDisplayWorkInfos(NestPropertyValue properties, ConfirmationApplyDto applyDto) {
		EmpSimple emp = applyDto.getEmp();
		long dataTime = System.currentTimeMillis();
		EmpWorkInfoDo workInfo = empWorkInfoService.getEmpWorkInfo(emp.getEmpId(), dataTime);
		PreCheck.preCheckArgument(null == workInfo
				|| StringUtil.isEmpty(workInfo.getEmpId()), "申请人不存在或为空");
		// 工号
		properties.add("main$workno", nullOrEmpty(workInfo.getWorkno()));
		// 姓名
		properties.add("main$name", nullOrEmpty(workInfo.getName()));
		// 员工empId
		properties.add("main$empId", nullOrEmpty(workInfo.getEmpId()));
		// 员工enName
		properties.add("main$enName", nullOrEmpty(workInfo.getEnName()));
		properties.add("main$hireDate", nullOrEmpty(workInfo.getHireDate()));
		// 试用期截止日期
		properties.add("main$probationPeriodEndDate", nullOrEmpty(workInfo.getProbationPeriodEndDate()));
		// 试用期期限
		properties.add("main$probation", workInfo.getProbation());
		properties.add("main$workHour", workInfo.getWorkHour());
		properties.add("main$empType", workInfo.getEmpType());
		properties.add("main$post", nullOrEmpty(workInfo.getPost()));
		properties.add("main$postTxt", nullOrEmpty(workInfo.getPostTxt()));
		properties.add("main$workplace", nullOrEmpty(workInfo.getWorkplace()));
		properties.add("main$workplaceTxt", nullOrEmpty(workInfo.getWorkplaceTxt()));
		String organize = nullOrEmpty(workInfo.getOrganize()), organizeTxt = nullOrEmpty(workInfo.getOrganizeTxt());
		properties.add("main$organize", organize);
		properties.add("main$organizeTxt", organizeTxt);
		properties.add("main$leader", workInfo.getLeadEmpId());
		if(StringUtil.isNotEmpty(organize)){
			OrgDo byBid = orgDomainService.getByBid(organize, dataTime);
			TreeParent pid = null;
			if(null != byBid && null != (pid = byBid.getPid())){
				organize = StringUtil.isNotEmpty(pid.getPath()) ? pid.getPath() + "/" + organize : organize;
				properties.add("main$organizePath", organize);
				organizeTxt = StringUtil.isNotEmpty(pid.getNamePath()) ? pid.getNamePath() + "/" + organizeTxt : organizeTxt;
				properties.add("main$organizePathTxt", organizeTxt);
			}
		}
		properties.add("main$companyTxt", nullOrEmpty(workInfo.getCompanyTxt()));
		properties.add("main$company", nullOrEmpty(workInfo.getCompany()));
		if (workInfo.getLeadEmpId() != null) {
			properties.add("main$leadEmpId_empId", nullOrEmpty(workInfo.getLeadEmpId().getEmpId()));
			properties.add("main$leadEmpId_workno", nullOrEmpty(workInfo.getLeadEmpId().getWorkno()));
			properties.add("main$leadEmpId_name", nullOrEmpty(workInfo.getLeadEmpId().getName()));
			properties.add("main$leadEmpId_enName", nullOrEmpty(workInfo.getLeadEmpId().getEnName()));
		}
	}

	private void saveFormData(ConfirmationApply applyDo, Map<String, Object> formData, ConfirmationApplyDto applyDto) {
		if (StringUtils.isNotEmpty(applyDo.getFormId())) {
			FormDataDto dataDto = new FormDataDto();
			dataDto.setPropertiesMap(formData);
			String formDataId = formService.saveFormData(applyDo.getFormId(), dataDto);
			if (StringUtils.isEmpty(formDataId)) {
				throw new ServerException("Form data saving failed");
			}
			applyDo.setFormValueId(formDataId);
		}
	}

	private void updateFormData(String formDefId, String formValueId, Map<String, Object> formData, List<String> writableFields){
		// 表单数据保存
		if (StringUtils.isNotEmpty(formDefId) && StringUtils.isNotEmpty(formValueId)) {
			val oldFormData = formService.getFormDataMap(formDefId, formValueId);
			for(String writableField : writableFields){
				if(writableField.startsWith("formData.")){
					oldFormData.put(writableField.replace("formData.", ""), formData.get(writableField.replace("formData.", "")));
				}
			}
			FormDataDto dataDto = new FormDataDto();
			dataDto.setId(formValueId);
			dataDto.setPropertiesMap(oldFormData);
			formService.updateFormData(formDefId, dataDto);
		}
	}

	private ConfirmationConfig getApplyConfig(String defId) {
		ConfirmationConfig config = ConfirmationConfig.getOne(defId);
		// 校验配置是否启
		PreCheck.preCheckArgument(!ConfirmationConfigStatus.ENABLED.equals(config.getStatus()), "配置未启用");
		return config;
	}

	private void checkApply(ConfirmationApplyDto applyDto){
		EmpSimple empSimple = applyDto.getEmp();
		PageResult<Map> pageResult = confirmationEsService.getPageMapList(new ConfirmationRecordSearchDto()
				.setEmpId(empSimple.getEmpId())
				.setDefId(applyDto.getDefId())
				.setApprovalStatus(WfProcessStatusEnum.IN_PROCESS.value));
		PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(pageResult.getItems()), "该员工存在审批中的数据，无法重复提交申请");
	}

	public void revokeApply(ConfirmationRevokeDto dto) {
		DataSimple apply = getConfirmationDataByBusinessId(dto.getBusinessKey());
		revokeApply(apply);
		revokeWorkflow(dto.getBusinessKey());
		confirmationEsService.updateConfirmationApply(apply);
		// 撤销删除归档附件
		archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.REGULARIZATION, apply.getBid(),
				ArchivePolicy.DELETE);
	}

	private void revokeApply(DataSimple apply) {
		EnumSimple approvalStatus = ((EnumSimple) apply.getProperties()
				.get("other$approvalStatus"));
		if (!WfProcessStatusEnum.IN_PROCESS.value.equals(approvalStatus.getValue())) {
			throw new ServerException("Failed to revoke confirmation apply");
		}
		approvalStatus.setValue(WfProcessStatusEnum.REVOKE.value);
		apply.getProperties().add("other$approvalStatus", approvalStatus);
		apply.setUpdateTime(System.currentTimeMillis());
	}

	private void revokeWorkflow(String businessKey) {
		WfTaskRevokeDTO wfRevokeDto = new WfTaskRevokeDTO();
		wfRevokeDto.setBusinessKey(businessKey);
		try {
			Result<?> result = wfOperateFeignClient.revokeProcessOfTask(wfRevokeDto);
			if (!result.isSuccess()) {
				PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
				throw new ServerException("Failed to revoke confirmation apply");
			}
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new ServerException(e.getMessage());
		}
	}

	public ConfirmationApplyVo detailVo(String applyId) {
		DataSimple dataSimple = confirmationEsService.getConfirmationApply(applyId);
		PreCheck.preCheckArgument(dataSimple == null || StringUtils.isEmpty(dataSimple.getId()), "申请表单不存在");
		return convertApplyData(dataSimple);
	}

	public ConfirmationApplyVo convertApplyData(DataSimple dataSimple) {
		ConfirmationApplyVo applyVo = new ConfirmationApplyVo();
		applyVo.setId(dataSimple.getId());
		NestPropertyValue properties = dataSimple.getProperties();
		applyVo.setDefId(DataSimpleUtil.getString(properties.get("defId")));
		applyVo.setBusinessKey(applyVo.getId() + "_CONFIRMATION-" + applyVo.getDefId());
		applyVo.setFormDefId(DataSimpleUtil.getString(properties.get("formDefId")));
		applyVo.setFormValueId(DataSimpleUtil.getString(properties.get("formValueId")));
		applyVo.setRemark(DataSimpleUtil.getString(properties.get("other$remark")));
		applyVo.setCreateTime(dataSimple.getCreateTime());
		applyVo.setUpdateTime(dataSimple.getUpdateTime());
		applyVo.setApprovalStatus(DataSimpleUtil.getEnumSimpleValue(properties.get("other$approvalStatus"), ""));
		EmpSimple empSimple = new EmpSimple();
		empSimple.setEmpId(DataSimpleUtil.getString(properties.get("main$empId")));
		empSimple.setWorkno(DataSimpleUtil.getString(properties.get("main$workno")));
		empSimple.setName(DataSimpleUtil.getString(properties.get("main$name")));
		empSimple.setEnName(DataSimpleUtil.getString(properties.get("main$enName")));
		applyVo.setEmp(empSimple);
		convertApplyVo(properties, applyVo);
		return applyVo;
	}

	private void convertApplyVo(NestPropertyValue properties, ConfirmationApplyVo applyVo){
		try{
			QueryInfoCache.init();
			properties.forEach((key, pv) -> {
				if(key.startsWith("old_") || key.endsWith("Txt") || key.endsWith("_enable")){
					return;
				}

				if(key.startsWith("other")){
					applyVo.getConfirmations().add(convertApplyVo(key, "other$", pv, properties));
					return;
				}

				if(key.startsWith("work")){
					applyVo.getWork().add(convertApplyVo(key, "work$", pv, properties));
					return;
				}

				if(key.startsWith("salary")){
					applyVo.getSalary().add(convertApplyVo(key, "salary$", pv, properties));
					return;
				}
			});
		} catch (Exception e){
			log.error("convertApplyVo err,{}", e.getMessage(), e);
		} finally {
			QueryInfoCache.clear();
		}
	}

	private ConfirmationChangeField convertApplyVo(String key, String type, PropertyValue pv, NestPropertyValue properties){
		ConfirmationChangeField ccf = new ConfirmationChangeField()
				.setProperty(key.replace(type, ""));
		PropertyValue oldPv = properties.get("old_" + key);
		if(pv instanceof SimplePropertyValue){
			ccf.setPropertyDataType(PropertyDataType.String);
			String value = ((SimplePropertyValue) pv).getValue();
			ccf.setAfter(value);
			String afterValue = DataSimpleUtil.getString(properties.get(key + "Txt"));
			ccf.setAfterTxt(afterValue);
			ccf.setAfterTxt(StringUtil.isEmpty(afterValue) ? value : afterValue);
			ccf.setBefore(DataSimpleUtil.getString(oldPv));
			ccf.setBeforeTxt(DataSimpleUtil.getString(properties.get("old_" + key + "Txt")));
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		if(pv instanceof DictSimple){
			ccf.setPropertyDataType(PropertyDataType.Dict);
			ccf.setAfter(((DictSimple) pv).getValue());
			ccf.setAfterTxt(((DictSimple) pv).getText());
			if(null != oldPv && oldPv instanceof DictSimple){
				ccf.setBefore(((DictSimple) oldPv).getValue());
				ccf.setBeforeTxt(((DictSimple) oldPv).getText());
			}
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		if(pv instanceof EnumSimple){
			ccf.setPropertyDataType(PropertyDataType.Enum);
			ccf.setAfter(((EnumSimple) pv).getValue());
			ccf.setAfterTxt(((EnumSimple) pv).getText());
			if(null != oldPv && oldPv instanceof EnumSimple){
				ccf.setBefore(((EnumSimple) oldPv).getValue());
				ccf.setBeforeTxt(((EnumSimple) oldPv).getText());
			}
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		if(pv instanceof EmpSimple){
			ccf.setPropertyDataType(PropertyDataType.Emp);
			ccf.setAfter(pv);
			ccf.setAfterTxt(((EmpSimple) pv).getName());
			if(null != oldPv && oldPv instanceof EmpSimple){
				ccf.setBefore(((EmpSimple) oldPv).getEmpId());
				ccf.setBeforeTxt(((EmpSimple) oldPv).getName());
			}
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		if(pv instanceof Attachment){
			ccf.setPropertyDataType(PropertyDataType.Attachment);
			ccf.setAfter(pv);
			ccf.setAfterTxt(((Attachment) pv).getNames().stream().collect(Collectors.joining(",")));
			if(null != oldPv && oldPv instanceof Attachment){
				ccf.setBefore(oldPv);
				ccf.setBeforeTxt(((Attachment) oldPv).getNames().stream().collect(Collectors.joining(",")));
			}
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		if(pv instanceof Address){
			ccf.setPropertyDataType(PropertyDataType.Address);
			ccf.setAfter(pv);
			Address addr = (Address) pv;
			Address.doAddress(addr);
			ccf.setAfterTxt(addr.doText());
			if(null != oldPv && oldPv instanceof Address){
				addr = (Address) oldPv;
				ccf.setBefore(addr);
				Address.doAddress(addr);
				ccf.setBeforeTxt(addr.doText());
			}
			ccf.setType(type.replace("$", ""));
			ccf.setEnable(Boolean.valueOf(DataSimpleUtil.getString(properties.get(key + "_enable"))));
			return ccf;
		}

		return ccf;
	}

	public ConfirmationTemplateDataVo getTemplateData(ConfirmationTemplateDto templateDto) {
		ConfirmationConfig configDef = ConfirmationConfig.getOne(templateDto.getDefId());
		return Optional.ofNullable(templateDto.getApplyId())
			.map(it -> {
				ConfirmationTemplateDataVo templateVo = new ConfirmationTemplateDataVo();
				ConfirmationApplyVo applyVo = detailVo(templateDto.getApplyId());
				templateVo.setTemplate(configDef);
				templateVo.setFormValueId(applyVo.getFormValueId());
				templateVo.setData(mergeApplyData(applyVo));
				return templateVo;
			}).orElseGet(() -> {
				PreCheck.preCheckArgument(StringUtils.isEmpty(templateDto.getDefId()), "获取数据失败，缺失必要参数");
				ConfirmationTemplateDataVo templateVo = new ConfirmationTemplateDataVo();
				templateVo.setTemplate(configDef);
				templateVo.setData(getDefaultApplyData(configDef, templateDto.getEmpId()));
				return templateVo;
			});
	}

	public List<ConfirmationChangeField> getDefaultApplyData(ConfirmationConfig configDef, String empId) {
		List<ConfirmationChangeField> defaultValues = Lists.list();
		// 任职信息
		if (CollectionUtils.isNotEmpty(configDef.getWorkProps())) {
			EmpWorkInfoDo workInfoDo = empWorkInfoService.getEmpWorkInfo(empId, System.currentTimeMillis());
			if (workInfoDo != null) {
				List<ConfirmationChangeFieldDef> fieldDefList = configDef.getWorkProps();
				defaultValues.addAll(fieldListByData(workInfoDo, fieldDefList));
			}
		}

		// 薪资变动
		if (CollectionUtils.isNotEmpty(configDef.getSalaryProps())) {
			EmpSalaryChangeDo salaryChangeDo = empSalaryChangeDomainService.getListByEffectDate(empId);
			if (salaryChangeDo != null) {
				defaultValues.addAll(fieldListByData(salaryChangeDo, configDef.getSalaryProps()));
			}
		}
		return defaultValues;
	}

	public List<ConfirmationChangeField> fieldListByData(DataSimple dataSimple, List<ConfirmationChangeFieldDef> fieldList) {
		NestPropertyValue props = dataSimple.getProperties();
		List<ConfirmationChangeField> collect = fieldList.stream().map(it -> {
			ConfirmationChangeField item = ObjectConverter.convert(it, ConfirmationChangeField.class);
			item.setEnable(false);
			PropertyValue value = props.get(it.getProperty());
			item.setBefore(item.parsePropertyData(value));
			if (StringUtils.isNotBlank(it.getDisplayProperty())) {
				SimplePropertyValue simpleValue = (SimplePropertyValue) props.get(it.getDisplayProperty());
				String propValue = simpleValue == null ? "" : simpleValue.getValue();
				item.setBeforeTxt(propValue);
			} else {
				try {
					PropertyValue beforeTxt = DataSimpleUtil.getTxtFieldPropertyValue(it.getDataType(), props.get(it.getProperty()));
					item.setBeforeTxt(((SimplePropertyValue) beforeTxt).getValue());
				} catch (Exception e){
					log.error("getTxtFieldPropertyValue err, it = {}, pv = {}, errmsg={}",
							FastjsonUtil.toJson(it), FastjsonUtil.toJson(props.get(it.getProperty())), e);
				}
			}
			return item;
		}).collect(Collectors.toList());
		return collect;
	}

	private List<ConfirmationChangeField> mergeApplyData(ConfirmationApplyVo applyVo){
		List<ConfirmationChangeField> dataList = applyVo.getConfirmations();
		dataList.addAll(applyVo.getWork());
		dataList.addAll(applyVo.getSalary());
		return dataList;
	}

	@SneakyThrows
	public void exportTodo(ConfirmationTodoQueryDto query, HttpServletResponse response) {
		query.setPageSize(-1);
		List<ConfirmationTodoEmpExcelDto> collect = page(query)
				.getItems().stream().map(it -> {
					val excel = new ConfirmationTodoEmpExcelDto();
					BeanUtils.copyProperties(it, excel, "hireDate", "probationPeriodEndDate");
					excel.setHireDate(it.getHireDate() == null ? null : new Date(it.getHireDate()));
					excel.setProbationPeriodEndDate(it.getProbationPeriodEndDate() == null ? null : new Date(it.getProbationPeriodEndDate()));
					excel.setEmpTypeName(it.getEmpType() == null ? "" : it.getEmpType().getText());
					return excel;
				}).collect(Collectors.toList());
		val workbook = ExcelExportUtil.exportExcel(new ExportParams("待转正", "待转正"),
				ConfirmationTodoEmpExcelDto.class, collect);
		val os = response.getOutputStream();
		workbook.write(os);
		os.close();
		workbook.close();
	}


	public String reapply(ConfirmationApplyDto applyDto) {
		DataSimple apply = confirmationEsService.getConfirmationApply(applyDto.getId());
		if (apply == null) {
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
		}

		String approvalStatus = ((EnumSimple)  apply.getProperties()
				.get("other$approvalStatus")).getValue();
		PreCheck.preCheckArgument(StringUtils.isNotEmpty(DataSimpleUtil.getString(apply.getProperties()
				.get("reapplyId"))) || !WfProcessStatusEnum.REVOKE.toString().equals(approvalStatus), "单据不能再次发起");

		applyDto.setId(null);
		String reapplyId = saveApply(applyDto);
		apply.getProperties().add("reapplyId", reapplyId);
		confirmationEsService.updateConfirmationApply(apply);
		return reapplyId;
	}

	public ConfirmationTemplateDataVo one(String businessKey) {
		String applyId = StringUtils.substringBefore(businessKey, "_");
		ConfirmationApplyVo applyVo = detailVo(applyId);
		ConfirmationTemplateDataVo templateVo = new ConfirmationTemplateDataVo();
		ConfirmationConfig configDef = ConfirmationConfig.getOne(applyVo.getDefId());
		templateVo.setTemplate(configDef);
		templateVo.setFormValueId(applyVo.getFormValueId());
		templateVo.setData(mergeApplyData(applyVo));
		if (StringUtils.isNotBlank(applyVo.getFormDefId()) && StringUtils.isNotBlank(applyVo.getFormValueId())) {
			var formDataMapResult = formFeignClient.getFormDataMap(applyVo.getFormDefId(), applyVo.getFormValueId());
			if (formDataMapResult.isSuccess() && formDataMapResult.getData() != null) {
				templateVo.setFormData(formDataMapResult.getData().getPropertiesMap());
			}
		}
        if(StringUtils.isNotBlank(applyVo.getFormDefId())) {
            templateVo.setFormDef(formService.getFormDefById(applyVo.getFormDefId()));
        }
		return templateVo;
	}
}
