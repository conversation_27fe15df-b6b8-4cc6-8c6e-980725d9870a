package com.caidaocloud.hr.service.dto.transfer;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class TransferPostEventDto {
    // 流程业务key
    private String businessKey;
    // 租户id
    private String tenantId;
    // 触发回调类型
    private String wfCallback;
    // 循环次数
    private int count = 0;
    //操作人userId
    private String userId;

    public void autoincrement(){
        count ++;
    }
}
