package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmpWorkInfoRetireDateRefreshService {

    @Autowired
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    @XxlJob("refreshRetireDate")
    public ReturnT<String> refreshRetireDate() {
        XxlJobHelper.log("XxlJob refreshRetireDate start");
        log.info("cronTask[ refreshRetireDate ]------------------------start execution,time {}", System.currentTimeMillis());

        for (String tenantId : tenantList) {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            // 系统跑批userid默认为0
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            try{
                val birthDateMap = DataQuery.identifier("entity.hr.EmpPrivateInfo").limit(-1, 1)
                        .filterProperties(DataFilter.ne("deleted",
                                Boolean.TRUE.toString()), Lists.list("empId", "birthDate"),
                                System.currentTimeMillis()).getItems()
                        .stream().filter(it->it.get("birthDate")!=null).collect(Collectors.toMap(it->it.get("empId"), it->it.get("birthDate")));
                val empLists = com.google.common.collect.Lists.partition(Lists.list(birthDateMap.keySet()),
                        1000);
                for(List<String> empIds : empLists){
                    DataQuery.identifier("entity.hr.EmpWorkInfo")
                            .limit(1000,1).filter(DataFilter.ne("deleted",
                            Boolean.TRUE.toString()).andIn("empId", empIds), DataSimple.class)
                            .getItems().forEach(workInfo->{
                        val empId = ((SimplePropertyValue)workInfo.getProperties().get("empId"))
                                .getValue();
                        try{
                            if(StringUtils.isEmpty(birthDateMap.get(empId))){
                                return;
                            }
                            val retireDate = empWorkInfoDomainService.calcRetireDate(empId, Long.parseLong(birthDateMap.get(empId)));
                            if(null != retireDate){
                                val retireDateProperty = workInfo.getProperties().get("retireDate");
                                if(null == retireDateProperty){
                                    workInfo.getProperties().add("retireDate",retireDate.toString());
                                    DataUpdate.identifier("entity.hr.EmpWorkInfo").update(workInfo);
                                }else{
                                    String oldValue = ((SimplePropertyValue)retireDateProperty).getValue();
                                    if(!retireDate.toString().equals(oldValue)){
                                        workInfo.getProperties().add("retireDate",retireDate.toString());
                                        DataUpdate.identifier("entity.hr.EmpWorkInfo").update(workInfo);
                                    }
                                }
                            }
                        }catch (Exception e){
                            log.error("retireDate calc fail:"+empId, e);
                        }
                    });
                }
            }finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }

        }

        log.info("cronTask[refreshRetireDate]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob refreshRetireDate end");
        return ReturnT.SUCCESS;
    }

}
