package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@ApiModel("续签意向模版分页vo")
@Accessors(chain = true)
public class ContinueContractTemplatePageVo {
    @ApiModelProperty("bid")
    private String bid;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("模版说明")
    private String desc;
}