package com.caidaocloud.hr.workflow.enums;

import java.util.Arrays;

public enum WfFieldEnum implements IWfField{
    ;

    @Override
    public String getKey() {
        return null;
    }

    @Override
    public String getText() {
        return null;
    }

    @Override
    public WfDetailFieldTypeEnum getType() {
        return null;
    }

    public static IWfField getTypeByKey(String key, IWfField [] values) {
        return Arrays.stream(values).filter(it -> it.getKey().equals(key)
                        || it.getKey().split("\\$")[0].equals(key))
                .findFirst().orElse(null);
    }
}
