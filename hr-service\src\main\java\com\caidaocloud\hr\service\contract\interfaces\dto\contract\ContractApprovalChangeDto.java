package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

/**
 * 合同解除与终止
 */
@Data
public class ContractApprovalChangeDto extends ContractChangeDto{
    private String businessKey;

    private String taskId;

    private String comment;

    private List<String> writableFields = Lists.list();
    private List<String> variables = Lists.list();
}
