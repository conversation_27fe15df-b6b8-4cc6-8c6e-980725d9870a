package com.caidaocloud.hr.service.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工个人基本信息VO")
public class EmpPrivateInfoVo {
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("员工英文名")
    private String enName;
    @ApiModelProperty("性别")
    private DictSimple sex;
    @ApiModelProperty("国籍")
    private DictSimple nationality;
    @ApiModelProperty("民族")
    private DictSimple nation;
    @ApiModelProperty("籍贯")
    private Address birthPlace;
    @ApiModelProperty("籍贯(弃用)")
    private ProvinceCity nativePlace;
    @ApiModelProperty("户口类型")
    private DictSimple familyType;
    @ApiModelProperty("户籍地址")
    private String permanentAddress;
    @ApiModelProperty("出生日期")
    private Long birthDate;
    @ApiModelProperty("年龄")
    private Integer divisionAge;
    @ApiModelProperty("婚姻状态")
    private EnumSimple maritalStatus;
    @ApiModelProperty("生育状态")
    private EnumSimple fertilityStatus;
    @ApiModelProperty("政治面貌")
    private DictSimple politicalOutlook;
    @ApiModelProperty("手机号")
    private PhoneSimple phone;
    @ApiModelProperty("员工个人邮箱")
    private String email;
    @ApiModelProperty("通讯地址")
    private String postalAddress;
    @ApiModelProperty("证件类型")
    private EnumSimple cardType;
    @ApiModelProperty("证件号")
    private String cardNo;
    @ApiModelProperty("证件有效日期")
    private Long cardEffectiveDate;
    @ApiModelProperty("是否残疾")
    private Boolean disability;
    @ApiModelProperty("监护人姓名")
    private String guardianName;
    @ApiModelProperty("监护人手机")
    private PhoneSimple guardianPhone;
    @ApiModelProperty("监护人邮箱")
    private String guardianEmail;
    @ApiModelProperty("残疾人证等级")
    private DictSimple disabilityLevelType;
    @ApiModelProperty("扩展字段")
    private Map ext = new LinkedHashMap();
}
