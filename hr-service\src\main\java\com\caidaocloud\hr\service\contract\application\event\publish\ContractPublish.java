package com.caidaocloud.hr.service.contract.application.event.publish;

import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractMessageDto;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractStartMsg;
import com.caidaocloud.hr.service.contract.application.service.MessageConfigService;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ContractPublish {

    private final static String CONTRACT_EXCHANGE = "esign.contract.fac.direct.exchange";

    private final static String CONTRACT_NEW_EXCHANGE = "message.fac.direct.exchange";

    private final static String CONTRACT_MANAGE_NEW_ROUTING_KEY = "routingKey.message.contract.new";

    private final static String CONTRACT_MANAGE_RENEW_ROUTING_KEY = "routingKey.message.contract.renew";

    private final static String CONTRACT_ROUTING_KEY = "routingKey.esign.contract";
    private final static String CONTRACT_CONTINUE_ROUTING_KEY = "routingKey.esign.contract.start";

    @Autowired
    private MessageConfigService messageConfigService;

    @Resource
    private MqMessageProducer<ContractMessage> producer;

    public void contractInitiated(ContractMessageDto message) {
        ContractMessage contract = new ContractMessage();
        contract.setBody(FastjsonUtil.toJson(message));
        contract.setExchange(CONTRACT_EXCHANGE);
        contract.setRoutingKey(CONTRACT_ROUTING_KEY);
        log.info("ContractMessage={}", contract.getBody());
        producer.publish(contract);
    }

    @Resource
    private MsgNoticeService msgNoticeService;

    /**
     * 合同新签保存触发消息通知
     */
    public void startContractPublishMsg(ContractMessageDto message) {
        if (message.getSignType().getValue().equals(SignTypeEnum.CHANGE.getCode())) {
            // 合同改签
            msgNoticeService.sendMsgNoticeEvent(NoticeType.CONTRACT_MANAGE_AMENDMENT,
                    Lists.newArrayList(message.getEmp().getEmpId()),
                    null, "contract", 0);
            return;
        }

        boolean contractNew = message.getSignType().getValue().equals(SignTypeEnum.NEW.getCode());
        List<MsgConfigDto> msgConfigList = msgNoticeService.getMsgConfigList(contractNew ? NoticeType.CONTRACT_MANAGE_NEW : NoticeType.CONTRACT_MANAGE_RENEW);
        if (CollectionUtils.isNotEmpty(msgConfigList)) {
            msgConfigList.forEach(it -> {
                ContractMessage contract = new ContractMessage();
                contract.setTenantId(message.getTenantId());
                contract.setType(0);
                contract.setUserId(0L);
                contract.setMsgConfig(it.getBid());
                contract.setSubjects(Lists.newArrayList(message.getEmp().getEmpId()));
                contract.setBody(FastjsonUtil.toJson(contract));
                contract.setExchange(CONTRACT_NEW_EXCHANGE);
                contract.setMsgFrom(contractNew ? "ContractNew" : "ContractRenew");
                contract.setRoutingKey(String.format("%s_%s", contractNew ? CONTRACT_MANAGE_NEW_ROUTING_KEY
                        : CONTRACT_MANAGE_RENEW_ROUTING_KEY, message.getTenantId()));
                log.info("ContractMessage={}", contract.getBody());
                producer.publish(contract);
            });
        }
    }

    public void contractStart(ContractStartMsg messageDto) {
        ContractMessage contract = new ContractMessage();
        contract.setBody(FastjsonUtil.toJson(messageDto));
        contract.setExchange(CONTRACT_EXCHANGE);
        contract.setRoutingKey(CONTRACT_CONTINUE_ROUTING_KEY);
        log.info("ContractStartMessage={}", contract.getBody());
        producer.publish(contract);

    }
}
