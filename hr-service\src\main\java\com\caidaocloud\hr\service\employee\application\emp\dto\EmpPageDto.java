package com.caidaocloud.hr.service.employee.application.emp.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工分页列表信息")
public class EmpPageDto {
    @ApiModelProperty("bid")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("合同公司")
    private String companyTxt;

    @ApiModelProperty("任职组织")
    private String organizeTxt;

    @ApiModelProperty("职务")
    private String jobTxt;

    @ApiModelProperty("岗位")
    private String postTxt;

    @ApiModelProperty("员工类型")
    private DictSimple empType;

    @ApiModelProperty("工作地")
    private String workplaceTxt;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工性别")
    private DictSimple sex;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("转正状态")
    private EnumSimple confirmationStatus;
    
    @ApiModelProperty("员工公司邮箱")
    private String companyEmail;
}
