package com.caidaocloud.hr.service.archive.beans;

import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * created by: FoAng
 * create time: 4/6/2024 6:42 下午
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveEvent implements Serializable {

    @ApiModelProperty("所属业务模块")
    private String businessModule;

    @ApiModelProperty("所属业务线")
    private String businessLine;

    @ApiModelProperty("业务ID, 不同的业务线定义")
    private String businessId;

    @ApiModelProperty("归档文件")
    private List<ArchiveData> archiveList;

    @ApiModelProperty("文件归档策略")
    private ArchivePolicy archivePolicy;

}
