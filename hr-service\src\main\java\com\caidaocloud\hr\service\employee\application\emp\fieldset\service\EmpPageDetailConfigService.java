package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import java.util.List;
import java.util.Set;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.emp.constant.EmpConstants;
import com.caidaocloud.hr.service.employee.application.emp.fieldset.enums.EmpPageDetailKey;
import com.caidaocloud.hr.service.transfer.application.dto.auth.AuthResourceDto;
import com.caidaocloud.hr.service.transfer.application.enums.ResourceActionEnum;
import com.caidaocloud.hr.service.transfer.application.enums.ResourceCategoryEnum;
import com.caidaocloud.hr.service.transfer.application.feign.AuthFeignClient;
import com.caidaocloud.hrpaas.paas.common.dto.PageDetailDto;
import com.caidaocloud.hrpaas.paas.common.dto.TenantPageDetailDto;
import com.caidaocloud.hrpaas.paas.common.feign.IPageFeignClient;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/4/24
 */
@Service
@Slf4j
public class EmpPageDetailConfigService {
	@Autowired
	private IPageFeignClient pageFeignClient;
	@Autowired
	private AuthFeignClient authFeignClient;

	public void registerAuthResource(String pageId) {
		log.info("注册员工信息子集页面权限，pageId={}", pageId);
		List<PageDetailDto.TabDetail> customPageDeatail = loadCustomPageDeatail(pageId);
		List<AuthResourceDto> authResourceDtoList =
				buildResources(customPageDeatail);
		log.info("注册子集页面权限,data={}", authResourceDtoList);
		authFeignClient.createResources(authResourceDtoList);
	}

	private List<AuthResourceDto> buildResources(List<PageDetailDto.TabDetail> customPageDeatail) {
		List<AuthResourceDto> list = Lists.newArrayList();
		for (PageDetailDto.TabDetail tabDetail : customPageDeatail) {
			log.info("生成子集页面权限，子集code={},子集名称={}", tabDetail.getKey(), tabDetail.getLabel());
			var code = String.format("%s%s", EmpConstants.EMP_PAGE_DETAIL_PREFIX, tabDetail.getKey().replaceAll("\\.", "_"));
			list.add(new AuthResourceDto().setCode(code)
					.setName(tabDetail.getLabel()).setLang("zh").setCategory(ResourceCategoryEnum.MENU)
					.setUrl(String.format("/api/hr/emp/subset/data/v1/%s/detail",code)).setResourceAction(ResourceActionEnum.VIEW)
					.setParentCode(EmpConstants.EMP_PAGE_CODE));
			// 编辑
			var editCode = String.format("%s_add", code);
			list.add(new AuthResourceDto().setCode(editCode)
					.setName("编辑").setLang("zh").setCategory(ResourceCategoryEnum.FUNC)
					.setUrl(String.format("/api/hr/emp/subset/data/v1/%s/edit",code))
					.setResourceAction(ResourceActionEnum.EDIT).setParentCode(code));
		}
		return list;
	}

	private void doRegister(List<PageDetailDto.TabDetail> customPageDeatail) {

	}

	private List<PageDetailDto.TabDetail> loadCustomPageDeatail(String pageId) {
		Result<TenantPageDetailDto> result = pageFeignClient.pageDetail(pageId);
		if (!result.isSuccess() || result.getData()==null) {
			log.error("获取页面配置失败，pageId={}",pageId);
			throw new ServerException("获取页面配置失败");
		}
		PageDetailDto pageDetail = FastjsonUtil.toObject(result.getData()
				.getStandardPageConfig(), PageDetailDto.class);
		Set<String> keySet = EmpPageDetailKey.getValues();
		return Sequences.sequence(pageDetail.getChildList()).filter(tabDetail -> !keySet.contains(tabDetail.getKey()))
				.toList();
	}
}
