package com.caidaocloud.hr.core.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SyncOnBoarDingDto {

    @ApiModelProperty("注册手机号")
    private PhoneSimple mobNum;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("员工ID")
    private Long empId;
}
