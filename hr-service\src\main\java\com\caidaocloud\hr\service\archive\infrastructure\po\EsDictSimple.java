package com.caidaocloud.hr.service.archive.infrastructure.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.enums.DataType;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 24/6/2024 4:45 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsDictSimple implements Serializable {

    @ESMapping(datatype = DataType.keyword_type)
    private String text;

    @ESMapping(datatype = DataType.keyword_type)
    private String value;
}
