package com.caidaocloud.hr.service.enums.system;

import com.caidaocloud.em.BaseEnum;

/**
 * 员工状态
 *
 * <AUTHOR>
 * @Date 2021/12/3
 */
public enum EmpStatusEnum implements BaseEnum {
    /**
     * 在职Active
     * 离职Resigned
     * 试用期Probation
     */
    IN_JOB(0, "在职", "Active"),
    LEAVE_JOB(1, "离职", "Resigned"),
    PROBATION(2, "试用期", "Probation");

    private Integer index;
    private String name;
    private String enName;

    EmpStatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    EmpStatusEnum(Integer index, String name, String enName) {
        this.index = index;
        this.name = name;
        this.enName = enName;
    }

    public static String getName(Integer index) {
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }


    public static String getLangeName(Integer index, boolean isZh) {
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return isZh ? c.name : c.enName;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String showText() {
        return name;
    }

    @Override
    public Object realValue() {
        return index.toString();
    }

    @Override
    public String desc() {
        return null;
    }

    public String getEnName() {
        return enName;
    }
}
