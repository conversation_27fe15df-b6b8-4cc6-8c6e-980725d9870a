package com.caidaocloud.hr.service.enums.system;

import com.googlecode.totallylazy.Lists;

import java.util.List;

/**
 * 系统定义的员工类型
 * <AUTHOR>
 * @date 2022/5/16
 */
public enum BaseEmpType implements LanguageEnum {
    FTI("FTI","全职实习生", "Full time intern"),
    PTI("PTI","兼职实习生", "Part time intern"),
    PART_TIME("PartTime","兼职员工", "Part time employees"),
    FULL_TIME("FullTime","正式员工", "FullTime employee"),

    ;

    private final String code;
    private final String text;
    private String enText;

    BaseEmpType(String code, String text, String enText) {
        this.code = code;
        this.text = text;
        this.enText = enText;
    }

    public static boolean isIntern(String dictCode) {
        if (dictCode == null) {
            return false;
        }
        return FTI.getCode().equals(dictCode) || PTI.getCode().equals(dictCode);
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public String getEnText() {
        return enText;
    }

    public static List<BaseEmpType> getInterns(){
        return Lists.list(FTI, PTI);
    }

    public static String getLangValue(String index, String lang) {
        for (BaseEmpType bet : BaseEmpType.values()) {
            if (bet.getCode().equals(index)) {
                return "CN".equals(lang) ? bet.getText() : bet.getEnText();
            }
        }

        return null;
    }

    @Override
    public String getEnumTextByIndexAndLang(String index, String lang) {
        return getLangValue(index, lang);
    }
}
