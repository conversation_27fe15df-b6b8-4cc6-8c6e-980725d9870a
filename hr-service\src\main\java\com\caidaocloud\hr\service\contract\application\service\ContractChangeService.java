package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.common.application.dto.ContractRevokeDto;
import com.caidaocloud.hr.service.common.application.feign.EsignFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.HyperlinkUtil;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractApprovalChangeDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractChangeDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractRevokeVo;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskApproveDTO;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.util.ObjVarValueUtil;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.workflow.application.service.WorkflowApproverService;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SysParamDictDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfProcessRuDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant.*;

@Slf4j
@Service
public class ContractChangeService {
    private ContractDomainService contractDomainService;
    private IWfRegisterFeign iWfRegisterFeign;
    private WfOperateFeignClient wfOperateFeignClient;
    private CacheService cacheService;
    @Autowired
    private CompanyService companyService;
    @Value("${env.domain.url:}")
    private String envDomainUrl;
    private EsignFeignClient esignFeignClient;

    public ContractChangeService(ContractDomainService contractDomainService, IWfRegisterFeign iWfRegisterFeign, WfOperateFeignClient wfOperateFeignClient, CacheService cacheService, EsignFeignClient esignFeignClient) {
        this.contractDomainService = contractDomainService;
        this.iWfRegisterFeign = iWfRegisterFeign;
        this.wfOperateFeignClient = wfOperateFeignClient;
        this.cacheService = cacheService;
        this.esignFeignClient = esignFeignClient;
    }

    // 解除、终止合同
    @PaasTransactional
    public List<String> changeContract(ContractChangeDto dto) {
        var data = contractDomainService.getById(dto.getBid());
        if (null == data || StringUtil.isEmpty(data.getBid()) || null == data.getContractStatus()
                || null == data.getApprovalStatus()) {
            return Lists.newArrayList();
        }

        // 非审批通过或拒绝的单据不能发起解除、终止;这里不考虑已撤销
        boolean isPaas = !ApprovalStatusEnum.PASSED.getIndex().toString().equals(data.getApprovalStatus().getValue());
        isPaas = isPaas && !ApprovalStatusEnum.REJECTED.getIndex().toString().equals(data.getApprovalStatus().getValue());
        PreCheck.preCheckArgument(isPaas, LangUtil.getMsg(HR_ERROR_CODE_32053));

        // 只有生效中的合同才能操作
        checkContractStatus(data.getContractStatus());
        // 终止和解除日期必须大于等于当前合同开始日期和小于等于当前合同的结束日期
        PreCheck.preCheckArgument(dto.getEffectiveDate() < data.getStartDate()
                        || dto.getEffectiveDate() > data.getEndDate(),
                LangUtil.getMsg(HR_ERROR_CODE_32052));

        if (null == dto.getOpenWorkflow() || !dto.getOpenWorkflow()) {
            // 如果未开启工作流，则立即生效
            return SpringUtil.getBean(ContractChangeService.class).doChangeContract(dto, data, ApprovalStatusEnum.PASSED);
        }

        // 对生效中的合同进行终止或解除后，如员工存在未生效的合同，则将未生效合同状态更新为“作废”
        PageResult<ContractDo> contractPage = getInEffectiveContract(data.getOwner().getEmpId());
        if (null == contractPage || null == contractPage.getItems()) {
            return Lists.newArrayList();
        }

        openWorkflow(dto, data);

        return contractPage.getItems().stream()
                .filter(kv -> !ApprovalStatusEnum.PASSED.getIndex().toString().equals(kv.getApprovalStatus().getValue()))
                .map(kv -> kv.getBid()).collect(Collectors.toList());
    }

    private void openWorkflow(ContractChangeDto dto, ContractDo data) {
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        workflowDto.setFuncCode(dto.isDissolve() ? "CONTRACTDISSOLVE" : "CONTRACTTERMINATION");
        workflowDto.setApplicantId(data.getOwner().getEmpId());
        workflowDto.setBusinessId(data.getBid());
        workflowDto.setApplicantName(data.getOwner().getName());
        // 业务单据事件时间
        workflowDto.setEventTime(dto.getEffectiveDate());
        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("Contract beginWorkflow err,{}", e.getMessage(), e);
        }

        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());
        changeContract(dto, data, ApprovalStatusEnum.IN_APPROVAL);
        contractDomainService.update(data);
    }

    private void checkContractStatus(EnumSimple contractStatus) {
        PreCheck.preCheckArgument(null == contractStatus
                        || !ContractStatusEnum.EFFECTIVE.getIndex().equals(contractStatus.getValue()),
                LangUtil.getMsg(HR_ERROR_CODE_32051));
    }

    public List doChangeContract(ContractChangeDto dto, ContractDo data, ApprovalStatusEnum statusEnum) {
        changeContract(dto, data, statusEnum);
        contractDomainService.update(data);

        List<String> result = Lists.newArrayList();
        if (!ApprovalStatusEnum.PASSED.equals(statusEnum)) {
            return result;
        }

        // 对生效中的合同进行终止或解除后，如员工存在未生效的合同，则将未生效合同状态更新为“作废”
        PageResult<ContractDo> contractPage = getInEffectiveContract(data.getOwner().getEmpId());
        if (null == contractPage || null == contractPage.getItems()) {
            return result;
        }

        contractPage.getItems().forEach(contractData -> {
            // 审批通过的作废
            if (ApprovalStatusEnum.PASSED.getIndex().toString().equals(contractData.getApprovalStatus().getValue())) {
                EnumSimple contractStatus = contractData.getContractStatus();
                contractStatus.setValue(ContractStatusEnum.CANCEL.getIndex());
                // 作废
                contractData.setContractStatus(contractStatus);
                // 删除合同
                contractDomainService.update(contractData);
                return;
            }
            result.add(contractData.getBid());
        });
        return result;
    }

    private void changeContract(ContractChangeDto dto, ContractDo data, ApprovalStatusEnum statusEnum) {
        data.setRemark(dto.getDesc());
        if (dto.isDissolve()) {
            // 合同解除
            data.setDissolveDate(dto.getEffectiveDate());
            data.setDissolveReason(dto.getReason());
            EnumSimple cse = data.getContractStatus();
            cse.setValue(ContractStatusEnum.RELEASED.getIndex());
            data.setContractStatus(dto.getEffectiveDate() < DateUtil.getCurrentTimestamp() ?
                    cse : data.getContractStatus());
        } else {
            // 合同终止
            data.setTerminationDate(dto.getEffectiveDate());
            data.setTerminationReason(dto.getReason());
            EnumSimple cse = data.getContractStatus();
            cse.setValue(ContractStatusEnum.TERMINATED.getIndex());
            data.setContractStatus(dto.getEffectiveDate() < DateUtil.getCurrentTimestamp() ?
                    cse : data.getContractStatus());
        }
        String value = statusEnum.getIndex().toString();
        value = ApprovalStatusEnum.REJECTED.getIndex().toString().equals(value)
                ? ApprovalStatusEnum.PASSED.getIndex().toString() : value;
        EnumSimple simple = new EnumSimple();
        simple.setValue(value);
        data.setApprovalStatus(simple);
    }

    private PageResult<ContractDo> getInEffectiveContract(String empId) {
        ContractStatusQueryDto queryDto = new ContractStatusQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(20);
        queryDto.setContractStatus(ContractStatusEnum.IN_EFFECTIVE.getIndex());
        queryDto.setEmpId(empId);
        PageResult<ContractDo> contractPage = contractDomainService.getContractPage(queryDto);
        log.info("Contract data to be deleted. PageResult={}", FastjsonUtil.toJson(contractPage));
        return contractPage;
    }

    public void updateContractApproval(ContractApprovalChangeDto dto, WfTaskActionEnum choice) {
        if (StringUtil.isEmpty(dto.getBusinessKey())) {
            return;
        }

        String[] keys = dto.getBusinessKey().split("_");
        if (null == keys || keys.length < 2) {
            return;
        }
        dto.setBid(keys[0]);
        dto.setDissolve("CONTRACTDISSOLVE".equals(keys[1]));
        dto.setRemark(dto.getDesc());
        if (dto.isDissolve()) {
            dto.setDissolveDate(dto.getEffectiveDate());
            dto.setDissolveReason(dto.getReason());
        } else {
            dto.setTerminationDate(dto.getEffectiveDate());
            dto.setTerminationReason(dto.getReason());
        }

        if (WfTaskActionEnum.REFUSE.equals(choice)) {
            dto.setDissolveDate(null);
            dto.setDissolveReason(null);
            dto.setTerminationDate(null);
            dto.setTerminationReason(null);
            dto.setEffectiveDate(null);
        }

        // 修改数据
        updateContract(dto);
        // 驱动流程
        updateWorkflow(dto, choice);
    }

    private void updateContract(ContractApprovalChangeDto dto) {
        var data = contractDomainService.getById(dto.getBid());
        if (null == data || StringUtil.isEmpty(data.getBid())) {
            return;
        }

        dto.setEffectiveDate(null == dto.getEffectiveDate() ?
                (dto.isDissolve() ? data.getTerminationDate() : data.getDissolveDate()) : dto.getEffectiveDate());
        Class clazz = ContractDo.class.getClass();
        Map map = FastjsonUtil.convertObject(dto, Map.class);
        dto.getWritableFields().forEach(prop -> {
            Object obj = map.get(prop);
            try {
                Field field = clazz.getDeclaredField(prop);
                field.setAccessible(true);
                field.set(data, obj);
            } catch (Exception e) {
                log.warn("Contract WritableFields err,{}", e);
            }
        });

        contractDomainService.update(data);
    }

    public void updateWorkflow(ContractApprovalChangeDto dto, WfTaskActionEnum choice) {
        WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
        wfApproveTaskDto.setChoice(choice);
        wfApproveTaskDto.setComment(dto.getComment());
        wfApproveTaskDto.setTaskId(dto.getTaskId());
        try {
            // 获取单据生效时间
            if (null != dto.getEffectiveDate()) {
                // 调用 feign 更新业务单据事件时间
                WfProcessRuDto wfProcessRuDto = new WfProcessRuDto();
                wfProcessRuDto.setBusinessKey(dto.getBusinessKey());
                wfProcessRuDto.setEventTime(dto.getEffectiveDate());
                try {
                    iWfRegisterFeign.updateEventTime(wfProcessRuDto);
                } catch (Exception e) {
                    log.error("Transfer updateEventTime err,{}", e.getMessage(), e);
                }
            }
            Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
        } catch (Exception e) {
            log.error("approveWorkflow err,{}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public ContractRevokeVo revoke(WfRevokeDto dto) {
        if (Objects.isNull(dto) || (StringUtils.isBlank(dto.getBusinessKey()))) {
            throw new ServerException("parameter is illegal");
        }
        val pageResult = DataQuery.identifier("entity.esign.ElectronContract").filter(DataFilter.eq("deleted", Boolean.FALSE.toString()).andEq("taskBusinessKey", dto.getBusinessKey()), DataSimple.class);
        String msg = "";
        if (!CollectionUtils.isEmpty(pageResult.getItems())) {
            List<String> revokeBidList = Lists.newArrayList();
            for (DataSimple dataSimple : pageResult.getItems()) {
                EnumSimple processStatus = (EnumSimple) dataSimple.getProperties().getOrDefault("processStatus", new EnumSimple());
                val processStatusStr = StringUtils.defaultString(processStatus.getValue(), "");
                switch (processStatusStr) {
                    case "1":
                        revokeBidList.add(dataSimple.getBid());
                        break;
                    case "2":
                        msg = MessageHandler.getMessage("caidao.electronic.signature.finish", WebUtil.getRequest());
                        break;
                    default:
                }
            }
            if (!CollectionUtils.isEmpty(revokeBidList)) {
                val contractRevokeDto = new ContractRevokeDto()
                        .setBids(revokeBidList)
                        .setRevokeReason("系统自动撤销");
                esignFeignClient.batchRevoke(contractRevokeDto);
            }
        }
        iWfRegisterFeign.revokeProcessOfTask(dto);
        return new ContractRevokeVo(msg);
    }

    public void callback(String tenantId, String businessKey, WfCallbackTriggerOperationEnum callbackType) {
        // 设置回调用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            String dataId = StringUtils.substringBefore(businessKey, "_");
            var data = contractDomainService.getById(dataId);
            if (null == data || StringUtil.isEmpty(data.getBid())) {
                return;
            }

            ContractChangeDto dto = new ContractChangeDto();
            dto.setDesc(data.getRemark());
            dto.setDissolve(null != data.getDissolveDate());
            dto.setEffectiveDate(dto.isDissolve() ? data.getDissolveDate() : data.getTerminationDate());
            dto.setReason(dto.isDissolve() ? data.getDissolveReason() : data.getTerminationReason());
            data.getContractStatus().setValue(dto.isDissolve() ? ContractStatusEnum.RELEASED.getIndex() : ContractStatusEnum.TERMINATED.getIndex());
            ApprovalStatusEnum statusEnum = ApprovalStatusEnum.PASSED;
            switch (callbackType) {
                case APPROVED:
                    break;
                case REFUSED:
                    statusEnum = ApprovalStatusEnum.REJECTED;
                    data.getContractStatus().setValue(ContractStatusEnum.EFFECTIVE.getIndex());
                    dto.setReason(null);
                    data.setDissolveReason(null);
                    data.setDissolveDate(null);
                    data.setTerminationReason(null);
                    data.setTerminationDate(null);
                    break;
                case REVOKE:
                    statusEnum = ApprovalStatusEnum.REVOKE;
                    break;
                default:
                    throw new ServerException("Unsupported approval callback type");
            }

            doChangeContract(dto, data, statusEnum);
        } catch (Exception e) {
            log.error("contract workflow callback err,{}", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public Map<String, String> getNoticeVar(String businessKey, List<String> variables) {
        log.info("ContractChangeService getNoticeVar businessKey={}, variables={}", businessKey, FastjsonUtil.toJson(variables));
        if (log.isDebugEnabled()) {
            log.debug("getNoticeVar,tenantId={}", SecurityUserUtil.getSecurityUserInfo().getTenantId());
        }
        Map<String, String> results = Maps.map();
        results.put("approvalLink", HyperlinkUtil.generateHyperlink(envDomainUrl + "/approval-node?businessKey=" + businessKey, "链接"));
        results.put("approvalLinkEn", HyperlinkUtil.generateHyperlink(envDomainUrl + "/approval-node?businessKey=" + businessKey, "link"));
        String dataId = StringUtils.substringBefore(businessKey, "_");
        var data = contractDomainService.getById(dataId);
        if (null == data || StringUtil.isEmpty(data.getBid())) {
            return results;
        }
        Map<String, Object> dataMap = new HashMap<>();
        for (String code : variables) {
            if (results.containsKey(code)) {
                // 处理变量时，存在同类变量推断，加快解析速度
                continue;
            } else if (code.startsWith("workInfo.")) {
                Object dataObj = dataMap.get("workInfo.");
                if (null == dataObj) {
                    dataMap.put("workInfo.", loadEmpWorkInfoVo(data.getOwner().getEmpId()));
                    dataObj = dataMap.get("workInfo.");
                }
                results.put(code, ObjVarValueUtil.getValueFromObjV1(code.replace("workInfo.", ""), dataObj));
                continue;
            } else if (code.startsWith("customOrgRole.")) {
                List<CustomOrgRoleDo> roleList = (List<CustomOrgRoleDo>) dataMap.get("customOrgRole.");
                if (null == roleList) {
                    List<CustomOrgRoleDo> roleDataList = SpringUtil.getBean(CustomOrgRoleDo.class)
                            .selectByOrgId(data.getOrganize(), System.currentTimeMillis());
                    dataMap.put("customOrgRole.", roleDataList.isEmpty() ? new ArrayList<>() : roleDataList);
                    roleList = (List<CustomOrgRoleDo>) dataMap.get("customOrgRole.");
                }
                if (null == roleList || roleList.isEmpty()) {
                    continue;
                }
                String prop = code.replace("customOrgRole.", "");
                String roleCode = StringUtils.substringBefore(prop, ".");
                CustomOrgRoleDo dataObj = roleList.stream().filter(role -> roleCode.equals(role.getRoleCode()))
                        .findFirst().orElse(null);
                prop = prop.replace(roleCode + ".", "");
                results.put(code, ObjVarValueUtil.getValueFromObjV1(prop, dataObj));
                continue;
            } else if (code.startsWith("approver.")) {
                putApproverVariables(code, data.getOwner().getEmpId(), results);
                continue;
            }
            results.put(code, ObjVarValueUtil.getValueFromObjV2(code, data));
        }

        buildEnVariable(results, data);
        //上一份合同相关字段
        //相同员工失效的合同
        preContractVariables(data, results);
        log.info("ContractChangeService results={}", FastjsonUtil.toJson(results));
        return results;
    }

    private void buildEnVariable(Map<String, String> results, ContractDo data) {
        String company = data.getCompany();
        CompanyDo companyDo = companyService.getCompanyById(company);
        if (null != companyDo && StringUtil.isNotEmpty(companyDo.getI18nCompanyName())) {
            Map i18nMap = FastjsonUtil.toObject(companyDo.getI18nCompanyName(), Map.class);
            results.put("companyTxtEn", null != i18nMap ? (String) i18nMap.getOrDefault("en-US", i18nMap.get("default")) : "");
        }

        if (null == data.getContractSettingType() || StringUtil.isEmpty(data.getContractSettingType().getValue())) {
            return;
        }

        String dictId = data.getContractSettingType().getValue();
        SysParamDictDto dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", dictId)), SysParamDictDto.class);
        if (dictDto != null) {
            results.put("contractSettingTypeEn", dictDto.getDictEngName());
        }
    }

    private void putApproverVariables(String code, String empId, Map<String, String> results) {
        String approverStr = code.replace("approver.", "");
        String approverBid = StringUtils.substringBefore(approverStr, ".");
        List<EmpSimple> approverEmpList = SpringUtil.getBean(WorkflowApproverService.class).getApproverEmpList(empId, approverBid);
        StringBuilder names = new StringBuilder(), enNames = new StringBuilder();
        approverEmpList.forEach(emp -> {
            names.append(appendOrEmpty(emp.getName())).append(";");
            enNames.append(appendOrEmpty(emp.getEnName())).append(";");
        });
        String str = names.toString();
        if (str.length() > 0) {
            results.put("approver." + approverBid + ".empWorkInfo.name", str.substring(0, str.length() - 1));
        }
        str = enNames.toString();
        if (str.length() > 0) {
            results.put("approver." + approverBid + ".empWorkInfo.enName", str.substring(0, str.length() - 1));
        }
    }

    private String appendOrEmpty(String str) {
        if (null == str) {
            str = "";
        }
        return str;
    }

    private EmpWorkInfoVo loadEmpWorkInfoVo(String ownerId) {
        EmpWorkInfoDo empWorkInfo = SpringUtil.getBean(EmpWorkInfoDo.class).getEmpWorkInfo(ownerId, System.currentTimeMillis());
        EmpWorkInfoVo workInfoVo = ObjectConverter.convert(empWorkInfo, EmpWorkInfoVo.class);
        if (StringUtil.isNotEmpty(empWorkInfo.getCostCenters())) {
            workInfoVo.setCostCenters(FastjsonUtil.toArrayList(empWorkInfo.getCostCenters(), EmpCostCenterDto.class));
        }
        if (null != workInfoVo.getLeadEmpId() && StringUtil.isEmpty(workInfoVo.getLeadEmpId().getEmpId())) {
            workInfoVo.setLeadEmpId(null);
        }
        Map<String, Object> ext = SpringUtil.getBean(EmpExtFieldService.class)
                .getEmpCustomPropertyValue(empWorkInfo.getDoIdentifier(), empWorkInfo);
        workInfoVo.setExt(ext);
        return workInfoVo;
    }

    private void preContractVariables(ContractDo data, Map<String, String> results) {
        ContractDo contractDo = null;
        if (StringUtils.isNotEmpty(data.getLastContract())) {
            contractDo = contractDomainService.getById(data.getLastContract());
        }

        if (contractDo != null && contractDo.getEndDate() != null) {
            results.put("lastContractEndDate", String.valueOf(contractDo.getEndDate()));
            results.put("lastContractExpireDays", String.valueOf(countLastContractExpireDays(contractDo.getEndDate())));

            String dictId = contractDo.getContractSettingType().getValue();
            SysParamDictDto dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", dictId)), SysParamDictDto.class);

            if (dictDto != null) {
                results.put("lastContractType", dictDto.getDictChnName());
                results.put("lastContractTypeEn", dictDto.getDictEngName());
            }
        }
    }

    private static Long countLastContractExpireDays(Long endDate) {
        return (endDate - System.currentTimeMillis()) / (24 * 60 * 60 * 1000);
    }
}
