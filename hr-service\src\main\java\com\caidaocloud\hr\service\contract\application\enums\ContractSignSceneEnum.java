package com.caidaocloud.hr.service.contract.application.enums;

/**
 * <AUTHOR>
 */
public enum ContractSignSceneEnum {
    RENEW("续签"),
    CHANGE("改签"),
    NEW("新签"),
    BATCH_RENEW("批量续签"),
    BATCH_NEW("批量新签")
    ;
    private String remark;

    ContractSignSceneEnum(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
