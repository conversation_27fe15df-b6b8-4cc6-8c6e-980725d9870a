package com.caidaocloud.hr.service.confirmation.application.factory;

import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.workflow.dto.WfComponentValueDto;
import com.caidaocloud.workflow.dto.WfMetaSeqConditionDto;
import com.caidaocloud.workflow.enums.WfSeqConditionCallTypeEnum;
import com.caidaocloud.workflow.enums.WfSeqConditionOperatorEnum;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/6/6
 */
public class ConfirmationWorkflowFactory {
	private static final Map<String, String> standardSeqCondition = Maps.map(
			Sequences.sequence(
					Pair.pair("companyChange", "合同公司是否变更"),
					Pair.pair("salaryChange", "薪资是否变更"),
					Pair.pair("postChange", "岗位是否变更"),
					Pair.pair("jobGradeChange", "职级是否变更"),
					Pair.pair("organizeChange", "组织是否变更"),
					Pair.pair("workplaceChange", "工作地是否变更")));


	/**
	 * 转正通用序列流
	 * @param funCode
	 * @return
	 */
	public static List<WfMetaSeqConditionDto> commonSeqCondition(String funCode,String callback){
		List<WfComponentValueDto> booleanComponentValue = booleanComponentValue();
		List<WfMetaSeqConditionDto> seqList = new ArrayList<>();
		for (Map.Entry<String, String> entry : standardSeqCondition.entrySet()) {
			seqList.add(enumSeqCondition(funCode, callback, booleanComponentValue, entry.getKey(),entry.getValue()));
		}
		return seqList;
	}

	public static WfMetaSeqConditionDto dictSeqCondition(String funCode, String callback,String name,String property,String dictType) {
		return new WfMetaSeqConditionDto(name,
				funCode.replaceAll("-", "_") + '_' + property,
				Lists.list(funCode),
				"caidaocloud-hr-service",
				callback,
				WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
				SecurityUserUtil.getSecurityUserInfo().getTenantId(),
				Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
						WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
				WfValueComponentEnum.DATA_SOURCE,
				Lists.list(),
				"/api/bcc/dict/common/v1/dict/getEnableDictList?belongModule=Employee&typeCode=" + dictType, false);
	}

	public static WfMetaSeqConditionDto enumSeqCondition(String funCode, String callback, List<WfComponentValueDto> componentValueDtos, String property, String name) {
		return new WfMetaSeqConditionDto(name,
				funCode.replaceAll("-", "_") + '_' + property,
				Lists.list(funCode),
				"caidaocloud-hr-service",
				callback,
				WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
				SecurityUserUtil.getSecurityUserInfo().getTenantId(),
				Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
						WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
				WfValueComponentEnum.ENUM,
				componentValueDtos,
				"", false);
	}


	public static List<WfComponentValueDto> booleanComponentValue() {
		List<WfComponentValueDto> componentValueList = Lists.list();
		componentValueList.add(new WfComponentValueDto("是", "true"));
		componentValueList.add(new WfComponentValueDto("否", "false"));
		return componentValueList;
	}
}
