package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.dto.EmpJobRecordDto;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpJobRecordDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpJobRecordDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpJobRecordQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EmpJobRecordService extends BaseServiceImpl {
    @Resource
    private EmpJobRecordDomainService empJobRecordDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    @Override
    protected BaseDomainService getDomainService() {
        return empJobRecordDomainService;
    }

    public PageResult<EmpJobRecordDo> getPageList(EmpJobRecordQueryDto dto) {
        return empJobRecordDomainService.getPageList(dto);
    }

    public List<EmpJobRecordDo> getList(EmpJobRecordQueryDto query) {
        return empJobRecordDomainService.getList(query);
    }

    public String saveEmpJobRecord(EmpJobRecordDto dto) {
        EmpJobRecordDo data = ObjectConverter.convert(dto, EmpJobRecordDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empJobRecordDomainService.saveEmpJobRecord(data);
    }

    private void doConverter(EmpJobRecordDto source, EmpJobRecordDo target) {
        if (StringUtils.isNotEmpty(source.getJobType())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getJobType());
            target.setJobType(enumSimple);
        }
        if (StringUtils.isNotEmpty(source.getEmpType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getEmpType());
            target.setEmpType(dictSimple);
        }
        if(null == source.getLeadEmpId()){
            EmpSimple empSimple = new EmpSimple();
            target.setLeadEmpId(empSimple);
        }
    }

    public void update(EmpJobRecordDto dto) {
        EmpJobRecordDo data = ObjectConverter.convert(dto, EmpJobRecordDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empJobRecordDomainService.update(data);
    }
}
