package com.caidaocloud.hr.service.archive.provider;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.application.ArchiveService;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 4:54 下午
 */
public interface IModuleProvider {

    String module();

    List<ArchiveData> fetchArchiveData(String businessLine, String businessId);

    /**
     * 通过服务间feign调用实现
     * @param page
     * @return
     */
    List<ArchiveData> fetchPageArchiveData(BasePage page);

    /**
     * 通过消息中间件实现
     */
    default void fetchAllArchiveData() {}

    /**
     * 触发文件归档删除事件
     * @param businessLine
     * @param businessId
     */
    void dispatchArchiveDelEvent(String businessLine, String subBusinessLine, String businessId, ArchiveFile archiveFile);

    @PostConstruct
    default void register() {
        ArchiveService.providers.putIfAbsent(module(), this);
    }

}
