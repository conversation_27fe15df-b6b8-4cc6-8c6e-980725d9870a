package com.caidaocloud.hr.service.contract.domain.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.hr.service.contract.domain.repository.ICompletedContractImportRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Service
public class CompletedContractImportDo {
    private String bid;
    private EmpSimple owner;

    private String empId;
    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工工号
     */
    private String workno;

    /**
     * 签订类型
     */
    private EnumSimple signType;

    private String signTypeTxt;

    /**
     * 合同公司
     */
    private String company;

    /**
     * 合同公司代码
     */
    private String companyCode;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同设置
     */
    private String contractTypeSet;

    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;

    /**
     * 合同期限
     */
    private EnumSimple periodType;

    private String periodTypeTxt;

    /**
     * 合同开始日期
     */
    private Long startDate;
    private String startDateTxt;

    /**
     * 合同结束日期
     */
    private Long endDate;
    private String endDateTxt;

    /**
     * 是否需要审批
     */
    private String needApprove;

    /**
     * 所属组织Id
     */
    private String organize;

    /**
     * 所属组织名称
     */
    private String organizeTxt;

    /**
     * 所属组织编码
     */
    private String organizeCode;

    /**
     * 员工状态
     */
    private EnumSimple empStatus;

    /**
     * 关联的职务ID
     */
    private String job;

    /**
     * 关联的职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 合同期（月）
     */
    private Integer contractPeriod;

    /**
     * 状态
     */
    private EnumSimple contractStatus;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 员工类型
     */
    private DictSimple empType;

    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;

    /**
     * 字段空检验
     */
    private boolean checkEmpty = false;

    /**
     * 字段空检验提示
     */
    private String checkEmptyTips;

    /**
     * 合同类型
     */
    private DictSimple contractType;

    @Resource
    private ICompletedContractImportRepository completedContractImportRepository;

    public List<CompletedContractImportDo> getCompletedContractImportDoFromExcel(InputStream inputStream){
        return ObjectConverter.convertList(completedContractImportRepository.getCompletedContractImportDoFromExcel(inputStream), CompletedContractImportDo.class);
    };
}
