package com.caidaocloud.hr.service.agreement.interfaces.dto;

import com.caidaocloud.hr.service.enums.AgreementTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 14/10/2024 4:37 下午
 */
@Data
public class UserAgreementDto implements Serializable {

    /**
     * 协议ID
     */
    private String bid;

    /**
     * 协议类型
     */
    private AgreementTypeEnum type;

    /**
     * 协议名称
     */
    private Map<String, String> i18Name;

    /**
     * 协议内容
     */
    private String content;
}
