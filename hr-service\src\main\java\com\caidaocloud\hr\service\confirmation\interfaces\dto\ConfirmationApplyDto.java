package com.caidaocloud.hr.service.confirmation.interfaces.dto;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 转正申请表单dto
 */
@Data
@ApiModel("转正申请内容")
public class ConfirmationApplyDto {
    private String formValueId;

    @ApiModelProperty("表单主键")
    private String id;

    @ApiModelProperty("申请员工")
    private EmpSimple emp;

    @ApiModelProperty("转正配置ID")
    private String defId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("表单数据")
    private Map<String, Object> formData;

    @ApiModelProperty("转正数据")
    private List<ConfirmationChangeField> confirmations = Lists.list();

    @ApiModelProperty("薪资数据")
    private List<ConfirmationChangeField> salary = Lists.list();

    @ApiModelProperty("任职数据")
    private List<ConfirmationChangeField> work = Lists.list();

    public void checkApplyEmp(){
        PreCheck.preCheckArgument(null == emp || StringUtil.isEmpty(emp.getEmpId()), "申请人不存在或为空");
    }
}
