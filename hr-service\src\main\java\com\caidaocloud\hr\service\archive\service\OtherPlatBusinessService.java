package com.caidaocloud.hr.service.archive.service;

import com.caidaocloud.hr.service.archive.domain.entity.OtherPlatBusinessDo;
import com.caidaocloud.hr.service.archive.domain.service.OtherPlatBusinessDomainService;
import com.caidaocloud.hr.service.archive.interfaces.dto.OtherPlatBusinessDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OtherPlatBusinessService {
    @Resource
    private OtherPlatBusinessDomainService otherPlatBusinessDomainService;

    public void save(List<OtherPlatBusinessDto> list) {
        for (OtherPlatBusinessDto dto : list) {
            OtherPlatBusinessDo convert = ObjectConverter.convert(dto, OtherPlatBusinessDo.class);
            DictSimple business = new DictSimple();
            business.setValue(dto.getBusinessDictValue());
            convert.setBusiness(business);

            DictSimple businessType = new DictSimple();
            businessType.setValue(dto.getBusinessTypeDictValue());
            convert.setBusinessType(businessType);
            if (StringUtils.isEmpty(dto.getBid())) {
                otherPlatBusinessDomainService.insert(convert);
            } else {
                otherPlatBusinessDomainService.update(convert);
            }
        }
    }

    public List<OtherPlatBusinessDo> selectList() {
        return otherPlatBusinessDomainService.selectList();
    }

    public void delete(String bid) {
        OtherPlatBusinessDo data = new OtherPlatBusinessDo();
        data.setBid(bid);
        otherPlatBusinessDomainService.delete(data);
    }
}
