package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractChangeService;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractChangeDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpBasicInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.temination.application.TerminationChangeService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 员工离职定时任务
 */
@Slf4j
@Service
public class EmpLeaveTask {

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private EmpBasicInfoDomainService empBasicInfoDomainService;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Autowired
    private TerminationChangeService terminationChangeService;
    @Resource
    private ContractService contractService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    /**
     * 员工离职定时任务
     * 离职日当天定时任务自动更新员工状态及合同状态
     */
    @XxlJob("empLeaveNoticeJobHandler")
    public ReturnT<String> empLeaveNoticeJobHandler() {
        XxlJobHelper.log("XxlJob empLeaveNoticeJobHandler start");
        log.info("cronTask[Emp Leave Notice]------------------------start execution,time {}", System.currentTimeMillis());
        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                /**
                 * 1、没有日志
                 * 2、一条数据失败，后续都不处理
                 */
                empLeave();
            } catch (Exception e){
              log.error("Employee Resignation Scheduled Tasks err,{}", e.getMessage(), e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        log.info("cronTask[Emp Leave Notice]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empLeaveNoticeJobHandler end");
        return ReturnT.SUCCESS;
    }

    public void empLeave() {
        Long leaveDate = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
        Integer empStatus = EmpStatusEnum.LEAVE_JOB.getIndex();
        List<EmpWorkInfoDo> workInfoDoList = empWorkInfoService.getLeaveEmpList(leaveDate, empStatus);
        if (CollectionUtils.isNotEmpty(workInfoDoList)) {
            for (EmpWorkInfoDo workInfoDo : workInfoDoList) {
                try {
                    doEmpLeave(workInfoDo);
                } catch (Exception e){
                    log.error("Employee resignation logic processing failed. EmpDataInfo={}, errMsg={}",
                            FastjsonUtil.toJson(workInfoDo), e.getMessage(), e);
                }
            }
        }
    }

    private void doEmpLeave(EmpWorkInfoDo workInfoDo){
        EmpWorkInfoDo beforeEmpWorkInfo = new EmpWorkInfoDo();
        BeanUtils.copyProperties(workInfoDo, beforeEmpWorkInfo);

        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(EmpStatusEnum.LEAVE_JOB.getIndex()));
        workInfoDo.setEmpStatus(enumSimple);
        //更新员工状态为离职
        workInfoDo.setDataStartTime(workInfoDo.getLeaveDate() + 24 * 60 * 60 * 1000);
        empWorkInfoDo.updateEmpWorkInfo(workInfoDo, false);

        // 账号停用的消息
        terminationChangeService.doLastDayEvent(workInfoDo.getLeaveDate(), workInfoDo.getEmpId());

        //更新员工基本信息
        EmpBasicInfoDo empBasicInfo = empBasicInfoService.getEmpBasicInfo(workInfoDo.getEmpId(), workInfoDo.getDataStartTime());
        if (empBasicInfo != null) {
            EnumSimple simple = new EnumSimple();
            simple.setValue(String.valueOf(EmpStatusEnum.LEAVE_JOB.getIndex()));
            empBasicInfo.setEmpStatus(simple);
            empBasicInfoDomainService.update(empBasicInfo);
        }

        //更新合同状态
        List<ContractDo> reList = contractService.getEmpCurrentContract(workInfoDo.getEmpId());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(reList)) {
            return;
        }
        ContractDo data = reList.get(0);
        ContractChangeDto dto = new ContractChangeDto();
        dto.setDesc("员工离职自动终止合同");
        dto.setDissolve(false);
        dto.setOpenWorkflow(false);
        // 离职日期
        dto.setEffectiveDate(workInfoDo.getLeaveDate());
        // 离职原因
        dto.setReason(workInfoDo.getResignReason().getValue());
        SpringUtil.getBean(ContractChangeService.class).doChangeContract(dto, data, ApprovalStatusEnum.PASSED);

        //生成一条离职的成长记录
        empWorkInfoDomainService.doProcessGrowthRecord(beforeEmpWorkInfo, workInfoDo);
    }
}
