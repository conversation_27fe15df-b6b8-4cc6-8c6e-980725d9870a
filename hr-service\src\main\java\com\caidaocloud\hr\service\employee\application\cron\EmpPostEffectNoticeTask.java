package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpConcurrentPostService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpEmpConcurrentPostRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主岗,兼岗失效通知定时任务
 */
@Slf4j
@Service
public class EmpPostEffectNoticeTask {
    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private MsgNoticeService messageService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private OrgService orgService;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private IEmpEmpConcurrentPostRepository empEmpConcurrentPostRepository;

    /**
     * 兼岗失效通知
     */
    @XxlJob("empPostEffectNoticeJobHandler")
    public ReturnT<String> empPostEffectNoticeJobHandler() {
        XxlJobHelper.log("XxlJob empPostEffectNoticeJobHandler start");
        log.info("cronTask[Emp Post Effect Notice]------------------------start execution,time {}", System.currentTimeMillis());

        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                // 发送兼岗失效变更上级员工消息
                sendPartTimeJobLeaderMsg(tenantId);
                // 发送兼岗失效变更组织负责人消息
                sendPartTimeJobOrgMsg(tenantId);
            }finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });

        log.info("cronTask[Emp Post Effect Notice]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empPostEffectNoticeJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 定时更新兼岗数据状态；
     * 未生效-》已生效
     * 已生效-》已失效
     * @return
     */
    @XxlJob("empConcurrentPostStatusChangeJobHandler")
    public ReturnT<String> empConcurrentPostStatusChangeJobHandler(){
        XxlJobHelper.log("XxlJob empConcurrentPostStatusChangeJobHandler start");
        log.info("cronTask[Emp Concurrent Post Status Change]------------------------start execution,time {}", System.currentTimeMillis());
        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                //要生效数据；
                List<EmpConcurrentPostDo> empConcurrentPostDosToBeEffect = empEmpConcurrentPostRepository.selectToBeEffect();
                //要失效数据；
                List<EmpConcurrentPostDo> empConcurrentPostDosToBeLose = empEmpConcurrentPostRepository.selectToBeLose();
                for (EmpConcurrentPostDo empConcurrentPostDo : empConcurrentPostDosToBeEffect) {
                    //变生效 数据；
                    empConcurrentPostDo.setStatus(0);
                    empEmpConcurrentPostRepository.updateById(empConcurrentPostDo);
                }
                for (EmpConcurrentPostDo empConcurrentPostDo : empConcurrentPostDosToBeLose) {
                    //变失效 数据；
                    empConcurrentPostDo.setStatus(1);
                    empEmpConcurrentPostRepository.updateById(empConcurrentPostDo);
                }
            }finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        log.info("cronTask[Emp Concurrent Post Status Change]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empConcurrentPostStatusChangeJobHandler end");
        return ReturnT.SUCCESS;
    }

    public void sendPartTimeJobLeaderMsg(String tenantId){
        // 获取消息配置
        List<MsgConfigDto> msgConfigList = messageService.getMsgConfigList(NoticeType.PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_SUPERIOR);
        log.info("Configuration of part-time leader messages = {}", FastjsonUtil.toJson(msgConfigList));
        if(null == msgConfigList || msgConfigList.isEmpty()){
            return;
        }

        msgConfigList.forEach(msgConfig -> {
            sendChangeSuperiorMsg(msgConfig, tenantId);
        });

        log.info("part time job loss leader send msg end.");
    }

    public void sendPartTimeJobOrgMsg(String tenantId){
        List<MsgConfigDto> msgConfigList = messageService.getMsgConfigList(NoticeType.PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_DEPT_HEAD);
        log.info("Configuration of part-time org messages = {}", FastjsonUtil.toJson(msgConfigList));
        if(null == msgConfigList || msgConfigList.isEmpty()){
            return;
        }

        msgConfigList.forEach(msgConfig -> {
            sendChangeDeptHeadMsg(msgConfig, tenantId);
        });

        log.info("part time job loss Org send msg end.");
    }

    private void sendChangeSuperiorMsg(MsgConfigDto msgNoticeConfig, String tenantId){
        Integer day = msgNoticeConfig.getDay();
        if(day <= 0){
            log.warn("Incorrect configuration of notification days.");
            return;
        }

        if(!"ADVANCE".equals(msgNoticeConfig.getRound())){
            log.warn("Part time job change superior msg|no other configurations are currently supported,msgconfig bid={}",
                    msgNoticeConfig.getBid());
            return;
        }

        // 提前通知 X天 即失效日期是X天之后
        Long dateTime = getDate(day + 1), beginTime = getDate(day);
        // 获取将要失效的数据
        List<EmpConcurrentPostDo> concurrentPostDos = empConcurrentPostService.selectListWilllostEffective(tenantId, dateTime, beginTime);
        if(CollectionUtils.isEmpty(concurrentPostDos)){
            log.warn("There is no will loss effective data msgType:{}", NoticeType.PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_SUPERIOR);
            return;
        }
        List<String> empIdList = concurrentPostDos.stream().map(x -> x.getEmpId()).distinct().collect(Collectors.toList());
        Map<String, EmpWorkInfoDo> empWorkInfoDoMap = empWorkInfoDomainService.getEmpListByEmpIds(empIdList, System.currentTimeMillis())
                .stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, EmpWorkInfoDo->EmpWorkInfoDo));
        for (EmpConcurrentPostDo currentPost : concurrentPostDos) {
            EmpReportLeaderDto empReportLeaderDto = new EmpReportLeaderDto();
            empReportLeaderDto.setEmpId(currentPost.getEmpId());
            empReportLeaderDto.setPostId(currentPost.getPost());
            empReportLeaderDto.setOrganizeId(currentPost.getOrganize());
            empReportLeaderDto.setDateTime(currentPost.getEndDate());
            // 作为兼岗找下级员工
            List<EmpConcurrentPostDo> empConcurrentPostListByLeader = empConcurrentPostService.getEmpConcurrentPostByLeader(empReportLeaderDto);
            Map<String, String> ext = new HashMap<>();
            ext.put("effectiveMsg.effectiveDateTxt", DateUtil.formatDate(beginTime));
            EmpWorkInfoDo thisworkInfoDo = empWorkInfoDoMap.get(currentPost.getEmpId());
            if(null != thisworkInfoDo){
                if(thisworkInfoDo.ifTermination()){
                    // 如果员工已经是离职状态，则忽略
                    continue;
                }
                ext.put("effectiveMsg.empNameTxt", thisworkInfoDo.getName());
                ext.put("effectiveMsg.empWorknoTxt", thisworkInfoDo.getWorkno());
            }
            ext.put("effectiveMsg.empOrganizeTxt", currentPost.getOrganizeTxt());
            ext.put("effectiveMsg.empPostTxt", currentPost.getPostTxt());
            ext.put("effectiveMsg.empJobTxt", currentPost.getJobTxt());

            List<String> postLines = new ArrayList<>();
            List<String> orgLines = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(empConcurrentPostListByLeader)) {
                List<String> empIds = empConcurrentPostListByLeader.stream().map(EmpConcurrentPostDo::getEmpId).collect(Collectors.toList());
                List<EmpBasicInfoDo> empBasicInfoListByBids = empBasicInfoService.getEmpBasicInfoListByBids(empIds, System.currentTimeMillis());
                Map<String, EmpBasicInfoDo> basicInfoMap = empBasicInfoListByBids.stream().collect(Collectors.toMap(EmpBasicInfoDo::getBid, st -> st));
                for (EmpConcurrentPostDo empConcurrentPostDo : empConcurrentPostListByLeader) {
                    //获取员工基本信息
                    EmpBasicInfoDo empBasicInfoDo = basicInfoMap.getOrDefault(empConcurrentPostDo.getEmpId(), new EmpBasicInfoDo());
                    if(empBasicInfoDo.ifTermination()){
                        // 排除离职员工
                        continue;
                    }
                    postLines.add(String.format(HrConstant.NAME_CODE_FORMAT, empBasicInfoDo.getName(), empBasicInfoDo.getWorkno()));
                }

                String res = String.join(";", postLines);
                ext.put("effectiveMsg.postLineTxt", res);
            }
            // 兼岗找下级员工主岗汇报的
            List<EmpWorkInfoDo> empWorkInfoDoList = empWorkInfoService.getEmpWorkInfoByEmpReportLeaderDto(empReportLeaderDto, -1);
            List<String> empLines = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(empWorkInfoDoList)) {
                empWorkInfoDoList.forEach(workInfo -> {
                    if(workInfo.ifTermination()){
                        // 排除离职员工
                        return;
                    }
                    empLines.add(String.format(HrConstant.NAME_CODE_FORMAT, workInfo.getName(), workInfo.getWorkno()));
                });
                String res = String.join(";", empLines);
                ext.put("effectiveMsg.empLineTxt", res);
            }

            // 作为组织负责人影响到的组织
            List<OrgDo> orgListByLeader = orgService.getOrgListByLeader(empReportLeaderDto, -1L);
            if (CollectionUtils.isNotEmpty(orgListByLeader)) {
                orgListByLeader.forEach(org -> {
                    orgLines.add(String.format(HrConstant.NAME_CODE_FORMAT, org.getName(), org.getCode()));
                });

                String res = String.join(";", orgLines);
                ext.put("effectiveMsg.orgLineTxt", res);
            }
            if(!ext.isEmpty()){
                messageService.sendMsgNoticeEvent(msgNoticeConfig.getBid(), Lists.list(currentPost.getEmpId()), ext, "currnetPostLossEffective", 0);
            }
        }

        log.info("Part time job change superior msg|advance send msg datetime :{}", dateTime);
    }

    private void sendChangeDeptHeadMsg(MsgConfigDto msgNoticeConfig,String tenantId){
        int day = msgNoticeConfig.getDay();
        if(day <= 0){
            log.info("ERROR Day msgNoticeConfig:{}", FastjsonUtil.toJsonStr(msgNoticeConfig));
            return;
        }

        if(!"ADVANCE".equals(msgNoticeConfig.getRound())){
            log.info("Part time job change superior msg|no other configurations are currently supported, msgconfig bid:{}",
                    msgNoticeConfig.getBid());
            return;
        }

        Long dateTime = getDate(day + 1), beginTime = getDate(day);
        log.info("Part time job change dept head Msg|advance send msg datetime :{}", dateTime);
        List<EmpConcurrentPostDo> concurrentPostDos = empConcurrentPostService.selectListWilllostEffective(tenantId, dateTime, beginTime);
        if(CollectionUtils.isEmpty(concurrentPostDos)){
            log.info("There is no will loss effective data msgType:{}", NoticeType.PART_TIME_JOB_LOSS_EFFECTIVE_CHANGE_SUPERIOR);
            return;
        }

        for (EmpConcurrentPostDo currentPost : concurrentPostDos) {
            EmpReportLeaderDto empReportLeaderDto = new EmpReportLeaderDto();
            empReportLeaderDto.setEmpId(currentPost.getEmpId());
            empReportLeaderDto.setPostId(currentPost.getPost());
            empReportLeaderDto.setOrganizeId(currentPost.getOrganize());
            empReportLeaderDto.setDateTime(0L);
            // 组织
            List<OrgDo> orgListByLeader = orgService.getOrgListByLeader(empReportLeaderDto, -1L);
            Map<String,String> ext = new HashMap<String,String>();
            List<String> orgLines = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orgListByLeader)) {
                for (OrgDo org : orgListByLeader) {
                    orgLines.add(org.getName()+"("+org.getCode()+")");
                }
                String res = String.join(";", orgLines);
                ext.put("effectiveMsg.orgLineTxt", res);
            }
            if(!ext.isEmpty()){
                messageService.sendMsgNoticeEvent(msgNoticeConfig.getBid(), Lists.list(currentPost.getEmpId()), ext, "currnetPostLossEffective", 0);
            }
        }
    }
    private Long getZero(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTimeInMillis();
    }

    private Long getDate(Integer day){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar.getTimeInMillis();
    }

}
