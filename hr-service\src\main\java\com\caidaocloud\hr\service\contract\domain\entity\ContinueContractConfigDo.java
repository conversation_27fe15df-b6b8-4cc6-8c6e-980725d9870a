package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractConfigRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 合同续签配置
 *
 * <AUTHOR>
 * @date 2024/2/20
 **/
@Data
public class ContinueContractConfigDo extends BaseDomainDoImpl<ContinueContractConfigDo> {
    public final static String IDENTIFIER = "entity.hr.continue.Contract.Config";
    private Boolean useWorkflow;

    public ContinueContractConfigDo() {
        this.setIdentifier(IDENTIFIER);
    }

    @Override
    public BaseRepository<ContinueContractConfigDo> getRepository() {
        return SpringUtil.getBean(IContinueContractConfigRepository.class);
    }

    public void save() {
        getRepository().insert(this);
    }

    public void update() {
        if (StringUtils.isBlank(this.getBid())) {
            this.setBid(SnowUtil.nextId());
            save();
            return;
        }
        getRepository().updateById(this);
    }
}