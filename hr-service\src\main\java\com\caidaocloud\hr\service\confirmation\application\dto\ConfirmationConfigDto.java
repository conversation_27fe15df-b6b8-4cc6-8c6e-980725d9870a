package com.caidaocloud.hr.service.confirmation.application.dto;

import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.EnabledConfirmationPropInfo;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationAppliedBy;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConfirmationConfigDto {

    private String bid;

    private ConfirmationAppliedBy appliedBy;

    private String name;

    private String description;

    private List<MetadataPropertyVo> displayWorkInfos = Lists.list();

    @ApiModelProperty("薪资字段")
    private List<ConfirmationChangeFieldDef> salaryProps = Lists.list();

    @ApiModelProperty("任职信息字段")
    private List<ConfirmationChangeFieldDef> workProps = Lists.list();

    private String formDefId;

    private String processCode;

    private List<EnabledConfirmationPropInfo> enabledConfirmationProps = Lists.list();

}
