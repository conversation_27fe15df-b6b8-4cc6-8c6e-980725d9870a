package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.hr.service.common.application.dto.ContractRevokeDto;
import com.caidaocloud.hr.service.temination.application.dto.esign.ContractSignDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Component
public class EsignFeignClientFallback  implements EsignFeignClient{
	/**
	 * 查看员工签署文件
	 *
	 * @param empId
	 * @param businessProcesses
	 * @return
	 */
	@Override
	public Result<List<ContractSignDto>> getContractByEmp(String empId, String businessProcesses) {
		return Result.fail();
	}

	@Override
	public Result<Attachment> uploadByContractId(String openContractId) {
		return Result.fail();
	}

	@Override
	public Result<String> previewContract(String openContractId) {
		return Result.fail();
	}

	@Override
	public Result batchRevoke(ContractRevokeDto dto) {
		return Result.fail();
	}
}