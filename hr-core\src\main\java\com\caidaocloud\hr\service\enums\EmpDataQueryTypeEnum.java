package com.caidaocloud.hr.service.enums;

/**
 * <AUTHOR>
 */
public enum EmpDataQueryTypeEnum {
    EMP("EMP", "员工"),
    ORGANIZATIONAL_ROLE("ORGANIZATIONAL_ROLE", "组织角色"),
    EMP_LEADER_EMP("EMP_LEADER_EMP", "直接上级"),
    ORG_LEADER_EMP("ORG_LEADER_EMP","组织负责人"),
    RECRUITMENT("RECRUITMENT", "招聘顾问");

    private String code;
    private String name;

    EmpDataQueryTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
