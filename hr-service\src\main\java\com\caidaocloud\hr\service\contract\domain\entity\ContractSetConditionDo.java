package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.hr.service.contract.application.enums.*;
import com.caidaocloud.hr.service.contract.domain.repository.IContractSetConditionRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.ConditionTreeDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.enums.system.BaseEmpType;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhou
 * @date 2022/5/6
 */
@Data
@Accessors(chain = true)
@Service
public class ContractSetConditionDo extends BaseDomainDoImpl<ContractSetConditionDo> {
    /**
     * 合同设置Bid
     */
    String contractTypeSet;
    /**
     * 分组条件树
     */
    String conditionTree;
    /**
     * 匹配条件
     */
    String conditionLabel;
    /**
     * 匹配条件表达式
     */
    String conditionExp;
    /**
     * 合同期限类型
     */
    EnumSimple periodType;
    /**
     * 合同期限（月）
     */
    Integer contractPeriod;
    /**
     * 合同结束日期类型
     */
    EnumSimple endDateType;
    /**
     * 有无试用期
     */
    Boolean probation;
    /**
     * 试用期（月）
     */
    Integer probationPeriod;

    /**
     * 是否根据预计毕业日期判断
     */
    Boolean baseExpectGraduateDate;

    /**
     * 试用期期限
     */
    EnumSimple probationDeadline;


    /**
     * 是否根据退休判断
     */
    Boolean baseRetireDate;

    /**
     * 用于查询数据的字段，方便合同设置查询对应数据
     */
    List<String> contractTypeSetBids;

    public void deleteByTypeSetId(String typeSetBid) {
        List<ContractSetConditionDo> exist = contractSetConditionRepository.selectByTypeSetId(IDENTIFIER, typeSetBid);
        exist.forEach(contractSetConditionDo -> contractSetConditionRepository.delete(contractSetConditionDo));
    }

    public ContractSetConditionDo getByTypeSetBid(String typeSetBid) {
        List<ContractSetConditionDo> datas = contractSetConditionRepository.selectByTypeSetId(IDENTIFIER, typeSetBid);
        return CollectionUtils.isEmpty(datas) ? null : datas.get(0);
    }

    public Optional<ConditionTreeDto> getConditionTree(String typeSetBid) {
        ContractSetConditionDo conditionDo = getByTypeSetBid(typeSetBid);
        if (conditionDo == null) {
            return Optional.empty();
        }
        return Optional.ofNullable(FastjsonUtil.toObject(conditionDo.getConditionTree(), ConditionTreeDto.class));
    }

    public List<ContractSetConditionDo> selectByTypeSetIds(List<String> typeSetIds) {
        return contractSetConditionRepository.selectByTypeSetIds(IDENTIFIER, typeSetIds);
    }

    @Deprecated
    public Long calcEndDate(ContractSetConditionDo conditionDo, EmpWorkInfoDo workInfo, Long startDate) {
        // 无固定期限，设置结束时间为9999-12-31
        if (PeriodTypeEnum.NO_FIXED.getCode().equals(conditionDo.getPeriodType().getValue())) {
            return DateUtil.MAX_TIMESTAMP;
        }
        Long endDate = com.caidaocloud.util.DateUtil.timeCalculate(com.caidaocloud.util.DateUtil.formatDate(startDate), null, null, conditionDo.getContractPeriod(), -1);
        EndDateTypeEnum endDateType = EndDateTypeEnum.getByCode(conditionDo.getEndDateType().getValue());
        switch (endDateType) {
            case MONTH:
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(new Date(endDate));
                // 获得本月最后一天
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = calendar.getTime().getTime();
                break;
            case SEASON:
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(new Date(endDate));
                //计算季度数：由于月份从0开始，即1月份的Calendar.MONTH值为0,所以计算季度的第三个月份只需 月份 / 3 * 3 + 2
                endCalendar.add(Calendar.MONTH, (endCalendar.get(Calendar.MONTH) / 3) * 3 + 2 - endCalendar.get(Calendar.MONTH));
                endCalendar.set(Calendar.DAY_OF_MONTH, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = endCalendar.getTime().getTime();
                break;
            //caidao - 1959 合同结束日期 增加 年度最后有一天；
            //todo: 年度最后一天操作；
            case YEAR:
                //年度最后一天
                Calendar currCal = Calendar.getInstance();
                int currentYear = currCal.get(Calendar.YEAR);
                Calendar endYearcalendar = Calendar.getInstance();
                endYearcalendar.clear();
                endYearcalendar.set(Calendar.YEAR, currentYear);
                endYearcalendar.roll(Calendar.DAY_OF_YEAR, -1);
                long currYearLast = endYearcalendar.getTime().getTime();
                endDate = currYearLast;
                break;
            case DEFAULT:
            default:
        }
        if (Optional.ofNullable(conditionDo.getBaseExpectGraduateDate()).orElse(false)) {
            // 用工类型是实习生时，合同结束日期不能大于预计毕业日期
            if (workInfo != null && BaseEmpType.getInterns().stream().map(BaseEmpType::getCode).collect(Collectors.toList())
                    .contains(workInfo.getEmpType().getCode())) {
                return Math.min(workInfo.getExpectGraduateDate(), endDate);
            }
        }
        if (Optional.ofNullable(conditionDo.getBaseRetireDate()).orElse(false)) {
            // 校验退休日期
            if (workInfo != null && workInfo.getRetireDate() != null) {
                return Math.min(workInfo.getRetireDate(), endDate);
            }
        }
        return endDate;
    }

    @Data
    @NoArgsConstructor
    public static class Condition {
        private String name;

        private String code;

        private String serviceId;

        private String address;

        private ConditionCallType type;

        private String tenantId;

        private List<ConditionOperator> operators;
        private ValueComponent component;
        private List<ComponentValue> componentValueEnum;
        private String dataSourceAddress;
        private Map<String, String> dataSourceParams;

        public Condition(String name, String code, ConditionCallType type, List<ConditionOperator> operators, ValueComponent component, List<ComponentValue> componentValueEnum) {
            this.name = name;
            this.code = code;
            this.type = type;
            this.operators = operators;
            this.component = component;
            this.componentValueEnum = componentValueEnum;
        }

        public Condition(String name, String code, ConditionCallType type, List<ConditionOperator> operators, ValueComponent component, List<ComponentValue> componentValueEnum, Map<String, String> dataSourceParams) {
            this.name = name;
            this.code = code;
            this.type = type;
            this.operators = operators;
            this.component = component;
            this.componentValueEnum = componentValueEnum;
            this.dataSourceParams = dataSourceParams;
        }

        public Condition(String name, String code, ConditionCallType type, List<ConditionOperator> operators, ValueComponent component, List<ComponentValue> componentValueEnum, Map<String, String> dataSourceParams, String dataSourceAddress) {
            this.name = name;
            this.code = code;
            this.type = type;
            this.operators = operators;
            this.component = component;
            this.componentValueEnum = componentValueEnum;
            this.dataSourceParams = dataSourceParams;
            this.dataSourceAddress = dataSourceAddress;
        }
    }

    @Resource
    IContractSetConditionRepository contractSetConditionRepository;

    private final static String IDENTIFIER = "entity.hr.ContractSetCondition";

    @Override
    public BaseRepository<ContractSetConditionDo> getRepository() {
        return contractSetConditionRepository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }
}

