package com.caidaocloud.hr.service.util;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONTokener;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public abstract class EntityDataUtil {
    private final static String COMPONENT_TEXT_FORMAT = "%sText", COMPONENT_VALUE_FORMAT = "%sValue";

    public static  <T> T convertEntityData(EntityDataDto source, Class<T> clazz) {
        Map<String, Object> target = BeanUtil.obj2map(source);
        target.remove("properties");
        source.getProperties().stream().forEach(propertyDataDto -> {
            try {
                putIfNotExsit(target, propertyDataDto.getProperty(), propertyDataDto.getValue());
                putIfNotExsit(target, String.format(COMPONENT_TEXT_FORMAT, propertyDataDto.getProperty()), propertyDataDto.getValue());
                putIfNotExsit(target, String.format(COMPONENT_VALUE_FORMAT, propertyDataDto.getProperty()), propertyDataDto.getValue());
                putIfNotExsit(target, formatDictProperty(propertyDataDto.getProperty()), propertyDataDto.getValue());
            } catch (Exception e) {
                throw new ServerException("EntityData convert Exception",e);
            }

            if(propertyDataDto.getProperty().contains(".")){
                String [] props = propertyDataDto.getProperty().split("\\.");
                ((Map) target.computeIfAbsent(props[0], key -> new HashMap<>())).put(props[1], propertyDataDto.getDataType() == PropertyDataType.Attachment ? propertyDataDto.getArrayValues() : propertyDataDto.getValue());
                // if(target.containsKey(props[0])){
                //     ((Map) target.get(props[0])).put(props[1], propertyDataDto.getValue());
                // } else {
                //     target.put(props[0], createMapAndPutValue(props[0], props[1]));
                // }
            }
        });

        return JsonEnhanceUtil.toObject(target, clazz);
    }

    public static Map createMapAndPutValue(String key, String value){
        Map map = new HashMap<>();
        map.put(key, value);
        return map;
    }

    public static void putIfNotExsit(Map<String, Object> map, String key, String obj) {
        if(null != map.get(key)){
            return;
        }

        if(null == obj || "".equals(obj)){
            return;
        }

        try {
            Object json = new JSONTokener(obj).nextValue();;
            if(json instanceof org.springframework.boot.configurationprocessor.json.JSONObject || json instanceof JSONArray){
                map.put(key, json);
            }else {
                map.put(key, obj);
            }
        }
        catch (JSONException e) {
            log.error("parse json error,output as string,value:{}",obj);
            map.put(key, obj);
        }

    }

    public static String formatDictProperty(String pro){
        String [] splits = pro.split("\\.");
        String property = splits[0];
        for(int i = 1; i < splits.length; ++i){
            property += formatUpFirstCharStringWithDict(splits[i]);
        }
        return property;
    }

    public static String formatUpFirstCharStringWithDict(String str){
        if(str.toLowerCase().equals("dict")){
            return "";
        }
        return str.toUpperCase().charAt(0) + str.substring(1);
    }
}
