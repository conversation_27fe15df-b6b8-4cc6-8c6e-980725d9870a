package com.caidaocloud.hr.service.archive.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.util.Map;

/**
 * 文件归档查询
 * created by: FoAng
 * create time: 18/6/2024 3:08 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveQueryDto extends BasePage implements Serializable {

    @ApiModelProperty("所属业务线")
    private String[] businessLine;

    @ApiModelProperty("业务类型")
    private String[] businessType;

    @ApiModelProperty("附件名称")
    private String fileName;

    @ApiModelProperty("姓名、工号筛选")
    private String keyword;

    @ApiModelProperty("组织筛选")
    private String[] organize;

    @ApiModelProperty("员工状态")
    private String[] empStatus;

    @ApiModelProperty("岗位")
    private String[] post;

    @ApiModelProperty("员工类型")
    private String[] empType;

    @ApiModelProperty("合同公司")
    private String[] company;

    @ApiModelProperty("工作地")
    private String[] workplace;

    @ApiModelProperty("入职开始时间")
    private Long entryStart;

    @ApiModelProperty("入职结束时间")
    private Long entryEnd;

    @ApiModelProperty("离职开始时间")
    private Long terminateStart;

    @ApiModelProperty("离职结束时间")
    private Long terminateEnd;

    @ApiModelProperty("自定义字段")
    private Map<String, String> ext;

    @ApiModelProperty("文件名排序")
    private SortOrder prefix;
}
