package com.caidaocloud.hr.service.handler;

import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 26/6/2024 3:09 下午
 */
@Component
public class CommonResolveHandler implements ITypeResolveHandler {

    @Override
    public List<String> getTypes(String simpleValue) {
        return Optional.ofNullable(simpleValue).map(it -> Arrays.asList(it.split(","))).orElse(Lists.newArrayList());
    }
}

