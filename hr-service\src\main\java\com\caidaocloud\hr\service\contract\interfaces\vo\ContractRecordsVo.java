package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ContractRecordsVo {
    private String bid;

    @ApiModelProperty("合同签署人")
    private EmpSimple owner;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("任职组织")
    private String organizeTxt;

    @ApiModelProperty("职务")
    private String jobTxt;

    @ApiModelProperty("岗位")
    private String postTxt;

    @ApiModelProperty("用工类型")
    private DictSimple empType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("公司ID")
    private String company;

    @ApiModelProperty("合同公司")
    private String companyTxt;

    @ApiModelProperty("合同类型")
    private DictSimple contractType;

    @ApiModelProperty("签订类型")
    private EnumSimple signType;

    @ApiModelProperty("签订次数")
    private Integer signTime;

    @ApiModelProperty("合同开始日期")
    private Long startDate;

    @ApiModelProperty("合同结束日期")
    private Long endDate;

    @ApiModelProperty("合同终止日期")
    private Long terminationDate;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("合同状态")
    private EnumSimple contractStatus;

    @ApiModelProperty("审批状态")
    private EnumSimple approvalStatus;

    @ApiModelProperty("流程状态")
    private EnumSimple signProcessStatus;

    @ApiModelProperty("合同附件")
    private Attachment attachFile;

    @ApiModelProperty("合同类型设置名称")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;
    @ApiModelProperty("合同到期天数")
    private String contractDays;
    @ApiModelProperty("发起签署状态")
    private EnumSimple initiateStatus;
    @ApiModelProperty("合同发起日期")
    private Long launchDate;
    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;
    @ApiModelProperty(value = "流程业务key")
    private String businessKey;
    @ApiModelProperty("合同解除日期")
    private Long dissolveDate;

    @ApiModelProperty("合同类型")
    private DictSimple contractSettingType;

}
