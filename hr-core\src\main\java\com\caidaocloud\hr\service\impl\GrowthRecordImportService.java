package com.caidaocloud.hr.service.impl;

import com.caidaocloud.hr.service.common.constant.GrowthRecordMqContant;
import com.caidaocloud.hr.service.dto.growthrecord.GrowthRecordDto;
import com.caidaocloud.hr.service.enums.growthrecord.BusinessEventTypeEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class GrowthRecordImportService {
    public void insertSaveGrowthRecord(NestPropertyValue properties, BusinessEventTypeEnum type) {
        if (Objects.isNull(type)) {
            log.info("No growth record created, type={} properties={}", type, FastjsonUtil.toJson(properties));
            return;
        }
        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
        growthRecordDto.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        growthRecordDto.setEmpId(((SimplePropertyValue) properties.get("empId")).getValue());
        growthRecordDto.setBusinessEventType(type.toString());
        growthRecordDto.setEffectiveDate(type.getEffectiveDate(properties));
        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();

        //记录数据变化情况
        properties.entrySet().stream().forEach(entry -> {
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            String propKey = entry.getKey();
            dataItem.setProp(propKey);

            if (properties.get(propKey) instanceof SimplePropertyValue) {
                dataItem.setNewValue(((SimplePropertyValue) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof DictSimple) {
                dataItem.setNewValue(((DictSimple) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof EnumSimple) {
                dataItem.setNewValue(((EnumSimple) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof Address) {
                dataItem.setNewValue(((Address) properties.get(propKey)).doText());
            } else if (properties.get(propKey) instanceof EmpSimple) {
                dataItem.setNewValue(((EmpSimple) properties.get(propKey)).getEmpId());
            } else if (properties.get(propKey) instanceof JobGradeRange) {
                dataItem.setNewValue(((JobGradeRange) properties.get(propKey)).getStartGrade());
            } else if (properties.get(propKey) instanceof ComponentPropertyValue) {
                log.warn("propKey:{},propType:{},value:{}", propKey, properties.get(propKey).getClass(), properties.get(propKey));
            } else {
                log.warn("[其他类型]propKey:{},propType:{},value:{}", propKey, properties.get(propKey) == null ? null : properties.get(propKey).getClass(), properties.get(propKey));
            }

            dataList.add(dataItem);
        });
        growthRecordDto.setDataList(dataList);
        publish(growthRecordDto);
    }

    public void updateSaveGrowthRecord(NestPropertyValue properties, NestPropertyValue oldProperties,
                                       BusinessEventTypeEnum businessEventTypeEnum, Map<String, String> ext) {
        log.info("成长记录类型：{}", businessEventTypeEnum);
        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
        growthRecordDto.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        growthRecordDto.setEmpId(((SimplePropertyValue) properties.get("empId")).getValue());
        growthRecordDto.setBusinessEventType(businessEventTypeEnum.toString());
        growthRecordDto.setDataChangeTitle(businessEventTypeEnum.toValue());
        growthRecordDto.setEffectiveDate(DateUtil.getCurrentTimestamp());
        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();

        if (businessEventTypeEnum == BusinessEventTypeEnum.ONBOARDING) {
            //入职
            //入职日期
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            dataItem.setProp("hireDate");
            dataItem.setText("入职日期");
            String hireDate = ((SimplePropertyValue) properties.get("hireDate")).getValue();
            dataItem.setNewValue(hireDate);
            dataList.add(dataItem);

            growthRecordDto.setEffectiveDate(Long.valueOf(hireDate));

        } else if (businessEventTypeEnum == BusinessEventTypeEnum.TERMINATION) {
            //离职
            //离职类型 离职日期 离职原因
            GrowthRecordDto.DataItem dataItem01 = new GrowthRecordDto.DataItem();
            dataItem01.setProp("resignType");
            dataItem01.setText("离职类型");
            dataItem01.setNewValue(properties.get("resignType") != null ? ((DictSimple) properties.get("resignType")).getValue() : null);
            dataList.add(dataItem01);

            GrowthRecordDto.DataItem dataItem02 = new GrowthRecordDto.DataItem();
            dataItem02.setProp("leaveDate");
            dataItem02.setText("离职日期");

            String leaveDate = ((SimplePropertyValue) properties.get("leaveDate")).getValue();

            dataItem02.setNewValue(leaveDate);
            dataList.add(dataItem02);

            growthRecordDto.setEffectiveDate(Long.valueOf(leaveDate));

            GrowthRecordDto.DataItem dataItem03 = new GrowthRecordDto.DataItem();
            dataItem03.setProp("resignReason");
            dataItem03.setText("离职原因");
            dataItem03.setNewValue(properties.get("resignReason") != null ? ((DictSimple) properties.get("resignReason")).getValue() : null);
            dataList.add(dataItem03);

        } else {
            properties.entrySet().stream().forEach(entry -> {
                GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
                String propKey = entry.getKey();
                dataItem.setProp(propKey);
                PropertyValue pValue = properties.get(propKey);
                PropertyValue oValue = oldProperties.get(propKey);

                if (pValue instanceof SimplePropertyValue
                        && oValue instanceof SimplePropertyValue) {
                    if (null != oValue) {
                        dataItem.setValue(((SimplePropertyValue) oValue).getValue());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((SimplePropertyValue) pValue).getValue());
                    }
                } else if (pValue instanceof DictSimple
                        && oValue instanceof DictSimple) {
                    if (null != oValue) {
                        dataItem.setValue(((DictSimple) oValue).getValue());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((DictSimple) pValue).getValue());
                    }
                } else if (pValue instanceof EnumSimple
                        && oValue instanceof EnumSimple) {
                    if (null != oValue) {
                        dataItem.setValue(((EnumSimple) oValue).getValue());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((EnumSimple) pValue).getValue());
                    }
                } else if (pValue instanceof Address
                        && oValue instanceof Address) {
                    if (null != oValue) {
                        dataItem.setValue(((Address) oValue).doText());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((Address) pValue).doText());
                    }
                } else if (pValue instanceof EmpSimple
                        && oValue instanceof EmpSimple) {
                    if (null != oValue) {
                        dataItem.setValue(((EmpSimple) oValue).getEmpId());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((EmpSimple) pValue).getEmpId());
                    }
                } else if (pValue instanceof JobGradeRange
                        && oValue instanceof JobGradeRange) {
                    if (null != oValue) {
                        dataItem.setValue(((JobGradeRange) oValue).getStartGrade());
                    }
                    if (null != pValue) {
                        dataItem.setNewValue(((JobGradeRange) pValue).getStartGrade());
                    }
                } else if (pValue instanceof ComponentPropertyValue
                        || oValue instanceof ComponentPropertyValue) {
                    log.warn("propKey:{},propType:{},value:{}", propKey, pValue.getClass(), pValue);
                } else {
                    log.warn("[other type component]propKey:{},value:{}", propKey, pValue);
                }
                dataList.add(dataItem);
            });
        }

        //员工外字段特殊处理；
        if (businessEventTypeEnum == BusinessEventTypeEnum.CONFIRMATION) {
            //转正类型 confirmationType
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            dataItem.setProp("confirmationType");
            dataItem.setText("转正类型");
            dataItem.setNewValue(ext.get("confirmationType"));
            dataList.add(dataItem);

            String confirmationDate = ((SimplePropertyValue) properties.get("confirmationDate")).getValue();
            growthRecordDto.setEffectiveDate(Long.valueOf(confirmationDate));

        } else if (businessEventTypeEnum == BusinessEventTypeEnum.TRANSFER) {
            //生效日期 effectiveDate
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            dataItem.setProp("effectiveDate");
            dataItem.setText("生效日期");
            dataItem.setNewValue(DateUtil.formatDate(Long.valueOf(ext.get("effectiveDate"))));
            dataList.add(dataItem);

            if (StringUtil.isNotEmpty(ext.get("effectiveDate"))) {
                growthRecordDto.setEffectiveDate(Long.valueOf(ext.get("effectiveDate")));
            }
        } else if (businessEventTypeEnum == BusinessEventTypeEnum.IMPORT || businessEventTypeEnum == BusinessEventTypeEnum.EDIT) {
            growthRecordDto.setEffectiveDate(Long.valueOf(ext.get("dataStartTime")));
        } 
        growthRecordDto.setDataList(dataList);
        log.info("成长记录dataList：{}", dataList);
        publish(growthRecordDto);
    }

    private void publish(GrowthRecordDto dto) {
        if (dto == null) {
            log.warn("parameter is empty");
            return;
        }
        var message = new RabbitBaseMessage();
        message.setExchange(GrowthRecordMqContant.GROWTH_RECORD_EXCHANGE);
        message.setRoutingKey(GrowthRecordMqContant.GROWTH_RECORD_ROUTING_KEY);
        message.setBody(FastjsonUtil.toJson(dto));
        SpringUtil.getBean(MqMessageProducer.class).publish(message);
        log.info("send GrowthRecord event, parameter={}", FastjsonUtil.toJson(message));
    }
}
