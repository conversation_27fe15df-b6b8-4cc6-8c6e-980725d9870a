package com.caidaocloud.hr.service.archive.infrastructure.provider;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * created by: FoAng
 * create time: 6/6/2024 10:26 上午
 */
@Slf4j
@Component
public class DynamicIndexProvider {

    public String getTenantIndex(String prefix) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        PreCheck.preCheckArgument(userInfo == null || StringUtil.isEmpty(userInfo.getTenantId()),
                "can't fetch session user info");
        String tenantId = userInfo.getTenantId();
        return String.format("%s_%s", prefix, tenantId);
    }

}
