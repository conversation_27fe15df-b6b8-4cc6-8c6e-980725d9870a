package com.caidaocloud.hr.service.common.infrastructure.config;

import com.caidaocloud.hr.service.archive.annotation.DynamicIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.annotation.Order;
import org.zxp.esclientrhl.annotation.ESMetaData;
import org.zxp.esclientrhl.auto.util.EnableESTools;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.util.MetaData;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Configuration
@Order(Integer.MAX_VALUE)
public class EsCreateIndex implements SmartInitializingSingleton, ApplicationContextAware {
    @Autowired
    private ElasticsearchIndex elasticsearchIndex;
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 扫描ESMetaData注解的类，并自动创建索引mapping
     *
     * @param event
     */
    @Override
    public void afterSingletonsInstantiated() {
        Map<String, Object> beansWithAnnotationMap = this.applicationContext.getBeansWithAnnotation(ESMetaData.class);
        log.info("扫描到@ESMetaData注解bean个数：{}", beansWithAnnotationMap.size());
        beansWithAnnotationMap.forEach((beanName, bean) -> {
                    try {
                        DynamicIndex dynamicIndex = bean.getClass().getAnnotation(DynamicIndex.class);
                        if (Objects.nonNull(dynamicIndex)) {
                            return;
                        }
                        MetaData metaData = elasticsearchIndex.getMetaData(bean.getClass());
                        if (metaData.isAutoCreateIndex()) {//配置自动创建索引
                            if (metaData.isAlias()) {//当配置了别名后自动创建索引功能将失效
                                elasticsearchIndex.createAlias(bean.getClass());
                            } else if (!elasticsearchIndex.exists(bean.getClass())) {
                                elasticsearchIndex.createIndex(bean.getClass());
                                if (EnableESTools.isPrintregmsg()) {
                                    log.info("创建索引成功，索引名称：" + metaData.getIndexname() + "索引类型：" + metaData.getIndextype());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("创建索引不成功", e);
                    }
                }
        );
    }
}
