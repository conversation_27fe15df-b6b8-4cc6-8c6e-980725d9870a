package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueLetterRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ContinueLetterDomainService extends BaseDomainServiceImpl<ContinueLetterDo, BasePage> {
	@Resource
	private ContinueLetterDo continueLetterDo;
	@Resource
	private ContractTypeSetDomainService contractTypeSetDomainService;
	@Resource
	private IContinueLetterRepository continueLetterRepository;


	@Override
	public BaseDomainDo getDoService() {
		return continueLetterDo;
	}


	public String createContinueLetter(ContractDo contract, ContinueContractTemplateDto template, Attachment attachment) {
		ContinueLetterDo continueLetter = new ContinueLetterDo();
		BeanUtils.copyProperties(contract,continueLetter,"id", "bid", "createBy",
				"createTime", "updateBy", "updateTime", "dataStartTime");
		BeanUtils.copyProperties(template,continueLetter,"id", "bid", "createBy",
				"createTime", "updateBy", "updateTime", "dataStartTime");
		continueLetter.setTemplateId(template.getBid());
		continueLetter.setContractId(contract.getBid());
		if (attachment!=null) {
			continueLetter.setAttachment(attachment);
		}
		continueLetter.save();
		return continueLetter.getBid();
	}

	public ContinueLetterDo confirm(String bid, String feedback) {
		ContinueLetterDo letter = getById(bid);
		if (letter == null) {
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
		}
		DictSimple dictSimple = new DictSimple();
		dictSimple.setValue(feedback);
		letter.setFeedback(dictSimple);
		letter.getContinueStatus().setValue(ContinueStatus.CONFIRMED.name());
		letter.setConfirmedTime(System.currentTimeMillis());
		updateById(letter);
		return letter;
	}

	public List<ContinueLetterDo> selectList(Long empId, ContinueStatus status) {
		ContinueLetterDo data = new ContinueLetterDo();
		data.setIdentifier(data.getDoIdentifier());
		data.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());

		EmpSimple empSimple = new EmpSimple();
		empSimple.setEmpId(String.valueOf(empId));
		data.setOwner(empSimple);

		EnumSimple enumSimple = new EnumSimple();
		enumSimple.setValue(status.name());
		data.setContinueStatus(enumSimple);

		return continueLetterRepository.selectList(data);
	}

	@Override
	public ContinueLetterDo getOne(ContinueLetterDo data) {
		return continueLetterRepository.selectOne(data);
	}

	/**
	 * 获取归档文件
	 * @param page
	 * @return
	 */
	public List<ContinueLetterDo> getArchiveData(BasePage page) {
		return continueLetterRepository.getArchiveData(continueLetterDo.getDoIdentifier(), page);
	}
}
