package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import lombok.Data;

/**
 * 合同解除与终止
 */
@Data
public class ContractChangeDto {
    // 合同id
    private String bid;
    // 生效日期
    private Long effectiveDate;
    // 是否开启工作流
    private Boolean openWorkflow;
    // 解除或终止原因
    private String reason;
    // 备注
    private String desc;
    // 是否是合同解除
    private boolean dissolve = false;
    private String remark;
    private Long terminationDate;
    private String terminationReason;
    /**
     * 合同解除日期
     */
    private Long dissolveDate;

    /**
     * 合同解除原因
     */
    private String dissolveReason;
}
