package com.caidaocloud.hr.service.config;

import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.archive.ArchiveMessage;
import com.caidaocloud.hr.service.archive.ArchiveProvider;
import com.caidaocloud.hr.service.archive.IArchiveProcessor;
import com.caidaocloud.hr.service.archive.anonation.EnableArchiveFile;
import com.caidaocloud.hr.service.archive.impl.DefaultArchiveProcessor;
import com.caidaocloud.hr.service.archive.properties.ArchiveProperty;
import com.caidaocloud.mq.rabbitmq.MqAutoConfiguration;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportAware;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 4/6/2024 4:31 下午
 */
@Configuration
@EnableConfigurationProperties(ArchiveProperty.class)
@Import(MqAutoConfiguration.class)
public class ArchiveAutoConfiguration implements ImportAware {

    private AnnotationMetadata annotationMetadata;

    @Bean
    @ConditionalOnMissingBean(ArchiveProvider.class)
    public ArchiveProvider archiveProvider() {
        return new ArchiveProvider();
    }

    @Bean
    @ConditionalOnMissingBean(IArchiveProcessor.class)
    public IArchiveProcessor archiveProcessor() {
        return new DefaultArchiveProcessor();
    }

    @Bean
    @ConditionalOnMissingBean(ArchiveEventProducer.class)
    public ArchiveEventProducer archiveEventProducer(@Autowired MqMessageProducer<ArchiveMessage> messageProducer, @Autowired ArchiveProperty archiveProperty) {
        return new ArchiveEventProducer(getModuleName(), messageProducer, archiveProperty);
    }

    public String getModuleName() {
        return Optional.ofNullable(annotationMetadata).map(it -> {
            AnnotationAttributes attributes =  AnnotationAttributes.fromMap(
                    annotationMetadata.getAnnotationAttributes(EnableArchiveFile.class.getName()));
            return attributes != null ? attributes.get("module").toString() : null;
        }).orElse("");
    }

    @Override
    public void setImportMetadata(@NotNull AnnotationMetadata annotationMetadata) {
        this.annotationMetadata = annotationMetadata;
    }
}
