package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工学历概况DTO")
public class EmpEduInfoDto {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("学位")
    private String degree;

    @ApiModelProperty("最高学历")
    private String background;

    @ApiModelProperty("毕业学校")
    private String school;

    @ApiModelProperty("专业")
    private String major;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
