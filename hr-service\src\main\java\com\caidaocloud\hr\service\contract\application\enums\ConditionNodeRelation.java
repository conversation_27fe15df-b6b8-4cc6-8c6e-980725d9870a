package com.caidaocloud.hr.service.contract.application.enums;

/**
 * <AUTHOR>
 */
public enum ConditionNodeRelation {
    and("and", "并且"), or("or", "或");

    private String code;
    private String name;

    ConditionNodeRelation(){}

    ConditionNodeRelation(String code){
        this.code = code;
    }

    ConditionNodeRelation(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public String getCode(){
        return code;
    }
}
