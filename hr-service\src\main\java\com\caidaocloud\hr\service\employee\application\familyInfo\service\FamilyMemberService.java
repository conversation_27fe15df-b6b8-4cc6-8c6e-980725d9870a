package com.caidaocloud.hr.service.employee.application.familyInfo.service;

import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.familyInfo.entity.FamilyMemberDo;
import com.caidaocloud.hr.service.employee.domain.familyInfo.service.FamilyMemberDomainService;
import com.caidaocloud.hr.service.dto.FamilyMemberDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FamilyMemberService {
    @Resource
    private FamilyMemberDomainService familyMemberDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public String save(FamilyMemberDto dto) {
        FamilyMemberDo data = ObjectConverter.convert(dto, FamilyMemberDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return StringUtils.isNotEmpty(data.getBid()) ? familyMemberDomainService.update(data) : familyMemberDomainService.save(data);
    }

    public List<FamilyMemberDo> selectList(String empId) {
        return familyMemberDomainService.selectList(empId);
    }

    public FamilyMemberDo selectById(String bid) {
        return familyMemberDomainService.selectById(bid);
    }

    public int delete(String bid) {
        return familyMemberDomainService.delete(bid);
    }

    public void doConvert(FamilyMemberDto dto, FamilyMemberDo target) {
        if (StringUtils.isNotEmpty(dto.getRelation())) {
            EnumSimple simple = new EnumSimple();
            simple.setValue(dto.getRelation());
            target.setRelation(simple);
        }
    }
}
