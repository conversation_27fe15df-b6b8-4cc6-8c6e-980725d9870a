package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工个人紧急联系人信息DTO")
public class EmpEmergencyContactDto {
    @ApiModelProperty("银行卡BID")
    private String bid;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("紧急联系人姓名")
    private String name;
    @ApiModelProperty("关系")
    private String relationType;
    @ApiModelProperty("联系方式")
    private String contactWay;
    @ApiModelProperty("联系人地址")
    private String contactAddress;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
