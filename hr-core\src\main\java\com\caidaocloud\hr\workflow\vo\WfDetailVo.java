package com.caidaocloud.hr.workflow.vo;

import com.caidaocloud.hr.workflow.dto.WfAttachmentDto;
import com.caidaocloud.hr.workflow.dto.WfDetailFieldDefDto;
import com.caidaocloud.hr.workflow.enums.IWfField;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.hr.workflow.enums.WfFieldEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流详情VO
 *
 * <AUTHOR>
 * @Date 2022/12/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("工作流详情VO")
public class WfDetailVo {

    @ApiModelProperty("字段详情")
    private List<WfDetailFieldDefDto> detailList;

    public static List<WfDetailFieldDefDto> convertWfDetail(Map<String, Object> objectMap,
        List<String> allFieldKeys, IWfField [] values){
        List<WfDetailFieldDefDto> wfDetailDtoList = objectMap.keySet().stream()
                .filter(allFieldKeys::contains)
                .sorted(Comparator.comparingInt(allFieldKeys::indexOf))
                .map(it -> convertWfItem(it, values, objectMap)).collect(Collectors.toList());
        return wfDetailDtoList;
    }

    @SneakyThrows
    public static WfDetailFieldDefDto convertWfItem(String it, IWfField [] values, Map<String, Object> objectMap){
        WfDetailFieldDefDto itemDto = new WfDetailFieldDefDto();
        IWfField typeEnum = WfFieldEnum.getTypeByKey(it, values);
        itemDto.setKey(it);
        itemDto.setText(typeEnum.getText());
        itemDto.setType(typeEnum.getType());
        Class<?> typeCls = typeEnum.getClass();
        if (typeCls == String.class) {
            String value = (String) objectMap.get(it);
            itemDto.setValue(value);
        } else if (typeCls == EmpSimple.class) {
            itemDto.setKey(it);
            Field field = typeCls.getDeclaredField(typeEnum.getKey().split("\\$")[1]);
            field.setAccessible(true);
            EmpSimple empSimple = FastjsonUtil.toObject(FastjsonUtil.toJson(objectMap.get(it)),
                    EmpSimple.class);
            String value = (String) field.get(empSimple);
            itemDto.setValue(value);
        } else if (typeCls == DictSimple.class) {
            DictSimple dictSimple = FastjsonUtil.toObject(FastjsonUtil.toJson(objectMap.get(it)),
                    DictSimple.class);
            itemDto.setValue(dictSimple.getText());
        } else if (typeCls == EnumSimple.class) {
            EnumSimple enumSimple = FastjsonUtil.toObject(FastjsonUtil.toJson(objectMap.get(it)),
                    EnumSimple.class);
            itemDto.setValue(enumSimple.getText());
        } else if (typeCls == Attachment.class) {
            Attachment attachment = FastjsonUtil.toObject(FastjsonUtil.toJson(objectMap.get(it)),
                    Attachment.class);
            List<WfAttachmentDto> attachmentList = Lists.newArrayList();
            List<String> fileUrls = attachment.getUrls();
            if (CollectionUtils.isNotEmpty(fileUrls)) {
                attachment.getNames().forEach(o1 -> {
                    int index = attachment.getNames().indexOf(o1);
                    attachmentList.add(new WfAttachmentDto(WfAttachmentTypeEnum.file, fileUrls.get(index), o1));
                });
            }
            itemDto.setValue(attachmentList);
        } else {
            String value = (String) objectMap.get(it);
            itemDto.setValue(value);
        }
        return itemDto;
    }
}