package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
@ApiModel("员工合同VO")
public class ContractVo {
    @ApiModelProperty("合同ID")
    private String bid;

    @ApiModelProperty("合同签署人")
    private EmpSimple owner;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("所属组织Id")
    private String organize;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("所属组织编码")
    private String organizeCode;

    @ApiModelProperty("关联的职务ID")
    private String job;

    @ApiModelProperty("关联的职务名称")
    private String jobTxt;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("员工类型")
    private DictSimple empType;

    @ApiModelProperty("签订类型")
    private EnumSimple signType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同公司Id")
    private String company;

    @ApiModelProperty("所属公司名称")
    private String companyTxt;

    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;

    @ApiModelProperty("合同设置名称（合同类型名称/合同名称）")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同类型")
    private DictSimple contractSettingType;

    @ApiModelProperty("合同类别")
    private DictSimple contractType;

    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;

    @ApiModelProperty("合同签订日期")
    private Long signDate;

    @ApiModelProperty("合同开始日期")
    private Long startDate;

    @ApiModelProperty("合同结束日期")
    private Long endDate;

    @ApiModelProperty("合同签订次数")
    private Integer signTime;

    @ApiModelProperty("状态")
    private EnumSimple contractStatus;

    @ApiModelProperty("审批状态")
    private EnumSimple approvalStatus;

    @ApiModelProperty("合同终止日期")
    private Long terminationDate;

    @ApiModelProperty("合同终止原因")
    private String terminationReason;

    @ApiModelProperty("合同附件")
    private Attachment attachFile;

    @ApiModelProperty("工作地ID")
    private String workplace;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("工时制")
    private EnumSimple workHour;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("签署流程状态")
    private EnumSimple signProcessStatus;
    @ApiModelProperty("合同到期天数")
    private String contractDays;
    @ApiModelProperty("合同发起日期")
    private Long launchDate;
    @ApiModelProperty("注册地址")
    private String registerAddress;
    @ApiModelProperty("试用期期限")
    private EnumSimple probation;
    @ApiModelProperty("合同期（年）")
    private BigDecimal contractYear;
    @ApiModelProperty("发起签署状态")
    private EnumSimple initiateStatus;
    @ApiModelProperty("合同解除日期")
    private Long dissolveDate;
    @ApiModelProperty("合同解除原因")
    private String dissolveReason;

    @ApiModelProperty("是否在审批记录显示")
    private EnumSimple isHideInApproval;

    @ApiModelProperty("意向反馈建议")
    private DictSimple feedback;

    @ApiModelProperty("上一份合同ID")
    private String lastContract;

    @ApiModelProperty("处理状态")
    private Boolean handled;

    public void changeStatus(long lastDay, Map<String, String> statusMap) {
        EnumSimple cs = this.getContractStatus();
        if (null == cs || null == cs.getValue()) {
            return;
        }

        convertContractStatus();

        if (lastDay < 0 && !ContractStatusEnum.EFFECTIVE.getIndex().equals(cs.getValue())) {
            String index = ContractStatusEnum.INVALID.getIndex(), textKey = ContractStatusEnum.INVALID.getName();
            cs.setValue(index);
            String text = cs.getText();
            if (statusMap.containsKey(textKey)) {
                text = statusMap.get(textKey);
            } else {
                text = LangUtil.getMsg(Integer.valueOf(textKey));
                statusMap.put(textKey, text);
            }
            cs.setText(text);
            this.setContractStatus(lastDay < 0 ? cs : cs);
        }
    }

    public void convertContractStatus() {
        long nowTime = DateUtil.getCurrentTimestamp();
        // 如果是生效中的合同，但是当天零点日期已经大于合同结束日期，则显示合同已失效
        if (ContractStatusEnum.EFFECTIVE.getIndex().equals(contractStatus.getValue()) && null != endDate && nowTime > endDate) {
            String index = ContractStatusEnum.INVALID.getIndex(), textKey = ContractStatusEnum.INVALID.getName();
            contractStatus.setValue(index);
            contractStatus.setText(LangUtil.getMsg(Integer.valueOf(textKey)));
            return;
        }

        nowTime = nowTime + 24 * 60 * 60 * 1000;
        // 如果是已终止的合同，但是合同终止日期还未到，则显示生效中；
        boolean effective = ContractStatusEnum.TERMINATED.getIndex().equals(contractStatus.getValue()) && null != terminationDate && nowTime < terminationDate;
        effective = effective || (ContractStatusEnum.RELEASED.getIndex().equals(contractStatus.getValue()) && null != dissolveDate && nowTime < dissolveDate);
        // 或者如果是已解除，且解除日期还为到，则显示生效中
        if (effective) {
            String index = ContractStatusEnum.EFFECTIVE.getIndex(), textKey = ContractStatusEnum.EFFECTIVE.getName();
            contractStatus.setValue(index);
            contractStatus.setText(LangUtil.getMsg(Integer.valueOf(textKey)));
            return;
        }

        String langName = ContractStatusEnum.getName(contractStatus.getValue());
        contractStatus.setText(null == langName ? contractStatus.getText() : langName);
    }

    public void convertEmpStatus(boolean isZh){
        if(null == empStatus || StringUtil.isEmpty(empStatus.getValue())){
            return;
        }

        String langEmpStatus = EmpStatusEnum.getLangeName(Integer.valueOf(empStatus.getValue()), isZh);
        empStatus.setText(null == langEmpStatus ? empStatus.getText() : langEmpStatus);
    }

    public void convertContractSettingType(boolean isZh, Map<String, String> dictMap){
        if(isZh || null == contractSettingType || StringUtil.isEmpty(contractSettingType.getValue())){
            return;
        }

        if(dictMap.containsKey(contractSettingType.getValue())){
            contractSettingType.setText(dictMap.get(contractSettingType.getValue()));
            return;
        }

        String langText = SpringUtil.getBean(DictService.class).getLangText(contractSettingType.getValue(), contractSettingType.getText(), isZh);
        if(StringUtil.isEmpty(langText)){
            return;
        }
        contractSettingType.setText(langText);
        dictMap.put(contractSettingType.getValue(), langText);
    }

    public void loadCompanyTxt(){
        CompanyDo i18nCompany = SpringUtil.getBean(CompanyDomainService.class)
                .loadCompanyLang(getCompany());
        if(null == i18nCompany || StringUtil.isEmpty(i18nCompany.getBid())){
            return;
        }
        this.setCompanyTxt(i18nCompany.getCompanyName());
    }
}
