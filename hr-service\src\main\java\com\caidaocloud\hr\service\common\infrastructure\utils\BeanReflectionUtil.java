package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.google.common.collect.Sets;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class BeanReflectionUtil {
    /**
     * 获取类（包括父类）的所有JavaBean属性名
     *
     * @param clazz 要分析的类
     * @return JavaBean属性名列表
     */
    public static List<Field> getBeanPropertyField(Class<?> clazz) {
        List<Field> propertyNames = new ArrayList<>();
        Set<String> existed = Sets.newHashSet();
        getBeanPropertyNamesRecursively(clazz, propertyNames, existed);
        return propertyNames;
    }

    /**
     * 递归获取类的JavaBean属性名
     *
     * @param clazz         要分析的类
     * @param property 属性名列表
     */
    private static void getBeanPropertyNamesRecursively(Class<?> clazz, List<Field> property, Set<String> existed) {
        // 如果到达Object类或者类为null，则停止递归
        if (clazz == null || clazz == Object.class) {
            return;
        }

        // 获取类的所有声明字段
        Field[] fields = clazz.getDeclaredFields();
        Method[] methods = clazz.getDeclaredMethods();

        // 遍历所有字段
        for (Field field : fields) {
            String fieldName = field.getName();
            // 构造getter方法名
            String getterName = "get" + capitalize(fieldName);
            String booleanGetterName = "is" + capitalize(fieldName);
            // 构造setter方法名
            String setterName = "set" + capitalize(fieldName);

            // 检查是否存在对应的getter和setter方法
            boolean hasGetter = hasMethod(methods, getterName) || hasMethod(methods, booleanGetterName);
            boolean hasSetter = hasMethod(methods, setterName);

            // 如果同时具有getter和setter方法，则认为是JavaBean属性
            if (hasGetter && hasSetter) {
                if(existed.contains(fieldName)) {
                    continue;
                }
                property.add(field);
                existed.add(fieldName);
            }
        }

        // 递归处理父类
        getBeanPropertyNamesRecursively(clazz.getSuperclass(), property, existed);
    }

    /**
     * 首字母大写
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 检查方法是否存在
     */
    private static boolean hasMethod(Method[] methods, String methodName) {
        return Arrays.stream(methods)
                .anyMatch(method -> method.getName().equals(methodName));
    }
}
