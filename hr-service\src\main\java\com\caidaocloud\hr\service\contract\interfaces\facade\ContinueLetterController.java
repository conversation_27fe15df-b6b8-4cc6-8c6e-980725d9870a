package com.caidaocloud.hr.service.contract.interfaces.facade;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.contract.application.service.ContinueLetterService;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueLetterConfirmDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueLetterCreateDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueLetterDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueTemplatePropertyDefDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContinueLetterVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContinueSignContractVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContinueTemplatePropertyDefVo;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.LanguageUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Zhou
 * @date 2023/3/19
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/continue/v1")
@Api(value = "/api/hr/continue/v1", description = "合同管理", tags = "v1.7")
public class ContinueLetterController {
	@Autowired
	private ContinueLetterService continueLetterService;

	@PostMapping("create")
	@ApiOperation("发起续签意向书")
	public Result createContinueLetter(@RequestBody ContinueLetterCreateDto dto) {
		continueLetterService.createContinueLetter(dto);
		return Result.ok(true);
	}

	@PutMapping("confirm")
	@ApiOperation("续签意向书确认")
	public Result confirm(@RequestBody ContinueLetterConfirmDto dto) {
		continueLetterService.confirm(dto);
		return Result.ok(true);
	}

	@GetMapping("def")
	@ApiOperation("续签意向书定义")
	public Result<ContinueTemplatePropertyDefVo> getLetterPropertyDef(@RequestParam("bid") String bid) {
		ContinueTemplatePropertyDefDto dto = continueLetterService.getPropertyDef(bid);
		ContinueTemplatePropertyDefVo vo = ObjectConverter.convert(dto, ContinueTemplatePropertyDefVo.class);
		vo.setName(LanguageUtil.getCurrentLangVal(dto.getI18nName()));
		return Result.ok(vo);
	}

	@GetMapping("detail")
	@ApiOperation("续签意向书详情")
	public Result<ContinueSignContractVo> getDetail(@RequestParam("bid") String bid) {
		ContinueLetterDo data = continueLetterService.getById(bid);
		ContinueSignContractVo vo = ObjectConverter.convert(data, ContinueSignContractVo.class);
		vo.setName(String.format("%s(%s)", data.getOwner().getName(), data.getOwner().getWorkno()));
		boolean isZh = LangUtil.chineseLocale();
		long nowDateTime = DateUtil.getCurrentTimestamp();
		final Map<String, String> dictMap = new HashMap<>();
		if (vo.getEndDate() != null) {
			long contractDay = TimeUnit.MILLISECONDS.toDays(vo.getEndDate() - nowDateTime);
			// 确认后，合同到期天数不小于0
			if (ContinueStatus.CONFIRMED.name().equals(data.getContinueStatus().getValue())) {
				contractDay = Math.max(contractDay, 0);
			}
			vo.changeStatus(contractDay, new HashMap<>());
			vo.setContractDays(String.valueOf(contractDay));
		}
		vo.convertEmpStatus(isZh);
		vo.convertContinueApprovalStatus(isZh);
		vo.convertContractSettingType(isZh, dictMap);
		vo.loadCompanyTxt();
		return Result.ok(vo);
	}

	@GetMapping("list/todo")
	@ApiOperation("待处理列表")
	public Result<List<ContinueLetterVo>> getInitiatedList() {
		Long empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
		List<ContinueLetterDto> list = continueLetterService.getList(empId, ContinueStatus.INITIATED);
		return Result.ok(ObjectConverter.convertList(list, ContinueLetterVo.class));
	}

	@GetMapping("list/done")
	@ApiOperation("已处理列表")
	public Result<List<ContinueLetterVo>> getDoneList() {
		Long empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
		List<ContinueLetterDto> list = continueLetterService.getList(empId, ContinueStatus.CONFIRMED);
		return Result.ok(ObjectConverter.convertList(list, ContinueLetterVo.class));
	}
}