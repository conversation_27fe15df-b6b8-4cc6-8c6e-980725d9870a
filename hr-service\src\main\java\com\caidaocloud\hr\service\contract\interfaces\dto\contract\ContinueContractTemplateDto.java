package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractTemplateDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("续签意向模版DTO")
public class ContinueContractTemplateDto extends DataSimple {
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nName = Maps.newHashMap();
    @ApiModelProperty("模版说明国际化")
    private Map<String, String> i18nDesc = Maps.newHashMap();
    @ApiModelProperty("条件")
    private ConditionTree condition;
    @ApiModelProperty("页面提示说明")
    private String tips;
    @ApiModelProperty("附件")
    private Attachment attachment;
    @ApiModelProperty("合同信息字段")
    private List<MetadataPropertyDto> contractFields;
    @ApiModelProperty("员工续签意向")
    private List<String> feedbacks;

    public ContinueContractTemplateDo convertDo() {
        return FastjsonUtil.convertObject(this, ContinueContractTemplateDo.class);
    }
}