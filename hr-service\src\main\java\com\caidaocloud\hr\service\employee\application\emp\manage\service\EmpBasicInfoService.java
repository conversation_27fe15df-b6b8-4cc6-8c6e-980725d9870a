package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.DataConvertUtil;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractVo;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.dto.EmpFileAttachmentDto;
import com.caidaocloud.hr.service.dto.EmpPrivateInfoDto;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.dto.ruleset.EmpDepartRuleDto;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpDynamicService;
import com.caidaocloud.hr.service.employee.application.workOverview.service.WorkOverviewService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpBasicInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpConcurrentPostDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpInfoDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpALlInfoVo;
import com.caidaocloud.hr.service.enums.system.CardTypeEnum;
import com.caidaocloud.hr.service.organization.application.emp.service.EmpDepartRuleService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.temination.application.TerminationChangeService;
import com.caidaocloud.hr.service.util.IdCardUtil;
import com.caidaocloud.hr.service.vo.*;
import com.caidaocloud.hr.service.workflow.domain.entity.WorkflowApproverSetting;
import com.caidaocloud.hr.service.workflow.domain.entity.WorkflowApproverSettingDetail;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpBasicInfoService {
    private static SnowflakeUtil snowFlake = new SnowflakeUtil(1L, 1L);
    @Resource
    private EmpBasicInfoDomainService empBasicInfoDomainService;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpFileAttachmentService empFileAttachmentService;
    @Resource
    private EmpPrivateInfoService empPrivateInfoService;
    @Resource
    private EmpDepartRuleService empDepartRuleService;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private EmpConcurrentPostDomainService empConcurrentPostDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private WorkOverviewService workOverviewService;
    @Resource
    private ContractService contractService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private Locker locker;
    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    /**
     * 新增员工
     */
    public String save(EmpInfoDto empInfoDto) {
        val securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        PreCheck.preCheckArgument(Objects.isNull(securityUserInfo), "please login user");
        val lockKey = String.format("%s%s", securityUserInfo.getUserId(), empInfoDto.hashCode());
        val lock = locker.getLock(lockKey);
        val isGetLock = lock.tryLock();
        if (!isGetLock) {
            log.info("not get lock, lockKey={} data={}", lockKey, FastjsonUtil.toJson(empInfoDto));
            return "";
        }
        try {
            String empId = String.valueOf(snowFlake.createId());
            // 个人信息
            EmpPrivateInfoDto empPrivateInfoDto = empInfoDto.getEmpPrivateInfo();
            empPrivateInfoDto.setEmpId(empId);
            EmpPrivateInfoDo privateInfo = empPrivateInfoService.save(empPrivateInfoDto);

            // 任职信息
            EmpWorkInfoDto workInfoDto = empInfoDto.getEmpWorkInfo();
            workInfoDto.setEmpId(empId);
            workInfoDto.setName(empPrivateInfoDto.getName());
            workInfoDto.setEnName(empPrivateInfoDto.getEnName());
            // 若入职日期存在 则 将时间轴开始日期替换 成 入职日期；
            if (workInfoDto.getHireDate() != null) {
                workInfoDto.setDataStartTime(workInfoDto.getHireDate());
            }
            EmpWorkInfoDo empWorkInfo = empWorkInfoService.save(workInfoDto);

            // 基本信息
            EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
            BeanUtil.copyProperties(empWorkInfo, basicInfo, "id", "identifier", "bid", "tenantId", "createTime",
                    "createBy",
                    "updateTime", "updateBy", "deleted");
            basicInfo.setBid(empId);
            basicInfo.setEmpId(empId);
            basicInfo.setSex(privateInfo.getSex());
            basicInfo.setPhone(empPrivateInfoDto.getPhone());
            empBasicInfoDomainService.save(basicInfo);

            // 保存员工档案信息
            EmpFileAttachmentDto empFileAttachment = empInfoDto.getEmpFileAttachment();
            if (null != empFileAttachment) {
                empFileAttachment.setEmpId(empId);
                empFileAttachmentService.save(empFileAttachment);
            }

            SpringUtil.getBean(EmpReportLineService.class).updateReportLine(Lists.newArrayList(empId));
            return empId;
        } finally {
            if (isGetLock && Objects.nonNull(lock)) {
                lock.unlock();
            }
        }
    }

    // 更新员工
    public String submitEmpInfo(EmpInfoDto empInfoDto) {
        // 个人信息
        EmpPrivateInfoDto empPrivateInfoDto = empInfoDto.getEmpPrivateInfo();
        if (StringUtil.isEmpty(empPrivateInfoDto.getEmpId())) {
            String empId = String.valueOf(snowFlake.createId());
            insertEmpData(empPrivateInfoDto, empInfoDto.getEmpWorkInfo(), empInfoDto.getEmpFileAttachment(), empId);
        } else {
            EmpPrivateInfoDo detail = empPrivateInfoService.getByEmpId(empPrivateInfoDto.getEmpId());
            if (detail != null && detail.getBid() != null) {
                empPrivateInfoService.update(empPrivateInfoDto);

                // 保存任职信息
                EmpWorkInfoDto workInfoDto = empInfoDto.getEmpWorkInfo();
                workInfoDto.setEmpId(empPrivateInfoDto.getEmpId());
                workInfoDto.setName(empPrivateInfoDto.getName());
                workInfoDto.setEnName(empPrivateInfoDto.getEnName());
                EmpWorkInfoDo empWorkInfoDo = empWorkInfoService.update(workInfoDto);

                // 基本信息
                EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
                BeanUtil.copyProperties(empWorkInfoDo, basicInfo, "id", "identifier", "bid", "tenantId", "createTime",
                        "createBy",
                        "updateTime", "updateBy", "deleted");
                basicInfo.setBid(empPrivateInfoDto.getEmpId());
                basicInfo.setEmpId(empPrivateInfoDto.getEmpId());
                basicInfo.setSex(detail.getSex());
                basicInfo.setPhone(empPrivateInfoDto.getPhone());
                empBasicInfoDomainService.update(basicInfo);
            }
            // 其他服务用雪花生成了empId，走新增
            else {
                insertEmpData(empPrivateInfoDto, empInfoDto.getEmpWorkInfo(), empInfoDto.getEmpFileAttachment(),
                        empPrivateInfoDto.getEmpId());
            }
        }
        return empPrivateInfoDto.getEmpId();
    }

    public String saveEmpInfo(EmpInfoDto empInfoDto) {
        // 个人信息
        EmpPrivateInfoDto empPrivateInfoDto = empInfoDto.getEmpPrivateInfo();
        if (StringUtil.isEmpty(empPrivateInfoDto.getEmpId())) {
            String empId = String.valueOf(snowFlake.createId());
            onlyInsertEmpData(empPrivateInfoDto, empInfoDto.getEmpWorkInfo(), empInfoDto.getEmpFileAttachment(), empId);
        } else {
            EmpPrivateInfoDo detail = empPrivateInfoService.getByEmpId(empPrivateInfoDto.getEmpId());
            if (detail != null && detail.getBid() != null) {
                empPrivateInfoService.update(empPrivateInfoDto);

                // 保存任职信息
                EmpWorkInfoDto workInfoDto = empInfoDto.getEmpWorkInfo();
                workInfoDto.setEmpId(empPrivateInfoDto.getEmpId());
                workInfoDto.setName(empPrivateInfoDto.getName());
                workInfoDto.setEnName(empPrivateInfoDto.getEnName());
                empWorkInfoService.oblyUpdate(workInfoDto);

                // 基本信息
                EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
                BeanUtil.copyProperties(empInfoDto.getEmpWorkInfo(), basicInfo, "id", "identifier", "bid", "tenantId",
                        "createTime", "createBy",
                        "updateTime", "updateBy", "deleted");
                basicInfo.setBid(empPrivateInfoDto.getEmpId());
                basicInfo.setEmpId(empPrivateInfoDto.getEmpId());
                basicInfo.setSex(detail.getSex());
                basicInfo.setPhone(empPrivateInfoDto.getPhone());
                empBasicInfoDomainService.update(basicInfo);
            }
            // 其他服务用雪花生成了empId，走新增
            else {
                onlyInsertEmpData(empPrivateInfoDto, empInfoDto.getEmpWorkInfo(), empInfoDto.getEmpFileAttachment(),
                        empPrivateInfoDto.getEmpId());
            }
        }
        return empPrivateInfoDto.getEmpId();
    }

    private void onlyInsertEmpData(EmpPrivateInfoDto empPrivateInfoDto, EmpWorkInfoDto workInfoDto,
            EmpFileAttachmentDto empFileAttachment, String empId) {
        empPrivateInfoDto.setEmpId(empId);
        EmpPrivateInfoDo privateInfoDo = empPrivateInfoService.save(empPrivateInfoDto);

        // 保存任职信息
        workInfoDto.setEmpId(empId);
        workInfoDto.setName(empPrivateInfoDto.getName());
        workInfoDto.setEnName(empPrivateInfoDto.getEnName());
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.onlySave(workInfoDto);

        // 基本信息
        EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
        BeanUtil.copyProperties(empWorkInfo, basicInfo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                "updateTime", "updateBy", "deleted");
        basicInfo.setBid(empId);
        basicInfo.setEmpId(empId);
        basicInfo.setSex(privateInfoDo.getSex());
        basicInfo.setPhone(empPrivateInfoDto.getPhone());
        empBasicInfoDomainService.save(basicInfo);
    }

    private void insertEmpData(EmpPrivateInfoDto empPrivateInfoDto, EmpWorkInfoDto workInfoDto,
            EmpFileAttachmentDto empFileAttachment, String empId) {
        empPrivateInfoDto.setEmpId(empId);
        EmpPrivateInfoDo privateInfoDo = empPrivateInfoService.save(empPrivateInfoDto);

        // 保存任职信息
        workInfoDto.setEmpId(empId);
        workInfoDto.setName(empPrivateInfoDto.getName());
        workInfoDto.setEnName(empPrivateInfoDto.getEnName());
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.save(workInfoDto);

        // 基本信息
        EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
        BeanUtil.copyProperties(empWorkInfo, basicInfo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                "updateTime", "updateBy", "deleted");
        basicInfo.setBid(empId);
        basicInfo.setEmpId(empId);
        basicInfo.setSex(privateInfoDo.getSex());
        basicInfo.setPhone(empPrivateInfoDto.getPhone());
        empBasicInfoDomainService.save(basicInfo);

        // 保存员工档案信息
        if (null != empFileAttachment) {
            empFileAttachment.setEmpId(empId);
            empFileAttachmentService.save(empFileAttachment);
        }
    }

    public EmpBasicInfoDo getEmpBasicInfo(String empId, Long dataTime) {
        return empBasicInfoDomainService.getEmpBasicInfo(empId, dataTime);
    }

    public List<EmpBasicInfoDo> getEmpBasicInfoListByWorkNo(List<String> workNos, long dateTime) {
        return empBasicInfoDomainService.getEmpBasicInfoList(workNos, dateTime);
    }

    public List<EmpBasicInfoDo> getEmpBasicInfoListByBids(List<String> bids, long dateTime) {
        return empBasicInfoDomainService.getEmpBasicInfoListByBids(bids, dateTime);
    }

    @Resource
    private TerminationChangeService terminationChangeService;

    public Result stopUserRule(String empId) {
        Result<EmpDepartRuleDto> ruleDetail = empDepartRuleService.getRuleDetail();
        EmpDepartRuleDto ruleDto = ruleDetail.getData();
        // 是否离职生效
        if (ruleDto.getIsDepartFlag()) {
            // 天数；
            Integer dayTime = ruleDto.getDayTime();
            if (dayTime == 0) {
                // 立即生效；
                userFeignClient.stopUser(empId);
            } else {
                // 设置员工离职后的停用规则；
                terminationChangeService.empDepartRule(empId, dayTime);
            }
        }

        return Result.ok();
    }

    /**
     * 判断申请人是否是
     * （主岗/兼岗）直接上级 empLeader
     * 组织负责人 orgLeader
     * 审批人管理 approverSetting
     *
     * @param empId
     * @return
     */
    public String checkIdentity(String empId) {
        StringBuilder result = new StringBuilder();
        // 判断：（主岗/兼岗）直接上级
        // 获取 主岗/兼岗 下级员工；
        if (!getEmpMainPostForLeader(empId).isEmpty() || !getEmpConcurrentPostByLeaderEmpId(empId).isEmpty()) {
            result.append("\"empLeader\"");
        }

        // 判断： 组织负责人
        // 获取 负责的组织信息；
        if (!getOrgByLeadEmpId(empId).isEmpty()) {
            if (result.length() == 0) {
                result.append("\"orgLeader\"");
            } else {
                result.append(",\"orgLeader\"");
            }
        }

        // 审批人管理
        if (!getSettingDetailByEmpId(empId).isEmpty()) {
            if (result.length() == 0) {
                result.append("\"approverSetting\"");
            } else {
                result.append(",\"approverSetting\"");
            }
        }

        return String.format("[%s]", result.length() == 0 ? "\"\"" : result.toString());
    }

    /**
     * 主岗直接上级 对应下级
     * 返回 下级empIds
     *
     * @param empId
     * @return
     */
    public List<String> getEmpMainPostForLeader(String empId) {
        List<String> empIds = new ArrayList<>();
        List<EmpWorkInfoDo> empMainPostForLeader = empWorkInfoDomainService.getEmpMainPostForLeader(empId,
                System.currentTimeMillis());
        if (!empMainPostForLeader.isEmpty()) {
            List<String> emp = empMainPostForLeader.stream().map(EmpWorkInfoDo::getEmpId).collect(Collectors.toList());
            empIds.addAll(emp);
        }
        return empIds;
    }

    /**
     * 兼岗直接上级 对应下级
     * 返回 下级empIds
     *
     * @param empId
     * @return
     */
    public List<String> getEmpConcurrentPostByLeaderEmpId(String empId) {
        List<String> empIds = new ArrayList<>();
        List<EmpConcurrentPostDo> empConcurrentPostDos = empConcurrentPostDomainService
                .getEmpConcurrentPostByLeaderEmpId(empId);
        if (!empConcurrentPostDos.isEmpty()) {
            List<String> emp = empConcurrentPostDos.stream().map(EmpConcurrentPostDo::getEmpId)
                    .collect(Collectors.toList());
            empIds.addAll(emp);
        }
        return empIds;
    }

    /**
     * 根据leadEmpId 获取 组织信息；
     *
     * @param empId
     * @return
     */
    public List<OrgDo> getOrgByLeadEmpId(String empId) {
        List<OrgDo> orgDos = new ArrayList<>();
        List<OrgDo> orgByLeaderEmpId = orgDomainService.getOrgByLeaderEmpId(empId);
        if (!orgByLeaderEmpId.isEmpty()) {
            orgDos.addAll(orgByLeaderEmpId);
        }
        return orgDos;
    }

    /**
     * empId 获取 审批人管理信息；
     *
     * @param empId
     * @return
     */
    public List<WorkflowApproverSettingDetail> getSettingDetailByEmpId(String empId) {
        List<WorkflowApproverSettingDetail> detailByEmpId = WorkflowApproverSetting
                .getDetailByEmpId(Arrays.asList(empId));
        return detailByEmpId;
    }

    public List<EmpALlInfoVo> listEmpInfoAll(List<String> empIds, Long dataTime) {
        // List<EmpBasicInfoDo> basicInfos =
        // DataQuery.identifier("entity.hr.EmpBasicInfo").limit(-1, 1)
        // .filter(DataFilter.in("bid", empIds), EmpBasicInfoDo.class,
        // dataTime).getItems();
        List<EmpWorkInfoDo> workInfoDos = DataQuery.identifier("entity.hr.EmpWorkInfo").limit(-1, 1)
                .filter(DataFilter.in("empId", empIds), EmpWorkInfoDo.class, dataTime).getItems();
        List<EmpPrivateInfoDo> privateInfos = DataQuery.identifier("entity.hr.EmpPrivateInfo").limit(-1, 1)
                .filter(DataFilter.in("empId", empIds), EmpPrivateInfoDo.class, dataTime).getItems();
        // Map<String, WorkOverviewVo> workOverviewVoMap =
        // workOverviewService.listByEmpIds(empIds).stream()
        // .collect(Collectors.toMap(WorkOverviewVo::getEmpId, obj -> obj));
        // Map<String, ContractDo> contractMap =
        // contractService.getCurrentContractList(empIds).stream()
        // .collect(Collectors.toMap(data -> data.getOwner().getEmpId(), obj -> obj));

        MetadataVo workInfoMetadata = metadataService.getMetadata("entity.hr.EmpWorkInfo");
        MetadataVo privateInfoMetadata = metadataService.getMetadata("entity.hr.EmpPrivateInfo");

        // 创建privateInfo的Map以提高查找效率
        Map<String, EmpPrivateInfoDo> privateInfoMap = privateInfos.stream()
                .collect(Collectors.toMap(EmpPrivateInfoDo::getEmpId, Function.identity(),
                        (existing, replacement) -> existing));

        List<EmpALlInfoVo> result = Lists.newArrayList();
        // 改为基于workInfo进行循环，避免重复处理相同的empId
        for (EmpWorkInfoDo workInfoDo : workInfoDos) {
            String empId = workInfoDo.getEmpId();
            // EmpBasicInfoDo basicInfo = basicInfos.stream().filter(it ->
            // empId.equals(it.getBid())).findFirst().orElse(null);
            EmpPrivateInfoDo privateInfo = privateInfoMap.get(empId);
            EmpALlInfoVo empInfoVo = new EmpALlInfoVo();
            // empInfoVo.setEmpBasicInfo(ObjectConverter.convert(basicInfo,
            // EmpBasicInfoVo.class));
            empInfoVo.setEmpWorkInfo(ObjectConverter.convert(workInfoDo, EmpWorkInfoVo.class));
            empInfoVo.getEmpWorkInfo()
                    .setCostCenters(FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
            empInfoVo.setEmpPrivateInfo(ObjectConverter.convert(privateInfo, EmpPrivateInfoVo.class));
            if (empInfoVo.getEmpWorkInfo() != null) {
                EmpWorkInfoVo workInfoVo = empInfoVo.getEmpWorkInfo();
                if (StringUtil.isNotEmpty(workInfoDo.getCostCenters())) {
                    workInfoVo.setCostCenters(
                            FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
                }

                // DEV-5215
                if (null != workInfoVo.getLeadEmpId() && StringUtil.isEmpty(workInfoVo.getLeadEmpId().getEmpId())) {
                    workInfoVo.setLeadEmpId(null);
                }

                // 自定义字段查询
                Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValueSimple(workInfoMetadata,
                        workInfoDo);
                workInfoVo.setExt(ext);
            }

            if (privateInfo != null) {
                // 自定义字段查询
                Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValueSimple(privateInfoMetadata,
                        privateInfo);
                EmpPrivateInfoVo privateInfoVo = empInfoVo.getEmpPrivateInfo();
                // 年龄实时计算
                if (StringUtils.isNotEmpty(privateInfoVo.getCardNo()) && null != privateInfoVo.getCardType()
                        && CardTypeEnum.ID_CARD.getIndex().equals(privateInfoVo.getCardType().getValue())) {
                    privateInfoVo.setDivisionAge(IdCardUtil.getAgeByIdCard(privateInfoVo.getCardNo()));
                }
                privateInfoVo.setExt(ext);
            }
            // empInfoVo.setEmpWorkOverview(workOverviewVoMap.get(empId));
            // empInfoVo.setContract(ObjectConverter.convert(contractMap.get(empId),
            // ContractVo.class));
            result.add(empInfoVo);
        }
        return result;
    }

    public EmpALlInfoVo getEmpInfoAll(String empId, Long dataTime) {
        EmpBasicInfoDo basicInfo = getEmpBasicInfo(empId, dataTime);
        EmpWorkInfoDo workInfoDo = empWorkInfoService.getEmpWorkInfo(empId, dataTime);
        EmpPrivateInfoDo privateInfo = empPrivateInfoService.getByEmpId(empId);
        EmpALlInfoVo empInfoVo = new EmpALlInfoVo();
        empInfoVo.setEmpBasicInfo(ObjectConverter.convert(basicInfo, EmpBasicInfoVo.class));
        if (Objects.nonNull(workInfoDo)) {
            val empWorkInfo = ObjectConverter.convert(workInfoDo, EmpWorkInfoVo.class);
            empWorkInfo.setJobGradeTxt(Objects.nonNull(empWorkInfo.getJobGrade())
                    ? StringUtils.defaultString(empWorkInfo.getJobGrade().getStartGradeName())
                    : "");
            empInfoVo.setEmpWorkInfo(empWorkInfo);
        }
        empInfoVo.setEmpPrivateInfo(ObjectConverter.convert(privateInfo, EmpPrivateInfoVo.class));
        if (empInfoVo.getEmpBasicInfo() != null) {
            val basic = empInfoVo.getEmpBasicInfo();
            if (!"enabled".equals(postTxtShowCode)) {
                String postTxt = basic.getPostTxt();
                if (StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0) {
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    basic.setPostTxt(postTxt);
                }
            }
        }

        if (empInfoVo.getEmpWorkInfo() != null) {
            EmpWorkInfoVo workInfoVo = empInfoVo.getEmpWorkInfo();
            if (StringUtil.isNotEmpty(workInfoDo.getCostCenters())) {
                workInfoVo
                        .setCostCenters(FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
            }

            // DEV-5215
            if (null != workInfoVo.getLeadEmpId() && StringUtil.isEmpty(workInfoVo.getLeadEmpId().getEmpId())) {
                workInfoVo.setLeadEmpId(null);
            }

            // 自定义字段查询
            Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(workInfoDo.getDoIdentifier(),
                    workInfoDo);
            workInfoVo.setExt(ext);
            if (!"enabled".equals(postTxtShowCode)) {
                String postTxt = workInfoVo.getPostTxt();
                if (StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0) {
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    workInfoVo.setPostTxt(postTxt);
                }
            }
        }

        if (empInfoVo.getEmpPrivateInfo() != null) {
            // 自定义字段查询
            Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(privateInfo.getDoIdentifier(),
                    privateInfo);
            EmpPrivateInfoVo privateInfoVo = empInfoVo.getEmpPrivateInfo();
            // 年龄实时计算
            if (StringUtils.isNotEmpty(privateInfoVo.getCardNo()) && null != privateInfoVo.getCardType()
                    && CardTypeEnum.ID_CARD.getIndex().equals(privateInfoVo.getCardType().getValue())) {
                privateInfoVo.setDivisionAge(IdCardUtil.getAgeByIdCard(privateInfoVo.getCardNo()));
            }
            privateInfoVo.setExt(ext);
        }
        empInfoVo.setEmpWorkOverview(workOverviewService.selectByEmpId(empId));
        empInfoVo.setContract(ObjectConverter.convert(contractService.getCurrentContract(empId), ContractVo.class));
        return empInfoVo;
    }

    public EmpInfoVo getEmpInfo(String empId, Long dataTime) {
        EmpALlInfoVo empInfoVo = getEmpInfoAll(empId, dataTime);
        EmpInfoVo vo = new EmpInfoVo();
        vo.setEmpBasicInfo(empInfoVo.getEmpBasicInfo());
        vo.setEmpPrivateInfo(empInfoVo.getEmpPrivateInfo());
        vo.setWorkOverview(empInfoVo.getEmpWorkOverview());
        vo.setEmpWorkInfo(empInfoVo.getEmpWorkInfo());
        return vo;
    }

    public Map getEmpInfoSimple(String empId, Long dataTime) {
        EmpALlInfoVo empInfo = getEmpInfoAll(empId, dataTime);
        Map<String, Object> data = new HashMap<>();
        data.put("empBasicInfo", DataConvertUtil.convert2map(empInfo.getEmpBasicInfo(), EmpBasicInfoVo.class));
        data.put("empWorkInfo", DataConvertUtil.convert2map(empInfo.getEmpWorkInfo(), EmpWorkInfoVo.class));
        data.put("empPrivateInfo", DataConvertUtil.convert2map(empInfo.getEmpPrivateInfo(), EmpPrivateInfoVo.class));
        data.put("empWorkOverview", DataConvertUtil.convert2map(empInfo.getEmpWorkOverview(), WorkOverviewVo.class));
        return data;
    }

    public List<Map> listEmpInfoSimple(List<String> empIds, Long dataTime) {
        List<EmpALlInfoVo> empInfos = listEmpInfoAll(empIds, dataTime);
        return empInfos.stream().map(empInfo -> {
            Map<String, Object> data = new HashMap<>();
            // data.put("empBasicInfo",
            // DataConvertUtil.convert2map(empInfo.getEmpBasicInfo(),
            // EmpBasicInfoVo.class));
            Map<String, Object> map = DataConvertUtil.convert2map(empInfo.getEmpWorkInfo(), EmpWorkInfoVo.class);
            map.put("costCenters", EmpDynamicService.formatCostCenterTxt(empInfo.getEmpWorkInfo().getCostCenters()));
            data.put("empWorkInfo", map);
            data.put("empPrivateInfo",
                    DataConvertUtil.convert2map(empInfo.getEmpPrivateInfo(), EmpPrivateInfoVo.class));
            // data.put("empWorkOverview",
            // DataConvertUtil.convert2map(empInfo.getEmpWorkOverview(),
            // WorkOverviewVo.class));
            return data;
        }).collect(Collectors.toList());
    }
}
