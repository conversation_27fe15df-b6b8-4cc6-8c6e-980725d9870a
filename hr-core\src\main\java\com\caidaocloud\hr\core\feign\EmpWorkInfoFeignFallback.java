package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@Component
public class EmpWorkInfoFeignFallback implements IEmpWorkInfoFeign {

	@Override
	public Result<List<EmpWorkInfoVo>> getEmpListByWorkno(List<String> worknoList) {
		return Result.fail();
	}

	@Override
	public Result<Object> getTransferTypeList() {
		return Result.fail();
	}
}
