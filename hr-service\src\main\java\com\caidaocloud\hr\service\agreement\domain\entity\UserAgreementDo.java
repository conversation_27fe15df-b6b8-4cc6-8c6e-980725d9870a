package com.caidaocloud.hr.service.agreement.domain.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.agreement.infrastructure.repository.UserAgreementRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户协议
 * created by: FoAng
 * create time: 12/10/2024 10:54 上午
 */
@Slf4j
@Data
@Component
@EqualsAndHashCode(callSuper = true)
public class UserAgreementDo  extends BaseDomainDoImpl<UserAgreementDo> {

    /**
     * 协议名称
     */
    private String i18Name;

    /**
     * 协议类型
     */
    private String type;

    /**
     * 协议汇总
     */
    private String summary;

    /**
     * 协议内容
     */
    private String content;

    private static final String IDENTIFIER = "entity.hr.UserAgreement";

    @Resource
    private UserAgreementRepository userAgreementRepository;

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public BaseRepository<UserAgreementDo> getRepository() {
        return userAgreementRepository;
    }

    public PageResult<UserAgreementDo> getSummaryList(BasePage basePage) {
        return userAgreementRepository.getSummaryList(getDoIdentifier(), basePage);
    }

    @PaasTransactional
    public void changeStatusByType(String bid, String type, Integer status) {
        userAgreementRepository.changeStatusByType(getDoIdentifier(), bid, type, status);
    }

    public UserAgreementDo detailByType(String type) {
        return userAgreementRepository.detailByType(getDoIdentifier(), type);
    }
}
