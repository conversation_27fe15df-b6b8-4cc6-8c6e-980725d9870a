package com.caidaocloud.hr.service.archive;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveEvent;
import com.caidaocloud.hr.service.archive.constants.ArchiveConstant;
import com.caidaocloud.hr.service.archive.properties.ArchiveProperty;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 文件归档事件
 * created by: FoAng
 * create time: 4/6/2024 7:05 下午
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveEventProducer {

    private String module;

    private MqMessageProducer<ArchiveMessage> mqMessageProducer;

    private ArchiveProperty archiveProperty;

    public void publishArchiveEvent(List<ArchiveData> archiveList) {
        ArchiveEvent archiveEvent = buildArchiveEvent(null, null, ArchivePolicy.ADD_ONLY, archiveList);
        this.publishArchiveEvent(archiveEvent);
    }

    public void publishArchiveEvent(List<ArchiveData> archiveList, ArchivePolicy archivePolicy) {
        ArchiveEvent archiveEvent = buildArchiveEvent(null, null, archivePolicy, archiveList);
        this.publishArchiveEvent(archiveEvent);
    }

    public void publishArchiveEvent(String businessLine, String businessLineDesc, String businessId, ArchivePolicy archivePolicy) {
        ArchiveEvent archiveEvent = buildArchiveEvent(archivePolicy == ArchivePolicy.DELETE ? businessLineDesc : businessLine,
                businessId, archivePolicy, null);
        this.publishArchiveEvent(archiveEvent);
    }

    public void publishArchiveEvent(ArchiveStandardLine line, String businessId, ArchivePolicy archivePolicy) {
        String businessLine = archivePolicy == ArchivePolicy.DELETE ? line.getDesc() : line.name();
        ArchiveEvent archiveEvent = buildArchiveEvent(businessLine, businessId, archivePolicy, null);
        this.publishArchiveEvent(archiveEvent);
    }

    private void publishArchiveEvent(ArchiveEvent archiveEvent) {
        if (archiveProperty.isEnable()) {
            SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
            PreCheck.preCheckArgument(mqMessageProducer == null || userInfo == null,
                    "[archive] archive msg producer is null or can't fetch session user info");
            final String tenantId = userInfo.getTenantId();
            ArchiveMessage archiveMessage = new ArchiveMessage();
            archiveMessage.setBody(FastjsonUtil.toJson(archiveEvent));
            archiveMessage.setExchange(ArchiveConstant.tenantExchangeKey(tenantId));
            archiveMessage.setRoutingKey(ArchiveConstant.tenantRoutingKey(tenantId));
            mqMessageProducer.publish(archiveMessage);
            log.info("[archive] send archive event msg, content:{}", FastjsonUtil.toJson(archiveMessage));
            return;
        }
        log.info("[archive] archive is not enable");
    }

    private ArchiveEvent buildArchiveEvent(String businessLine, String businessId, ArchivePolicy archivePolicy, List<ArchiveData> archiveList) {
        return ArchiveEvent.builder()
                .businessModule(module)
                .businessLine(businessLine)
                .businessId(businessId)
                .archiveList(archiveList)
                .archivePolicy(archivePolicy)
                .build();
    }

    /**
     *
     * 发送文件归档事件
     * @param businessLine 所属业务
     * @param businessId   所属业务ID
     */
    @Deprecated
    public void publishArchiveEvent(String businessLine, String businessId) {
        this.publishArchiveEvent(businessLine, businessLine, businessId, ArchivePolicy.ADD_ONLY);
    }

    public void publishArchiveEvent(String businessLine, String businessLineDesc, String businessId) {
        this.publishArchiveEvent(businessLine, businessLineDesc, businessId, ArchivePolicy.ADD_ONLY);
    }


    /**
     * 文件归档事件
     * @param line
     * @param businessId
     */
    public void publishArchiveEvent(ArchiveStandardLine line, String businessId) {
        this.publishArchiveEvent(line, businessId, ArchivePolicy.ADD_ONLY);
    }
}
