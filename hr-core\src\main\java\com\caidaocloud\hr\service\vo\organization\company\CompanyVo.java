package com.caidaocloud.hr.service.vo.organization.company;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/24
 */
@Data
public class CompanyVo {
    @ApiModelProperty("公司ID")
    private String bid;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("公司名称-多语言")
    private Map<String, Object> i18nCompanyName;
    @ApiModelProperty("公司编码")
    private String companyCode;
    @ApiModelProperty("企业类型")
    private DictSimple enterpriseType;
    @ApiModelProperty("营业执照注册号")
    private String businessNo;
    @ApiModelProperty("组织机构代码")
    private String organizationCode;
    @ApiModelProperty("成立日期")
    private Long establishDate;
    @ApiModelProperty("法定代表人")
    private String legalPerson;
    @ApiModelProperty("法人证件类型")
    private EnumSimple legalPersonIdType;
    @ApiModelProperty("法人证件号码")
    private String legalPersonIdNum;
    @ApiModelProperty("开户银行")
    private String bankPlace;
    @ApiModelProperty("银行帐号")
    private String bankNum;
    @ApiModelProperty("注册地址")
    private String registerAddress;
    @ApiModelProperty("注册地址多语言")
    private Map<String, Object> i18nRegisterAddress;
    @ApiModelProperty("公司类型")
    private DictSimple companyType;
    @ApiModelProperty("工作地ID")
    private String workplaceId;
    @ApiModelProperty("工作地名称")
    private String workplaceName;
    @ApiModelProperty("纳税人识别号")
    private String taxpayerNumber;
    @ApiModelProperty("主管税务机关登记序号")
    private String taxAuthorityNumber;
    @ApiModelProperty("行政区划代码")
    private String areaNumber;
    @ApiModelProperty("部门编号（子税号）")
    private String departmentNumber;
    @ApiModelProperty("报税月份（修正）")
    private EnumSimple taxMonth;
    @ApiModelProperty("电子报税帐号")
    private String eleTaxNum;
    @ApiModelProperty("网税系统用户名")
    private String taxSystemUserName;
    @ApiModelProperty("申报密码")
    private String declarationPassword;
    @ApiModelProperty("实名账户")
    private String realNameAccount;
    @ApiModelProperty("实名密码")
    private String realNamePassword;
    @ApiModelProperty("PC公司Logo")
    private Attachment pcCompanyLogo;
    @ApiModelProperty("APP公司Logo")
    private Attachment appCompanyLogo;
    @ApiModelProperty("是否更新该公司下的员工工作地信息")
    private Boolean syncEmpWorkplace;
    @ApiModelProperty("企业状态")
    private DictSimple enterpriseStatus;
    @ApiModelProperty("联系人电话")
    private String contactNumber;
    @ApiModelProperty("联系人邮箱")
    private String contactEmail;
    @ApiModelProperty("联系人姓名")
    private String contactName;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();

    @ApiModelProperty("企业类型txt")
    private String enterpriseTypeTxt;
    @ApiModelProperty("企业状态txt")
    private String enterpriseStatusTxt;
    @ApiModelProperty("成立日期")
    private String establishDateTxt;
}
