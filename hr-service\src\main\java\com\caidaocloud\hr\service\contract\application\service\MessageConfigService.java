package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.hr.service.contract.application.event.dto.QueryMsgConfigDto;
import com.caidaocloud.hr.service.contract.application.feign.MessageFeign;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * created by: FoAng
 * create time: 1/9/2022 11:13 上午
 */
@Service
@Slf4j
public class MessageConfigService {

    @Resource
    private MessageFeign messageFeign;

    public List<MsgConfigDto> getMsgConfigByType(NoticeType noticeType, List<String> ownerEmpIds) {
        QueryMsgConfigDto configDto = new QueryMsgConfigDto();
        configDto.setType(noticeType);
        configDto.setEmpList(ownerEmpIds);
        Result<List<MsgConfigDto>> result = messageFeign.getEnableMsgConfigList(configDto);
        return result.isSuccess() ? result.getData() : Lists.newArrayList();
    }
}
