package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("任职记录信息DTO")
public class EmpJobRecordDto {
    @ApiModelProperty("bid")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("任职类型")
    private String jobType;

    @ApiModelProperty("所属组织ID")
    private String organize;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("所属岗位ID")
    private String post;

    @ApiModelProperty("所属岗位名称")
    private String postTxt;

    @ApiModelProperty("员工类型")
    private String empType;

    @ApiModelProperty("生效日期")
    private Long effectiveDate;

    @ApiModelProperty("上级领导")
    private EmpSimple leadEmpId;

    @ApiModelProperty("上级岗位ID")
    private String leaderPost;

    @ApiModelProperty("上级岗位名称")
    private String leaderPostTxt;

    @ApiModelProperty("直接上级组织ID")
    private String leaderOrganize;

    @ApiModelProperty("直接上级组织名称")
    private String leaderOrganizeTxt;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("任职记录关联的表单数据ID")
    private String dataId;

    @ApiModelProperty("表单")
    private String formId;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
