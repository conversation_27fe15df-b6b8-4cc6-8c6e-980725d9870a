package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRuleDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractRenewRuleDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractRenewRuleVo;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.dto.MsgConfigItemVo;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.feign.MessageFeignClient;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 2/12/2024 10:46 上午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractRenewRuleDomainService extends BaseDomainServiceImpl<ContractRenewRuleDo, BasePage> {

    private static final String CONTRACT_RENEW_CONDITION_KEY = "auto_renew_contract";

    private ContractRenewRuleDo contractRenewRuleDo;

    private IConditionFeign conditionFeign;

    private MessageFeignClient messageFeignClient;

    private ContractRenewRelDomainService contractRenewRelDomainService;

    @Override
    public BaseDomainDo<ContractRenewRuleDo> getDoService() {
        return contractRenewRuleDo;
    }

    public List<Integer> listAdvanceDay() {
        return contractRenewRuleDo.listAdvanceDay();
    }

    public List<MsgConfigDto> getNoticeType() {
        Result<List<MsgConfigDto>> result = messageFeignClient.getEnableMsgConfigList(NoticeType.CONTRACT_AUTO_RENEW.getIndex());
        if (result != null && result.isSuccess()) {
            return Optional.ofNullable(result.getData()).orElse(Lists.newArrayList());
        }
        return Lists.newArrayList();
    }

    public List<ConditionDataVo> getConditionTree() {
        List<ConditionDataVo> conditionDataVos = conditionFeign.getConditionDataByCode(CONTRACT_RENEW_CONDITION_KEY, false).getData();
        return Optional.ofNullable(conditionDataVos).map(it -> it.stream().peek(o1 -> {
            o1.setCode(String.format("%s#%s", o1.getIdentifier(), o1.getQueryProperty().replace(".", "$")));
        }).collect(Collectors.toList())).orElse(Lists.newArrayList());
    }

    @PaasTransactional
    public void saveOrUpdate(ContractRenewRuleDto dto) {
        ContractRenewRuleDo ruleDo = ObjectConverter.convert(dto, ContractRenewRuleDo.class);
        saveOrUpdate(ruleDo);
        contractRenewRelDomainService.doRefreshRuleRelAction(ruleDo, SecurityUserUtil.getSecurityUserInfo().getTenantId());
    }

    /**
     * 刷新模板匹配关系
     * @param ruleId
     */
    public void refreshRuleEmpRel(String ruleId) {
        ContractRenewRuleDo contractRenewRuleDo = getById(ruleId);
        PreCheck.preCheckArgument(contractRenewRuleDo == null, LangUtil.getMsg(MsgCodeConstant.NO_DATA_EXIST));
        contractRenewRelDomainService.doRefreshRuleRelAction(contractRenewRuleDo, SecurityUserUtil.getSecurityUserInfo().getTenantId());
    }

    @PaasTransactional
    public void deleteContractRules(List<String> bids) {
        for (String bid : bids) {
            delete(bid);
            contractRenewRelDomainService.deleteRuleRel(null, bid);
        }
    }

    public PageResult<ContractRenewRuleVo> pageRule(BasePage page) {
        PageResult<ContractRenewRuleDo> pageResult = contractRenewRuleDo.getPage(page);
        List<ContractRenewRuleVo> ruleVoList = Optional.ofNullable(pageResult.getItems())
                .map(it -> {
                    Map<String, KeyValue> msgMap = fetchMsgMap(it);
                    return it.stream().map(o1 -> {
                        ContractRenewRuleVo ruleVo = ObjectConverter.convert(o1, ContractRenewRuleVo.class);
                        ruleVo.setMsgConfigs(Optional.ofNullable(o1.getMsgConfig())
                                .map(o2 -> Arrays.stream(o2.split(",")).map(msgMap::get).collect(Collectors.toList()))
                                .orElse(Lists.newArrayList()));
                        return ruleVo;
                    }).collect(Collectors.toList());
                }).orElse(Lists.newArrayList());
        return new PageResult<>(ruleVoList, page.getPageNo(), page.getPageSize(), pageResult.getTotal());
    }

    public Map<String, KeyValue> fetchMsgMap(List<ContractRenewRuleDo> list) {
        String msgBids = list.stream().map(ContractRenewRuleDo::getMsgConfig)
                .filter(Objects::nonNull)
                .flatMap(o1 -> Arrays.stream(o1.split(","))).distinct()
                .collect(Collectors.joining(","));
        if (StringUtil.isNotEmpty(msgBids)) {
            Result<List<MsgConfigItemVo>> result = messageFeignClient.listByIds(msgBids);
            if (result != null && result.isSuccess()) {
                return Optional.ofNullable(result.getData())
                        .map(it -> it.stream().collect(Collectors.toMap(MsgConfigItemVo::getBid, o1 -> new KeyValue(o1.getBid(), o1.getName()))))
                        .orElse(Maps.newHashMap());
            }
        }
        return Maps.newHashMap();
    }

}
