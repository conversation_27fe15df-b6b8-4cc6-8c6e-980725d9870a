package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContractTypeSetRepository;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.ContractTypeSetPo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractTypeSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.DictDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ContractTypeSetRepositoryImpl extends BaseRepositoryImpl<ContractTypeSetDo> implements IContractTypeSetRepository {
    ContractTypeSetPo toPo(ContractTypeSetDo data) {
        if (data == null) {
            return null;
        }
        ContractTypeSetPo po = ObjectConverter.convert(data, ContractTypeSetPo.class);
        po.setCompanyTxt(FastjsonUtil.toJson(data.getCompanyTxt()));
        po.setSignTypeTxt(FastjsonUtil.toJson(data.getSignTypeTxt()));
        po.setEmpTypeTxt(FastjsonUtil.toJson(data.getEmpTypeTxt()));
        return po;
    }

    ContractTypeSetDo toDo(ContractTypeSetPo po) {
        if (po == null) {
            return null;
        }
        ContractTypeSetDo data = ObjectConverter.convert(po, ContractTypeSetDo.class);
        data.setCompanyTxt(FastjsonUtil.toList(po.getCompanyTxt(), ComponentValue.class));
        data.setSignTypeTxt(FastjsonUtil.toList(po.getSignTypeTxt(), ComponentValue.class));
        data.setEmpTypeTxt(FastjsonUtil.toList(po.getEmpTypeTxt(), DictDto.class));
        return data;
    }

    @Override
    public int updateById(ContractTypeSetDo data) {
        DataUpdate.identifier(data.getIdentifier()).update(toPo(data));
        return 0;
    }

    @Override
    public ContractTypeSetDo insert(ContractTypeSetDo data) {
        String dataId = DataInsert.identifier(data.getIdentifier()).insert(toPo(data));
        data.setBid(dataId);
        return data;
    }

    @Override
    public ContractTypeSetDo selectById(String bid, String identifier) {
        ContractTypeSetPo po = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .oneOrNull(bid, ContractTypeSetPo.class);
        return po == null ? null : toDo(po);
    }

    @Override
    public PageResult<ContractTypeSetDo> selectPage(BasePage page, ContractTypeSetDo data) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        ContractTypeSetQueryDto queryDto = (ContractTypeSetQueryDto) page;
        if(StringUtil.isNotEmpty(queryDto.getStatus())){
            dataFilter = dataFilter.andEq("status", queryDto.getStatus());
        }
        dataFilter = (DataFilter) page.doDataFilter(page.getFilters(), dataFilter);
        PageResult<ContractTypeSetPo> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, ContractTypeSetPo.class);
        List<ContractTypeSetDo> doList = pageResult.getItems().stream().map(this::toDo).collect(Collectors.toList());
        return new PageResult<>(doList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    @Override
    public List<ContractTypeSetDo> selectByCondition(ContractTypeSetDo data) {
        PageResult<ContractTypeSetPo> result = DataQuery.identifier(data.getIDENTIFIER()).decrypt().queryInvisible().specifyLanguage()
                .filter(DataFilter.eq("contractType$dict$value", data.getContractType().getValue())
                        .andEq("company", FastjsonUtil.toJson(data.getCompany()))
                        .andEq("signType", FastjsonUtil.toJson(data.getSignType()))
                        .andEq("empType", FastjsonUtil.toJson(data.getEmpType()))
                        .andEq("deleted", Boolean.FALSE.toString())
                        .andNeIf("bid", data.getBid(), () -> data.getBid() != null), ContractTypeSetPo.class);
        return result.getItems().stream().map(this::toDo).collect(Collectors.toList());
    }

    @Override
    public List<ContractTypeSetDo> selectByRegex(String identifier, String company, String signType) {
        PageResult<ContractTypeSetPo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage()
                .queryInvisible().filter(DataFilter.regex("company", company)
                        .andRegex("signType",signType)
                        .andNe("deleted", Boolean.TRUE.toString()), ContractTypeSetPo.class);

        return pageResult.getItems().stream().map(this::toDo).collect(Collectors.toList());
    }

    @Override
    public List<ContractTypeSetDo> getListCompanyByRegex(String identifier, String signType) {
        DataFilter filter = getBaseFilter();
        if (StringUtils.isEmpty(signType)) {
            signType = "2";
        }
        filter = filter.andRegex("signType", signType);
        PageResult<ContractTypeSetPo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(filter, ContractTypeSetPo.class);
        return pageResult.getItems().stream().map(this::toDo).collect(Collectors.toList());
    }

    @Override
    public List<ContractTypeSetDo> selectList(String identifier,String tenantId) {
        PageResult<ContractTypeSetPo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage()
                .queryInvisible().limit(5000, 1).filter(DataFilter.eq("tenantId", tenantId)
                        .andNe("deleted", Boolean.TRUE.toString()), ContractTypeSetPo.class);
        return null != pageResult ? pageResult.getItems().stream().map(this::toDo).collect(Collectors.toList()) : Lists.newArrayList();
    }

    @Override
    public List<ContractTypeSetDo> selectBatchIds(List<String> ids, String identifier) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("bid", ids);
        PageResult<ContractTypeSetPo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage()
                .queryInvisible().filter(filter, ContractTypeSetPo.class);

        return null != pageResult ? pageResult.getItems().stream().map(this::toDo).collect(Collectors.toList()) : Lists.newArrayList();
    }
}