package com.caidaocloud.hr.service.confirmation.domain.entity;

import com.caidaocloud.hr.service.transfer.domain.entity.FieldDef;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ConfirmationChangeFieldDef implements FieldDef {

    /**
     * 字段类别
     */
    private String type;

    /**
     * 字段定义
     */
    private String property;

    /**
     * 展示字段定义
     */
    private String displayProperty;

    /**
     * 字段名称
     */
    private String name;
    private Map<String, String> i18nName;

    /**
     * 字段是否必填
     */
    private boolean required;

    /**
     * 系统字段
     */
    private boolean system;

    /**
     * 组件类型
     */
    private String widgetType;

    /**
     * 数据类型
     */
    private PropertyDataType dataType;

    /**
     * 关联字段
     */
    private String linkProperty;

    /**
     * 枚举定义值
     */
    private List<PropertyEnumDefDto> enumDef;

    /**
     * 数据源
     */
    private String datasource;

    /**
     * 自定义字段
     */
    private boolean custom;

    ConfirmationChangeFieldDef(String type, String property, String name, PropertyDataType dataType) {
        this.type = type;
        this.property = property;
        this.name = name;
        this.dataType = dataType;
    }

    public String generateCode() {
        StringBuilder sb = new StringBuilder();
        sb.append(type).append('$').append(property);
        // if (dataType== PropertyDataType.Job_Grade_Range) {
        //     sb.append(".startGradeName");
        // }
        return sb.toString();
    }

    /**
     * 冗余的辅助字段
     */
    private boolean backUp = false;

    // 是否回写
    private boolean writeback = false;

    @Override
    public String fieldProperty() {
        return this.property;
    }

    @Override
    public PropertyDataType fieldDataType() {
        return dataType;
    }

    @Override
    public List<PropertyEnumDefDto> fieldEnumDef() {
        return enumDef;
    }
}
