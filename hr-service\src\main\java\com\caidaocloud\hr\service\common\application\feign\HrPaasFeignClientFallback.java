package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class HrPaasFeignClientFallback implements HrPaasFeignClient {
    @Override
    public Result<Map<String, Object>> pageDetail(String pageId) {
        log.error("pageDetail occur error, pageId={}", pageId);
        return Result.ok(Maps.newHashMap());
    }

    @Override
    public Result<PageResult<Map<String, Object>>> formPage(String formId, int pageNo, int pageSize, String empId, boolean isPlain) {
        log.error("formPage occur error, formId={} pageNo={} pageSize={} empId={} isPlain={}", formId, pageNo, pageSize, empId, isPlain);
        return Result.ok(new PageResult());
    }

    @Override
    public Result<List<DynamicColumnConfigDto>> dynamicTablesSet(List<String> codes) {
        return Result.ok(Lists.newArrayList());
    }
}