package com.caidaocloud.hr.service.employee.application.emp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工统计信息")
public class EmpStatisticsDto {
    @ApiModelProperty("任职状态")
    private List<StatisticsBaseDto> workStatus;
    @ApiModelProperty("员工状态")
    private List<StatisticsBaseDto> empStatus;
    @ApiModelProperty("员工类型")
    private List<StatisticsBaseDto> empType;
    @ApiModelProperty("离职状态")
    private List<StatisticsBaseDto> resignationStatus;
}
