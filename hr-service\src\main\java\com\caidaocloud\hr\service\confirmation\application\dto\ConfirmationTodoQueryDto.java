package com.caidaocloud.hr.service.confirmation.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class ConfirmationTodoQueryDto {

    private int pageNo = 1;

    private int pageSize = 20;

    private List<String> orgIds = Lists.list();

    private String keywords;

    private Long probationPeriodDay;

    private ConfirmationTodoQueryFilter filters;

    public DataFilter toDataFilter(){
        DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andEq("confirmationStatus", "1")
                .andNe("empStatus", "1");
        if(CollectionUtils.isNotEmpty(orgIds)){
            dataFilter = dataFilter.andIn("organize", orgIds);
        }
        if(StringUtils.isNotEmpty(keywords)){
            dataFilter = dataFilter.and(DataFilter.regex("name", keywords)
                    .orRegex("workno", keywords));
        }
        if(probationPeriodDay != null){
            long time = DateUtil.getMidnightTimestamp() + probationPeriodDay * 24 * 3600 * 1000;
            dataFilter = dataFilter
                    .andLe("probationPeriodEndDate", String.valueOf(time));
        }
        if(null != filters){
            dataFilter = filters.toDataFilter(dataFilter);
        }
        return dataFilter;
    }

    @Data
    public static class ConfirmationTodoQueryFilter{
        private List<String> empTypes = Lists.list();
        private List<String> postIds = Lists.list();
        private List<String> hireDateRange = Lists.list();
        private List<String> probationPeriodEndDateRange = Lists.list();

        public DataFilter toDataFilter(DataFilter dataFilter) {
            if(CollectionUtils.isNotEmpty(empTypes)){
                dataFilter = dataFilter.andIn("empType$dictValue", empTypes);
            }
            if(CollectionUtils.isNotEmpty(postIds)){
                dataFilter = dataFilter.andIn("post", postIds);
            }
            if(CollectionUtils.isNotEmpty(hireDateRange)){
                dataFilter = dataFilter
                        .andGe("hireDate", hireDateRange.get(0))
                        .andLe("hireDate", hireDateRange.get(1));
            }
            if(CollectionUtils.isNotEmpty(probationPeriodEndDateRange)){
                dataFilter = dataFilter
                        .andGe("probationPeriodEndDate", probationPeriodEndDateRange.get(0))
                        .andLe("probationPeriodEndDate", probationPeriodEndDateRange.get(1));
            }
            return dataFilter;
        }
    }

}
