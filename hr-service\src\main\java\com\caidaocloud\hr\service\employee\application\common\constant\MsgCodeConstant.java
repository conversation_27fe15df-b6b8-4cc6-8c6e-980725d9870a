package com.caidaocloud.hr.service.employee.application.common.constant;

public class MsgCodeConstant {
    public final static int COST_CENTER_DUPLICATION_KEY = 30015;

    /**
     * 结束时间需大于开始时间
     */
    public final static int END_GREATER_STR = 30016;

    /**
     * 工号不能重复
     */
    public final static int WORKNO_NOT_DUPLICATE = 35000;

    public final static int HR_ERROR_CODE_30032 = 30032;
    public final static int HR_ERROR_CODE_30033 = 30033;
    public final static int HR_ERROR_CODE_30034 = 30034;
    public final static int HR_ERROR_CODE_30035 = 30035;
    public final static int HR_ERROR_CODE_30036 = 30036;
    public final static int HR_ERROR_CODE_30037 = 30037;
    public final static int HR_ERROR_CODE_30038 = 30038;
    public final static int HR_ERROR_CODE_30039 = 30039;
    public final static int HR_ERROR_CODE_30040 = 30040;
    public final static int HR_ERROR_CODE_30041 = 30041;
    public final static int HR_ERROR_CODE_30042 = 30042;
    public final static int HR_ERROR_CODE_30043 = 30043;
    public final static int HR_ERROR_CODE_30044 = 30044;
    public final static int HR_ERROR_CODE_30045 = 30045;
    public final static int HR_ERROR_CODE_30046 = 30046;
    public final static int HR_ERROR_CODE_30047 = 30047;
    public final static int HR_ERROR_CODE_30048 = 30048;
    public final static int HR_ERROR_CODE_30049 = 30049;
    public final static int HR_ERROR_CODE_30050 = 30050;
    public final static int HR_ERROR_CODE_30051 = 30051;
    public final static int HR_ERROR_CODE_30052 = 30052;
    public final static int HR_ERROR_CODE_30053 = 30053;
    public final static int HR_ERROR_CODE_30054 = 30054;
    public final static int HR_ERROR_CODE_30055 = 30055;
    public final static int HR_ERROR_CODE_30056 = 30056;
    public final static int HR_ERROR_CODE_30057 = 30057;
    public final static int HR_ERROR_CODE_30058 = 30058;
    public final static int HR_ERROR_CODE_30059 = 30059;
    public final static int HR_ERROR_CODE_30060 = 30060;
    public final static int HR_ERROR_CODE_30061 = 30061;
    public final static int HR_ERROR_CODE_30062 = 30062;
    public final static int HR_ERROR_CODE_30063 = 30063;
    public final static int HR_ERROR_CODE_30064 = 30064;
    public final static int HR_ERROR_CODE_30065 = 30065;
    public final static int HR_ERROR_CODE_30066 = 30066;
    public final static int HR_ERROR_CODE_30067 = 30067;
    public final static int HR_ERROR_CODE_30068 = 30068;
    public final static int HR_ERROR_CODE_30069 = 30069;
    public final static int HR_ERROR_CODE_30070 = 30070;
    public final static int HR_ERROR_CODE_30071 = 30071;
    public final static int HR_ERROR_CODE_30072 = 30072;
    public final static int HR_ERROR_CODE_30073 = 30073;
    public final static int HR_ERROR_CODE_30074 = 30074;
    public final static int HR_ERROR_CODE_30075 = 30075;
    public final static int HR_ERROR_CODE_30076 = 30076;
    public final static int HR_ERROR_CODE_30077 = 30077;
    public final static int HR_ERROR_CODE_30078 = 30078;
    public final static int HR_ERROR_CODE_30079 = 30079;
    public final static int HR_ERROR_CODE_30080 = 30080;
    public final static int HR_ERROR_CODE_30081 = 30081;
    public final static int HR_ERROR_CODE_30082 = 30082;
    public final static int HR_ERROR_CODE_30083 = 30083;
    public final static int HR_ERROR_CODE_30084 = 30084;
    public final static int HR_ERROR_CODE_30085 = 30085;
    public final static int HR_ERROR_CODE_30086 = 30086;
    public final static int HR_ERROR_CODE_30087 = 30087;
    public final static int HR_ERROR_CODE_30088 = 30088;
    public final static int HR_ERROR_CODE_30089 = 30089;
    public final static int HR_ERROR_CODE_30090 = 30090;
    public final static int HR_ERROR_CODE_30091 = 30091;
    public final static int HR_ERROR_CODE_30092 = 30092;
    public final static int HR_ERROR_CODE_30093 = 30093;
    public final static int HR_ERROR_CODE_30094 = 30094;
    public final static int HR_ERROR_CODE_30095 = 30095;
    public final static int HR_ERROR_CODE_30096 = 30096;
    public final static int HR_ERROR_CODE_30097 = 30097;
    public final static int HR_ERROR_CODE_30098 = 30098;
    public final static int HR_ERROR_CODE_30099 = 30099;
    public final static int HR_ERROR_CODE_30100 = 30100;
    public final static int HR_ERROR_CODE_30101 = 30101;
    public final static int HR_ERROR_CODE_30102 = 30102;

    public final static int HR_ERROR_CODE_30103 = 30103;
    public final static int HR_ERROR_CODE_30104 = 30104;
    public final static int HR_ERROR_CODE_30105 = 30105;

    public final static int HR_ERROR_CODE_30106 = 30106;
    public final static int HR_ERROR_CODE_30107 = 30107;
    public final static int HR_ERROR_CODE_30108 = 30108;
    public final static int HR_ERROR_CODE_30109 = 30109;
    public final static int HR_ERROR_CODE_30110 = 30110;
    public final static int HR_ERROR_CODE_30111 = 30111;
    public final static int HR_ERROR_CODE_30112 = 30112;
    // 组织下存在员工
    public final static int ORGANIZE_EXIST_EMPLOYEES = 30113;

    // 组织人事模块多语言，后面从 32000 开始，30114 到 31999 留给合同管理

    // 请先选择或创建岗位序列
    public final static int HR_ERROR_CODE_32000 = 32000;

    // 证件号码格式错误
    public final static int HR_ERROR_CODE_32001 = 32001;
    // 公司编码不能为空
    public final static int HR_ERROR_CODE_32002 = 32002;
    // 公司编码不能重复
    public final static int HR_ERROR_CODE_32003 = 32003;
    // 公司全称长度不能超过500
    public final static int HR_ERROR_CODE_32004 = 32004;
    // 公司简称长度不能超过500
    public final static int HR_ERROR_CODE_32005 = 32005;
    // 公司全称重复
    public final static int HR_ERROR_CODE_32006 = 32006;
    // 公司简称重复
    public final static int HR_ERROR_CODE_32007 = 32007;

    // 该职级已关联职务信息，不允许停用
    public final static int HR_ERROR_CODE_32008 = 32008;

    // 该职级已关联职务信息，不允许删除
    public final static int HR_ERROR_CODE_32009 = 32009;

    // 该职级已关联岗位信息，不允许停用
    public final static int HR_ERROR_CODE_32010 = 32010;

    // 该职级已关联岗位信息，不允许删除
    public final static int HR_ERROR_CODE_32011 = 32011;

    // 组织已被引用不可停用
    public final static int HR_ERROR_CODE_32012 = 32012;


    /**
     * 文件中手机号重复
     */
    public static final int HR_ERROR_CODE_32017 = 32017;

    /**
     * 手机号已存在
     */
    public static final int HR_ERROR_CODE_32018 = 32018;

    /**
     * 文件中证件号码重复
     */
    public static final int HR_ERROR_CODE_32019 = 32019;

    /**
     * 证件号码已存在
     */
    public static final int HR_ERROR_CODE_32020 = 32020;

    public final static int HR_ERROR_CODE_32021 = 32021;

    public final static int HR_ERROR_CODE_32022 = 32022;

    public final static int HR_ERROR_CODE_32023 = 32023;

    public final static int HR_ERROR_CODE_32024 = 32024;

    public final static int HR_ERROR_CODE_32025 = 32025;

    public final static int HR_ERROR_CODE_32026 = 32026;

    public final static int HR_ERROR_CODE_32027 = 32027;

    public final static int HR_ERROR_CODE_32028 = 32028;

    public final static int HR_ERROR_CODE_32029 = 32029;

    /**
     * 员工离职规则 不合规
     */

    public final static int HR_ERROR_CODE_32030 = 32030;

    /**
     * 岗位导入，关联为空
     */
    public final static int HR_ERROR_CODE_32032 = 32032;

    /**
     * 职级不存在
     */
    public final static int HR_ERROR_CODE_32033 = 32033;

    /**
     * 岗位关系参数错误
     */
    public final static int HR_ERROR_CODE_32034 = 32034;

    // 组织编码不能为空
    public final static int HR_ERROR_CODE_32035 = 32035;
    // 员工工号不能为空
    public final static int HR_ERROR_CODE_32036 = 32036;

    // 职务管理层级不能为空且范围只能在2到4之间
    public final static int HR_ERROR_CODE_32037 = 32037;

    // 保存失败
    public final static int HR_ERROR_CODE_32038 = 32038;

    // 组织编码规则配置不合法
    public final static int HR_ERROR_CODE_32039 = 32039;

    /**
     * 节点已被子节点引用
     */
    public final static int HR_ERROR_CODE_32040 = 32040;

    /**
     * 上级职务类型不存在
     */
    public final static int HR_ERROR_CODE_32041 = 32041;

    /**
     * 职务管理层级不能大于
     */
    public final static int HR_ERROR_CODE_32042 = 32042;

    /**
     * 兼岗上级组织不存在或已失效
     */
    public final static int HR_ERROR_CODE_32043 = 32043;

    /**
     * 兼岗上级岗位不存在或已失效
     */
    public final static int HR_ERROR_CODE_32044 = 32044;

    /**
     * 直接上级组织不存在或已失效
     */
    public final static int HR_ERROR_CODE_32045 = 32045;

    /**
     * 直接上级岗位不存在或已失效
     */
    public final static int HR_ERROR_CODE_32046 = 32046;

    /**
     * 请先停用职务族下面的职务类
     */
    public final static int HR_ERROR_CODE_32047 = 32047;

    /**
     * 请先停用职务类下面的职务子类
     */
    public final static int HR_ERROR_CODE_32048 = 32048;

    /**
     * 请先停用职务子类下面的职务
     */
    public final static int HR_ERROR_CODE_32049 = 32049;

    // 同级职务名称不能重复
    public final static int HR_ERROR_CODE_32050 = 32050;
    // 只有生效中的合同才能操作
    public final static int HR_ERROR_CODE_32051 = 32051;
    // 终止和解除日期必须大于等于当前合同开始日期和小于等于当前合同的结束日期
    public final static int HR_ERROR_CODE_32052 = 32052;
    // 非审批通过的记录不能再次发起
    public final static int HR_ERROR_CODE_32053 = 32053;
}