package com.caidaocloud.hr.service.contract.application.enums;

/**
 * 发起签署状态
 */
public enum InitiateStatus {
    initiate("0","未发起"),
    signing("1","已发起"),
    ;

    private final  String value;
    private final  String name;

    InitiateStatus(String value, String name){
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public String getValue(){
        return value;
    }
    public static InitiateStatus getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (InitiateStatus status : InitiateStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
