package com.caidaocloud.hr.service.contract.interfaces.facade;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.application.service.ContinueLetterBatchService;
import com.caidaocloud.hr.service.contract.application.service.ContinueLetterNotifyService;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueLetterBatchDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContinueLetterBatchVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/3/19
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/continue/batch/v1")
@Api(value = "/api/hr/continue/batch/v1", tags = "合同续签批量发起")
public class ContinueLetterBatchController {
	@Autowired
	private ContinueLetterBatchService continueLetterBatchService;
	@Autowired
	private ContinueLetterNotifyService continueLetterNotifyService;

	@PostMapping("create")
	@ApiOperation("批量发起续签意向书")
	public Result createContinueLetter(@RequestBody ContinueLetterBatchDto dto) {
		List<String> bids = dto.getBids();
		ContinueLetterBatchDo batch = continueLetterBatchService.createBatch(bids);
		continueLetterBatchService.startBatch(bids, batch, SecurityUserUtil.getSecurityUserInfo());
		return Result.ok(true);
	}

	@PostMapping("page")
	@ApiOperation("批量结果分页")
	public Result<PageResult<ContinueLetterBatchVo>> confirm(@RequestBody BasePage dto) {
		return Result.ok(continueLetterBatchService.loadBatchPage(dto));
	}

	@GetMapping("source")
	@ApiOperation(value = "下载批量发起人员excel", produces = "application/octet-stream")
	public void downloadSource(@RequestParam("bid") String bid, HttpServletResponse response) {
		continueLetterBatchService.downloadBatchExcel(bid,false,response);
	}

	@GetMapping("failed")
	@ApiOperation(value = "下载失败数据excel", produces = "application/octet-stream")
	public void downloadFailed(@RequestParam("bid") String bid, HttpServletResponse response) {
		continueLetterBatchService.downloadBatchExcel(bid, true, response);
	}

	@PostMapping("notice")
	@ApiOperation("续签意向通知补发")
	public Result notice(@RequestBody List<String> contractIds) {
		continueLetterNotifyService.notice(contractIds);
		return Result.ok();
	}
}
