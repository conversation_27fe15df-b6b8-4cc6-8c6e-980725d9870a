package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.LastContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;
import java.util.Optional;

public interface ILastContractRepository extends BaseRepository<LastContractDo> {
    Optional<LastContractDo> selectByEmpId(String identifier, String empId);

    PageResult<LastContractDo> selectPage(String identifier, ContractQueryDto query);

    List<LastContractDo> selectByEmpIds(String identifier, List<String> empIds);

    PageResult<LastContractDo> selectContractPage(String identifier, ContractQueryDto query);

    Optional<LastContractDo> selectByContractId(String identifier, String contractId);
}
