package com.caidaocloud.hr.service.contract.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 3/12/2024 5:35 下午
 */
@Data
public class ContractRenewRuleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("提前天数")
    private Integer advance;

    @ApiModelProperty("匹配条件")
    private String condition;

    @ApiModelProperty("匹配条件标签")
    private String conditionLabel;

    @ApiModelProperty("匹配条件表达式")
    private String conditionExp;

    @ApiModelProperty("消息配置")
    private String msgConfig;

}
