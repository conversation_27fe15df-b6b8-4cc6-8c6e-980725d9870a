package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.exception.ContractAutoStartException;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRelDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRuleDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractRenewRelDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractRenewRuleDomainService;
import com.caidaocloud.hr.service.contract.domain.service.LastContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetRelListVo;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.application.emp.ruleset.service.RuleSetService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RuleSetDo;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApply;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 2/12/2024 10:16 上午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractAutoRenewService {

    private static final ThreadLocal<List<MsgConfigDto>> configLocal = new ThreadLocal<>();

    private ContractRenewRuleDomainService contractRenewRuleDomainService;

    private ContractRenewRelDomainService contractRenewRelDomainService;

    private LastContractDomainService lastContractDomainService;

    private EmpContractTypeSetRelService empContractTypeSetRelService;

    private RuleSetService ruleSetService;

    private EmpWorkInfoService empWorkInfoService;

    private CompanyService companyService;

    private ContractService contractService;

    private ContractDomainService contractDomainService;

    private MsgNoticeService msgNoticeService;

    private CacheService cacheService;

    private Locker locker;

    /**
     * 触发自动续签任务、消息通知
     */
    public void doAutoRenewTask() {
        RuleSetDo rule = ruleSetService.getRuleSet();
        if (rule == null || rule.getAutoStart() == null || !rule.getAutoStart()) {
            log.info("[contractAutoRenew] auto start is not config");
            return;
        }
        List<Integer> advanceDays = contractRenewRuleDomainService.listAdvanceDay();
        if (CollectionUtils.isEmpty(advanceDays)) {
            log.info("[contractAutoRenew] renew rules is not config");
        } else {
            List<Long> endDates = advanceDays.stream().map(this::calculateEndTime).collect(Collectors.toList());
            fetchExpireContract(endDates);
        }
        configLocal.remove();
    }

    private void fetchExpireContract(List<Long> endDates) {
        ContractQueryDto queryDto = new ContractQueryDto();
        queryDto.setPageSize(20);
        queryDto.setPageNo(0);
        queryDto.setEndDates(endDates);
        // 审批中、审批拒绝、已终止、已失效
        queryDto.setContractNeStatusList(Arrays.asList(ContractStatusEnum.TERMINATED, ContractStatusEnum.CANCEL));
        PageResult<ContractDto> page;
        do {
            queryDto.setPageNo(queryDto.getPageNo() + 1);
            log.info("[contractAutoRenew] fetch last contract at page:{}", queryDto.getPageNo());
            page = lastContractDomainService.getContractPage(queryDto);
            if (CollectionUtils.isNotEmpty(page.getItems())) {
                handleContractRenew(page.getItems());
            } else {
                log.info("[contractAutoRenew] fetch last contract is empty at page:{}", queryDto.getPageNo());
            }
        } while (CollectionUtils.isNotEmpty(page.getItems()));
    }

    private long calculateEndTime(Integer advanceDay) {
        ZoneOffset zoneOffset = OffsetDateTime.now().getOffset();
        LocalDateTime time = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        return time.plusDays(Optional.ofNullable(advanceDay).orElse(0)).toEpochSecond(zoneOffset) * 1000L;
    }

    private void handleContractRenew(List<ContractDto> contractDtoList) {
        List<String> empIds = Sequences.sequence(contractDtoList).map(item -> item.getOwner().getEmpId()).toList();
        Map<String, EmpWorkInfoDo> empMap = empWorkInfoService.getNoLeaveEmpWorkInfoByEmpIds(empIds, DateUtil.getCurrentTimestamp())
                .stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, obj -> obj, (a, b) -> a));
        for (ContractDto item : contractDtoList) {
            List<ContractRenewRelDo> ruleRelList = fetchEmpRenewRule(item.getOwner().getEmpId());
            if (CollectionUtils.isEmpty(ruleRelList)) {
                log.warn("[contractAutoRenew] un resolve emp renew rule, empId: {}", item.getOwner().getEmpId());
                continue;
            }
            List<String> ruleIds = ruleRelList.stream().map(ContractRenewRelDo::getRenewRuleId).distinct().collect(Collectors.toList());
            List<ContractRenewRuleDo> ruleDoList = contractRenewRuleDomainService.selectByIds(ruleIds);
            List<ContractRenewRuleDo> filterList = Optional.ofNullable(ruleDoList).map(it ->
                    it.stream().filter(o1 -> calculateEndTime(o1.getAdvance()) == item.getEndDate())
                            .collect(Collectors.toList())).orElse(Lists.list());
            if (CollectionUtils.isEmpty(filterList)) {
                log.warn("[contractAutoRenew] not found suit renew rule, empId: {}", item.getOwner().getEmpId());
                return;
            }
            EmpWorkInfoDo empWorkInfo = empMap.getOrDefault(item.getOwner().getEmpId(), null);
            if (empWorkInfo == null) {
                log.warn("[contractAutoRenew] un resolve empWork info");
                continue;
            }
            EmpSimple empSimple = new EmpSimple();
            empSimple.setEmpId(empWorkInfo.getEmpId());
            BatchRenewalDto dto = new BatchRenewalDto();
            try {
                boolean verifyStatus = verifyContractRule(empWorkInfo, item, dto);
                if (!verifyStatus) {
                    log.error("[contractAutoRenew] verify renew rule failed");
                    continue;
                }
                dto.setCompany(item.getBid());
                dto.setEmpList(Lists.list(empSimple));
                dto.setOpenWorkflow(true);
                log.info("自动发起合同,empId={},contractId={},renewalData={}", empWorkInfo.getEmpId(), item.getBid(), dto);
                List<ContractDo> contractDos = contractService.batchRenewal(dto);
                if (CollectionUtils.isNotEmpty(contractDos)) {
                    ContractDo contractDo = contractDos.get(0);
                    log.info("[contractAutoRenew] send contract notice, bid:{}", contractDo.getBid());
                    sendSuccessNotice(empWorkInfo, contractDo, filterList);
                }
            } catch (Exception e) {
                log.error("自动发起合同失败，empId={},contractId={}", empWorkInfo.getEmpId(), item.getBid(), e);
                sendErrorNotice(empWorkInfo, item, String.format("发起续签流程失败:%s", e.getMessage()));
            }
        }
    }


    private List<ContractRenewRelDo> fetchEmpRenewRule(String empId) {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        String cacheRuleKey = String.format("contract_renew_rule_%s_%s", tenantId, empId);
        String cacheRuleValue = cacheService.getValue(cacheRuleKey);
        if (StringUtil.isNotEmpty(cacheRuleValue)) {
            return FastjsonUtil.toList(cacheRuleValue, ContractRenewRelDo.class);
        } else {
            List<ContractRenewRelDo> ruleList = contractRenewRelDomainService.listByEmpId(empId);
            cacheService.cacheValue(cacheRuleKey, FastjsonUtil.toJson(ruleList), 60L * 10);
            return ruleList;
        }
    }

    private boolean verifyContractRule(EmpWorkInfoDo empWorkInfoDo, ContractDto contractDto, BatchRenewalDto batchRenewalDto) {
        try {
            verifyContractSet(empWorkInfoDo, contractDto, batchRenewalDto)
                    .verifyEmpStatus(empWorkInfoDo)
                    .verifyLinkContract(contractDto);
        } catch (Exception e) {
            sendErrorNotice(empWorkInfoDo, contractDto, e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 发送成功消息
     */
    private void sendSuccessNotice(EmpWorkInfoDo workInfoDo, ContractDo contractDo, List<ContractRenewRuleDo> ruleList) {
        List<String> msgConfigs = ruleList.stream().map(ContractRenewRuleDo::getMsgConfig).flatMap(it -> Arrays.stream(it.split(",")))
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(msgConfigs)) {
            log.info("[contractAutoRenew] contract send notice config is empty");
            return;
        }
        Map<String, String> params = contractDomainService.genContractNoticeParam(contractDo, workInfoDo, System.currentTimeMillis());
        // 发送消息
        for (String msgConfig : msgConfigs) {
            msgNoticeService.sendMsgNoticeEvent(msgConfig, Lists.list(contractDo.getOwner().getEmpId()), params, "hr", 0);
        }
    }


    /**
     * 发送失败消息
     */
    private void sendErrorNotice(EmpWorkInfoDo workInfoDo, ContractDto contractDto, String errorMsg) {
        List<MsgConfigDto> msgConfigList = Optional.ofNullable(configLocal.get()).orElseGet(() -> {
           List<MsgConfigDto> list = msgNoticeService.getMsgConfigList(NoticeType.CONTRACT_AUTO_RENEW_ERROR);
           configLocal.set(list);
           return list;
        });
        if (CollectionUtils.isEmpty(msgConfigList)) {
            log.info("[contractAutoRenew] msg config list is empty, empId:{}", workInfoDo.getEmpId());
            return;
        }
        ContractDo contractDo = FastjsonUtil.convertObject(contractDto, ContractDo.class);
        Map<String, String> params = contractDomainService.genContractNoticeParam(contractDo, workInfoDo, System.currentTimeMillis());
        params.put("failedReason", errorMsg);
        for (MsgConfigDto configDto : msgConfigList) {
            msgNoticeService.sendMsgNoticeEvent(configDto.getBid(), Lists.list(workInfoDo.getEmpId()), params, "hr", 0);
        }
    }

    /**
     * 检查是否有对应历史续签单据
     * @param contractDto
     */
    private void verifyLinkContract(ContractDto contractDto) {
        List<ContractDo> linkContract = contractDomainService.getLinkContracts(Lists.list(contractDto.getBid()),
                Lists.list(ApprovalStatusEnum.PASSED.getIndex().toString(),
                        ApprovalStatusEnum.IN_APPROVAL.getIndex().toString(), ApprovalStatusEnum.REJECTED.getIndex().toString()));
        if (CollectionUtils.isNotEmpty(linkContract)) {
            boolean rejectStatus = Optional.of(linkContract).map(it -> it.stream().anyMatch(o1 -> o1.getApprovalStatus().getValue().equals(ApprovalStatusEnum.REJECTED.getIndex().toString())))
                    .orElse(false);
            throw new ContractAutoStartException(rejectStatus ? "续签合同被拒绝" : "已存在续签合同");
        }
    }


    /**
     * 有审批中的离职单据或者员工有对应的离职日期字段
     * @param empWorkInfoDo
     * @return
     */
    private ContractAutoRenewService verifyEmpStatus(EmpWorkInfoDo empWorkInfoDo) {
        List<TerminationApply> applyList = TerminationApply.listSelfApplyByEmp(empWorkInfoDo.getEmpId());
        boolean processApply = applyList.stream().anyMatch(it -> it.getStatus() == TerminationStatus.IN_PROCESS);
        if (processApply || empWorkInfoDo.getLeaveDate() != null) {
            throw new ContractAutoStartException("员工存在离职单据或者离职日期");
        }
        return this;
    }

    /**
     * 合同公司、合同类型检查
     * @param empWorkInfo
     * @param contractDto
     * @param batchRenewalDto
     * @return
     */
    private ContractAutoRenewService verifyContractSet(EmpWorkInfoDo empWorkInfo, ContractDto contractDto, BatchRenewalDto batchRenewalDto) {
        List<ContractTypeSetRelListVo> contractTypeList = empContractTypeSetRelService.getEmpContractTypeList(empWorkInfo.getEmpId(),
                contractDto.getCompany(), "0");
        if (CollectionUtils.isEmpty(contractTypeList)) {
            log.warn("合同类型为空,empId={}, workNo: {}, contractId={}", empWorkInfo.getEmpId(), empWorkInfo.getWorkno(), contractDto.getBid());
            throw new ContractAutoStartException("无匹配的合同类型");
        }
        CompanyDo company = companyService.getCompanyById(contractDto.getCompany());
        if (company == null) {
            log.warn("合同公司不存在,empId={},contractId={}", empWorkInfo.getEmpId(), contractDto.getBid());
            throw new ContractAutoStartException("合同公司不存在");
        }
        ContractTypeSetRelListVo contractTypeSetVo = contractTypeList.get(0);
        batchRenewalDto.setContractPeriod(contractTypeSetVo.getContractPeriod());
        batchRenewalDto.setContractSetId(contractTypeSetVo.getBid());
        batchRenewalDto.setPeriodType(contractTypeSetVo.getPeriodType());
        return this;
    }


    /**
     * 刷新匹配关系
     * @param tenantId
     */
    public void doRefreshTenantRuleRelAction(String tenantId) {
        String lockerKey = String.format("contract_renew_rule_rel_refresh_tenant_lock_key_%s", tenantId);
        Lock lock = locker.getLock(lockerKey);
        try {
            boolean locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (locked) {
                List<ContractRenewRuleDo> ruleDoList = contractRenewRuleDomainService.selectList();
                if (CollectionUtils.isNotEmpty(ruleDoList)) {
                    // 移除匹配关系
                    for (ContractRenewRuleDo ruleDo : ruleDoList) {
                        try {
                            contractRenewRelDomainService.doRefreshRuleRelAction(ruleDo, tenantId);
                        } catch (Exception e) {
                            log.error("[contractRenewRel] refresh rel error, ruleId: {}, msg:{}", ruleDo.getBid(), e.getMessage());
                        }
                    }
                } else {
                    removeVersionRule(null, null);
                }
            }
        } catch (InterruptedException e) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
        } finally {
            lock.unlock();
        }
    }

    /**
     * 移除消息关联rel
     * @param version
     * @param ruleId
     */
    private void removeVersionRule(Integer version, String ruleId) {
        log.error("[contractRenewRel] remove rule emp rel data, version:{}, ruleId:{}", version, ruleId);
        contractRenewRelDomainService.deleteRuleRel(version == null ? null : Objects.toString(version), ruleId);
    }
}
