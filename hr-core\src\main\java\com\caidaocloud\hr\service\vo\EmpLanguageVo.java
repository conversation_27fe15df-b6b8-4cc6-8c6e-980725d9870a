package com.caidaocloud.hr.service.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工语言能力Vo")
public class EmpLanguageVo {
    @ApiModelProperty("员工语言能力ID")
    private String bid;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("语种")
    private DictSimple language;
    @ApiModelProperty("听说能力")
    private DictSimple hearSkill;
    @ApiModelProperty("读写能力")
    private DictSimple readWriteSkill;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}