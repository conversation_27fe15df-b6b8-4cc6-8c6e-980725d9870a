package com.caidaocloud.hr.service.contract.application.enums;

public enum ApprovalStatusEnum {
    IN_APPROVAL(0, "审批中"),
    PASSED(1, "已通过"),
    REJECTED(2, "已拒绝"),
    REVOKE(3, "已撤销");

    private Integer index;
    private String name;

    ApprovalStatusEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (ApprovalStatusEnum c : ApprovalStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
