package com.caidaocloud.hr.service.employee.application.dataimport.service;

import com.caidaocloud.hr.service.contract.application.event.dto.ContractMessageDto;
import com.caidaocloud.hr.service.contract.application.event.publish.ContractPublish;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
public class ContractEsignService {
    @Resource
    private ContractPublish contractPublish;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    public void contractCirculation(ContractDo contractDo,
            Map<String, EmpBasicInfoDo> oldEmpInfoMap, Map<String, EmpWorkInfoDo> oldWorkInfoMap,
            Long dataTime){
        // 流程通过的，流转到电子签待发起列表
        ContractMessageDto messageDto = ObjectConverter.convert(contractDo, ContractMessageDto.class);
        messageDto.setTenantId(contractDo.getTenantId());
        messageDto.setEmp(contractDo.getOwner());
        messageDto.setContract(contractDo.getBid());
        String workno = messageDto.getEmp().getWorkno();
        String empId = messageDto.getEmp().getEmpId();
        messageDto.setUserId(Long.valueOf(contractDo.getCreateBy()));

        EmpBasicInfoDo basicInfo = oldEmpInfoMap.get(workno);
        EmpWorkInfoDo empWorkInfo = null;
        EmpPrivateInfoDo empPrivateInfo = null;

        if(null != basicInfo){
            messageDto.setPhone(basicInfo.getPhone());
            messageDto.setCompanyEmail(basicInfo.getCompanyEmail());
        }

        empWorkInfo = oldWorkInfoMap.get(workno);
        if(null != empWorkInfo){
            // 补全职务信息
            messageDto.setJob(empWorkInfo.getJob());
            messageDto.setJobTxt(empWorkInfo.getJobTxt());
            messageDto.setWorkHour(empWorkInfo.getWorkHour());
            messageDto.setWorkplace(empWorkInfo.getWorkplace());
            messageDto.setWorkplaceTxt(empWorkInfo.getWorkplaceTxt());

            // 审批通过的修改，且合同公司有编号的，修改合同公司
            empWorkInfoDomainService.updateContractCompany(empWorkInfo, contractDo.getCompany(),
                    contractDo.getCompanyTxt(), contractDo.getProbation(), contractDo.getProbationPeriodEndDate(), dataTime);
        }

        // 查询个人信息
        empPrivateInfo = empPrivateInfoDomainService.getByEmpId(empId);
        if (empPrivateInfo != null) {
            messageDto.setDisability(empPrivateInfo.getDisability());
            messageDto.setGuardianName(empPrivateInfo.getGuardianName());
            messageDto.setGuardianPhone(empPrivateInfo.getGuardianPhone());
            messageDto.setGuardianEmail(empPrivateInfo.getGuardianEmail());
            // 国籍
            messageDto.setNationality(empPrivateInfo.getNationality());
        }

        long createTime = contractDo.getCreateTime();
        messageDto.setCreateTime(0 == createTime ? System.currentTimeMillis() : createTime);
        messageDto.setUpdateTime(0 == contractDo.getUpdateTime() ? createTime : contractDo.getUpdateTime());
        contractPublish.contractInitiated(messageDto);
    }
}
