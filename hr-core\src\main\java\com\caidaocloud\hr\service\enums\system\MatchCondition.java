package com.caidaocloud.hr.service.enums.system;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.match.ConditionComponentEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum MatchCondition {

    NAME("姓名", "name", Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpWorkInfo","name"),
    WORK_NO("工号", "workNo",
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpWorkInfo","workno"),
    EMPLOY_TYPE("用工类型", "empType",
            Lists.list(ConditionOperatorEnum.IN, ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.DICT_SELECTOR,
            Maps.map("belongModule", "Employee",
                    "typeCode", "EmployType"), "entity.onboarding.EmpWorkInfo","empType.dict.value"),
    ORG("任职组织", "org",
            Lists.list(ConditionOperatorEnum.IN, ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.ORG, Maps.map(), "entity.onboarding.EmpWorkInfo","organize"),
    POST("岗位", "post",
            Lists.list(ConditionOperatorEnum.IN, ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.POS_SELECTOR, Maps.map(), "entity.onboarding.EmpWorkInfo","post"),
    COMPANY("合同公司", "company",
            Lists.list(ConditionOperatorEnum.IN, ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.COMPANY, Maps.map(), "entity.onboarding.EmpWorkInfo","company"),
    CONTRACT_TYPE("合同类型", "contractType",
            Lists.list(ConditionOperatorEnum.IN, ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.DICT_SELECTOR,
            Maps.map("belongModule", "Employee",
                    "typeCode", "ContractType"), "entity.onboarding.EmpWorkInfo","contractType.dict.value"),
    HEALTH_REQUIREMENT("职业健康体检要求", "healthRequirement",
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpPrivateInfo","rohExamination"),
    HEALTH_CARD("健康证", "healthCard",
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpWorkInfo","healthyCertificates"),
    DORMITORY("住宿情况", "dormitory",
            Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpWorkInfo","accommodation"),
    JOB_KEY("Job key", "jobKey", Lists.list(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE),
            ConditionComponentEnum.STRING_INPUT, Maps.map(), "entity.onboarding.EmpWorkInfo", "jobKey"),
    ;

    private String text;
    private String code;
    private List<ConditionOperatorEnum> operators;
    private ConditionComponentEnum component;
    private String identifier;
    private String empProperty;
    private Map<String, Object> dataSourceParams;

    MatchCondition(String text, String code, List<ConditionOperatorEnum> operators,
                   ConditionComponentEnum component, Map<String, Object> dataSourceParams,String identifier,
                   String empProperty) {
        this.text = text;
        this.code = code;
        this.operators = operators;
        this.component = component;
        this.dataSourceParams = dataSourceParams;
        this.identifier = identifier;
        this.empProperty = empProperty;
    }


    public static MatchCondition codeOf(String code) {
        return Arrays.stream(MatchCondition.values())
                .filter(it->it.getCode().equals(code))
                .findFirst().orElseThrow(()->new ServerException("错误的入职匹配条件"));
    }

    public boolean match(Map<String, String> preEmp, ConditionOperatorEnum symbol, String compareValue) {
        String value = preEmp.get(empProperty);
        switch (symbol) {
            case EQ:
                return StringUtils.equals(value, compareValue);
            case NE:
                return !StringUtils.equals(value, compareValue);
            case IN:
                if (StringUtils.isEmpty(compareValue)) {
                    return false;
                } else {
                    return Lists.list(compareValue.split(",")).contains(value);
                }
            default:
                throw new ServerException("入职包匹配条件不支持的比较符");
        }
    }
}
