package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@FeignClient(
        value = "caidaocloud-auth-service",
        fallback = AuthDataScopeFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "authDataScopeFeign"
)
public interface AuthDataScopeFeignClient {
    @GetMapping("/api/auth/v1/subject/resource/scope/detail")
    Result<Map<String, String>> getDataScope(@RequestParam("subjectId") Long subjectId,
                                             @RequestParam("parentCode") String parentCode);

    // 获取用户数据范围权限
    @GetMapping("/api/auth/v1/subject/role/scope/list")
    @ResponseBody
    Result<List<AuthRoleScopeFilterDetail>> getScopeBySubject(@RequestParam("identifier") String identifier,
                                                              @RequestParam("subjectId") Long subjectId,
                                                              @RequestParam(value = "dymaicTarget") String dymaicTarget);

    @GetMapping("/api/auth/v1/subject/resource/codes/all")
    Result<List<String>> getAuthCodes();
}
