package com.caidaocloud.hr.service.employee.application.emp.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode
public class StatisticsBaseDto {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("名称")
    private String text;
    @ApiModelProperty("值")
    private String value;
}
