package com.caidaocloud.hr.service.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工附件信息VO")
public class EmpFileAttachmentVo {
    @ApiModelProperty("员工附件ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("证件照正面")
    private Attachment idCardFace;

    @ApiModelProperty("证件照反面")
    private Attachment idCardBackFace;

    @ApiModelProperty("学历证书")
    private Attachment academicCertificate;

    @ApiModelProperty("学位证书")
    private Attachment diploma;

    @ApiModelProperty("银行卡")
    private Attachment bankCard;

    @ApiModelProperty("扩展字段")
    private Map ext = new LinkedHashMap();
}
