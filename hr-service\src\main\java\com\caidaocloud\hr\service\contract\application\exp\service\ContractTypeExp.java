package com.caidaocloud.hr.service.contract.application.exp.service;

import com.caidaocloud.hr.service.contract.interfaces.dto.ConditionExpDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.googlecode.totallylazy.Sequences;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
// @Component
public class ContractTypeExp extends AbsContractExp {

    @Override
    public List<Long> filterEmpIds(List<Long> empIds, ConditionExpDto exp) {
        return null;
        // return findLastContract(empIds, exp).filter(it ->
        //         empIds.contains(Long.valueOf(((EmpSimple) fetchDataProperty(it, "owner")).getEmpId()))
        //                 && optFilter(fetchDataProperty(it, "contractSettingType"), exp)
        // ).map(it -> Long.valueOf(((EmpSimple) fetchDataProperty(it, "owner")).getEmpId())).toList();
    }

    @Override
    public boolean optFilter(Object value, ConditionExpDto exp) {
        DictSimple dict = (DictSimple) value;
        if (dict == null) {
            return false;
        }
        switch (exp.getSymbol()) {
            case EQ:
                return exp.getValue().equals(dict.getValue());
            case NE:
                return !exp.getValue().equals(dict.getValue());
            case IN:
                return Sequences.sequence((((List) exp.getValue()))).toSet().contains(dict.getValue());
            case GE:
            case GT:
            case LE:
            case LT:
            default:
                return false;
        }

    }

}
