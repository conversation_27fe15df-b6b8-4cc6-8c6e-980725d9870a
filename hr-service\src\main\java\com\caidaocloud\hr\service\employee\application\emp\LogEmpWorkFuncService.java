package com.caidaocloud.hr.service.employee.application.emp;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.record.core.service.IParseFunction;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LogEmpWorkFuncService implements IParseFunction {
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Override
    public String functionName() {
        return "workInfo";
    }

    @Override
    public String apply(String empId) {
        EmpWorkInfoDo workInfoDo = empWorkInfoDomainService.getEmpWorkInfo(empId, System.currentTimeMillis());
        return workInfoDo.getName() + "(" + workInfoDo.getWorkno() + ")";
    }
}
