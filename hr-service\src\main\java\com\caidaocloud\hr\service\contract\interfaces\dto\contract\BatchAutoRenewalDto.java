package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@Data
public class BatchAutoRenewalDto {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("合同类型ID")
    private String contractTypeSet;

    @ApiModelProperty("合同类型名称")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同类型")
    private DictSimple contractSettingType;

    @ApiModelProperty("合同类别")
    private DictSimple contractType;

    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;

    @ApiModelProperty("合同期限")
    private Integer contractPeriod;

    @ApiModelProperty("有无试用期")
    private Boolean probation;

    @ApiModelProperty("试用期")
    private Integer probationPeriod;

    @ApiModelProperty("合同接收日期类型")
    private EnumSimple endDateType;

    @ApiModelProperty("是否根据预计毕业日期判断")
    private Boolean baseExpectGraduateDate;

    /**
     * 试用期期限
     */
    private EnumSimple probationDeadline;
}
