package com.caidaocloud.hr.service.confirmation.application.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.caidaocloud.hr.service.confirmation.application.ConfirmationConfigService;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationService;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationAppliedBy;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationApplyDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationImportDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationTemplateDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/6/19
 */
@Slf4j
@Service
public class ConfirmationImportService {
	@Autowired
	private ConfirmationConfigService confirmationConfigService;
	@Autowired
	private ConfirmationService confirmationService;

	public void importApply(ConfirmationImportDto dto) {
		ConfirmationConfig config = confirmationConfigService.one(ConfirmationAppliedBy.BY_AGENT);
		Map<String, PropertyDataType> dataTypeMap = config.fetchAllBusinessField().stream()
				.collect(Collectors.toMap(field -> {
					if ("other".equals(field.getType())) {
						return "other#" + StringUtil.underlineToHump(field.getProperty().toLowerCase());
					}
					else {
						return field.getType() + '#' + field.getProperty();
					}
				}, ConfirmationChangeFieldDef::getDataType));
		dataTypeMap.put("other#confirmationDate", PropertyDataType.Timestamp);
		dataTypeMap.put("other#probationPeriodEndDate", PropertyDataType.Timestamp);
		List<ConfirmationChangeField> beforeData = confirmationService.getTemplateData(new ConfirmationTemplateDto(config.getBid(), dto.getEmp()
				.getEmpId())).getData();
		Map<String, ConfirmationChangeField> beforeMap = beforeData.stream()
				.collect(Collectors.toMap(field -> field.getType() + '#' + field.getProperty(), field -> field));
		List<ConfirmationImportDto.ImportDataItem> afterData = dto.getData();
		List<ConfirmationChangeField> confirmations = new ArrayList<>();
		List<ConfirmationChangeField> salary = new ArrayList<>();
		List<ConfirmationChangeField> work = new ArrayList<>();
		log.info("afterData: {}, dataTypeMap={}", FastjsonUtil.toJson(afterData), FastjsonUtil.toJson(dataTypeMap));
		for (ConfirmationImportDto.ImportDataItem data : afterData) {
			ConfirmationChangeField field = FastjsonUtil.convertObject(data, ConfirmationChangeField.class);
			String key = data.getType().toLowerCase() + '#' + data.getProperty();
			beforeMap.computeIfPresent(key, (k, v) -> {
				field.setBefore(v.getBefore());
				field.setBeforeTxt(v.getBeforeTxt());
				field.setEnable(true);
				return v;
			});
			if ("leaderOrganize".equals(field.getProperty()) || "leaderPost".equals(field.getProperty())) {
				field.setPropertyDataType(PropertyDataType.String);
			}
			else {
				PropertyDataType dataType = dataTypeMap.get(key);
				field.setPropertyDataType(dataType);
				switch (dataType) {
				case Enum:
					EnumSimple enumSimple = new EnumSimple();
					enumSimple.setValue(String.valueOf(data.getValue()));
					field.setAfter(enumSimple);
					break;
				case Boolean:
					field.setAfter(Boolean.valueOf(String.valueOf(data.getValue())));
					break;
				case Timestamp:
					field.setAfter(Long.valueOf(String.valueOf(data.getValue())));
					break;
				default:
					field.setAfter(data.getValue());
				}
			}
			if ("other".equalsIgnoreCase(data.getType())) {
				field.setType("other");
				confirmations.add(field);
			}
			else if ("work".equalsIgnoreCase(data.getType())) {
				field.setType("work");
				work.add(field);
			}
			else if ("salary".equalsIgnoreCase(data.getType())) {
				field.setType("salary");
				salary.add(field);
			}
		}
		ConfirmationApplyDto confirmationApplyDto = ObjectConverter.convert(dto, ConfirmationApplyDto.class);
		confirmationApplyDto.setDefId(config.getBid());
		confirmationApplyDto.setConfirmations(confirmations);
		confirmationApplyDto.setSalary(salary);
		confirmationApplyDto.setWork(work);
		confirmationService.saveApply(confirmationApplyDto);
	}
}
