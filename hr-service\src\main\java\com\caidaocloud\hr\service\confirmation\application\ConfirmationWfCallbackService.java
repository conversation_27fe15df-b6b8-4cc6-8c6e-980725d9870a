package com.caidaocloud.hr.service.confirmation.application;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationEsService;
import com.caidaocloud.hr.service.confirmation.application.service.EmpConfirmationService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/6/7
 */
@Slf4j
@Service
public class ConfirmationWfCallbackService {
	@Autowired
	private ConfirmationService confirmationService;
	@Autowired
	private ConfirmationEsService confirmationEsService;
	@Autowired
	private EmpConfirmationService empConfirmationService;
	@Autowired
	private Locker locker;
	@Resource
	private ArchiveEventProducer archiveEventProducer;
	private final String CALL_BACK_KEY = "CALLBACK_%s";

	public void callback(String tenantId, String businessKey, WfCallbackTriggerOperationEnum callbackType) {
		// 设置回调用户信息
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId(tenantId);
		// 回调默认用户id为 0
		userInfo.setUserId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		String key = String.format(CALL_BACK_KEY,businessKey);
		Lock lock = locker.getLock(key);
        var isLocked = false;
		try {
            isLocked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.info("not get lock, businessKey={} tenantId={} callbackType={}", businessKey, tenantId, callbackType);
                return;
            }
            DataSimple dataSimple = null;
            if (WfCallbackTriggerOperationEnum.APPROVED.equals(callbackType) || WfCallbackTriggerOperationEnum.REFUSED.equals(callbackType)) {
                dataSimple = confirmationService.getConfirmationDataByBusinessId(businessKey);
				if (dataSimple != null && StringUtils.isNotBlank(dataSimple.getCreateBy())){
					var securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
					securityUserInfo.setUserId(Long.valueOf(dataSimple.getCreateBy()));
					SecurityUserUtil.setSecurityUserInfo(userInfo);
				}
			}
            switch (callbackType) {
                case APPROVED:
                    approve(businessKey, dataSimple);
                    break;
                case REFUSED:
                    refuse(businessKey, dataSimple);
                    break;
                case REVOKE:
                case ERROR:
                case TIMED_TASK:
                default:
			}
		} catch (Exception e) {
            log.error("callback occurs error", e);
			throw new ServerException("callback occurs error", e);
		} finally {
            if (isLocked  && lock != null) {
                lock.unlock();
            }
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	private void approve(String businessKey, DataSimple dataSimple) {
		EnumSimple approvalStatus = ((EnumSimple) dataSimple.getProperties()
				.get("other$approvalStatus"));
		if (!WfProcessStatusEnum.IN_PROCESS.value.equals(approvalStatus.getValue())) {
			throw new ServerException("Failed to approve confirmation apply");
		}
		approvalStatus.setValue(WfProcessStatusEnum.APPROVE.value);
		dataSimple.getProperties().add("other$approvalStatus", approvalStatus);
		dataSimple.setUpdateTime(System.currentTimeMillis());
		confirmationEsService.updateConfirmationApply(dataSimple);
		empConfirmationService.updateEmpInfoWithConfirmationApply(dataSimple);
		archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.REGULARIZATION, dataSimple.getBid());
	}

	private void refuse(String businessKey, DataSimple dataSimple) {
		EnumSimple approvalStatus = ((EnumSimple) dataSimple.getProperties()
				.get("other$approvalStatus"));
		if (!WfProcessStatusEnum.IN_PROCESS.value.equals(approvalStatus.getValue())) {
			throw new ServerException("Failed to refuse confirmation apply");
		}
		approvalStatus.setValue(WfProcessStatusEnum.REFUSE.value);
		dataSimple.getProperties().add("other$approvalStatus",approvalStatus);
		dataSimple.setUpdateTime(System.currentTimeMillis());
		confirmationEsService.updateConfirmationApply(dataSimple);
	}
}