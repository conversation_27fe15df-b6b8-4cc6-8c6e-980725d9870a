package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

import javax.annotation.Resource;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.enums.system.RegularStatusEnum;
import com.caidaocloud.hr.service.search.application.dto.EmpSearchInfoDto;
import com.caidaocloud.hr.service.search.application.dto.EntityDataChangeDto;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.util.EntityDataUtil;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
 import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import static com.caidaocloud.hr.service.employee.application.emp.constant.EmpConstants.EMP_DYNAMIC_REFRESHING_CODE;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/6/20
 */
@Service
@Slf4j
public class EmpDynamicRefreshService {
	@Resource
	private EmpWorkInfoDomainService empWorkInfoDomainService;
	@Resource
	private EmpSearchInfoRepository empSearchInfoRepository;
	@Resource
	private EmpDynamicService empDynamicService;
	@Resource
	private CacheService cacheService;

	@Resource(name = "dynamicTaskExecutor")
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	private Future oldTask = null;

	private static final Map<String, RegularStatusEnum> regularMap;

	private static final Map<String, EmpStatusEnum> statusMap;

	static {
		regularMap = new HashMap<>();
		for (RegularStatusEnum c : RegularStatusEnum.values()) {
			regularMap.put(String.valueOf(c.getIndex()), c);
		}
		 statusMap = new HashMap<>();
		for (EmpStatusEnum c : EmpStatusEnum.values()) {
			statusMap.put(String.valueOf(c.getIndex()), c);
		}
	}

	public void doRefresh(){
		try {
			cacheService.cacheValue(EMP_DYNAMIC_REFRESHING_CODE, "EMP_DYNAMIC_REFRESHING_CODE");
			val metadataService = SpringUtil.getBean(MetadataOperatorService.class);
			val metadata = metadataService.load("entity.hr.EmpWorkInfo");

			log.info("开始全量同步动态表头,租户：{}", SecurityUserUtil.getSecurityUserInfo().getTenantId());
			EmpPageQueryDto dto = new EmpPageQueryDto();
			dto.setDateTime(-1L);
			boolean flag = true;
			int pageSize = 500;
			int pageNo = 0;
			int total = 0;
			while (flag) {
				if (Thread.currentThread().isInterrupted()) {
					log.info("接收到中断信号，中断全量同步逻辑");
					return;
				}
				dto.setPageSize(pageSize);
				pageNo++;
				dto.setPageNo(pageNo);
				PageResult<EmpWorkInfoDo> page = empWorkInfoDomainService.selectPage(dto);
				try {
					List<EmpSearchInfoDto> addList = Sequences.sequence(page.getItems()).map(data -> empDynamicService.buildEmpEsData(data, metadata, statusMap, regularMap)).toList();
					List<EmpSearchInfoPo> dataList = ObjectConverter.convertList(addList, EmpSearchInfoPo.class);
					empSearchInfoRepository.save(dataList);
				} catch (Throwable e) {
					log.error("全量同步动态表头异常", e);
				}
				total += pageSize;
				if (page.getTotal() <= total) {
					flag = false;
				}
			}
		} catch (Throwable e) {
			log.error("全量同步动态表数据异常", e);
		} finally {
			log.info("结束全量同步表头,租户：{}", SecurityUserUtil.getSecurityUserInfo().getTenantId());
			cacheService.remove(EMP_DYNAMIC_REFRESHING_CODE);
		}
	}

	public void refresh() {
		if (oldTask != null && !oldTask.isDone()) {
			log.info("正在全量同步数据到es中，尝试中断线程");
			oldTask.cancel(true);
		}
		SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
		oldTask = threadPoolTaskExecutor.submit(() -> {
			try{
				SecurityUserUtil.setSecurityUserInfo(userInfo);
				doRefresh();
			}finally {
				SecurityUserUtil.removeSecurityUserInfo();
			}
		});
	}
}
