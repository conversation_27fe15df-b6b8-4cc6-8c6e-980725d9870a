package com.caidaocloud.hr.service.vo;

import lombok.Data;

@Data
public class EmpEmailRuleVo {
    private String bid;

    /**
     * 是否自动生成邮箱
     * 0--关闭(默认)，1--开启
     */
    private Boolean autoGenderEmail;

    /**
     * 生成规则启用时机
     * 1--员工信息启用
     * 2--入职时启用
     */
    private String ruleEnableTiming;

    /**
     * 邮箱生成节点
     * 1. 发起入职
     * 2. 入职完成
     * 3. 指定步骤完成
     */
    private String emailGenderNode;
}
