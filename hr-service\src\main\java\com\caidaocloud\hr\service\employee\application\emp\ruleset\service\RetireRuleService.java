package com.caidaocloud.hr.service.employee.application.emp.ruleset.service;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RetireRuleDo;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.RetireRuleDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.ruleset.RetireRuleDto;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.hrpaas.paas.match.ConditionNameMapping;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequences;

import org.springframework.beans.BeanUtil<PERSON>;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@Service
public class RetireRuleService {
	@Resource
	private RetireRuleDomainService retireRuleDomainService;
	@Resource
	private IConditionFeign conditionFeign;

	private final String RETIRE_CODE = "retire";

	public RetireRuleDo create(RetireRuleDto retireRuleDto) {
		RetireRuleDo ruleDo = ObjectConverter.convert(retireRuleDto, RetireRuleDo.class);
		List<ConditionDataVo> condition = conditionFeign.getConditionDataByCode(RETIRE_CODE, true).getData();
		List<ConditionNameMapping> mappingList = Sequences.sequence(condition)
				.map(c -> new ConditionNameMapping(c.getProperty(), c.getIdentifier(), c.getQueryProperty())).toList();
		ruleDo.setMapping(mappingList);
		return retireRuleDomainService.create(ruleDo);
	}

	public void update(RetireRuleDto retireRuleDto) {
		RetireRuleDo exist = getDetail(retireRuleDto.getBid());
		PreCheck.preCheckNotNull(exist, ErrorMessage.fromCode("caidao.exception.error_30006"));
		BeanUtils.copyProperties(retireRuleDto, exist);
		List<ConditionDataVo> condition = conditionFeign.getConditionDataByCode(RETIRE_CODE, true).getData();
		List<ConditionNameMapping> mappingList = Sequences.sequence(condition)
				.map(c -> new ConditionNameMapping(c.getProperty(), c.getIdentifier(), c.getQueryProperty())).toList();
		exist.setMapping(mappingList);
		retireRuleDomainService.update(exist);
	}

	public RetireRuleDo getDetail(String bid) {
		return retireRuleDomainService.getDetail(bid);
	}

	public List<RetireRuleDo> getList() {
		return retireRuleDomainService.getList();
	}

	public void delete(String bid) {
		RetireRuleDo exist = getDetail(bid);
		PreCheck.preCheckNotNull(exist, ErrorMessage.fromCode("caidao.exception.error_30006"));
		retireRuleDomainService.delete(exist);
	}

	public List<ConditionDataVo> listCondition(){
		return conditionFeign.getConditionDataByCode(RETIRE_CODE, false).getData();
	}
}
