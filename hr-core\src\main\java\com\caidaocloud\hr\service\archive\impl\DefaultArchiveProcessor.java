package com.caidaocloud.hr.service.archive.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.IArchiveProcessor;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;

import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 2:51 下午
 */
public class DefaultArchiveProcessor implements IArchiveProcessor {

    @Override
    public String businessLine() {
        return "default";
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        return null;
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        return null;
    }
}
