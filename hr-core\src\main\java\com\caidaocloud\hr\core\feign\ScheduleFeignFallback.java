package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.web.Result;

/**
 *
 * <AUTHOR>
 * @date 2023/1/16
 */
public class ScheduleFeignFallback implements ScheduleFeignClient {

	@Override
	public Result<Boolean> addSchedule(ScheduleTaskDto scheduleTaskDto) {
		return Result.fail();
	}

	@Override
	public Result<Boolean> deletePage(String taskTopic, String taskId) {
		return Result.fail();
	}
}
