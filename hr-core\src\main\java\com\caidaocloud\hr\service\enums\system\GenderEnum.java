package com.caidaocloud.hr.service.enums.system;


/**
 * <AUTHOR>
 */

public enum GenderEnum {
    /**
     * 0-女
     */
    F(0, "女", "F"),

    /**
     * 1-男
     */
    M(1, "男", "M");

    private Integer index;
    private String name;
    private String code;

    GenderEnum(Integer index, String name, String code) {
        this.index = index;
        this.name = name;
        this.code = code;
    }

    public static String getName(Integer index) {
        for (GenderEnum c : GenderEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public static String getCode(Integer index) {
        for (GenderEnum c : GenderEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.code;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
