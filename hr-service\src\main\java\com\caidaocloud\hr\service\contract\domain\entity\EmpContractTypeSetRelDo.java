package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.contract.domain.repository.IEmpContractTypeSetRelRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Data
@Service
@Accessors(chain = true)
public class EmpContractTypeSetRelDo extends BaseDomainDoImpl {

    /**
     * 员工ID
     */
    private String empId;

    /**
     * 合同类型ID
     */
    private String contractTypeSet;

    /**
     * 合同类型名称
     */
    private String contractTypeSetTxt;

    private final static String IDENTIFIER = "entity.hr.EmpContractTypeSetRel";

    @Resource
    private IEmpContractTypeSetRelRepository empContractTypeSetRelRepository;

    @Override
    public BaseRepository getRepository() {
        return empContractTypeSetRelRepository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    protected DataSimple build() {
        return new EmpContractTypeSetRelDo();
    }

    public List<EmpContractTypeSetRelDo> getEmpContractTypeList(String empId){
        return empContractTypeSetRelRepository.getEmpContractTypeList(empId,IDENTIFIER);
    }

    public void batchSave(List<EmpContractTypeSetRelDo> data) {
        empContractTypeSetRelRepository.batchInsert(getDoIdentifier(), data);
    }

    public void deleteByTypeSetBid(String bid) {
        empContractTypeSetRelRepository.deleteByTypeSetBid(getDoIdentifier(), bid);
    }

    public List<EmpContractTypeSetRelDo> selectByEmp(List<String> empIds) {
        return empContractTypeSetRelRepository.selectByEmpId(IDENTIFIER, empIds);
    }

  /*  *//**
     * 根据上一份合同的合同公司，签订类型获取自动续签的合同设置
     * @param lastContracts 上一份合同集合
     * @param signType 签订类型
     * @return
     *//*
    public List<EmpContractTypeSetRelDo> checkContractTypeSetRel(List<LastContractDo> lastContracts, SignTypeEnum signType) {
        Map<String, LastContractDo> contractMap = lastContracts.stream()
                .collect(Collectors.toMap(contract -> contract.getOwner().getEmpId(), contract -> contract, (a, b) -> a));
        List<String> empIds = Lists.list(contractMap.keySet());

        List<EmpContractTypeSetRelDo> empRelList = selectByEmp(empIds);


        Map<String, List<EmpContractTypeSetRelDo>> empRelMap = empRelList.stream().collect(Collectors.groupingBy(EmpContractTypeSetRelDo::getEmpId));
        return empIds.stream().filter(emp -> {
            List<EmpContractTypeSetRelDo> relList = empRelMap.get(emp);
            return relList != null && relList.size() == 1;
        }).map(emp -> empRelMap.get(emp).get(0)).collect(Collectors.toList());

    }*/
}
