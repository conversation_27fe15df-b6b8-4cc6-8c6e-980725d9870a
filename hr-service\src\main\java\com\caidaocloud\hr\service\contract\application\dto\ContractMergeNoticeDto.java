package com.caidaocloud.hr.service.contract.application.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.googlecode.totallylazy.Pair;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
@Data
public class ContractMergeNoticeDto {
	// 合同变量
	private Map<String, String> map;
	// 合并通知变量
	private List<Pair<EmpWorkInfoDo, ContractDo>> list = new ArrayList<>();
	// 通知对象
	private String empId;
}
