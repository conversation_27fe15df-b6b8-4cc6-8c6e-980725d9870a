package com.caidaocloud.hr.service.archive.interfaces.facade;

import com.caidaocloud.hr.service.archive.application.ArchiveDataService;
import com.caidaocloud.hr.service.archive.application.ArchiveService;
import com.caidaocloud.hr.service.archive.beans.ArchiveEvent;
import com.caidaocloud.hr.service.archive.interfaces.dto.ArchiveDto;
import com.caidaocloud.hr.service.archive.interfaces.dto.ArchiveQueryDto;
import com.caidaocloud.hr.service.archive.interfaces.vo.ArchivePreviewVo;
import com.caidaocloud.hr.service.archive.interfaces.vo.ExportVo;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.UUID;

/**
 * created by: FoAng
 * create time: 18/6/2024 3:00 下午
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/v1/archive/data")
@Api(value = "/api/hr/v1/archive/data", description = "文件归档", tags = "文件归档")
public class ArchiveDataController {

    @Resource
    private ArchiveDataService archiveDataService;

    @Resource
    private ArchiveService archiveService;

    @GetMapping("/businessLine")
    @ApiOperation("获取所属业务")
    public Result<List<String>> fetchBusinessLine() {
        return Result.ok(archiveService.getAllBusinessLine());
    }

    @GetMapping("/businessType")
    @ApiOperation("获取所属业务类型")
    public Result<List<String>> fetchBusinessType(@RequestParam(required = false) String businessType) {
        return Result.ok(ArchiveStandardLine.getBusinessTypes(businessType));
    }

    @PostMapping("/init")
    @ApiOperation("文件归档初始化")
    public Result<?> startArchiveInit() {
        archiveService.startArchiveInit();
        return Result.ok(true);
    }

    @PostMapping("/one")
    @ApiOperation("归档历史数据")
    public Result<?> archiveData(@RequestBody ArchiveDto dto) {
        ArchiveEvent archiveEvent = new ArchiveEvent();
        archiveEvent.setArchivePolicy(dto.getArchivePolicy());
        archiveEvent.setBusinessId(dto.getBusinessId());
        archiveEvent.setBusinessLine(dto.getBusinessLine());
        archiveEvent.setBusinessModule(dto.getArchiveLine().name());
        archiveService.dispatchArchiveEvent(archiveEvent);
        return Result.ok();
    }

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public Result<?> page(@RequestBody ArchiveQueryDto archiveQueryDto) {
        return Result.ok(archiveDataService.pageArchiveData(archiveQueryDto));
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除归档文件")
    public Result<?> delete(@RequestParam("archiveId") String archiveId) {
        return Result.ok(archiveDataService.delArchiveData(archiveId));
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除归档文件")
    public Result<?> batchDelete(@RequestBody List<String> ids) {
        return Result.ok(archiveDataService.batchDelArchive(ids));
    }

    @GetMapping("/preview")
    @ApiOperation("预览文件")
    public Result<ArchivePreviewVo> preview(@RequestParam String archiveId) {
        return Result.ok(archiveDataService.previewArchive(archiveId));
    }

    @GetMapping("/download")
    @ApiOperation("下载附件")
    public void download(HttpServletResponse response, @RequestParam String archiveId) {
        // 返回url地址或者电子签预览地址
        archiveDataService.download(response, archiveId);
    }

    @PostMapping("/batchDownload")
    @ApiOperation("批量下载附件")
    public Result<String> batchDownload(@RequestBody List<String> ids, HttpServletResponse response) {
        String taskId = UUID.randomUUID().toString().replaceAll("-", "");
        archiveDataService.batchDownload(ids, SecurityUserUtil.getSecurityUserInfo(), taskId, response);
        return Result.ok(taskId);
    }

    @GetMapping("/fetch/progress")
    @ApiOperation("获取附件生成进度")
    public Result<ExportVo> fetchProgress(@RequestParam String taskId) {
        return Result.ok(archiveDataService.getProgress(taskId));
    }

    @GetMapping("/downloadByPath")
    @ApiOperation("下载附件")
    public void downloadByPath(HttpServletResponse response, @RequestParam String path) {
        // 返回url地址或者电子签预览地址
        archiveDataService.downloadByPath(path, response);
    }
}
