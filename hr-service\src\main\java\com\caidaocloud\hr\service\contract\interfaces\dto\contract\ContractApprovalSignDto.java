package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

/**
 * 合同签订（新签，改签，续签）
 *
 * <AUTHOR>
 */
@Data
public class ContractApprovalSignDto extends ContractDto {

    private String businessKey;

    private String taskId;

    private String comment;

    private List<String> writableFields = Lists.list();

    // 生效日期
    private Long effectiveDate;
    // 是否开启工作流
    private Boolean openWorkflow;
    // 解除或终止原因
    private String reason;
    // 备注
    private String desc;
    // 是否是合同解除
    private boolean dissolve = false;


}
