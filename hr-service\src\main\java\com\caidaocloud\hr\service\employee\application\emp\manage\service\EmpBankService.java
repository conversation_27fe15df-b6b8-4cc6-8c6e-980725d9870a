package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.EmpBankDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBankDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpBankDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmpBankService {
    @Resource
    private EmpBankDomainService empBankDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public String save(EmpBankDto dto) {
        EmpBankDo data = ObjectConverter.convert(dto, EmpBankDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empBankDomainService.save(data);
    }

    public void update(EmpBankDto dto) {
        EmpBankDo data = ObjectConverter.convert(dto, EmpBankDo.class);
        doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empBankDomainService.update(data);
    }

    private void doConverter(EmpBankDto source, EmpBankDo target) {
        if (StringUtils.isNotEmpty(source.getPurpose())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getPurpose());
            target.setPurpose(enumSimple);
        }
    }

    public void deleteById(EmpBankDo data) {
        empBankDomainService.deleteById(data);
    }

    public EmpBankDo getById(String bid) {
        return empBankDomainService.selectById(bid);
    }

    public List<EmpBankDo> getList(String empId) {
        return empBankDomainService.selectList(empId);
    }
}
