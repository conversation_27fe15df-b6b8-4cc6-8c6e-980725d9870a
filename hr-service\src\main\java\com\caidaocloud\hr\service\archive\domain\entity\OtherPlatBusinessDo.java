package com.caidaocloud.hr.service.archive.domain.entity;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.archive.infrastructure.repository.OtherPlatBusinessRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Data
@Component
public class OtherPlatBusinessDo extends BaseDomainDoImpl<OtherPlatBusinessDo> {
    private String businessCode;

    private DictSimple business;

    private DictSimple businessType;

    @Resource
    private OtherPlatBusinessRepository otherPlatBusinessRepository;

    public static String IDENTIFIER = "entity.onboarding.OtherPlatformBusinessMapping";

    public void insert(OtherPlatBusinessDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        data.setCreateBy(userInfo.getUserId().toString());
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(data.getCreateBy());
        data.setUpdateTime(data.getCreateTime());
        data.setIdentifier(IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        data.setTenantId(userInfo.getTenantId());
        otherPlatBusinessRepository.insert(data);
    }

    public void update(OtherPlatBusinessDo data) {
        OtherPlatBusinessDo businessDo = otherPlatBusinessRepository.selectById(data.getBid(), IDENTIFIER);
        BeanUtil.copyProperties(data, businessDo, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                "deleted", "dataStartTime", "dataEndTime");
        data.setUpdateBy(UserContext.preCheckUser().getUserId().toString());
        data.setUpdateTime(System.currentTimeMillis());
        otherPlatBusinessRepository.updateById(businessDo);
    }

    public List<OtherPlatBusinessDo> selectList() {
        return otherPlatBusinessRepository.selectList(IDENTIFIER, SecurityUserUtil.getSecurityUserInfo().getTenantId());
    }

    public void delete(OtherPlatBusinessDo data) {
        otherPlatBusinessRepository.delete(data);
    }

}
