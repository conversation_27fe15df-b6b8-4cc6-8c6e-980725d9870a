package com.caidaocloud.hr.service.archive.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 18/6/2024 3:03 下午
 */
@Data
public class ArchiveDataVo implements Serializable {

    @ApiModelProperty("归档主键ID")
    private Long archiveId;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("员工英文名")
    private String enName;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工empType")
    private int archiveEmpType;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("任职组织")
    private String organize;

    @ApiModelProperty("任职组织名称")
    private String organizeTxt;

    @ApiModelProperty("岗位")
    private String post;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("用工类型")
    private DictSimple empType;

    @ApiModelProperty("工作地ID")
    private String workplace;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("合同公司ID")
    private String company;

    @ApiModelProperty("合同公司名称")
    private String companyTxt;

    @ApiModelProperty("所属业务")
    private String businessLine;

    @ApiModelProperty("子业务")
    private String subBusinessLine;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("业务流程ID")
    private String businessId;

    @ApiModelProperty("附件名称")
    private String fileName;

    @ApiModelProperty("附件url地址")
    private String fileUrl;

    @ApiModelProperty("文件扩展类型名")
    private String suffixType;

    @ApiModelProperty("电子签合同ID")
    private String openContractId;

    @ApiModelProperty("电子签签署平台")
    private String signPlatform;

    @ApiModelProperty("REDate")
    private String REDate;

    @ApiModelProperty("sapId")
    private String sapid;

    @ApiModelProperty("离职时间")
    private Long leaveDate;
}
