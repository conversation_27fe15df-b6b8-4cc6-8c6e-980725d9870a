package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueLetterRepository;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.ContinueLetterPo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.googlecode.totallylazy.Sequences;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ContinueLetterRepositoryImpl extends BaseRepositoryImpl<ContinueLetterDo> implements IContinueLetterRepository {
	@Override
	public ContinueLetterDo selectById(String bid, String identifier) {
		return selectById(bid, identifier, System.currentTimeMillis());
	}

	@Override
	public ContinueLetterDo selectById(String bid, String identifier, Long dateTime) {
		ContinueLetterPo po = DataQuery.identifier(identifier).decrypt().specifyLanguage()
				.queryInvisible()
				.oneOrNull(bid, ContinueLetterPo.class, dateTime);
		return po == null ? null : po.toEntity();
	}

	@Override
	public ContinueLetterDo insert(ContinueLetterDo data) {
		String dataId = DataInsert.identifier(data.getIdentifier()).insert(ContinueLetterPo.fromEntity(data));
		data.setBid(dataId);
		return data;
	}

	@Override
	public int updateById(ContinueLetterDo data) {
		DataUpdate.identifier(data.getIdentifier()).update(ContinueLetterPo.fromEntity(data));
		return 0;
	}

	@Override
	public List<ContinueLetterDo> selectList(ContinueLetterDo data) {
		DataFilter filter = getBaseFilter();
		if (data.getOwner() != null) {
			filter = filter.andEq("owner$empId", data.getOwner().getEmpId());
		}
		if (data.getContinueStatus() != null) {
			filter = filter.andEq("continueStatus", data.getContinueStatus().getValue());
		}
		PageResult<ContinueLetterPo> result = DataQuery.identifier(data.getIdentifier()).specifyLanguage().decrypt()
				.queryInvisible()
				.filter(filter, ContinueLetterPo.class);
		return Sequences.sequence(result.getItems()).map(ContinueLetterPo::toEntity).toList();
	}

	@Override
	public ContinueLetterDo selectOne(ContinueLetterDo data) {
		DataFilter filter = getBaseFilter();
		filter = filter.andEq("contractId", data.getContractId());
		PageResult<ContinueLetterPo> result = DataQuery.identifier("entity.hr.ContinueLetter").decrypt()
				.specifyLanguage()
				.queryInvisible()
				.filter(filter, ContinueLetterPo.class);
		return result.getItems().isEmpty() ? null : result.getItems().get(0).toEntity();
	}

	@Override
	public List<ContinueLetterDo> getArchiveData(String identifier, BasePage page) {
		DataFilter filter = getBaseFilter();
		filter = filter.andNe("attachment$names", null)
				.andEq("continueStatus", "CONFIRMED");
		PageResult<ContinueLetterDo> result = DataQuery.identifier(identifier).specifyLanguage().decrypt()
				.queryInvisible()
				.limit(page.getPageSize(), page.getPageNo())
				.filter(filter, ContinueLetterDo.class);
		return getPageList(result);
	}
}