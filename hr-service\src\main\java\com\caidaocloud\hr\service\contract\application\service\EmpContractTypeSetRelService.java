package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.entity.EmpContractTypeSetRelDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractSetConditionDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractTypeSetDomainService;
import com.caidaocloud.hr.service.contract.domain.service.EmpContractTypeSetRelDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.EmpContractTypeSetRelQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetRelListVo;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class EmpContractTypeSetRelService extends BaseServiceImpl<EmpContractTypeSetRelDo, EmpContractTypeSetRelQueryDto> {
    @Resource
    private EmpContractTypeSetRelDomainService empContractTypeSetRelDomainService;
    @Resource
    private ContractTypeSetDomainService contractTypeSetDomainService;
    @Resource
    private ContractSetConditionDomainService contractSetConditionDomainService;
    @Resource
    private DictService dictService;
    @Override
    protected BaseDomainService getDomainService() {
        return empContractTypeSetRelDomainService;
    }

    /**
     * 获取员工所属合同类型
     * @param empId
     * @param company
     * @param signType
     * @return
     */
    public List<ContractTypeSetRelListVo> getEmpContractTypeList(String empId, String company, String signType){
        List<EmpContractTypeSetRelDo> empContractTypeSetRelDoList = empContractTypeSetRelDomainService.getEmpContractTypeList(empId);
        log.info("empContractTypeSetRelDoList={}", FastjsonUtil.toJson(empContractTypeSetRelDoList));
        List<ContractTypeSetRelListVo> contractTypeSetListVos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(empContractTypeSetRelDoList)){
            empContractTypeSetRelDoList.forEach(empContractTypeSetRelDo -> {
                ContractTypeSetDo contractTypeSetDo = contractTypeSetDomainService.getById(empContractTypeSetRelDo.getContractTypeSet());
                if (contractTypeSetDo == null
                        // 过滤删除或停用的合同设置
                        || contractTypeSetDo.isDeleted()
                        || !StatusEnum.isEnable(contractTypeSetDo.getStatus())) {
                    return;
                }

                if(null != contractTypeSetDo.getCompany() && contractTypeSetDo.getCompany().contains("-1")
                    && null != contractTypeSetDo.getSignType() && contractTypeSetDo.getSignType().contains(signType)){
                    convertVo(contractTypeSetDo, contractTypeSetListVos);
                    return;
                }

                if (!contractTypeSetDomainService.filterCondition(contractTypeSetDo, company, signType)) {
                    return;
                }
                convertVo(contractTypeSetDo, contractTypeSetListVos);
            });
            return contractTypeSetListVos;
        }
        return new ArrayList<>();
    }



    private void convertVo(ContractTypeSetDo contractTypeSetDo, List<ContractTypeSetRelListVo> contractTypeSetListVos){
        ContractTypeSetRelListVo vo = ObjectConverter.convert(contractTypeSetDo, ContractTypeSetRelListVo.class);
        vo.convertData(contractTypeSetDo);
        vo.setContractTypeSimple(contractTypeSetDo.getContractClass());
        DictSimple contractType = contractTypeSetDo.getContractType();
        Optional.ofNullable(contractType)
                .filter(it -> StringUtil.isNotEmpty(it.getValue()))
                .ifPresent(it -> {
                    if (!LangUtil.chineseLocale()) {
                        vo.setContractTypeSetTxt(dictService.getLangText(contractType.getValue(), it.getText(), false));
                    } else {
                        vo.setContractTypeSetTxt(it.getText());
                    }
                });
        ContractSetConditionDo setConditionDo = new ContractSetConditionDo();
        setConditionDo.setContractTypeSetBids(Lists.list(vo.getBid()));
        ContractSetConditionDo conditionDo = contractSetConditionDomainService.selectList(setConditionDo).get(0);
        if (conditionDo != null) {
            vo.setPeriodType(conditionDo.getPeriodType());
            vo.setContractPeriod(conditionDo.getContractPeriod());
            vo.setProbationPeriod(conditionDo.getProbationPeriod());
            vo.setEndDateType(conditionDo.getEndDateType().getValue());
            vo.setBaseExpectGraduateDate(conditionDo.getBaseExpectGraduateDate());
            vo.setProbation(conditionDo.getProbationDeadline());
            vo.setBaseRetireDate(Optional.ofNullable(conditionDo.getBaseRetireDate()).orElse(false));
        }
        contractTypeSetListVos.add(vo);
    }

    public void initEmpContractRel(String progress) {
        empContractTypeSetRelDomainService.doEmpContractRel(progress);
    }

    public void initEmpContractRel(String bid, String progress) {
        empContractTypeSetRelDomainService.initEmpContractRel(bid, progress, new ArrayList<>(5000));
    }
}
