package com.caidaocloud.hr.service.confirmation.application.enums;

/**
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
public enum ConfirmationApproverType {
	APPROVER_X_HRBP("WfConfirmationApproverXHrbp"),
	APPROVER_HRBP("WfConfirmationApproverHrbp"),
	APPROVER_ORG_LEADER("WfConfirmationApproverOrgLeader"),
	APPROVER_X_LEADER("WfConfirmationApproverXLeader"),
	APPROVER_X_ORG_LEADER("WfConfirmationApproverXOrgLeader");
	public final String code;

	ConfirmationApproverType(String code) {
		this.code = code;
	}

	
	public static ConfirmationApproverType fromCode(String code) {
		for (ConfirmationApproverType type : ConfirmationApproverType.values()) {
			if (type.code.equals(code)) {
				return type;
			}
		}
		throw new IllegalArgumentException("Invalid ConfirmationApproverType code: " + code);
	}

	
}
