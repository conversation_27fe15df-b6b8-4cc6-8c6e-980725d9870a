package com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2023/5/11
 */
@Data
@ApiModel("组织信息")
@NoArgsConstructor
@AllArgsConstructor
public class OrgTreeVo {
	@ApiModelProperty(value = "bid")
	private String bid;
	@ApiModelProperty(value = "组织名称")
	private String name;
	@ApiModelProperty(value = "是否有子组织")
	private Boolean isParent = false;

	public OrgTreeVo(String bid, String name) {
		this.bid = bid;
		this.name = name;
	}
}
