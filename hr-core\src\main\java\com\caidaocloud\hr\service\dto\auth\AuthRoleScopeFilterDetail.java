package com.caidaocloud.hr.service.dto.auth;

import com.caidaocloud.hr.service.enums.auth.AuthRoleScopeTargetType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import lombok.Data;

@Data
public class AuthRoleScopeFilterDetail {

    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;

    private String property;

    private boolean inToOr;

    private AuthRoleScopeRestriction restriction;

    private String simpleValues;
}
