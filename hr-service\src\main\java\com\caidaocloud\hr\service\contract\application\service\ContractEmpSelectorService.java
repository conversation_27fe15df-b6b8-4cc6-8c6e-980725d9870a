package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.EmpTreeNodeDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector.EmpNodeVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector.EmpTreeVo;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Sequences;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/8/7
 */
@Service
public class ContractEmpSelectorService {

	@Autowired
	private ContractService contractService;

	public EmpTreeVo loadSubOrgAndEmp(int pageNo, int pageSize, boolean isNewly) {
		EmpTreeVo vo = new EmpTreeVo(pageNo, pageSize);
		vo.setOrgId("-1");
		vo.setName(contractService.getTenantName());
		vo.setOrgList(new ArrayList<>());

		ContractQueryDto dto = new ContractQueryDto();
		dto.setPageNo(pageNo);
		dto.setPageSize(pageSize);
		PageResult<EmpTreeNodeDto> emp;
		if (isNewly) {
			emp = contractService.searchNewlyContractUser(dto);
		}
		else {
			emp = contractService.searchContractUser(dto);
		}
		vo.setTotal(emp.getTotal());
		List<EmpNodeVo> list = Sequences.sequence(emp.getItems())
				.map(e -> ObjectConverter.convert(e, EmpNodeVo.class)).toList();
		vo.setEmpList(list);
		return vo;
	}

	public PageResult<EmpNodeVo> searchEmpPage(String keyword, int pageNo, int pageSize, boolean isNewly) {
		ContractQueryDto dto = new ContractQueryDto();
		dto.setPageNo(pageNo);
		dto.setPageSize(pageSize);
		dto.setKeywords(keyword);
		PageResult<EmpTreeNodeDto> emp;
		if (isNewly) {
			emp = contractService.searchNewlyContractUser(dto);
		} else {
			emp = contractService.searchContractUser(dto);
		}
		List<EmpNodeVo> list = Sequences.sequence(emp.getItems())
				.map(e -> ObjectConverter.convert(e, EmpNodeVo.class)).toList();
		return new PageResult<>(list, emp.getPageNo(), emp.getPageSize(), emp.getTotal());
	}
}
