package com.caidaocloud.hr.service.contract.domain.service;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ContinueLetterBatchDomainService extends BaseDomainServiceImpl<ContinueLetterBatchDo, BasePage> {
	@Resource
	private ContinueLetterBatchDo continueLetterBatchDo;

	@Override
	public BaseDomainDo getDoService() {
		return continueLetterBatchDo;
	}


	public ContinueLetterBatchDo createBatch(List<String> contractIds){
		ContinueLetterBatchDo batch = new ContinueLetterBatchDo();
		batch.setContractIds(contractIds);
		continueLetterBatchDo.save(batch);
		return batch;
	}

	public void update(ContinueLetterBatchDo batch) {
		continueLetterBatchDo.update(batch);
	}

	public PageResult<ContinueLetterBatchDo> loadPage(BasePage basePage) {
		return continueLetterBatchDo.selectPage(basePage);
	}
}
