package com.caidaocloud.hr.service.common.infrastructure.config;

import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.workflow.service.IBusinessDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CdApplicationRunner implements ApplicationRunner {
    @Autowired
    private List<IBusinessDetailService> businessServices;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        HrConstant.ready = true;
        businessServices.forEach(bds -> {
            bds.init();
        });
    }
}
