package com.caidaocloud.hr.service.contract.domain.entity;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.enums.ContinueLetterBatchMatchStatus;
import com.caidaocloud.hr.service.contract.application.enums.ContinueLetterBatchStartStatus;
import com.caidaocloud.hr.service.contract.application.enums.ContinueLetterBatchType;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueLetterBatchRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/4/25
 */
@Data
@Service
@Slf4j
public class ContinueLetterBatchDo extends BaseDomainDoImpl<ContinueLetterBatchDo> {
	/**
	 * 创建人姓名
	 */
	private String createByName;

	/**
	 * 合同id
	 */
	private List<String> contractIds;
	/**
	 * 匹配状态
	 */
	private EnumSimple matchStatus;
	/**
	 * 发起状态
	 */
	private EnumSimple startStatus;

	/**
	 * 失败详情
	 */
	private List<ContinueLetterBatchRecord> record = new ArrayList<>();

	/**
	 * 发起成功数
	 */
	private int succeed = 0;

	/**
	 * 匹配失败数
	 */
	private int matchFailed = 0;

	/**
	 * 发起失败数
	 */
	private int startFailed = 0;

	@Autowired
	private IContinueLetterBatchRepository repository;

	private static final String IDENTIFIER = "entity.hr.ContinueLetterBatch";

	@Override
	public ContinueLetterBatchDo getByBid(String bid) {
		return repository.selectById(bid, IDENTIFIER);
	}

	public void save(ContinueLetterBatchDo batch) {
		batch.checkValid();
		batch.updateStatus();
		batch.init();
		repository.insert(batch);
	}

	private void init() {
		long currentTimeMillis = System.currentTimeMillis();
		var userInfo = UserContext.preCheckUser();
		Long userId = userInfo.getUserId();
		if (this.getCreateBy() == null) {
			setCreateTime(currentTimeMillis);
			setCreateBy(String.valueOf(userId));
			setCreateByName(userInfo.getUserName());

		}
		setUpdateTime(currentTimeMillis);
		setUpdateBy(String.valueOf(userId));
		setIdentifier(IDENTIFIER);
		setTenantId(userInfo.getTenantId());
	}

	private void checkValid() {
		if (contractIds == null || contractIds.isEmpty()) {
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80016"));
		}
	}

	public void update(ContinueLetterBatchDo batch) {
		batch.checkValid();
		batch.updateStatus();
		batch.init();
		repository.updateById(batch);
	}

	private void updateStatus() {
		EnumSimple matchStatus = new EnumSimple();
		EnumSimple startStatus = new EnumSimple();
		int total = record.size();
		if (total==contractIds.size()) {
			matchStatus.setValue(ContinueLetterBatchMatchStatus.MATCHED.toString());
			startStatus.setValue(ContinueLetterBatchStartStatus.STARTED.toString());
		}else {
			matchStatus.setValue(ContinueLetterBatchMatchStatus.MATCHING.toString());
			startStatus.setValue(ContinueLetterBatchStartStatus.STARTING.toString());
		}
		this.matchStatus = matchStatus;
		this.startStatus = startStatus;

		int matchFailed = Sequences.sequence(record).filter(r -> ContinueLetterBatchType.MATCH == r.getType()).size();
		int startFailed = Sequences.sequence(record).filter(r -> ContinueLetterBatchType.START == r.getType()).size();
		this.matchFailed = matchFailed;
		this.startFailed = startFailed;
		this.succeed = total - matchFailed - startFailed;
	}

	public PageResult<ContinueLetterBatchDo> selectPage(BasePage basePage) {
		ContinueLetterBatchDo batch = new ContinueLetterBatchDo();
		batch.setIdentifier(IDENTIFIER);
		batch.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
		return repository.selectPage(basePage, batch);
	}
}
