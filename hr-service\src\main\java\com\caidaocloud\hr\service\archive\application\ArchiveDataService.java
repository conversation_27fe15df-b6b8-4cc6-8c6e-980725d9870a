package com.caidaocloud.hr.service.archive.application;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveProvider;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.archive.infrastructure.po.ArchiveEsData;
import com.caidaocloud.hr.service.archive.infrastructure.repository.ArchiveRepository;
import com.caidaocloud.hr.service.archive.interfaces.dto.ArchiveQueryDto;
import com.caidaocloud.hr.service.archive.interfaces.vo.ArchiveDataVo;
import com.caidaocloud.hr.service.archive.interfaces.vo.ArchivePreviewVo;
import com.caidaocloud.hr.service.archive.interfaces.vo.ExportVo;
import com.caidaocloud.hr.service.archive.util.ZipData;
import com.caidaocloud.hr.service.archive.util.ZipUtil;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.common.application.feign.EsignFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.oss.file.LocalMultipartFile;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 18/6/2024 4:00 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ArchiveDataService {

    private ArchiveRepository archiveRepository;

    private ArchiveProvider archiveProvider;

    private OssService ossService;

    private EsignFeignClient esignFeignClient;

    private CacheService cacheService;

    public PageResult<ArchiveDataVo> pageArchiveData(ArchiveQueryDto queryDto) {
        PageResult<ArchiveEsData> pageResult = archiveRepository.pageArchiveData(queryDto);
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {

            return new PageResult<>(convertDataVo(pageResult.getItems()), pageResult.getPageNo(), pageResult.getPageSize(),
                    pageResult.getTotal());
        }
        return new PageResult<>(Lists.newArrayList(), queryDto);
    }

    private List<ArchiveDataVo> convertDataVo(List<ArchiveEsData> esDataList) {
        if (CollectionUtils.isNotEmpty(esDataList)) {
            List<ArchiveDataVo> vos = new ArrayList<>();
            for (ArchiveEsData archiveEsData : esDataList) {
                ArchiveDataVo vo = ObjectConverter.convert(archiveEsData, ArchiveDataVo.class);
                vo.setEmpType(ObjectConverter.convert(archiveEsData.getEmpType(), DictSimple.class));
                vo.setEmpStatus(ObjectConverter.convert(archiveEsData.getEmpStatus(), EnumSimple.class));
                vos.add(vo);
            }
            return vos;
        }
        return Lists.newArrayList();
    }

    /**
     * 删除归档文件
     * @param archiveId
     */
    public boolean delArchiveData(String archiveId) {
        ArchiveEsData archiveEsData = archiveRepository.detail(Long.valueOf(archiveId));
        ArchiveFile delFile = new ArchiveFile();
        delFile.setUrls(Lists.newArrayList(archiveEsData.getFileUrl()));
        if (archiveEsData.getBusinessLine().equals(ArchiveStandardLine.EMPLOYEE.getDesc())) {
            try {
                archiveRepository.deleteArchiveData(Long.valueOf(archiveId));
                archiveProvider.dispatchArchiveDelEvent(archiveEsData.getBusinessLine(), archiveEsData.getSubBusinessLine(),
                        archiveEsData.getBusinessId(), delFile);
            } catch (Exception e) {
                log.error("[archive] dispatch archive del event error, {}", e.getMessage(), e);
                archiveRepository.batchSaveUpdateArchiveData(Lists.newArrayList(archiveEsData));
                log.info("[archive] dispatch archive del event rollback");
                return false;
            }
            archiveRepository.refreshEsData();
            return true;
        } else {
            throw new ServerException("文件类型不支持删除");
        }
    }


    /**
     * 查看预览文件地址
     * @param archiveId
     * @return
     */
    public ArchivePreviewVo previewArchive(String archiveId) {
        ArchiveEsData archiveEsData = archiveRepository.detail(Long.valueOf(archiveId));
        PreCheck.preCheckArgument(archiveEsData == null, "数据不存在");
        ArchivePreviewVo previewVo = new ArchivePreviewVo();
        previewVo.setFileName(archiveEsData.getFileName());
        previewVo.setSuffixType(archiveEsData.getSuffixType());
        if (StringUtils.isNotBlank(archiveEsData.getOpenContractId())) {
            previewVo.setRedirect(true);
            Result<String> previewResult = esignFeignClient.previewContract(archiveEsData.getOpenContractId());
            previewVo.setFilePath(Optional.ofNullable(previewResult)
                    .filter(it -> it.isSuccess() && StringUtils.isNotBlank(it.getData()))
                    .map(Result::getData).orElseThrow(() -> new ServerException("获取预览链接失败")));
        } else {
            previewVo.setFilePath(archiveEsData.getFileUrl());
        }
        return previewVo;
    }

    /**
     * 批量删除
     * @param
     * @return
     */
    public boolean batchDelArchive(List<String> delArchiveIds) {
        List<ArchiveEsData> archiveEsDataList = delArchiveIds.stream().map(it -> {
            ArchiveEsData archiveEsData = archiveRepository.detail(Long.valueOf(it));
            if (ArchiveStandardLine.EMPLOYEE.getDesc().equals(archiveEsData.getBusinessLine())) {
                return archiveEsData;
            }
            throw new ServerException("文件类型不支持删除");
        }).collect(Collectors.toList());
        boolean result = true;
        for (ArchiveEsData archiveEsData : archiveEsDataList) {
            Long archiveId = archiveEsData.getArchiveId();
            try {
                archiveRepository.deleteArchiveData(archiveId);
            } catch (Exception e) {
                log.error("[archive] dispatch archive del error, {}", e.getMessage(), e);
                result = false;
                break;
            }
        }
        if (result) {
            dispatchDelEvent(archiveEsDataList);
        } else {
            archiveRepository.batchSaveUpdateArchiveData(archiveEsDataList);
        }
        archiveRepository.refreshEsData();
        return result;
    }

    private void dispatchDelEvent(List<ArchiveEsData> archiveEsDataList) {
        for (ArchiveEsData archiveEsData : archiveEsDataList) {
            ArchiveFile delFile = new ArchiveFile();
            delFile.setUrls(Lists.newArrayList(archiveEsData.getFileUrl()));
            try {
                archiveProvider.dispatchArchiveDelEvent(archiveEsData.getBusinessLine(), archiveEsData.getSubBusinessLine(),
                        archiveEsData.getBusinessId(), delFile);
            } catch (Exception e) {
                log.error("[archive] dispatch archive del event error, {}", e.getMessage(), e);
            }
        }
    }

    //    @Async("taskExecutor")
    public void batchDownload(List<String> ids, SecurityUserInfo securityUserInfo, String taskId, HttpServletResponse response) {
        String cacheKey = getBatchExportCacheKey(taskId);
        try {
            ExportVo vo = new ExportVo();
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            List<ArchiveEsData> esData = archiveRepository.getByIds(ids);
            Map<String, Integer> fileNameMap = new HashMap<>();
            Map<String, List<ZipData>> map = new HashMap<>();
            int j = 0;
            BigDecimal progress = new BigDecimal("90");
            for (ArchiveEsData es : esData) {
                String folder = getFolderName(es);
                List<ZipData> aDefault = map.getOrDefault(folder, new ArrayList<>());
                String prefixFileName = es.getBusinessLine() + "-" + es.getBusinessType();
                if (StringUtils.isEmpty(es.getOpenContractId())) {
                    if (!StringUtils.isEmpty(es.getFileUrl())) {
                        InputStream inputStream = ossService.getInputStream(es.getFileUrl());
                        if (inputStream == null) {
                            continue;
                        }
                        aDefault.add(new ZipData(convertDuplicateName(fileNameMap, prefixFileName + "-" + es.getFileName() + "." + es.getSuffixType()), inputStream));
                    }
                }
                // todo 电子签
                else {
                    Result<Attachment> attachmentResult = esignFeignClient.uploadByContractId(es.getOpenContractId());
                    if (attachmentResult.isSuccess() && attachmentResult.getData() != null) {
                        List<String> urls = attachmentResult.getData().getUrls();
                        if (urls != null) {
                            List<String> names = attachmentResult.getData().getNames();
                            for (int i = 0; i < urls.size(); i++) {
                                aDefault.add(new ZipData(convertDuplicateName(fileNameMap, prefixFileName + "-" + names.get(i)), ossService.getInputStream(urls.get(i))));
                            }
                        }
                    }
                }
                map.put(folder, aDefault);
                j++;
                if (j % 2 == 0) {
                    String value = progress.multiply(new BigDecimal(j)).divide(new BigDecimal(esData.size()), 2, BigDecimal.ROUND_DOWN).toString();
                    vo.setPercentage(value);
                    cacheService.cacheObject(cacheKey, vo, 120L);
                }
            }
            try {
                vo.setPercentage("90");
                cacheService.cacheObject(cacheKey, vo, 120L);
                byte[] bytes = ZipUtil.zipAndDownload(map);
                LocalMultipartFile localMultipartFile = new LocalMultipartFile(bytes);
                localMultipartFile.setOriginalFilename("员工文件档案包.zip");
                UploadResult result = ossService.upload(getBucketName(), localMultipartFile);
                vo.setPercentage("100");
                vo.setPath(result.getObjectPath());
                cacheService.cacheObject(cacheKey, vo, 120L);
            } catch (IOException e) {
                log.error("zip download error:{}, {}", e.getMessage(), e);
            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public String convertDuplicateName(Map<String, Integer> fileNameMap, String fileName) {
        String[] split = fileName.split("\\.");
        fileName = split[0];
        int count = fileNameMap.getOrDefault(fileName, 0) + 1;
        fileNameMap.put(fileName, count);
        if (count > 1) {
            fileName = fileName + count;
            fileNameMap.put(fileName, 1);
        }
        return fileName + "." + split[1];
    }

    public void download(HttpServletResponse response, String id) {
        ArchiveEsData es = archiveRepository.detail(Long.valueOf(id));
        if (StringUtils.isEmpty(es.getOpenContractId())) {
            if (StringUtils.isEmpty(es.getFileUrl())) {
                log.error("download error:{}", FastjsonUtil.toJsonStr(es));
            }
            ossService.download(es.getFileUrl(), response);
        }else {
            Result<Attachment> attachmentResult = esignFeignClient.uploadByContractId(es.getOpenContractId());
            if (attachmentResult.isSuccess() && attachmentResult.getData() != null) {
                List<String> urls = attachmentResult.getData().getUrls();
                if (urls != null) {
                    List<String> names = attachmentResult.getData().getNames();
                    List<InputStream> streamList = new ArrayList<>();
                    for (int i = 0; i < urls.size(); i++) {
                        streamList.add(ossService.getInputStream(urls.get(i)));
                    }
                    try {
                        ZipUtil.zipAndDownload(streamList.toArray(new InputStream[0]), names.toArray(new String[0]), response, "员工文件档案包.zip");
                    } catch (IOException e) {
                        log.error("zip download error:{},{}", e.getMessage(), e);
                    }
                }
            }
        }
    }

    private String getBucketName() {
        return "caidao";
    }

    private String getBatchExportCacheKey(String taskId) {
        return "archiveData-batchExport-" + taskId;
    }

    private String getFolderName(ArchiveEsData es) {
        StringBuilder builder = new StringBuilder();
        if (!StringUtils.isEmpty(es.getName())) {
            builder.append(es.getName());
        }
        if (!StringUtils.isEmpty(es.getEnName())) {
            builder.append("-").append(es.getEnName());
        }
        if (!StringUtils.isEmpty(es.getSapid())) {
            builder.append("-").append(es.getSapid());
        }
        if (!StringUtils.isEmpty(es.getWorkno())) {
            builder.append("-").append(es.getWorkno());
        }
        return builder.toString();
    }

    public ExportVo getProgress(String taskId) {
        String objJsonStr = cacheService.getObjJsonStr(getBatchExportCacheKey(taskId));
        ExportVo vo = FastjsonUtil.convertObject(objJsonStr, ExportVo.class);
        if (vo == null) {
            vo = new ExportVo(null, "0");
        }
        return vo;
    }

    public void downloadByPath(String objectPath, HttpServletResponse response) {
        try {
            InputStream inputStream = ossService.getInputStream(objectPath);
            ZipUtil.downloadZipByInputStream(inputStream, "员工文件档案包.zip", response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
