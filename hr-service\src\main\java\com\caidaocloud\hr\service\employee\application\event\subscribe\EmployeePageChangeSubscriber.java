package com.caidaocloud.hr.service.employee.application.event.subscribe;

import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpPageDetailConfigService;
import com.caidaocloud.hrpaas.paas.common.event.PageDetailChangeEvent;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/4/24
 */
@Slf4j
@Component
public class EmployeePageChangeSubscriber implements MessageHandler<PageDetailChangeEvent> {
	@Autowired
	private EmpPageDetailConfigService empPageDetailConfigService;

	@Override
	public String topic() {
		return PageDetailChangeEvent.topic;
	}

	@Override
	public void handle(PageDetailChangeEvent message) throws Exception {
		log.info("EmployeePageChangeSubscriber handle message: {}", message);
		if (!"page.detail.employee".equals(message.getPageId())) {
			log.info("忽略非员工信息消息: {}", message.getPageId());
			return;
		}
		try {
			SecurityUserInfo securityUserInfo = new SecurityUserInfo();
			securityUserInfo.setTenantId(message.getTenantId());
			securityUserInfo.setEmpId(0L);
			securityUserInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(securityUserInfo);

			empPageDetailConfigService.registerAuthResource(message.getPageId());
		}finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}
}
