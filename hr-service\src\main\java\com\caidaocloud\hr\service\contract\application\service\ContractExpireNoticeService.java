package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.core.feign.ScheduleFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.TableGenerateUtil;
import com.caidaocloud.hr.service.contract.application.dto.ContractMergeNoticeDto;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.exception.ContractAutoStartException;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.domain.service.LastContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
  import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetRelListVo;
import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.application.emp.ruleset.service.RuleSetService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RuleSetDo;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.application.post.service.PostService;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.message.sdk.utils.MsgConditionUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 合同管理通知
 * <AUTHOR> Zhou
 * @date 2023/3/7
 */
@Service
@Slf4j
public class ContractExpireNoticeService {
	@Autowired
	private MsgNoticeService msgNoticeService;
	@Autowired
	private LastContractDomainService lastContractDomainService;
	@Autowired
	private ContractDomainService contractDomainService;
	@Autowired
	private EmpWorkInfoService empWorkInfoService;
	@Autowired
	private ScheduleFeignClient scheduleFeignClient;
	@Autowired
	private WorkplaceService workplaceService;
	@Autowired
	private CompanyService companyService;
	@Autowired
	private EmpContractTypeSetRelService empContractTypeSetRelService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private RuleSetService ruleSetService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private PostService postService;

	private final int PAGE_SIZE = 200;

	public static final String TOPIC = "CONTRACT_EXPIRE";


	public void expireNotify(long currentTimestamp) {
		List<MsgConfigDto> configList = msgNoticeService.getMsgConfigList(NoticeType.CONTRACT_EXPIRE);
		Map<Boolean, List<MsgConfigDto>> msgConfigMap = Sequences.sequence(configList)
				.toMap(config -> config.getFunc() == NotificationMethodEnum.MERGE);
		// 合同到期提醒
		commonNotify(currentTimestamp, msgConfigMap.get(false));
		// 合同到期合并提醒
		mergeNotify(currentTimestamp, msgConfigMap.get(true));
	}

	private void mergeNotify(long currentTimestamp, List<MsgConfigDto> msgConfigDtos) {
		if (CollectionUtils.isEmpty(msgConfigDtos)) {
			return;
		}
		// 过滤消息的合并通知日
		msgConfigDtos.removeIf(msg -> !checkMergeDate(msg, currentTimestamp));
		if (CollectionUtils.isEmpty(msgConfigDtos)) {
			return;
		}
		Map<MsgConfigDto, ContractMergeNoticeDto> sbMap = new HashMap<>();

		ContractQueryDto queryDto = new ContractQueryDto();
		queryDto.setPageSize(PAGE_SIZE);
		//不等于作废或终止
		queryDto.setContractNeStatusList(Arrays.asList(ContractStatusEnum.TERMINATED, ContractStatusEnum.CANCEL));
		queryDto.setPageNo(0);
		PageResult<ContractDto> page;
		do {
			queryDto.setPageNo(queryDto.getPageNo() + 1);
			page = lastContractDomainService.getContractPage(queryDto);
			var items = page.getItems();

			for (MsgConfigDto msgConfig : msgConfigDtos) {
				// 发送到期合并提醒
				doMergeNotify(currentTimestamp, items, msgConfig, sbMap.computeIfAbsent(msgConfig, config -> new ContractMergeNoticeDto()));
			}
		}
		while (queryDto.getPageNo() * PAGE_SIZE < page.getTotal());

		for (Map.Entry<MsgConfigDto, ContractMergeNoticeDto> entry : sbMap.entrySet()) {
			MsgConfigDto msgConfig = entry.getKey();
			ContractMergeNoticeDto mergeNotice = entry.getValue();
			if (mergeNotice.getEmpId() == null) {
				continue;
			}
			Map<String, String> extMap = mergeNotice.getMap();
			buildCollectParameter(mergeNotice, extMap);
			doNotify(msgConfig, currentTimestamp, mergeNotice.getEmpId(), extMap);
		}
	}

	private void buildCollectParameter(ContractMergeNoticeDto mergeNotice, Map<String, String> extMap) {
		List<Pair<EmpWorkInfoDo, ContractDo>> pairs = mergeNotice.getList();
		List<String> orgnaizeList = Sequences.sequence(pairs).map(p -> p.first().getOrganize()).filter(obj->obj!=null).toList();
		List<String> postList = Sequences.sequence(pairs).map(p -> p.first().getPost()).filter(obj->obj!=null).toList();
		Map<String, OrgDo> orgDoMap = orgService.selectByIds(orgnaizeList, System.currentTimeMillis()).stream()
				.collect(Collectors.toMap(AbstractData::getBid, obj -> obj));
		Map<String, PostDo> postDoMap = postService.selectByIds(postList, System.currentTimeMillis()).stream()
				.collect(Collectors.toMap(AbstractData::getBid, obj -> obj));
		List<String> cnList = new ArrayList<>();
		List<String> enList = new ArrayList<>();
		for (Pair<EmpWorkInfoDo, ContractDo> pair : pairs) {
			EmpWorkInfoDo workInfo = pair.first();
			ContractDo contract = pair.second();
			OrgDo orgDo = orgDoMap.get(workInfo.getOrganize());
			PostDo postDo = postDoMap.get(workInfo.getPost());
			// TODO: 2024/4/7 中英文
			Map<String, String> orgDoI18nName = orgDo != null && orgDo.getI18nName()!=null ? FastjsonUtil.toObject(orgDo.getI18nName(), Map.class) : new HashMap<>();
			Map<String, String> postDoI18nName = postDo != null && postDo.getI18nName()!=null ? FastjsonUtil.toObject(postDo.getI18nName(), Map.class) : new HashMap<>();
			cnList.add(StringUtils.join(Lists.list(workInfo.getName(), workInfo.getWorkno(), orgDoI18nName.getOrDefault("zh-CN",orgDoI18nName.get("default")), postDoI18nName.getOrDefault("zh-CN", postDoI18nName.get("default")), DateUtil.format(contract.getEndDate(), "yyyy年MM月dd日")), TableGenerateUtil.symbol));
			enList.add(StringUtils.join(Lists.list(workInfo.getName(), workInfo.getWorkno(), orgDoI18nName.getOrDefault("en-US", orgDoI18nName.get("default")), postDoI18nName.getOrDefault("en-US", postDoI18nName.get("default")), DateUtil.formatDate(contract.getEndDate())), TableGenerateUtil.symbol));
		}
		extMap.put("contract.expireCollect", TableGenerateUtil.buildHtmlContent(TableGenerateUtil.TableStyle.CONTRACT_EXPIRE_SUMMARY, cnList));
		extMap.put("contract.expireCollect.en", TableGenerateUtil.buildHtmlContent(TableGenerateUtil.TableStyle.CONTRACT_EXPIRE_SUMMARY_EN, enList));
	}

	private void doMergeNotify(long currentTimestamp, List<ContractDto> items, MsgConfigDto msgConfig, ContractMergeNoticeDto mergeNoticeDto) {
		items = Sequences.sequence(items).filter(item -> check(currentTimestamp,msgConfig,item)).toList();
		if (items.isEmpty()) {
			return;
		}
		// 获取员工信息
		List<String> empIds = Sequences.sequence(items).map(item -> item.getOwner().getEmpId()).toList();
		Map<String, EmpWorkInfoDo> empMap = empWorkInfoService.getNoLeaveEmpWorkInfoByEmpIds(empIds, currentTimestamp)
				.stream()
				.collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, obj -> obj, (a, b) -> a));
		// 2024-11-29 移除原有自动发起续签逻辑 CAIDAOM-2131
		//beforeNotify(currentTimestamp, empMap, items);
		for (ContractDto item : items) {
			if (empMap.get(item.getOwner().getEmpId()) == null) {
				continue;
			}
			// 生成消息变量
			Map<String, String> ext = contractDomainService.genContractNoticeParam(ObjectConverter.convert(item, ContractDo.class), empMap.get(item.getOwner()
					.getEmpId()), currentTimestamp, mergeNoticeDto.getList());
			// 合并发送，获取随机合同的员工和合同变量
			mergeNoticeDto.setEmpId(item.getOwner().getEmpId());
			mergeNoticeDto.setMap(ext);
		}
	}

	private boolean checkMergeDate(MsgConfigDto msg, long currentTimestamp) {
		Integer day = msg.getMergeRule().getMergeDay();
		LocalDateTime now = LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTimestamp), OffsetDateTime.now()
				.getOffset());
		// 兼容2月
		day = Math.min(day, (int) now.range(ChronoField.DAY_OF_MONTH).getMaximum());
		LocalDateTime target = now.withDayOfMonth(day);
		return now.equals(target);
	}

	/**
	 * 单次通知
	 * @param currentTimestamp
	 * @param msgConfigDtos 单次通知消息配置集合
	 */
	private void commonNotify(long currentTimestamp, List<MsgConfigDto> msgConfigDtos) {
		if (CollectionUtils.isEmpty(msgConfigDtos)) {
			return;
		}
		ContractQueryDto queryDto = new ContractQueryDto();
		queryDto.setPageSize(PAGE_SIZE);
		queryDto.setPageNo(0);
		PageResult<ContractDto> page;

		do {
			queryDto.setPageNo(queryDto.getPageNo() + 1);
			//不等于作废或终止
			queryDto.setContractNeStatusList(Arrays.asList(ContractStatusEnum.TERMINATED, ContractStatusEnum.CANCEL));
			page = lastContractDomainService.getContractPage(queryDto);
			var items = page.getItems();

			for (MsgConfigDto msgConfig : msgConfigDtos) {
				// 发送到期提醒
				doCommonNotify(currentTimestamp, items, msgConfig);
			}
		}
		while (queryDto.getPageNo() * PAGE_SIZE < page.getTotal());
	}

	private void doCommonNotify(long currentTimestamp, List<ContractDto> items, MsgConfigDto msgConfig) {
		// 过滤到期合同
		items = Sequences.sequence(items).filter(item ->{
			return check(currentTimestamp, msgConfig, item);
		}).toList();
		if (items.isEmpty()) {
			return;
		}
		// 获取员工信息
		List<String> empIds = Sequences.sequence(items).map(item -> item.getOwner().getEmpId()).toList();
		Map<String, EmpWorkInfoDo> empMap = empWorkInfoService.getNoLeaveEmpWorkInfoByEmpIds(empIds, currentTimestamp)
				.stream()
				.collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, obj -> obj, (a, b) -> a));
		items = Sequences.sequence(items).filter(item -> empMap.get(item.getOwner().getEmpId()) != null).toList();
		// 2024-11-29 移除原有自动发起续签逻辑 CAIDAOM-2131
		//beforeNotify(currentTimestamp, empMap, items);
		for (ContractDto item : items) {
			// 生成消息变量
			Map<String, String> ext = contractDomainService.genContractNoticeParam(ObjectConverter.convert(item, ContractDo.class), empMap.get(item.getOwner()
					.getEmpId()), currentTimestamp);
			// 发送消息通知
			doNotify(msgConfig, currentTimestamp, item.getOwner().getEmpId(), ext);
		}
	}

	private boolean check(long currentTimestamp, MsgConfigDto msgConfig, ContractDto item) {
		boolean flag = checkMsgConfig(item, currentTimestamp, msgConfig);
		if (log.isDebugEnabled()) {
			log.debug("合同期限提醒过滤结果：{}；>>>>>入参参数：合同id={},所属员工={},合同开始时间={},合同结束时间={},当前时间={}，消息={}", flag, item.getBid(), item.getOwner(), item.getStartDate(), item.getEndDate(), currentTimestamp, msgConfig);
		}
		if (msgConfig.getSubjectFilter()==null || !msgConfig.getSubjectFilter()) {
			return flag;
		}

		return flag && (msgConfig.getCondition() == null || MsgConditionUtil.match(item.getOwner()
				.getEmpId(), msgConfig.getCondition()));
	}

	private void beforeNotify(long currentTimestamp, Map<String, EmpWorkInfoDo> empMap, List<ContractDto> items) {
		RuleSetDo rule = ruleSetService.getRuleSet();
		if (rule == null || rule.getAutoStart() == null || !rule.getAutoStart()) {
			return;
		}
		// 自动发起流程
		for (ContractDto item : items) {
			autoStartContract(empMap.get(item.getOwner().getEmpId()), item);
		}
	}



	private void autoStartContract(EmpWorkInfoDo empInfo, ContractDto item) {
		if (empInfo == null) {
			return;
		}
		CompanyDo company = companyService.getCompanyById(item.getCompany());
		WorkplaceDo workplace = workplaceService.getWorkplaceById(item.getWorkplace());
		List<ContractTypeSetRelListVo> typeList = empContractTypeSetRelService.getEmpContractTypeList(empInfo.getEmpId(), item.getCompany(), "0");
		try {
			contractService.autoStart(empInfo, item, company, workplace, typeList);
		}
		catch (ContractAutoStartException e) {
			log.error("自动发起合同失败，empId={},contractId={},msg={}", empInfo.getEmpId(), item.getBid(), e.getMessage());
		}
		catch (Exception e) {
			log.error("自动发起合同失败，empId={},contractId={}", empInfo.getEmpId(), item.getBid(), e);
		}
	}

	public static void main(String[] args) {
		long l = DateUtil.getCurrentTimestamp() + TimeUnit.DAYS.toMillis(70);
		System.out.println(l == 1688054400000L);
		long date = DateUtil.getCurrentTimestamp() + TimeUnit.DAYS.toMillis(12);
		long diff = TimeUnit.MILLISECONDS.toDays(1853856000000L - date);
		System.out.println(diff%7== 0);
	}

	/**
	 * 判断合同是否符合发送时间
	 * @param contract 合同
	 * @param currentTimestamp 当天0点时间戳
	 * @param msgConfig 消息配置
	 * @return
	 */
	private boolean checkMsgConfig(ContractDto contract, long currentTimestamp, MsgConfigDto msgConfig) {
		if (contract.getEndDate()==null) {
			return false;
		}
		long sendDate = currentTimestamp;
		NotificationCycleTypeEnum cycleType = NotificationCycleTypeEnum.getByName(msgConfig.getRound());
		switch (cycleType) {
		// 提前通知
		case ADVANCE:
			sendDate += TimeUnit.DAYS.toMillis(msgConfig.getDay());
			break;
		// 延后通知
		case DELAY:
			sendDate -= TimeUnit.DAYS.toMillis(msgConfig.getDay());
			break;
		case TODAY:
			break;
		default:
			log.warn("Unexpected value = {},msgId={}", cycleType, msgConfig.getBid());
			return false;
		}
		// if (sendDate>contract.getEndDate()) {
		// 	return false;
		// }
		// 单次通知，判断结束日期 == 发送日期
		switch (msgConfig.getFunc()) {
		case ONCE:
			return sendDate == contract.getEndDate();
		// 循环通知
		case CYCLE:
			// 循环通知到 loopDay 天
			if ( msgConfig.getLoopDay() != null && sendDate > contract.getEndDate() + TimeUnit.DAYS.toMillis(msgConfig.getLoopDay())) {
				return false;
			}

			long diff = TimeUnit.MILLISECONDS.toDays(sendDate - contract.getEndDate());
			return diff>=0 &&  diff % msgConfig.getLoop() == 0;
		//	合并发送
		case MERGE:
			// 合并发送时间范围
			long startDate = sendDate, endDate = startDate + TimeUnit.DAYS.toMillis(msgConfig.getMergeRule()
					.getMergePeriod());
			return startDate <= contract.getEndDate() && contract.getEndDate() <= endDate;
		default:
			return false;
		}
	}


	/**
	 * 发送消息
	 * @param msgConfig 消息设置x
	 * @param currentTimestamp 0点时间戳
	 * @param empId 事件人id
	 * @param ext 合同变量
	 */
	public void doNotify(MsgConfigDto msgConfig, long currentTimestamp, String empId, Map<String, String> ext){
		// 发送时间为空,直接发送
		Integer configTime = msgConfig.getFunc() == NotificationMethodEnum.MERGE ? msgConfig.getMergeRule().getMergeTime() : msgConfig.getSendTime();
		if (configTime == null) {
			msgNoticeService.sendMsgNoticeEvent(msgConfig.getBid(), Lists.list(empId), ext, "hr", 0);
			return;
		}
		// 发送时间小于当前时间
		long sendTime = currentTimestamp + configTime;
		if (sendTime <= System.currentTimeMillis()) {
			msgNoticeService.sendMsgNoticeEvent(msgConfig.getBid(), Lists.list(empId), ext, "hr", 0);
			return;
		}
		// 创建定时任务
		scheduleFeignClient.addSchedule(new ScheduleTaskDto(SecurityUserUtil.getSecurityUserInfo()
				.getTenantId(), TOPIC, String.format("%s_%s", msgConfig.getBid(), empId), FastjsonUtil.toJson(ext), sendTime));
	}


}
