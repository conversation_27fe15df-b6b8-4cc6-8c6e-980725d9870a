package com.caidaocloud.hr.service.dto.es;

import com.caidaocloud.hr.service.enums.ChangeDataEnum;
import lombok.Data;

import java.util.List;

/**
 * 组织变更记录
 */
@Data
public class ChangeDataRecordDto {
    private String id;
    private String bid;
    private String tenantId;
    // 变更类型
    private ChangeDataEnum type;
    private List<ChangeDataPropDto> keyList;
    private List<ChangeDataPropDto> dataList;
    private String i18n;
    private Long effectiveDate;
    private List<ChangeDataUserDto> userList;
    private Long createTime;
    /**
     * 创建人
     */
    private String createBy;
}
