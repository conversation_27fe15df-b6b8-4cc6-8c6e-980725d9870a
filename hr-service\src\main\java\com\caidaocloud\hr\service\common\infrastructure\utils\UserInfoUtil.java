package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.core.dto.UserBaseInfoDto;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class UserInfoUtil {
    public static Map<String, String> getUsername(List<Long> userIds) {
        Result<List<UserBaseInfoDto>> userResult = SpringUtil.getBean(UserFeignClient.class).getUserByIds(userIds);
        if (!userResult.isSuccess()) {
            log.info("request fail, userIds={}", userIds);
            return Maps.newHashMap();
        }
        var userBaseInfoList = userResult.getData();
        if (CollectionUtils.isEmpty(userBaseInfoList)) {
            return Maps.newHashMap();
        }
        val empIdList = userResult.getData().stream().map(e -> e.getEmpId()).filter(e -> Objects.nonNull(e)).collect(Collectors.toList());
        Map<String, DataSimple> workInfoMap = null;
        if (!CollectionUtils.isEmpty(empIdList)) {
            PageResult<DataSimple> empPageResult = DataQuery.identifier("entity.hr.EmpWorkInfo").filter(DataFilter.ne("deleted", Boolean.TRUE.toString()), DataSimple.class, System.currentTimeMillis());
            workInfoMap = !CollectionUtils.isEmpty(empPageResult.getItems()) ? empPageResult.getItems().stream().collect(Collectors.toMap(e -> ((SimplePropertyValue) e.getProperties().get("empId")).getValue(), Function.identity(), (v1, v2) -> v1)) : null;
        }
        Map<String, DataSimple> finalWorkInfoMap = workInfoMap;
        return userResult.getData().stream().collect(Collectors.toMap(e -> e.getUserId().toString(), e -> {
            String userName = e.getUserName();
            if (MapUtils.isNotEmpty(finalWorkInfoMap) && Objects.nonNull(e.getEmpId()) && finalWorkInfoMap.containsKey(String.valueOf(e.getEmpId()))) {
                DataSimple workInfo = finalWorkInfoMap.get(String.valueOf(e.getEmpId()));
                String workno = ((SimplePropertyValue) workInfo.getProperties().getOrDefault("workno", new SimplePropertyValue(""))).getValue();
                return String.format("%s(%s)", userName, workno);
            }
            return userName;
        }, (v1, v2) -> v2));
    }
}
