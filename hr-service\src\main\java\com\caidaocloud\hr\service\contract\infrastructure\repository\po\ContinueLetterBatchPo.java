package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchRecord;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
public class ContinueLetterBatchPo extends DataSimple {
	/**
	 * 创建人姓名
	 */
	private String createByName;

	/**
	 * 合同id
	 */
	private String contractIds;
	/**
	 * 匹配状态
	 */
	private EnumSimple matchStatus;
	/**
	 * 发起状态
	 */
	private EnumSimple startStatus;

	/**
	 * 失败详情
	 */
	private String record;

	/**
	 * 发起成功数
	 */
	private int succeed = 0;

	/**
	 * 匹配失败数
	 */
	private int matchFailed = 0;

	/**
	 * 发起失败数
	 */
	private int startFailed = 0;


	public static ContinueLetterBatchPo fromEntity(ContinueLetterBatchDo entity){
		ContinueLetterBatchPo po = ObjectConverter.convert(entity, ContinueLetterBatchPo.class);
		po.setRecord(FastjsonUtil.toJson(entity.getRecord()));
		po.setContractIds(FastjsonUtil.toJson(entity.getContractIds()));
		return po;
	}

	public ContinueLetterBatchDo toEntity() {
		ContinueLetterBatchDo entity = ObjectConverter.convert(this, ContinueLetterBatchDo.class);
		entity.setRecord(FastjsonUtil.toList(record, ContinueLetterBatchRecord.class));
		entity.setContractIds(FastjsonUtil.toList(contractIds, String.class));
		return entity;
	}
}
