package com.caidaocloud.hr.service.contract.application.service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.enums.ContinueLetterBatchType;
import com.caidaocloud.hr.service.contract.application.exception.ContinueMatchException;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchRecord;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContinueLetterBatchDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractTypeSetDomainService;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.ContinueLetterBatchExportPo;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.ContinueLetterBatchResultExportPo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContinueLetterBatchVo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hrpaas.paas.match.ConditionNameMapping;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 续签意向书批量发起
 * <AUTHOR> Zhou
 * @date 2023/4/25
 */
@Service
@Slf4j
public class ContinueLetterBatchService {
	@Autowired
	private ContinueLetterBatchDomainService continueLetterBatchDomainService;
	@Autowired
	private ContractDomainService contractDomainService;
	@Autowired
	private ContractTypeSetService contractTypeSetService;
	@Autowired
	private ContractTypeSetDomainService contractTypeSetDomainService;
	@Autowired
	private EmpWorkInfoDomainService empWorkInfoDomainService;
	@Resource
	private ContinueLetterService continueLetterService;


	/**
	 * 续签意向书批量发起
	 * @param contractIds 合同id的list集合
	 */
	public ContinueLetterBatchDo createBatch(List<String> contractIds) {
		return continueLetterBatchDomainService.createBatch(contractIds);
	}

	@Async("taskExecutor")
	public void startBatch(List<String> contractIds, ContinueLetterBatchDo batch, SecurityUserInfo securityUserInfo) {
		try {
			SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
			List<ContinueContractTemplateDto> templateList = contractTypeSetDomainService.getContinueTemplateList();
			List<ConditionNameMapping> mapping = contractTypeSetService.getNameMapping();
			for (String id : contractIds) {
				try {
					createContinueLetter(id, templateList, mapping);
					batch.getRecord().add(new ContinueLetterBatchRecord(id, ContinueLetterBatchType.SUCCEED));
				}
				catch (ContinueMatchException e) {
					log.error("Matching failed for contract id: {},msg:{}", id, e.getMessage());
					batch.getRecord().add(new ContinueLetterBatchRecord(id, ContinueLetterBatchType.MATCH));
				}
				catch (Exception e) {
					log.error("Failed to create continue letter for contract id: {}", id, e);
					batch.getRecord().add(new ContinueLetterBatchRecord(id, ContinueLetterBatchType.START));
				}
			}

			continueLetterBatchDomainService.update(batch);
		}finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	/**
	 * 创建续签意向书
	 * @param contractId 合同id
	 * @param templateList 续签模板
	 * @param mapping 匹配条件映射
	 */
	private void createContinueLetter(String contractId, List<ContinueContractTemplateDto> templateList, List<ConditionNameMapping> mapping) {
		// 获取合同
		ContractDo contract = contractDomainService.getById(contractId);
		if (contract == null) {
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
		}
		// 过滤已发起合同
		if (contract.getContinueLetter() != null) {
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80015"));
		}
		// 续签意向模板匹配
		List<ContinueContractTemplateDto> matched = contractTypeSetDomainService.findMatchedContinueTemplate(contract.getOwner()
				.getEmpId(), templateList, mapping);
		if (matched.size() > 1) {
			throw ContinueMatchException.globalException(ErrorMessage.fromCode("caidao.exception.error_80014"));
		}
		contractDomainService.createContinueLetter(contract, matched.get(0), null);
		continueLetterService.continueLetterNotify(contract);
	}

	/**
	 * 批量发起结果分页
	 * @param basePage
	 * @return
	 */
	public PageResult<ContinueLetterBatchVo> loadBatchPage(BasePage basePage){
		PageResult<ContinueLetterBatchDo> page = continueLetterBatchDomainService.loadPage(basePage);
		List<ContinueLetterBatchVo> list = Sequences.sequence(page.getItems()).map(d -> {
			ContinueLetterBatchVo vo = ObjectConverter.convert(d, ContinueLetterBatchVo.class);
			vo.setMatchSucceed(d.getContractIds().size() - d.getMatchFailed());
			vo.setStartSucceed(d.getSucceed());
			return vo;
		}).toList();
		return new PageResult<>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
	}

	/**
	 * 导出批量发起excel
	 * @param batchId 批量发起id
	 * @param isFailedResult 是否只下载失败数据
	 */
	public void downloadBatchExcel(String batchId, boolean isFailedResult, HttpServletResponse response) {
		ContinueLetterBatchDo batch = continueLetterBatchDomainService.getById(batchId);
		List<String> contractIds = batch.getContractIds();
		List<ContractDo> contractList = contractDomainService.selectByIds(contractIds);
		List<String> empIds = Sequences.sequence(contractList).map(contractDo -> contractDo.getOwner().getEmpId())
				.toList();
		List<EmpWorkInfoDo> workInfoList = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
		List<ExcelExportEntity> baseColumn = ExcelUtils.buildExportEntity(ContinueLetterBatchExportPo.class);
		if (!isFailedResult) {
			List<ContinueLetterBatchResultExportPo> data = buildExportData(contractList, Lists.list(),workInfoList);
			ExcelUtils.downloadDataListMapExcel(baseColumn, data, "人员列表", response);
			return;
		}
		List<ContinueLetterBatchResultExportPo> data = buildExportData(contractList, batch.getRecord(),workInfoList);
		data = Sequences.sequence(data)
				.filter(contract -> !contract.getStartStatus().equals(ContinueLetterBatchType.SUCCEED.getText()))
				.toList();
		baseColumn.addAll(ExcelUtils.buildExportEntity(ContinueLetterBatchResultExportPo.class));
		ExcelUtils.downloadDataListMapExcel(baseColumn, data, "匹配失败数据", response);
	}

	private List<ContinueLetterBatchResultExportPo> buildExportData(List<ContractDo> contractList, List<ContinueLetterBatchRecord> record, List<EmpWorkInfoDo> workInfoList) {
		Map<String, List<ContinueLetterBatchRecord>> recordMap = Sequences.sequence(record)
				.toMap(ContinueLetterBatchRecord::getContractId);
		Map<String, List<EmpWorkInfoDo>> workInfoMap = Sequences.sequence(workInfoList)
				.toMap(EmpWorkInfoDo::getEmpId);
		return Sequences.sequence(contractList).map(contract -> {
			ContinueLetterBatchResultExportPo export = ObjectConverter.convert(contract, ContinueLetterBatchResultExportPo.class);
			if (workInfoMap.get(contract.getOwner().getEmpId()) != null) {
				EmpWorkInfoDo workInfoDo = workInfoMap.get(contract.getOwner().getEmpId()).get(0);
				BeanUtils.copyProperties(workInfoDo, export);
				export.setHireDate(DateUtil.formatDate(workInfoDo.getHireDate()));
				export.setEmpType(workInfoDo.getEmpType().getText());
				export.setEmpStatus(workInfoDo.getEmpStatus().getText());
			}

			export.setStartDate(DateUtil.formatDate(contract.getStartDate()));
			export.setEndDate(DateUtil.formatDate(contract.getEndDate()));
			export.setContractStatus(contract.getContractStatus().getText());
			if (recordMap.get(contract.getBid()) != null) {
				switch (recordMap.get(contract.getBid()).get(0).getType()) {
				case MATCH:
					export.setMatchStatus(ContinueLetterBatchType.MATCH.getText());
					export.setStartStatus(ContinueLetterBatchType.START.getText());
					break;
				case START:
					export.setMatchStatus(ContinueLetterBatchType.SUCCEED.getText());
                    export.setStartStatus(ContinueLetterBatchType.START.getText());
                    break;
				case SUCCEED:
					export.setMatchStatus(ContinueLetterBatchType.SUCCEED.getText());
                    export.setStartStatus(ContinueLetterBatchType.SUCCEED.getText());
                    break;
				default:
				}
			}
			return export;
		}).toList();
	}
}
	