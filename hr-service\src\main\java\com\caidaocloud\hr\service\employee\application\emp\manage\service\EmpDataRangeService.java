package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.dto.growthrecord.GrowthRecordDto;
import com.caidaocloud.hr.service.employee.application.common.tool.LogChangeDataUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportResp;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.ReportLineDto;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.temination.application.TerminationChangeService;
import com.caidaocloud.hr.service.util.PaasUpdUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpDataRangeService {
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    public void updatePostAndReportLineV2(ReportLineDto dto) {
        if (dto.isRange()) {
            empWorkInfoService.updatePostAndReportLine(dto);
            return;
        }

        EmpWorkInfoDto ewi = dto.getWorkInfo();
        if (null == ewi) {
            return;
        }
        EmpWorkInfoDo beforeEmpWorkInfo = empWorkInfoService.getEmpWorkInfo(ewi.getEmpId(), ewi.getDataStartTime());
        DataSimple dataSimple;
        try {
            dataSimple = ewi.toDataSimple();
            dataSimple.setIdentifier(EmpWorkInfoDo.IDENTIFIER);
            dataSimple.setBid(beforeEmpWorkInfo.getBid());
            empWorkInfoService.doConverter(ewi, dataSimple);
            empWorkInfoService.doConvertPersistObject(ewi, dataSimple);
            empExtFieldService.doCusExtProps(EmpWorkInfoDo.IDENTIFIER, ewi.getExt(), dataSimple);
        } catch (Exception e) {
            log.error("EmpWorkInfoDto toDataSimple error,errMsg={}", e.getMessage(), e);
            throw new ServerException("Merge editing failed");
        }

        List<DataSimple> changeList = new ArrayList<>();
        DataSimple before = empWorkInfoService.getEmpWorkInfo(ewi.getEmpId(), dto.getDataTime());
        PaasUpdUtil.compareUpdate(dataSimple, dto.getDataTime(), dto.getDataEndTime(), changeList, true);

        // 如果是离职，则发送离职消息
        if (EmpStatusEnum.LEAVE_JOB.realValue().equals(ewi.getEmpStatus()) && ewi.getLeaveDate() != null && dto.getDataTime().equals(ewi.getLeaveDate())) {
            SpringUtil.getBean(TerminationChangeService.class).doLastDayEvent(ewi.getLeaveDate(), ewi.getEmpId());
        }

        empWorkInfoService.doChangeContract(ewi);
        LogRecordContext.putVariable("name", dto.getWorkInfo().getName());
        LogRecordContext.putVariable("workno", dto.getWorkInfo().getWorkno());

        for (DataSimple ds : changeList) {
            GrowthRecordDto growthRecordDto = new GrowthRecordDto();
            growthRecordDto.setTenantId(ds.getTenantId());
            growthRecordDto.setEmpId(ewi.getEmpId());
            growthRecordDto.setCreateBy(ds.getUpdateBy());
            growthRecordDto.setEffectiveDate(ds.getDataStartTime());
            LogRecordContext.putVariable("change", LogChangeDataUtil.getChangeInfo(before, ds));
            empWorkInfoDomainService.doProcessGrowthRecord(ds.getProperties(), before.getProperties(), growthRecordDto);
            before = ds;
        }
    }

    public EmpReportResp checkReportLeader(EmpReportLeaderDto empReportLeaderDto) {
        if (empReportLeaderDto == null || StringUtils.isEmpty(empReportLeaderDto.getEmpId()) || StringUtils.isEmpty(empReportLeaderDto.getOrganizeId()) || StringUtils.isEmpty(empReportLeaderDto.getPostId())) {
            // 主岗查询下级任职兼岗和组织信息参数有误！
            throw new ServerException(LangUtil.getLangMsg("caidao.exception.check_report_leader_args_error"));
        }
        EmpReportResp empReportResp = new EmpReportResp();
        List<EmpReportResp.EmpReportInfo> empReportInfoList = Lists.newArrayList();
        List<EmpReportResp.EmpOrgInfo> empOrgInfoList = Lists.newArrayList();
        // 组织跟岗位没有变更,则不查询 如果被修改人的组织岗位发生变化 那么原下属的汇报关系可能修改调整 比如原下属需要调整新的上级领导
        if (empReportLeaderDto.getOrganizeId().equals(empReportLeaderDto.getUpdateAfterOrganizeId())
                && empReportLeaderDto.getPostId().equals(empReportLeaderDto.getUpdateAfterPostId())) {
            return empReportResp;
        }
        // 作为主岗找下级员工
        List<EmpWorkInfoDo> empWorkInfoDoList = empWorkInfoService.getEmpWorkInfoByEmpReportLeaderDto(empReportLeaderDto, -1);
        empReportResp.buildEmpWorkInfo(empWorkInfoDoList, empReportInfoList, true);

        // 作为兼岗找下级员工
        List<EmpConcurrentPostDo> empConcurrentPostListByLeader = SpringUtil.getBean(EmpConcurrentPostService.class)
                .getEmpConcurrentPostByLeader(empReportLeaderDto);
        buildEmpConcurrentPostDo(empConcurrentPostListByLeader, empReportInfoList, false);

        empReportResp.setEmpReportInfoList(empReportInfoList);
        // 组织 部门岗位发生变化那么如果组织负责人是当前被修改人员的组织可能需要调整新的组织负责人
        List<OrgDo> orgListByLeader = SpringUtil.getBean(OrgService.class).getOrgListByLeader(empReportLeaderDto, -1L);
        empReportResp.buildOrgInfo(orgListByLeader, empOrgInfoList);
        empReportResp.setEmpOrgInfoList(empOrgInfoList);
        if (null != empReportLeaderDto.getDataEndTime() && (!empOrgInfoList.isEmpty() || !empReportInfoList.isEmpty())) {
            // 该员工有下属汇报关系，不能合并修改
            throw new ServerException(LangUtil.getLangMsg("caidao.exception.emp_has_report_chain"));
        }
        return empReportResp;
    }

    public void buildEmpConcurrentPostDo(List<EmpConcurrentPostDo> empConcurrentPostListByLeader, List<EmpReportResp.EmpReportInfo> empReportInfoList, boolean majorPost) {
        if (!CollectionUtils.isNotEmpty(empConcurrentPostListByLeader)) {
            return;
        }

        List<String> empIds = empConcurrentPostListByLeader.stream().map(EmpConcurrentPostDo::getEmpId).collect(Collectors.toList());
        List<EmpBasicInfoDo> empBasicInfoListByBids = SpringUtil.getBean(EmpBasicInfoService.class).getEmpBasicInfoListByBids(empIds, System.currentTimeMillis());
        Map<String, EmpBasicInfoDo> basicInfoMap = empBasicInfoListByBids.stream().collect(Collectors.toMap(EmpBasicInfoDo::getBid, st -> st));
        for (EmpConcurrentPostDo empConcurrentPostDo : empConcurrentPostListByLeader) {
            EmpReportResp.EmpReportInfo empReportInfo = new EmpReportResp.EmpReportInfo();
            //获取员工基本信息
            EmpBasicInfoDo empBasicInfoDo = basicInfoMap.get(empConcurrentPostDo.getEmpId());
            if (empBasicInfoDo == null) {
                log.warn("empBasicIsDel:{} notShowThisCurrentPostInfo", empConcurrentPostDo.getEmpId());
                // 兼容删除员工 上级领导汇报关系中显示
                continue;
            }
            empReportInfo.setEmpId(empConcurrentPostDo.getEmpId());
            empReportInfo.setWorkno(empBasicInfoDo.getWorkno());
            empReportInfo.setName(empBasicInfoDo.getName());
            empReportInfo.setOrganize(empConcurrentPostDo.getOrganize());
            empReportInfo.setOrganizeTxt(empConcurrentPostDo.getOrganizeTxt());
            empReportInfo.setPost(empConcurrentPostDo.getPost());
            empReportInfo.setPostTxt(empConcurrentPostDo.getPostTxt());
            empReportInfo.setLeadEmpId(empConcurrentPostDo.getPostSuperior());
            empReportInfo.setMajorPost(majorPost);
            empReportInfo.setBid(empConcurrentPostDo.getBid());
            empReportInfo.setDataStartTime(empConcurrentPostDo.getStartDate());
            empReportInfo.setDataEndTime(empConcurrentPostDo.getEndDate());
            empReportInfo.setId(empConcurrentPostDo.getId());
            empReportInfoList.add(empReportInfo);
        }
    }
}
