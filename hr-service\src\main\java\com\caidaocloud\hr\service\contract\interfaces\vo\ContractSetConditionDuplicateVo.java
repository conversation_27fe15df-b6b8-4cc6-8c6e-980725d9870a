package com.caidaocloud.hr.service.contract.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Data
@ApiModel("合同设置重复适用条件VO")
@NoArgsConstructor
public class ContractSetConditionDuplicateVo {
    @ApiModelProperty("是否重复")
    boolean flag;
    @ApiModelProperty("重复msg")
    String msg;

    public ContractSetConditionDuplicateVo(String msg) {
        this.flag = true;
        this.msg = msg;
    }
}
