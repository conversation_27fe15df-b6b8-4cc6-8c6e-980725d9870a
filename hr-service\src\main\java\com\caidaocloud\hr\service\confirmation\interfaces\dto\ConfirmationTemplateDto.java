package com.caidaocloud.hr.service.confirmation.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class ConfirmationTemplateDto implements Serializable {

    @ApiModelProperty("异动类型Id")
    private String defId;

    @ApiModelProperty("异动员工")
    private String empId;

    @ApiModelProperty("转正申请数据Id")
    private String applyId;

    public ConfirmationTemplateDto(String defId, String empId) {
        this.defId = defId;
        this.empId = empId;
    }
}
