package com.caidaocloud.hr.service.employee.application.emp.ruleset.service;

import com.caidaocloud.hr.service.employee.interfaces.vo.MatchConditionVo;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.util.PackMatchUtil.CODE_MAPPING;

/**
 * <AUTHOR>
 */
@Service
public class ApproveManagementService {


    @Resource
    private ITenantFeignClient tenantFeignClient;

    public static final String APPROVE_MANAGEMENT = "approve_management";


    public List<MatchConditionVo> listMatchCondition() {
        List<ConditionDataVo> conditionDataList = tenantFeignClient.getConditionDataByCode(APPROVE_MANAGEMENT, false).getData();
        return conditionDataList.stream().map(data -> {
                    MatchConditionVo vo = JsonEnhanceUtil.toObject(data, MatchConditionVo.class);
                    vo.setCode(CODE_MAPPING.getOrDefault(data.getCode(), data.getCode()));
                    return vo;})
                .collect(Collectors.toList());
    }

}
