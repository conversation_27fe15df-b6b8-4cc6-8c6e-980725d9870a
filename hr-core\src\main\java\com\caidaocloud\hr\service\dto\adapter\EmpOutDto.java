package com.caidaocloud.hr.service.dto.adapter;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EmpOutDto {
    /**
     * 员工工号
     */
    private String workno;

    /**
     * 合同公司ID
     */
    private String companyCode;

    /**
     * 用工类型\员工类型
     */
    private DictSimple empType;

    /**
     * 职级职等
     */
    private String jobGradeCode;

    /**
     * 所属组织编码
     */
    private String organizeCode;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * jobKey
     */
    private String jobKey;

    /**
     * 员工姓名(迪士尼 firstName)
     */
    private String name;

    /**
     * 员工英文名
     */
    private String enName;

    /**
     * 性别
     */
    private DictSimple sex;

    /**
     * 出生日期
     */
    private Long birthDate;

    /**
     * 国籍
     */
    private DictSimple nationality;

    /**
     * 手机号
     */
    private PhoneSimple phone;
}
