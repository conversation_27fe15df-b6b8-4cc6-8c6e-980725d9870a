package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工任职信息DTO")
public class EmpWorkInfoDto {
    @ApiModelProperty("bid")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("员工英文名")
    private String enName;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private String empStatus;
    //
    // @ApiModelProperty("员工头像")
    // private Attachment photo;

    @ApiModelProperty("直接上级")
    private EmpSimple leadEmpId;

    @ApiModelProperty("所属组织Id")
    private String organize;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("职级职等")
    private JobGradeRange jobGrade;

    @ApiModelProperty("关联的职务ID")
    private String job;

    @ApiModelProperty("关联的职务名称")
    private String jobTxt;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("试用期期限")
    private String probation;

    @ApiModelProperty("转正日期")
    private Long confirmationDate;

    @ApiModelProperty("转正状态")
    private String confirmationStatus;

    @ApiModelProperty("员工类型")
    private String empType;

    @ApiModelProperty("离职日期")
    private Long leaveDate;

    @ApiModelProperty("工时制")
    private String workHour;

    @ApiModelProperty("司龄")
    private BigDecimal divisionAge;

    @ApiModelProperty("司龄调整")
    private BigDecimal divisionAgeAdjust;

    @ApiModelProperty("员工公司邮箱")
    private String companyEmail;

    @ApiModelProperty("工作地ID")
    private String workplace;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("入司途径")
    private String joinCompanyWay;

    @ApiModelProperty("成本中心")
    private List<EmpCostCenterDto> costCenters;

    @ApiModelProperty("合同公司ID")
    private String company;

    @ApiModelProperty("合同公司名称")
    private String companyTxt;

    @ApiModelProperty("时间轴数据变更时间")
    private long dataStartTime;

    @ApiModelProperty("预计毕业日期")
    private Long expectGraduateDate;

    @ApiModelProperty("姓名拼音")
    private String namePinyin;

    @ApiModelProperty("岗位英文")
    private String enPostTxt;

    @ApiModelProperty("通讯地址邮编")
    private String mailAddressZipCode;

    @ApiModelProperty("工时制英文")
    private String enWorkHour;

    @ApiModelProperty("学号")
    private String studentNumber;

    @ApiModelProperty("预计离职日期")
    private Long expectedResignDate;

    @ApiModelProperty("社保缴纳地")
    private Address socialSecurity;

    @ApiModelProperty("公积金缴纳地")
    private Address providentFund;

    @ApiModelProperty("直接上级组织ID")
    private String leaderOrganize;

    @ApiModelProperty("直接上级组织名称")
    private String leaderOrganizeTxt;

    @ApiModelProperty("直接上级岗位ID")
    private String leaderPost;

    @ApiModelProperty("直接上级岗位名称")
    private String leaderPostTxt;

    @ApiModelProperty("司龄（至年底）")
    private BigDecimal divisionAgeToYear;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();

    @ApiModelProperty("离职类型")
    private String resignType;

    @ApiModelProperty("离职原因")
    private String resignReason;

    @ApiModelProperty("离职原因（员工申请）")
    private String empResignReason;

    @ApiModelProperty("离职状态")
    private String resignationStatus;

    @ApiModelProperty("合同类型")
    private DictSimple contractType;

    @ApiModelProperty("退休日期")
    private Long retireDate;

    @ApiModelProperty("招聘顾问")
    private EmpSimple recruitment;

    @ApiModelProperty("实习日期")
    private Long internshipDate;

    @ApiModelProperty("基准岗位txt")
    private String benchPostTxt;
    @ApiModelProperty("基准岗位")
    private String benchPost;

    public DataSimple toDataSimple() throws Exception{
        // 员工信息合并编辑使用，比较转成 DataSimple
        DataSimple dataSimple = new DataSimple();
        dataSimple.setBid(bid);
        NestPropertyValue properties = dataSimple.getProperties();
        Field [] fields = this.getClass().getDeclaredFields();
        String fName;
        Class<?> fType;
        Object obj;
        for (Field field : fields) {
            fName  = field.getName();
            fType = field.getType();
            obj = field.get(this);
            if(null == obj){
                continue;
            }
            if (fType.equals(EnumSimple.class)) {
                properties.add(fName, (EnumSimple) obj);
            } else if (fType.equals(EmpSimple.class)) {
                properties.add(fName, (EmpSimple) obj);
            } else if (fType.equals(DictSimple.class)) {
                properties.add(fName, (DictSimple) obj);
            } else if (fType.equals(JobGradeRange.class)) {
                properties.add(fName, (JobGradeRange) obj);
            } else if (fType.equals(Address.class)) {
                properties.add(fName, (Address) obj);
            } else {
                properties.add(fName, obj.toString());
            }
        }

        return dataSimple;
    }

    public static void main(String[] args) {
        EmpWorkInfoDto ewi = new EmpWorkInfoDto();
        ewi.setBid("bid1");
        ewi.setWorkplaceTxt("1txt");
        ewi.setWorkplace("2");
        ewi.setWorkHour("3");
        EmpSimple empSimple = new EmpSimple();
        empSimple.setWorkno("workno1");
        empSimple.setName("name1");
        empSimple.setEnName("enName1");
        empSimple.setEmpId("empId1");
        ewi.setLeadEmpId(empSimple);
        ewi.setJob("job");
        ewi.setJobTxt("jobTxt");
        Address address = FastjsonUtil.convertObject("{province: \"130000\", city: null, area: \"130100\"}", Address.class);
        Map<String, Object> ext = new HashMap<>();
        ext.put("cityWalk", address);
        ewi.setExt(ext);
        try {
            DataSimple dataSimple = ewi.toDataSimple();
            System.out.println(FastjsonUtil.toJsonStr(dataSimple));
        } catch (Exception e){
            e.printStackTrace();
        }
    }
}
