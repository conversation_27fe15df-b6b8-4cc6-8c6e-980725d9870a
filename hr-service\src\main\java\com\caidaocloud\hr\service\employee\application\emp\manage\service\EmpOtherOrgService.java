package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.EmpOtherOrgDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpOtherOrgDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpOtherOrgDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpNewLeaderOrgQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpOtherOrgQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpOrgSelectVo;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class EmpOtherOrgService {
    @Resource
    private EmpOtherOrgDomainService empOtherOrgDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    private void doConvertPersistObject(EmpOtherOrgDo data) {
        if (data.getOrganize() != null) {
            OrgDo org = orgDomainService.selectById(data.getOrganize(), data.getDataStartTime());
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30049));
            data.setOrganizeTxt(org.getName());
        }

        if (data.getPost() != null) {
            PostDo post = postDomainService.selectById(data.getPost(), data.getDataStartTime());
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30109));
            data.setPostTxt(post.getName());
        }

        if (data.getJob() != null) {
            JobDo job = jobDomainService.selectById(data.getJob());
            PreCheck.preCheckArgument(null == job || null == job.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30111));
            data.setJobTxt(job.getName());
        }
    }

    public String save(EmpOtherOrgDto dto) {
        EmpOtherOrgDo data = ObjectConverter.convert(dto, EmpOtherOrgDo.class);
        doConverter(dto, data);
        doConvertPersistObject(data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empOtherOrgDomainService.save(data);
    }

    public void update(EmpOtherOrgDto dto) {
        EmpOtherOrgDo data = ObjectConverter.convert(dto, EmpOtherOrgDo.class);
        doConverter(dto, data);
        doConvertPersistObject(data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empOtherOrgDomainService.update(data);
    }

    private void doConverter(EmpOtherOrgDto source, EmpOtherOrgDo target) {
        if (StringUtils.isNotEmpty(source.getSchemaType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getSchemaType());
            target.setSchemaType(dictSimple);
        }
    }

    public void deleteById(EmpOtherOrgDo data) {
        empOtherOrgDomainService.delete(data);
    }

    public void enable(EmpOtherOrgDo data) {
        empOtherOrgDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public void disable(EmpOtherOrgDo data) {
        empOtherOrgDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE);
    }

    public EmpOtherOrgDo getById(String bid, Long dateTime) {
        EmpOtherOrgDo empOtherOrgDo = empOtherOrgDomainService.selectById(bid, dateTime);
        //如果empSimple的域全是null 将对象设为null
        checkEmpSimpleNull(empOtherOrgDo);
        return empOtherOrgDo;
    }

    public List<EmpOtherOrgDo> getEmpOtherOrgList(EmpOtherOrgQueryDto dto) {
        return empOtherOrgDomainService.getEmpOtherOrgList(dto);
    }

    private void checkEmpSimpleNull(EmpOtherOrgDo data) {
        if (data.getOrgLeader() != null && StringUtils.isEmpty(data.getOrgLeader().getEmpId())) {
            data.setOrgLeader(null);
        }
        if (data.getDirectSuperior() != null && StringUtils.isEmpty(data.getDirectSuperior().getEmpId())) {
            data.setDirectSuperior(null);
        }
    }

    public List<EmpOrgSelectVo> getOrgByEmp(String empId, Long dataTime) {
        return empConcurrentPostService.getOrgByEmp(empId, dataTime);
    }

    public List<EmpOrgSelectVo> getPostByEmp(String empId, String orgId, Long dataTime) {
        EmpWorkInfoDo emp = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (StringUtil.isEmpty(emp.getEmpId())) {
            return Lists.newArrayList();
        }

        EmpOrgSelectVo keyValue = new EmpOrgSelectVo();
        PostDo postDo = postDomainService.selectById(emp.getPost(), dataTime);
        if (StringUtil.isNotEmpty(postDo.getBid()) && StringUtil.isNotEmpty(postDo.getOrgId())
                && Arrays.asList(postDo.getOrgId().split(",")).contains(orgId)) {
            keyValue.setName(postDo.getName());
            keyValue.setCode(postDo.getCode());
            keyValue.setValue(postDo.getBid());
            keyValue.setMajorPosition(true);
        }

        List<EmpOrgSelectVo> collect = empConcurrentPostService.getPostByEmp(empId, orgId, dataTime);
        if (keyValue.isMajorPosition()) {
            collect.add(keyValue);
        }
        return collect;
    }

    public List<EmpOrgSelectVo> getNewOrgByEmp(EmpNewLeaderOrgQueryDto dto) {
        return empConcurrentPostService.getNewOrgByEmp(dto);
    }

    public List<EmpOrgSelectVo> newPostByEmp(EmpNewLeaderOrgQueryDto dto) {
        String empId = dto.getEmpId();
        Long dataTime = dto.getDataTime();
        String orgId = dto.getOrgId();
        EmpWorkInfoDo emp = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (StringUtil.isEmpty(emp.getEmpId())) {
            return Lists.newArrayList();
        }

        // 替换主岗岗位
        replaceNewPost(emp, dto);

        EmpOrgSelectVo keyValue = new EmpOrgSelectVo();
        PostDo postDo = postDomainService.selectById(emp.getPost(), dataTime);
        if (StringUtil.isNotEmpty(postDo.getBid()) && StringUtil.isNotEmpty(postDo.getOrgId())
                && Arrays.asList(postDo.getOrgId().split(",")).contains(orgId)) {
            keyValue.setName(postDo.getName());
            keyValue.setCode(postDo.getCode());
            keyValue.setValue(postDo.getBid());
            keyValue.setMajorPosition(true);
        }

        List<EmpOrgSelectVo> collect = empConcurrentPostService.getNewPostByEmp(dto);
        if (keyValue.isMajorPosition()) {
            collect.add(keyValue);
        }
        return collect;
    }


    public void replaceNewPost(EmpWorkInfoDo emp, EmpNewLeaderOrgQueryDto dto) {
        if (Objects.isNull(dto.getMajorPost()) || Objects.isNull(dto.getEmpId()) || Objects.isNull(dto.getUpdEmpId())) {
            return;
        }
        // 更新前的岗位与数据库岗位一致
        if (!dto.getMajorPost() || !dto.getUpdEmpId().equals(dto.getEmpId()) || !dto.getCurrentPostId().equals(emp.getPost())) {
            return;
        }
        emp.setPost(dto.getUpdPostId());
    }
}
