package com.caidaocloud.hr.service.archive.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件归档配置参数
 * created by: FoAng
 * create time: 5/6/2024 10:52 上午
 */
@Data
@Component
@ConfigurationProperties(prefix = "caidaocloud.archive")
public class ArchiveProperty {

    /**
     * 是否开启文件归档
     */
    private boolean enable;

    /**
     * 初始化加载mode,feign或者mq
     */
    private String initMode = "feign";

    /**
     * 是否加载history文件历史
     */
    private boolean loadHistory;

    /**
     * 文件归档租户，初始启动需加载历史缓存服务
     */
    private String[] tenantIds;

}
