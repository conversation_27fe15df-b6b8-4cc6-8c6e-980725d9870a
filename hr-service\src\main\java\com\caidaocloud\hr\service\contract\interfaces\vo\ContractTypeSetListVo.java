package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ContractTypeSetListVo {
    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("合同类型名称")
    private String contractTypeSetName;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("合同公司bid")
    private List<String> company;

    @ApiModelProperty("合同公司名称")
    private List<String> companyTxt;

    @ApiModelProperty("员工类型")
    private List<String> empType;

    @ApiModelProperty("签订类型")
    private List<String> signType;

    @ApiModelProperty("状态")
    private EnumSimple status;

    @ApiModelProperty("是否有合同期限")
    private String periodType;

    @ApiModelProperty("合同期限（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("合同类型Simple")
    private DictSimple contractTypeSimple;

    @ApiModelProperty("合同类型名称TXT")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同结束日期类型")
    private String endDateType;

    @ApiModelProperty("是否根据预计毕业日期判断")
    private Boolean baseExpectGraduateDate;

    @ApiModelProperty("试用期期限")
    private EnumSimple probation;
}
