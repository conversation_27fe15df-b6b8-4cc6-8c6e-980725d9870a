package com.caidaocloud.hr.service.agreement.infrastructure.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.agreement.domain.entity.UserAgreementDo;
import com.caidaocloud.hr.service.agreement.infrastructure.repository.UserAgreementRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 14/10/2024 1:35 下午
 */
@Slf4j
@Service
public class UserAgreementRepositoryImpl extends BaseRepositoryImpl<UserAgreementDo> implements UserAgreementRepository {

    /**
     * 获取用户协议简介列表
     * @return
     */
    @Override
    public PageResult<UserAgreementDo> getSummaryList(String identifier, BasePage page) {
        DataFilter dataFilter = getBaseFilter();
        PageResult<Map<String, String>> pageResult = DataQuery.identifier(identifier).queryInvisible()
                .filterProperties(dataFilter, Lists.newArrayList("bid", "i18Name", "summary", "type", "status", "content"), System.currentTimeMillis());
        List<UserAgreementDo> resultList = Optional.ofNullable(pageResult).filter(it -> CollectionUtils.isNotEmpty(it.getItems()))
                .map(it -> it.getItems().stream().map(o1 -> {
                            String status = o1.remove("status");
                            UserAgreementDo agreementDo = FastjsonUtil.convertObject(o1, UserAgreementDo.class);
                            agreementDo.setStatus(status);
                            return agreementDo;
                        }).collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
        return new PageResult<>(resultList, page.getPageNo(), page.getPageSize(), pageResult.getTotal());
    }

    @Override
    public void changeStatusByType(String identifier, String bid, String type, Integer status) {
        DataFilter dataFilter = getBaseFilter().andNotIn("bid", Lists.newArrayList(bid))
                .andEq("type", type);
        PageResult<Map<String, String>> pageResult = DataQuery.identifier(identifier).queryInvisible()
                .filterProperties(dataFilter, Lists.newArrayList("bid"), System.currentTimeMillis());
        if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getItems())) {
            for (Map<String, String> item : pageResult.getItems()) {
                UserAgreementDo itemData = selectById(item.get("bid"), identifier);
                itemData.setStatus(status);
                DataUpdate.identifier(identifier).update(itemData);
            }
        }
    }

    @Override
    public UserAgreementDo detailByType(String identifier, String type) {
        DataFilter dataFilter = getBaseFilter().andEq("type", type)
                .andEq("status", "1");
        PageResult<UserAgreementDo> pageResult = DataQuery.identifier(identifier).queryInvisible()
                .filter(dataFilter, UserAgreementDo.class);
        return Optional.ofNullable(pageResult).filter(it -> CollectionUtils.isNotEmpty(it.getItems()))
                .map(it -> it.getItems().get(0)).orElse(null);
    }
}
