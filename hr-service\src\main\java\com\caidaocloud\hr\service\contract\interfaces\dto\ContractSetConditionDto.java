package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Data
@ApiModel("合同设置自定义条件、合同期限DTO")
@Accessors(chain = true)
public class ContractSetConditionDto extends BasePage {
    @ApiModelProperty("合同设置Bid")
    String contractTypeSet;
    @ApiModelProperty("分组条件树")
    private ConditionTreeDto conditionTree;
    @ApiModelProperty("合同期限类型")
    String periodType;
    @ApiModelProperty("合同期限（月）")
    Integer contractPeriod;
    @ApiModelProperty("合同结束日期类型")
    String endDateType;
    @ApiModelProperty("有无试用期")
    Boolean probation;
    @ApiModelProperty("试用期（月）")
    Integer probationPeriod;
    @ApiModelProperty("是否根据预计毕业日期判断")
    Boolean baseExpectGraduateDate;
    @ApiModelProperty("试用期期限")
    EnumSimple probationDeadline;
    @ApiModelProperty("是否根据退休日期判断")
    Boolean baseRetireDate;
}
