package com.caidaocloud.hr.service.enums.system;

/**
 * 试用期期限
 * <AUTHOR>
 * @date 2023/3/24
 */
public enum ProbationType {
	// 无试用期
	NONE(0),
	// 一个月
	ONE(1),
	// 两个月
	TWO(2),
	// 三个月
	THREE(3),
	// 六个月
	SIX(6),
	;

	public final int value;

	ProbationType(int value) {
		this.value = value;
	}

	public static ProbationType getByValue(String value) {
		if (value==null) {
			return null;
		}
		for (ProbationType type : ProbationType.values()) {
			if (String.valueOf(type.value).equals(value)) {
				return type;
			}
		}
		return null;
	}
}
