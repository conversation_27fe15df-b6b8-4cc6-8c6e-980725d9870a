package com.caidaocloud.hr.service.employee.application.dataimport.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.dto.onboarding.ImportPostSyncDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.feign.dict.service.ProvinceCityService;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.organization.application.post.service.PostService;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.contract.application.event.dto.EmpChangeMessageDto;
import com.caidaocloud.hr.service.contract.application.event.publish.EmpChangePublish;
import com.caidaocloud.hr.service.contract.domain.service.EmpContractTypeSetRelDomainService;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInfoImportDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpInfoImportDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.WorknoAutoDomainService;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.EmpInfoImportPo;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.organization.domain.jobgrade.factory.JobGradeRangeFactory.singleJobGrade;

@Slf4j
@Service
public class EmpInfoImportService extends DataImportService<EmpInfoImportDo, EmpInfoImportPo> {

    private static final String WORK_INFO_IDENTIFIER = "entity.hr.EmpWorkInfo";
    private static final String EMPPRIVATE_INFO_IDENTIFIER = "entity.hr.EmpPrivateInfo";
    @Resource
    private CompanyService companyService;
    @Resource
    private WorkplaceService workplaceService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private DictService dictService;
    @Resource
    private PostService postService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private EmpInfoImportDomainService empInfoImportDomainService;
    @Resource
    private WorknoAutoDomainService worknoAutoDomainService;
    @Resource
    private EmpContractTypeSetRelDomainService empContractTypeSetRelDomainService;
    @Resource
    private EmpChangePublish empChangePublish;
    @Resource
    private ProvinceCityService provinceCityService;
    @NacosValue("${caidaocloud.import.syncCreatePost:false}")
    private boolean syncCreatePost = false;
    @NacosValue("${caidaocloud.import.jobkey.required:false}")
    public boolean jobKeyRequired;


    private static Map<String, Map<String, KeyValue>> staticMap = new HashMap<>();
    private static Map<String, Map<String, PropertyEnumDefDto>> staticPropMap = new HashMap<>();
    private static Map<String, Map<String, PostDo>> postMaps = new HashMap<>();
    private static Map<String, Map<String, OrgDo>> orgDoMaps = new HashMap<>();
    //        获取公司集合
    private static Map<String, Map<String, CompanyDo>> companyMaps = new HashMap<>();
    private static Map<String, Map<String, WorkplaceDo>> workplacesMaps = new HashMap<>();
    private static Map<String, Map<String, JobGradeDo>> jobGradeMaps = new HashMap<>();
    private static String businessCode = "EMP_INFO_IMPORT";

    private static final String EmployTypeDicCode = "EmployType", GenderDictCode = "Gender", FamilyTypeDictCode = "FamilyType",
            EntryRouteDictCode = "EntryRoute", NationalityDictCode = "Nationality", NationDictCode = "Nation",
            PoliticalPartyDictCode = "PoliticalParty";
    private static final String PostList = "PostList", CompanyList = "CompanyList", WorkPlaceList = "WorkPlaceList", OrganizationList = "OrganizationList";
    private final String JOB_GRADE = "JOB_GRADE";

    private static final String EmpStatusProp = "empStatus", ConfirmationStatusProp = "confirmationStatus", WorkHourProp = "workHour",
            ProbationProp = "probation", EmpPriMaritalStatusProp = "maritalStatus",
            EmpPriFertilityStatusProp = "fertilityStatus", EmpPriCardTypeProp = "cardType";

    @Override
    public String getExcelCode() {
        return businessCode;
    }

    public List<EmpInfoImportDo> getPoDataFromExcel(InputStream inputStream) {
        return empInfoImportDomainService.getEmpInfoImportDoFromExcel(inputStream);
    }

    public List<EmpInfoImportDo> batchInsertUpdateData(List<EmpInfoImportDo> passEmpInfoList) {
        if (null == passEmpInfoList || passEmpInfoList.size() == 0) {
            return new ArrayList<>();
        }

        List<EmpInfoImportDo> errorList = new ArrayList<>();
        // 获取 手机号 证件号与其他人重复的数据
        passEmpInfoList.removeAll(duplicateVerificationData(errorList, passEmpInfoList));
        if (passEmpInfoList.isEmpty()) {
            return errorList;
        }

        // 生成省市数据
        provinceCityService.transferEmpNativePlace(passEmpInfoList, UserContext.getTenantId());

        List<String> workNos = new ArrayList<>();
        for (EmpInfoImportDo emp : passEmpInfoList) {
            workNos.add(emp.getWorkno());
            if (null != emp.getLeadEmpIdTxt()) {
                workNos.add(emp.getLeadEmpIdTxt());
            }
        }
        List<EmpBasicInfoDo> basicInfoList = empBasicInfoService.getEmpBasicInfoListByWorkNo(workNos, System.currentTimeMillis());
        Map<String, EmpBasicInfoDo> oldEmpInfoMap = new HashMap<>();
        for (EmpBasicInfoDo basicInfoDo : basicInfoList) {
            oldEmpInfoMap.put(basicInfoDo.getWorkno(), basicInfoDo);
        }
        List<EmpInfoImportDo> insertList = new ArrayList<>();
        List<EmpInfoImportDo> updateList = new ArrayList<>();
        for (EmpInfoImportDo importDo : passEmpInfoList) {
            EmpBasicInfoDo basicInfo = oldEmpInfoMap.get(importDo.getWorkno());
            EmpBasicInfoDo leadBasicInfo = oldEmpInfoMap.get(importDo.getLeadEmpIdTxt());
            if (null != importDo.getLeadEmpIdTxt() && null != leadBasicInfo) {
                EmpSimple leadEmpId = new EmpSimple();
                leadEmpId.setName(leadBasicInfo.getName());
                leadEmpId.setEnName(leadBasicInfo.getEnName());
                leadEmpId.setEmpId(leadBasicInfo.getBid());
                leadEmpId.setDataStartTime(leadBasicInfo.getDataStartTime());
                leadEmpId.setWorkno(leadBasicInfo.getWorkno());
                importDo.setLeadEmpId(leadEmpId);
            } else {
//                errorList.add(importDo);
//                setEmptyTips(importDo, "直属上级有误");
//                continue;
            }
            if (basicInfo == null) {
                insertList.add(importDo);
            } else {
                importDo.setEmpId(basicInfo.getBid());
                updateList.add(importDo);
            }
        }
        errorList.addAll(insertEmpInfoImportList(insertList));
        errorList.addAll(updateEmpInfoImportList(updateList, oldEmpInfoMap));

        return errorList;
    }

    public void dealErrorListBeforeWrite(List<EmpInfoImportDo> list) {
        for (EmpInfoImportDo emp : list) {
            emp.setPostTxt(emp.getPostCode() == null ? emp.getPostTxt() : emp.getPostCode());
            emp.setOrganizeTxt(emp.getOrgCode() == null ? emp.getOrganizeTxt() : emp.getOrgCode());
        }
    }

    private List<EmpInfoImportDo> insertEmpInfoImportList(List<EmpInfoImportDo> list) {
        List<EmpInfoImportDo> errorList = new ArrayList<>();
        String tenantId = getTenantId();
        long userId = getUserId();
        Map<String, PostDo> postMap = postMaps.get(getMapKeyWithTenantId(PostList, tenantId));
        Map<String, OrgDo> orgMap = orgDoMaps.get(getMapKeyWithTenantId(OrganizationList, tenantId));
        Map<String, CompanyDo> companyMap = companyMaps.get(getMapKeyWithTenantId(CompanyList, tenantId));
        Map<String, WorkplaceDo> workplacesMap = workplacesMaps.get(getMapKeyWithTenantId(WorkPlaceList, tenantId));
        errorList = empInfoImportDomainService.batchInsertImportDo(list, orgMap, companyMap, postMap, workplacesMap, tenantId, userId);
        return errorList;
    }

    private List<EmpInfoImportDo> updateEmpInfoImportList(List<EmpInfoImportDo> list, Map<String, EmpBasicInfoDo> basicInfoDoMap) {
        if(null == list || list.isEmpty()){
            return new ArrayList<>();
        }

        String tenantId = getTenantId();
        long userId = getUserId();
        Map<String, PostDo> postMap = postMaps.get(getMapKeyWithTenantId(PostList, tenantId));
        Map<String, OrgDo> orgMap = orgDoMaps.get(getMapKeyWithTenantId(OrganizationList, tenantId));
        Map<String, CompanyDo> companyMap = companyMaps.get(getMapKeyWithTenantId(CompanyList, tenantId));
        Map<String, WorkplaceDo> workplacesMap = workplacesMaps.get(getMapKeyWithTenantId(WorkPlaceList, tenantId));
        List<EmpInfoImportDo> errorList = empInfoImportDomainService.updateEmpInfoImportList(list, basicInfoDoMap, orgMap, companyMap, postMap, workplacesMap, tenantId, userId);
        return errorList;
    }

    private void setEmptyTips(EmpInfoImportDo emp, String tip) {
        emp.setCheckEmpty(true);
        if (null == emp.getCheckEmptyTips()) {
            emp.setCheckEmptyTips(tip);
        } else {
            emp.setCheckEmptyTips(emp.getCheckEmptyTips() + "，" + tip);
        }
    }

    private void installDate(EmpInfoImportDo emp) {
        DateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        try {
            if (emp.getBirthDateTxt() != null) {
                Date date = format.parse(emp.getBirthDateTxt());
                emp.setBirthDate(date.getTime());
                emp.setDivisionAge(getAgeByBirthdayStr(date.getTime()));
            }
            if (emp.getHireDateTxt() != null) {
                emp.setHireDate(format.parse(emp.getHireDateTxt()).getTime());
            }
            if (emp.getConfirmationDateTxt() != null) {
                emp.setConfirmationDate(format.parse(emp.getConfirmationDateTxt()).getTime());
            }
            if (emp.getCardEffectiveDateTxt() != null) {
                emp.setCardEffectiveDate(format.parse(emp.getCardEffectiveDateTxt()).getTime());
            }
            if (emp.getLeaveDateTxt() != null) {
                emp.setLeaveDate(format.parse(emp.getLeaveDateTxt()).getTime());
            }
            if (emp.getExpectGraduateDateTxt() != null) {
                emp.setExpectGraduateDate(format.parse(emp.getExpectGraduateDateTxt()).getTime());
            }
        } catch (ParseException e) {
            setEmptyTips(emp, "日期转换异常");
            log.error("import EmpInfo Data parse error:{}", e.getMessage());
            // throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30037));
        }
    }

    private int getAgeByBirthdayStr(long birthDay) {
        long nowTime = System.currentTimeMillis();
        long year = 31536000000l;
        Double age = Math.ceil((nowTime - birthDay) / year);
        return age.intValue();
    }

    private void installWorkHourProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> workHourMap) {
        if (StringUtil.isEmpty(emp.getWorkHourTxt())) {
            return;
        }
        if (null != workHourMap && null != workHourMap.get(emp.getWorkHourTxt())) {
            PropertyEnumDefDto dto = workHourMap.get(emp.getWorkHourTxt());
            EnumSimple status = new EnumSimple();
            status.setValue(dto.getValue());
            status.setText(dto.getDisplay());
            emp.setWorkHour(status);
        } else {
            if (null != workHourMap && workHourMap.size() > 0) {
                setEmptyTips(emp, "工时制不存在");
            }
        }
    }

    private void installConfirmationStatusProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> confirmationStatusMap) {
        if (StringUtil.isEmpty(emp.getConfirmationStatusTxt())) {
            return;
        }
        if (null != confirmationStatusMap && null != confirmationStatusMap.get(emp.getConfirmationStatusTxt())) {
            PropertyEnumDefDto dto = confirmationStatusMap.get(emp.getConfirmationStatusTxt());
            EnumSimple status = new EnumSimple();
            status.setValue(dto.getValue());
            status.setText(dto.getDisplay());
            emp.setConfirmationStatus(status);
        } else {
            if (null != confirmationStatusMap && confirmationStatusMap.size() > 0) {
                setEmptyTips(emp, "转正状态不存在");
            }
//            emp.setConfirmationStatus(null);
        }
    }

    private void installWorkplaceProp(EmpInfoImportDo emp, Map<String, WorkplaceDo> workplacesMap) {
        if (StringUtil.isEmpty(emp.getWorkplaceTxt())) {
            return;
        }
        if (null != workplacesMap && null != workplacesMap.get(emp.getWorkplaceTxt())) {
            String placeId = workplacesMap.get(emp.getWorkplaceTxt()).getBid();
            emp.setWorkplace(placeId);
        } else {
            if (null != workplacesMap && workplacesMap.size() > 0) {
                setEmptyTips(emp, "工作地不存在");
            }
//            emp.setWorkplace(null);
        }
    }

    private void installCompanyProp(EmpInfoImportDo emp, Map<String, CompanyDo> companyMap) {
        if (null != companyMap && null != companyMap.get(emp.getCompanyTxt())) {
            String companyId = companyMap.get(emp.getCompanyTxt()).getBid();
            String code = companyMap.get(emp.getCompanyTxt()).getOrganizationCode();
            emp.setCompany(companyId);
            emp.setCompanyCode(code);
        } else {
            if (null != companyMap && companyMap.size() > 0) {
                setEmptyTips(emp, "公司不存在");
            }
        }
    }

    private void installOrganization(EmpInfoImportDo emp, Map<String, OrgDo> organizationMap) {
        if (null != organizationMap && null != organizationMap.get(emp.getOrganizeTxt())) {
            String companyId = organizationMap.get(emp.getOrganizeTxt()).getBid();
            emp.setOrganize(companyId);
            emp.setOrgCode(organizationMap.get(emp.getOrganizeTxt()).getCode());
            emp.setOrganizeTxt(organizationMap.get(emp.getOrganizeTxt()).getName());
        } else {
            if (null != organizationMap && organizationMap.size() > 0) {
                setEmptyTips(emp, "组织不存在");
            }
        }
    }

    private void installPostProp(EmpInfoImportDo emp, Map<String, PostDo> postMap) {
        if (StringUtil.isEmpty(emp.getPostCode())) {
            return;
        }
        if (null != postMap && null != postMap.get(emp.getPostCode())) {
            PostDo post = postMap.get(emp.getPostCode());
            emp.setPostTxt(post.getName());
            String postId = post.getBid();
            String orgId = post.getOrgId();
            if (null != emp.getOrganize() && !emp.getOrganize().equals(orgId)) {
                setEmptyTips(emp, "岗位所属组织不匹配");
            }
            String jobId = post.getJobId();
            String jobGradeId = null == post.getJobGrade() ? null : post.getJobGrade().getEndGrade();
            emp.setPost(postId);
            emp.setPostCode(post.getCode());
            emp.setOrganize(orgId);
            emp.setJob(jobId);
            emp.setJobGrade(jobGradeId);
        } else {
//            if(null!=postMap && postMap.size()>0){
            setEmptyTips(emp, "岗位编码不存在");
//            }
//            emp.setPostTxt(null);
//            emp.setPost(null);
//            emp.setJob(null);
//            emp.setJobGrade(null);
        }
    }

    private void installFamilyTypeProp(EmpInfoImportDo emp, Map<String, KeyValue> familyTypeMap) {
        if (StringUtil.isEmpty(emp.getFamilyTypeTxt())) {
            return;
        }
        if (null != familyTypeMap && null != familyTypeMap.get(emp.getFamilyTypeTxt())) {
            KeyValue kv = familyTypeMap.get(emp.getFamilyTypeTxt());
            DictSimple dict = new DictSimple();
            dict.setText(kv.getText());
            dict.setValue(String.valueOf(kv.getValue()));
            emp.setFamilyType(dict);
        } else {
            if (null != familyTypeMap && familyTypeMap.size() > 0) {
                setEmptyTips(emp, "户口类型不存在");
            }
//            emp.setFamilyType(null);
        }
    }

    private void installNationalityProp(EmpInfoImportDo emp, Map<String, KeyValue> nationalityMap) {
        if (null != nationalityMap && null != nationalityMap.get(emp.getNationalityTxt())) {
            KeyValue kv = nationalityMap.get(emp.getNationalityTxt());
            DictSimple nationality = new DictSimple();
            nationality.setText(kv.getText());
            nationality.setValue(String.valueOf(kv.getValue()));
            emp.setNationality(nationality);
        } else {
            if (null != nationalityMap && nationalityMap.size() > 0) {
                setEmptyTips(emp, "国籍不存在");
            }
        }
    }

    private void installNationProp(EmpInfoImportDo emp, Map<String, KeyValue> nationMap) {
        if (StringUtil.isEmpty(emp.getNationTxt())) {
            return;
        }
        if (null != nationMap && null != nationMap.get(emp.getNationTxt())) {
            KeyValue kv = nationMap.get(emp.getNationTxt());
            DictSimple nation = new DictSimple();
            nation.setText(kv.getText());
            nation.setValue(String.valueOf(kv.getValue()));
            emp.setNation(nation);
        } else {
            if (null != nationMap && nationMap.size() > 0) {
                setEmptyTips(emp, "民族不存在");
            }
//            emp.setNation(null);
        }
    }

    private void installCompanyWayProp(EmpInfoImportDo emp, Map<String, KeyValue> companyWayMap) {
        if (StringUtil.isEmpty(emp.getJoinCompanyWayTxt())) {
            return;
        }
        if (null != companyWayMap && null != companyWayMap.get(emp.getJoinCompanyWayTxt())) {
            KeyValue way = companyWayMap.get(emp.getJoinCompanyWayTxt());
            DictSimple joinWay = new DictSimple();
            joinWay.setText(way.getText());
            joinWay.setValue(String.valueOf(way.getValue()));
            emp.setJoinCompanyWay(joinWay);
        } else {
            if (null != companyWayMap && companyWayMap.size() > 0) {
                setEmptyTips(emp, "入司途径不存在");
            }
//            emp.setJoinCompanyWay(null);
        }
    }

    private void installEmpStatusProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> empStatusMap) {
        if (null != empStatusMap && null != empStatusMap.get(emp.getEmpStatusTxt())) {
            PropertyEnumDefDto status = empStatusMap.get(emp.getEmpStatusTxt());
            EnumSimple empStatus = new EnumSimple();
            empStatus.setText(status.getDisplay());
            empStatus.setValue(status.getValue());
            emp.setEmpStatus(empStatus);
        } else {
            if (null != empStatusMap && empStatusMap.size() > 0) {
                setEmptyTips(emp, "员工状态不存在");
            }
        }
    }

    private void installSexProp(EmpInfoImportDo emp, Map<String, KeyValue> sexMap) {
        if (StringUtil.isEmpty(emp.getSexTex())) {
            return;
        }
        if (null != sexMap && null != sexMap.get(emp.getSexTex())) {
            KeyValue kv = sexMap.get(emp.getSexTex());
            DictSimple sex = new DictSimple();
            sex.setText(emp.getSexTex());
            sex.setValue(String.valueOf(kv.getValue()));
            emp.setSex(sex);
        } else {
            if (null != sexMap && sexMap.size() > 0) {
                setEmptyTips(emp, "性别不存在");
            }
        }
    }

    private void installEmpTypeProp(EmpInfoImportDo emp, Map<String, KeyValue> empTypeMap) {
        if (null != empTypeMap && null != empTypeMap.get(emp.getEmpTypeTxt())) {
            KeyValue kv = empTypeMap.get(emp.getEmpTypeTxt());
            DictSimple empType = new DictSimple();
            empType.setText(emp.getEmpTypeTxt());
            empType.setValue(String.valueOf(kv.getValue()));
            emp.setEmpType(empType);
        } else {
            if (null != empTypeMap && empTypeMap.size() > 0) {
                setEmptyTips(emp, "用工类型不存在");
            }
        }
    }

    protected String getMapKeyWithTenantId(String key, String tenantId) {
        return String.format("%s_%s", key, tenantId);
    }

    public boolean installProp(EmpInfoImportDo emp) {
        String tenantId = getTenantId();
        Map<String, KeyValue> companyWayMap = staticMap.get(getMapKeyWithTenantId(EntryRouteDictCode, tenantId));
        Map<String, KeyValue> sexMap = staticMap.get(getMapKeyWithTenantId(GenderDictCode, tenantId));
        Map<String, KeyValue> nationMap = staticMap.get(getMapKeyWithTenantId(NationDictCode, tenantId));
        Map<String, KeyValue> nationalityMap = staticMap.get(getMapKeyWithTenantId(NationalityDictCode, tenantId));
        Map<String, KeyValue> politicalPartyOutLookMap = staticMap.get(getMapKeyWithTenantId(PoliticalPartyDictCode, tenantId));
        Map<String, KeyValue> empTypeMap = staticMap.get(getMapKeyWithTenantId(EmployTypeDicCode, tenantId));
        Map<String, KeyValue> familyTypeMap = staticMap.get(getMapKeyWithTenantId(FamilyTypeDictCode, tenantId));
        Map<String, PropertyEnumDefDto> empStatusMap = staticPropMap.get(getMapKeyWithTenantId(EmpStatusProp, tenantId));
        Map<String, PropertyEnumDefDto> confirmationStatusMap = staticPropMap.get(getMapKeyWithTenantId(ConfirmationStatusProp, tenantId));
        Map<String, PropertyEnumDefDto> workHourMap = staticPropMap.get(getMapKeyWithTenantId(WorkHourProp, tenantId));
        Map<String, PostDo> postMap = postMaps.get(getMapKeyWithTenantId(PostList, tenantId));
        Map<String, OrgDo> orgMap = orgDoMaps.get(getMapKeyWithTenantId(OrganizationList, tenantId));
        Map<String, CompanyDo> companyMap = companyMaps.get(getMapKeyWithTenantId(CompanyList, tenantId));
        Map<String, WorkplaceDo> workplacesMap = workplacesMaps.get(getMapKeyWithTenantId(WorkPlaceList, tenantId));
        Map<String, PropertyEnumDefDto> probationMap = staticPropMap.get(getMapKeyWithTenantId(ProbationProp, tenantId));
//        Map<String, PropertyEnumDefDto> empPriFamilyTypeMap = staticPropMap.get(getMapKeyWithTenantId(EmpPriFamilyTypeProp, tenantId));
        Map<String, PropertyEnumDefDto> empPriMaritalStatusMap = staticPropMap.get(getMapKeyWithTenantId(EmpPriMaritalStatusProp, tenantId));
        Map<String, PropertyEnumDefDto> empPriFertilityStatusMap = staticPropMap.get(getMapKeyWithTenantId(EmpPriFertilityStatusProp, tenantId));
        Map<String, PropertyEnumDefDto> empPriCardTypeMap = staticPropMap.get(getMapKeyWithTenantId(EmpPriCardTypeProp, tenantId));
        Map<String, JobGradeDo> jobGradeDoMap = jobGradeMaps.get(getMapKeyWithTenantId(JOB_GRADE, tenantId));
        installSexProp(emp, sexMap);
        installEmpTypeProp(emp, empTypeMap);
        installOrganization(emp, orgMap);
        installEmpStatusProp(emp, empStatusMap);
        installCompanyWayProp(emp, companyWayMap);
        installEmpPriPoliticalOutlookProp(emp, politicalPartyOutLookMap);
        installNationProp(emp, nationMap);
        installNationalityProp(emp, nationalityMap);
        installFamilyTypeProp(emp, familyTypeMap);
        installPostProp(emp, postMap);
        installCompanyProp(emp, companyMap);
        installWorkplaceProp(emp, workplacesMap);
        installConfirmationStatusProp(emp, confirmationStatusMap);
        installWorkHourProp(emp, workHourMap);
        installProbationProp(emp, probationMap);
//        installEmpPriFamilyTypeProp(emp, empPriFamilyTypeMap);
        installEmpPriMaritalStatusProp(emp, empPriMaritalStatusMap);
        installEmpPriFertilityStatusProp(emp, empPriFertilityStatusMap);
        installEmpPriCardTypeProp(emp, empPriCardTypeMap);

        installDate(emp);
        installJobGrade(emp, jobGradeDoMap);

        if (emp.isCheckEmpty()) {
            return false;
        }
        return true;
    }

    private void installJobGrade(EmpInfoImportDo emp, Map<String, JobGradeDo> jobGradeDoMap) {
        if (StringUtils.isEmpty(emp.getJobGradeCode())) {
            return;
        }
        JobGradeDo jobGradeDo = jobGradeDoMap.get(emp.getJobGradeCode());
        if (jobGradeDo == null) {
            setEmptyTips(emp, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32033));
            return;
        }
        emp.setJobGradeRange(singleJobGrade(jobGradeDo));
    }

    private void installEmpPriCardTypeProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> empPriCardTypeMap) {
        if (null != empPriCardTypeMap && null != empPriCardTypeMap.get(emp.getCardTypeTxt())) {
            PropertyEnumDefDto dto = empPriCardTypeMap.get(emp.getCardTypeTxt());
            EnumSimple type = new EnumSimple();
            type.setValue(dto.getValue());
            type.setText(dto.getDisplay());
            emp.setCardType(type);
        } else {
            if (null != empPriCardTypeMap && empPriCardTypeMap.size() > 0) {
                setEmptyTips(emp, "证件类型不存在");
            }
        }
    }

    private void installEmpPriPoliticalOutlookProp(EmpInfoImportDo emp, Map<String, KeyValue> empPriPoliticalOutlookMap) {
        if (StringUtil.isEmpty(emp.getPoliticalOutlookTxt())) {
            return;
        }
        if (null != empPriPoliticalOutlookMap && null != empPriPoliticalOutlookMap.get(emp.getPoliticalOutlookTxt())) {
            KeyValue kv = empPriPoliticalOutlookMap.get(emp.getPoliticalOutlookTxt());
            DictSimple empType = new DictSimple();
            empType.setText(emp.getPoliticalOutlookTxt());
            empType.setValue(String.valueOf(kv.getValue()));
            emp.setPoliticalOutlook(empType);
        } else {
            if (null != empPriPoliticalOutlookMap && empPriPoliticalOutlookMap.size() > 0) {
                setEmptyTips(emp, "政治面貌不存在");
            }
//            emp.setPoliticalOutlook(null);
        }
    }

    private void installEmpPriFertilityStatusProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> empPriFertilityStatusMap) {
        if (null != empPriFertilityStatusMap && null != empPriFertilityStatusMap.get(emp.getFertilityStatusTxt())) {
            PropertyEnumDefDto dto = empPriFertilityStatusMap.get(emp.getFertilityStatusTxt());
            EnumSimple status = new EnumSimple();
            status.setValue(dto.getValue());
            status.setText(dto.getDisplay());
            emp.setFertilityStatus(status);
        } else {
//            if(null!=empPriFertilityStatusMap && empPriFertilityStatusMap.size()>0) {
//                setEmptyTips(emp, "生育状态不存在");
//            }
            emp.setFertilityStatus(null);
        }
    }

    private void installEmpPriMaritalStatusProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> empPriMaritalStatusMap) {
        if (StringUtil.isEmpty(emp.getMaritalStatusTxt())) {
            return;
        }
        if (null != empPriMaritalStatusMap && null != empPriMaritalStatusMap.get(emp.getMaritalStatusTxt())) {
            PropertyEnumDefDto dto = empPriMaritalStatusMap.get(emp.getMaritalStatusTxt());
            EnumSimple status = new EnumSimple();
            status.setValue(dto.getValue());
            status.setText(dto.getDisplay());
            emp.setMaritalStatus(status);
        } else {
            if (null != empPriMaritalStatusMap && empPriMaritalStatusMap.size() > 0) {
                setEmptyTips(emp, "婚姻状态不存在");
            }
//            emp.setMaritalStatus(null);
        }
    }

//    private void installEmpPriFamilyTypeProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> empPriFamilyTypeMap) {
//        if(null!=empPriFamilyTypeMap && null!=empPriFamilyTypeMap.get(emp.getWorkHourTxt())){
//            PropertyEnumDefDto dto = empPriFamilyTypeMap.get(emp.getWorkHourTxt());
//            DictSimple status = new DictSimple();
//            status.setValue(dto.getValue());
//            status.setText(dto.getDisplay());
//            emp.setFamilyType(status);
//        }else {
//            if(null!=empPriFamilyTypeMap && empPriFamilyTypeMap.size()>0) {
//                setEmptyTips(emp, "工时制不存在");
//            }
//        }
//    }

    private void installProbationProp(EmpInfoImportDo emp, Map<String, PropertyEnumDefDto> probationMap) {
        if (null != probationMap && null != probationMap.get(emp.getProbationTxt())) {
            PropertyEnumDefDto dto = probationMap.get(emp.getProbationTxt());
            EnumSimple status = new EnumSimple();
            status.setValue(dto.getValue());
            status.setText(dto.getDisplay());
            emp.setProbation(status);
        } else {
//            if(null!=probationMap && probationMap.size()>0) {
//                setEmptyTips(emp, "试用期期限不存在");
//            }
            emp.setProbation(null);
        }
    }

    public boolean checkEmptyMark(EmpInfoImportDo data) {
        return data.isCheckEmpty();
    }

    public boolean checkEmptyProp(EmpInfoImportDo emp) {
        String props = "";
        String tenantId = getTenantId();
//        判断是否有必填字段为空
//        @TODO
        if (StringUtil.isEmpty(emp.getName())) {
            setEmptyTips(emp, "姓名不能为空");
        }
        if (StringUtil.isEmpty(emp.getNationalityTxt())) {
            setEmptyTips(emp, "国籍不能为空");
        }
        if (StringUtil.isEmpty(emp.getPhone())) {
            setEmptyTips(emp, "手机号不能为空");
        }
        if (StringUtil.isEmpty(emp.getCardTypeTxt())) {
            setEmptyTips(emp, "证件类型不能为空");
        }
        if (StringUtil.isEmpty(emp.getCardNo())) {
            setEmptyTips(emp, "证件号码不能为空");
        }
        if (StringUtil.isEmpty(emp.getWorkno())) {
            String workNo = worknoAutoDomainService.nextWorkno(tenantId, null, null);
            if (null == workNo) {
                setEmptyTips(emp, "工号不能为空");
            } else {
                emp.setWorkno(workNo);
            }
        }
        if (StringUtil.isEmpty(emp.getOrganizeTxt())) {
            setEmptyTips(emp, "任职组织不能为空");
        }
        if (StringUtil.isEmpty(emp.getEmpTypeTxt())) {
            setEmptyTips(emp, "用工类型不能为空");
        }
//        if(StringUtil.isEmpty(emp.getLeadEmpIdTxt())){
//            setEmptyTips(emp, "直接上级不能为空");
//        }
        if (StringUtil.isEmpty(emp.getEmpStatusTxt())) {
            setEmptyTips(emp, "员工状态不能为空");
        }
        /*
        DEV-7780 导入时公司邮箱非必填
        if (StringUtil.isEmpty(emp.getCompanyEmail())) {
            setEmptyTips(emp, "公司邮箱不能为空");
        }
        */
        if (StringUtil.isEmpty(emp.getCompanyTxt())) {
            setEmptyTips(emp, "合同公司不能为空");
        }
//        if (StringUtil.isEmpty(emp.getHireDateTxt())) {
//            setEmptyTips(emp, "入职日期不能为空");
//        }
        if (StringUtil.isEmpty(emp.getDisabilityTxt())) {
            emp.setDisabilityTxt("否");
            emp.setDisability(false);
        }
        if (emp.getDisabilityTxt().equals("是")) {
            emp.setDisability(true);
            if (StringUtil.isEmpty(emp.getGuardianName())) {
                setEmptyTips(emp, "监护人姓名不能为空");
            }
            if (StringUtil.isEmpty(emp.getGuardianPhone())) {
                setEmptyTips(emp, "监护人手机号不能为空");
            }
//            if(StringUtil.isEmpty(emp.getGuardianEmail())){
//                setEmptyTips(emp, "监护人邮箱不能为空");
//            }
        }
        if (jobKeyRequired && StringUtil.isEmpty(emp.getJobKey())) {
            setEmptyTips(emp, "JobKey不能为空");
        }
        // 岗位编码不能为空
        if (StringUtil.isBlank(emp.getPostCode())) {
            setEmptyTips(emp, "岗位编码不能为空");
        }
        // 岗位名称不能为空
        if (StringUtil.isBlank(emp.getPostTxt())) {
            setEmptyTips(emp, "岗位名称不能为空");
        }
        return !emp.isCheckEmpty();
    }

    private Map<String, PropertyEnumDefDto> getEmpPrivateMetadataPropertyMap(String prop) {
        Map<String, PropertyEnumDefDto> map = new HashMap<String, PropertyEnumDefDto>();
        MetadataPropertyVo vo = metadataService.getPropertyDef(EMPPRIVATE_INFO_IDENTIFIER, prop);
        for (PropertyEnumDefDto dto : vo.getEnumDef()) {
            map.put(dto.getDisplay(), dto);
        }
        return map;
    }

    private Map<String, PropertyEnumDefDto> getEmpMetadataPropertyMap(String prop) {
        Map<String, PropertyEnumDefDto> map = new HashMap<String, PropertyEnumDefDto>();
        MetadataPropertyVo vo = metadataService.getPropertyDef(WORK_INFO_IDENTIFIER, prop);
        for (PropertyEnumDefDto dto : vo.getEnumDef()) {
            map.put(dto.getDisplay(), dto);
        }
        return map;
    }

    public void initProperty() {
        String tenantId = UserContext.getTenantId();
        staticMap.put(getMapKeyWithTenantId(EntryRouteDictCode, tenantId), getEmployDictMap(EntryRouteDictCode));
        staticMap.put(getMapKeyWithTenantId(GenderDictCode, tenantId), getEmployDictMap(GenderDictCode));
        staticMap.put(getMapKeyWithTenantId(NationalityDictCode, tenantId), getEmployDictMap(NationalityDictCode));
        staticMap.put(getMapKeyWithTenantId(PoliticalPartyDictCode, tenantId), getEmployDictMap(PoliticalPartyDictCode));
        staticMap.put(getMapKeyWithTenantId(NationDictCode, tenantId), getEmployDictMap(NationDictCode));
        staticMap.put(getMapKeyWithTenantId(FamilyTypeDictCode, tenantId), getEmployDictMap(FamilyTypeDictCode));
        staticMap.put(getMapKeyWithTenantId(EmployTypeDicCode, tenantId), getEmployDictMap(EmployTypeDicCode));
        companyMaps.put(getMapKeyWithTenantId(CompanyList, tenantId), getCompanyMap());
        jobGradeMaps.put(getMapKeyWithTenantId(JOB_GRADE, tenantId), getJobGradeMap());
//        postMaps.put(getMapKeyWithTenantId(PostList, tenantId), getPostMap());
        orgDoMaps.put(getMapKeyWithTenantId(OrganizationList, tenantId), getOrganizeMap());
        workplacesMaps.put(getMapKeyWithTenantId(WorkPlaceList, tenantId), getWorkplaceMap());
        staticPropMap.put(getMapKeyWithTenantId(EmpStatusProp, tenantId), getEmpMetadataPropertyMap(EmpStatusProp));
        staticPropMap.put(getMapKeyWithTenantId(ConfirmationStatusProp, tenantId), getEmpMetadataPropertyMap(ConfirmationStatusProp));
        staticPropMap.put(getMapKeyWithTenantId(WorkHourProp, tenantId), getEmpMetadataPropertyMap(WorkHourProp));
        staticPropMap.put(getMapKeyWithTenantId(ProbationProp, tenantId), getEmpMetadataPropertyMap(ProbationProp));
//        staticPropMap.put(getMapKeyWithTenantId(EmpPriFamilyTypeProp, tenantId), getEmpPrivateMetadataPropertyMap(EmpPriFamilyTypeProp));
        staticPropMap.put(getMapKeyWithTenantId(EmpPriMaritalStatusProp, tenantId), getEmpPrivateMetadataPropertyMap(EmpPriMaritalStatusProp));
        staticPropMap.put(getMapKeyWithTenantId(EmpPriFertilityStatusProp, tenantId), getEmpPrivateMetadataPropertyMap(EmpPriFertilityStatusProp));
//        staticPropMap.put(getMapKeyWithTenantId(EmpPriPoliticalOutlookProp, tenantId), getEmpPrivateMetadataPropertyMap(EmpPriPoliticalOutlookProp));
        staticPropMap.put(getMapKeyWithTenantId(EmpPriCardTypeProp, tenantId), getEmpPrivateMetadataPropertyMap(EmpPriCardTypeProp));


    }

    private Map<String, KeyValue> getEmployDictMap(String typeCode) {
        List<KeyValue> list = dictService.getEnableDictList(typeCode, "Employee");
        Map<String, KeyValue> map = new HashMap<>();
        for (KeyValue kv : list) {
            map.put(kv.getText(), kv);
        }
        return map;
    }

    /**
     * 查询岗位信息
     * orgId相关都可以匹配
     *
     * @return
     */
    private Map<String, PostDo> getPostMap() {
        PostQueryDto dto = new PostQueryDto();
        dto.setDateTime(System.currentTimeMillis());
        dto.setPageNo(1);
        dto.setPageSize(4999);
        PageResult<PostDo> page = postService.getPageResultAll(dto);
        Map<String, PostDo> map = new HashMap<>();
        for (PostDo post : page.getItems()) {
            map.put(post.getCode(), post);
        }
        return map;
    }

    @Override
    public List<EmpInfoImportDo> initDataWithExcel(List<EmpInfoImportDo> list) {
        List<String> postCodes = new ArrayList<>();
        Map<String, PostDo> map = new HashMap<>();
        Map<String, EmpInfoImportDo> mapWorkNo = new HashMap<>();
        Set<EmpInfoImportDo> repeatDos = new HashSet<>(list.size());
        Map<String, KeyValue> postCodeTxtMap = new HashMap<>();
        for (EmpInfoImportDo emp : list) {
            EmpInfoImportDo tempDo = mapWorkNo.get(emp.getWorkno());
            if (null == tempDo) {
                mapWorkNo.put(emp.getWorkno(), emp);
            } else {
                if (repeatDos.add(emp)) {
                    setEmptyTips(emp, "工号重复，请检查");
                }
                if (repeatDos.add(tempDo)) {
                    setEmptyTips(tempDo, "工号重复，请检查");
                }
//                已经重复的可以不查询直接跳过
                continue;
            }
            if (null != emp && emp.getPostCode() != null) {
                postCodes.add(emp.getPostCode());
                postCodeTxtMap.put(emp.getPostCode(), new KeyValue(emp.getOrganizeTxt(), emp.getPostTxt()));
            }
            if (list.size() == 5000) {
                Map<String, PostDo> page = getPostMapByCodes(postCodes);
                map.putAll(page);
                postCodes.clear();
            }
        }
        if (list.size() > 0) {
            Map<String, PostDo> page = getPostMapByCodes(postCodes);
            map.putAll(page);
        }
        postMaps.put(getMapKeyWithTenantId(PostList, getTenantId()), map);
        syncCreatePost(postCodeTxtMap);
        return Sequences.sequence(repeatDos).toList();
    }

    private Map<String, PostDo> getPostMapByCodes(List<String> codes) {
        BasePageQueryDto dto = new BasePageQueryDto();
        dto.setDateTime(System.currentTimeMillis());
        dto.setPageNo(1);
        dto.setPageSize(5000);
        List<PostDo> page = postService.selectPostDoByCodes(codes, dto);
        Map<String, PostDo> map = new HashMap<>();
        for (PostDo post : page) {
            map.put(post.getCode(), post);
        }
        return map;
    }

    private Map<String, OrgDo> getOrganizeMap() {
        List<OrgDo> list = orgDomainService.fetchAllOrgDo(null, System.currentTimeMillis());
        Map<String, OrgDo> map = new HashMap<>();
        for (OrgDo orgDo : list) {
            map.put(orgDo.getCode(), orgDo);
        }
        return map;
    }

    /**
     * 查询公司信息
     *
     * @return
     */
    private Map<String, CompanyDo> getCompanyMap() {
        List<CompanyDo> companys = companyService.selectListAll();
        Map<String, CompanyDo> map = new HashMap<>();
        for (CompanyDo companyDo : companys) {
            map.put(companyDo.getCompanyName(), companyDo);
        }
        return map;
    }

    // private Map<String, WorkplaceDo> getWorkplaceMap() {
    //     BasePageQueryDto dto = new BasePageQueryDto();
    //     dto.setDateTime(System.currentTimeMillis());
    //     Map<String, WorkplaceDo> map = new HashMap<>();
    //     List<WorkplaceDo> list = workplaceService.selectList(StatusEnum.ENABLED);
    //     for (WorkplaceDo workplaceDo : list) {
    //         map.put(workplaceDo.getName(), workplaceDo);
    //     }
    //     return map;
    // }

    @Override
    public void operateAfterImport() {
        // 员工、合同模板包关系
        empChangePublish.empImported(new EmpChangeMessageDto(UserContext.getTenantId()));

        // 处理员工、合同设置关系
        empContractTypeSetRelDomainService.doEmpContractRel(null);
    }

    private List<EmpInfoImportDo> duplicateVerificationData(List<EmpInfoImportDo> errorList, List<EmpInfoImportDo> passEmpInfoList) {
        List<EmpInfoImportDo> removeList = new ArrayList<>();

        Map<String, List<EmpInfoImportDo>> phoneMap = passEmpInfoList.stream()
                .collect(Collectors.groupingBy(eInfo -> null == eInfo.getPhone() ? null : eInfo.getPhone().getValue()));
        Map<String, List<EmpInfoImportDo>> cardNoMap = passEmpInfoList.stream().collect(Collectors.groupingBy(EmpInfoImportDo::getCardNo));
        Map<String, List<EmpInfoImportDo>> workNoMap = passEmpInfoList.stream().collect(Collectors.groupingBy(EmpInfoImportDo::getWorkno));

        for (EmpInfoImportDo empInfoImport : passEmpInfoList) {

            Boolean  phoneExists =    phoneMap.get(empInfoImport.getPhone()).size() > 1;
            Boolean cardNoExists =  cardNoMap.get(empInfoImport.getCardNo()).size() > 1;
            Boolean workNoExists =  workNoMap.get(empInfoImport.getWorkno()).size() > 1;

            if (phoneExists || cardNoExists || workNoExists) {
                removeList.add(empInfoImport);
            }
            if (workNoExists) {
                setEmptyTips(empInfoImport, LangUtil.getMsg(MsgCodeConstant.WORKNO_NOT_DUPLICATE));
            }
            if (phoneExists) {
                setEmptyTips(empInfoImport, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32017));
            }
            if (cardNoExists) {
                setEmptyTips(empInfoImport, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32019));
            }
            if (empInfoImport.isCheckEmpty()) {
                errorList.add(empInfoImport);
            }
        }
        return removeList;
    }

    /**
     * 如果 postCodeTxtMap 中对应的数据在 postMaps 的不存在，则新增插入岗位
     */
    private void syncCreatePost(Map<String, KeyValue> postCodeTxtMap){
        if(!syncCreatePost){
            log.warn("sync Create Post switch is closed. syncCreatePost={}", syncCreatePost);
            return;
        }

        if(null == postCodeTxtMap || postCodeTxtMap.isEmpty()){
            log.warn("sync Create Post postCodeTxtMap is empty. postCodeTxtMap={}", FastjsonUtil.toJson(postCodeTxtMap));
            return;
        }

        String tenantId = getTenantId();
        Map<String, PostDo> localPostMap = getLocalPostMap(tenantId);
        Map<String, OrgDo> localOrgMap = getLocalOrgMap(tenantId);
        List<PostDto> createPostList = new ArrayList<>();
        final long dataTime = DateUtil.getMidnightTimestamp();
        // 比较出要新建的岗位数据
        postCodeTxtMap.forEach((k, kv) -> {
            if(localPostMap.containsKey(k)){
                return;
            }

            Object benchmarkPositionName = kv.getValue();
            if(null == benchmarkPositionName){
                return;
            }

            OrgDo orgPair = localOrgMap.get(kv.getText());
            if(null == orgPair){
                return;
            }

            PostDto postData = new PostDto();
            postData.setCode(k);
            postData.setBenchmarkPositionName(benchmarkPositionName.toString());
            postData.setName(postData.getBenchmarkPositionName());
            postData.setDataStartTime(dataTime);
            postData.setRelation(2);
            postData.setOrgId(orgPair.getBid());
            postData.setOrgName(orgPair.getName());

            createPostList.add(postData);
        });

        // 创建岗位
        ImportPostSyncDto<PostDto, PostDto> result = createPostList.isEmpty() ? null : postService.importSyncSave(createPostList);
        if(null == result || null == result.getSuccess() || result.getSuccess().isEmpty()){
            return;
        }

        List<PostDo> postDoList = ObjectConverter.convertList(result.getSuccess(), PostDo.class);
        postDoList.forEach(post -> localPostMap.put(post.getCode(), post));
        postMaps.put(getMapKeyWithTenantId(PostList, tenantId), localPostMap);
    }

    private Map<String, PostDo> getLocalPostMap(String tenantId){
        Map<String, PostDo> localPostMap = postMaps.get(getMapKeyWithTenantId(PostList, tenantId));
        return null == localPostMap ? new HashMap<>() : localPostMap;
    }

    private Map<String, OrgDo> getLocalOrgMap(String tenantId){
        Map<String, OrgDo> localOrgMap = orgDoMaps.get(getMapKeyWithTenantId(OrganizationList, tenantId));
        return null == localOrgMap ? new HashMap<>() : localOrgMap;
    }
}
