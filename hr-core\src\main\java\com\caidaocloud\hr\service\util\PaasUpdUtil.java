package com.caidaocloud.hr.service.util;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;

;

@Slf4j
public class PaasUpdUtil {
    /**
     * 合并修改时间切片
     *
     * @param transferData
     * @param startTime
     * @param dataEndTime
     * @param changeList
     * @param isFieldLevel 是否是字段级更新
     */
    public static void compareUpdate(DataSimple transferData, long startTime, Long dataEndTime, List<DataSimple> changeList, boolean... isFieldChange) {
        log.info("transfer data：{}, startTime={}", FastjsonUtil.toJson(transferData), startTime);
        if (transferData.getBid() == null) {
            log.error("transfer data update error:{}", "bid为空");
            return;
        }
        String identifier = transferData.getIdentifier();
        //数据库数据
        PageResult<DataSimple> result = DataQuery.identifier(identifier).decrypt().specifyLanguage().limit(5000, 1)
                .filter(queryFilter(startTime, dataEndTime, transferData.getBid()), DataSimple.class, "dataStartTime asc", -1);
        if (result != null && CollectionUtils.isNotEmpty(result.getItems())) {
            if (dataEndTime == null) {
                Map<Integer, Boolean> map = new HashMap<>();
                List<DataSimple> items = result.getItems();
                long nowTime = System.currentTimeMillis();
                //异动数据
                for (Map.Entry<String, PropertyValue> entry : transferData.getProperties().entrySet()) {
                    String property = entry.getKey();
                    PropertyValue value = entry.getValue();
                    if (value == null) {
                        continue;
                    }
                    //异动前数据
                    PropertyValue firstValue = items.get(0).getProperties().get(property);
                    //没变
                    if (value.equals(firstValue)) {
                        continue;
                    }
                    items.get(0).getProperties().put(property, value);
                    for (int i = 1; i < items.size(); i++) {
                        NestPropertyValue properties = items.get(i).getProperties();
                        log.info("before：{}, after：{}", FastjsonUtil.toJson(firstValue), FastjsonUtil.toJson(properties.get(property)));
                        if (!Objects.equals(firstValue, properties.get(property))) {
                            break;
                        }
                        items.get(i).setUpdateTime(nowTime);
                        properties.put(property, value);
                        map.putIfAbsent(i, Boolean.TRUE);
                    }
                }
                items.get(0).setUpdateTime(System.currentTimeMillis());
                items.get(0).setDataStartTime(startTime);
                DataUpdate.identifier(identifier).update(items.get(0));
                if (null != changeList) {
                    changeList.add(items.get(0));
                }
                map.forEach((key, value) -> {
                    DataUpdate.identifier(identifier).update(items.get(key));
                    if (null != changeList) {
                        changeList.add(items.get(key));
                    }
                });
            } else {
                updData(transferData, result.getItems(), changeList, startTime, ArrayUtils.isEmpty(isFieldChange) ? false : isFieldChange[0]);
            }
        }
    }

    private static void updData(DataSimple transferData, List<DataSimple> items, List<DataSimple> changeList, long startTime, boolean isFieldChange) {
        long nowTime = System.currentTimeMillis();
        Map<Integer, Boolean> map = new HashMap<>();
        String identifier = transferData.getIdentifier();
        Set<String> changeProperty = null;
        if (isFieldChange) {
            Optional<DataSimple> oldDataOptional = items.stream().filter(e -> e.getDataStartTime() <= startTime && startTime <= e.getDataEndTime()).findFirst();
            if (oldDataOptional.isPresent()) {
                changeProperty = DataSimpleUtil.getChangeDataProperty(transferData, oldDataOptional.get());
            }
        }
        //异动数据
        for (Map.Entry<String, PropertyValue> entry : transferData.getProperties().entrySet()) {
            String property = entry.getKey();
            PropertyValue value = entry.getValue();
            if (value == null || (Objects.nonNull(changeProperty) && !changeProperty.contains(property))) {
                continue;
            }
            for (int i = 0; i < items.size(); i++) {
                NestPropertyValue properties = items.get(i).getProperties();
                if (value instanceof Address) {
                    Address oldValue = (Address) properties.get(property);
                    Address newValue = (Address) value;
                    if (Objects.equals(oldValue.getProvince(), newValue.getProvince())
                            && Objects.equals(oldValue.getCity(), newValue.getCity())
                            && Objects.equals(oldValue.getArea(), newValue.getArea())) {
                        continue;
                    }
                } else if (Objects.equals(value, properties.get(property))) {
                    continue;
                }
                items.get(i).setUpdateTime(nowTime);
                properties.put(property, value);
                map.putIfAbsent(i, Boolean.TRUE);
            }
        }
        items.get(0).setDataStartTime(startTime);
        map.forEach((key, value) -> {
            DataUpdate.identifier(identifier).update(items.get(key));
            if (null != changeList) {
                changeList.add(items.get(key));
            }
        });
    }

    /**
     * 异动时间切片更新
     *
     * @param transferData 异动后数据
     * @param startTime    生效时间
     */
    public static void compareUpdate(DataSimple transferData, long startTime) {
        compareUpdate(transferData, startTime, null, null);
    }

    private static DataFilter queryFilter(long dataStartTime, Long dataEndTime, String bid) {
        DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .andGt("dataEndTime", String.valueOf(dataStartTime))
                .andEq("bid", bid)
                .andEq("deleted", Boolean.FALSE.toString());
        filter = null != dataEndTime ? filter.andLt("dataStartTime", String.valueOf(dataEndTime)) : filter;
        return filter;
    }
}
