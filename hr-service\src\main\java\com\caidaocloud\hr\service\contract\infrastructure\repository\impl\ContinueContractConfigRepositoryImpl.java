package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractConfigDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractConfigRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21
 **/
@Slf4j
@Repository
public class ContinueContractConfigRepositoryImpl extends BaseRepositoryImpl<ContinueContractConfigDo> implements IContinueContractConfigRepository {
}