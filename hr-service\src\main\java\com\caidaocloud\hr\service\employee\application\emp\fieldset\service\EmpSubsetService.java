package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpSubsetDataQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.fieldset.EmpSubsetVo;
import com.caidaocloud.hr.service.temination.application.FormService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class EmpSubsetService {
    @Autowired
    private FormService formService;
    public EmpSubsetVo getEntityData(EmpSubsetDataQueryDto queryDto) {
        SecurityUserInfo sui = SecurityUserUtil.getSecurityUserInfo();
        String identifier = getEntityIdentifier(queryDto.getIdentifier(), sui);
        DataFilter dataFilter = DataFilter.eq("tenantId", sui.getTenantId())
                .andEq("deleted", Boolean.FALSE.toString());
        if(StringUtil.isBlank(queryDto.getEmpId())){
            dataFilter = dataFilter.eq("form_owner.empId", queryDto.getEmpId());
        }

        Long dataTime = null == queryDto.getDataTime() ? System.currentTimeMillis() : queryDto.getDataTime();
        List<DataSimple> items = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(queryDto.getPageSize(), queryDto.getPageNo()).filter(dataFilter, DataSimple.class, dataTime).getItems();
        EmpSubsetVo vo = new EmpSubsetVo();
        if(null != items && !items.isEmpty()){
            vo.setItems(items);
            vo.setLastItem(items.get(0));
        } else {
            vo.setItems(new ArrayList<>());
            vo.setLastItem(new DataSimple());
        }
        return vo;
    }

    private String getEntityIdentifier(String identifier){
        return getEntityIdentifier(identifier, SecurityUserUtil.getSecurityUserInfo());
    }

    private String getEntityIdentifier(String identifier, SecurityUserInfo sui){
        return identifier + "_" + sui.getTenantId();
    }
}
