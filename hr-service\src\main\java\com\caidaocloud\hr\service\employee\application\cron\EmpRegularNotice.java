package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationRecordService;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.enums.NotificationMethodEnum;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.googlecode.totallylazy.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 转正通知定时任务；
 *
 * @Authot CI29616
 * @Date 2023/11/6 17:32
 * @Version 1.0
 **/
@Slf4j
@Service
public class EmpRegularNotice {

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private MsgNoticeService messageService;
    @Resource
    private ConfirmationRecordService confirmationRecordService;

    @XxlJob("empRegularNoticeJobHandler")
    public ReturnT<String> empRegularNoticeJobHandler() {
        XxlJobHelper.log("XxlJob empRegularNoticeJobHandler start");
        log.info("cronTask[Emp Regular Notice]------------------------start execution,time {}", System.currentTimeMillis());

        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                //caodaom-2016 转正通知；
                //转正配置规则；
                List<MsgConfigDto> msgConfigList = messageService.getMsgConfigList(NoticeType.EMP_REGULAR_REMIND);
                if (!msgConfigList.isEmpty()) {
                    //存在启用配置；
                    msgConfigList.forEach(msgConfigDto -> {
                        //转正--转正通知；
                        sendEmpRegular(msgConfigDto);
                    });
                }
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });


        log.info("cronTask[Emp Regular Notice]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empRegularNoticeJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 只做转正日期的；
     *
     * @param msgConfigDto
     */
    private void sendEmpRegular(MsgConfigDto msgConfigDto) {
        //发送转正消息；
        //获取所有 审批通过的 转正记录；
        List<ConfirmationRecordVo> allConfirmation = getAllConfirmation();
        log.info("审批通过的转正记录有{}", allConfirmation.size());
        //通知轮次
        NotificationCycleTypeEnum cycleType = NotificationCycleTypeEnum.getByName(msgConfigDto.getRound());
        long sendDate = DateUtil.getCurrentTimestamp();
        //通过通知轮次确定发送时间；
        switch (cycleType) {
            // 提前通知
            case ADVANCE:
                sendDate += TimeUnit.DAYS.toMillis(msgConfigDto.getDay());
                break;
            // 延后通知
            case DELAY:
                sendDate -= TimeUnit.DAYS.toMillis(msgConfigDto.getDay());
                break;
            case TODAY:
                break;
            default:
                log.warn("Unexpected value = {},msgId={}", cycleType, msgConfigDto.getBid());
                return;
        }

        //单次 、 循环 发送消息；
        if (msgConfigDto.getFunc() == NotificationMethodEnum.ONCE) {
            //单次;
            long finalSendDate = sendDate;
            allConfirmation.forEach(confirmationRecordVo -> {
                if (finalSendDate == confirmationRecordVo.getConfirmationDate()) {
                    //转正日期 与 发送日期相同；
                    //发送消息；
                    Map<String, String> ext = new HashMap<>();
                    ext.put("confirmation.empId", confirmationRecordVo.getEmpId());
                    ext.put("confirmation.confirmationDate", String.valueOf(confirmationRecordVo.getConfirmationDate()));
                    ext.put("confirmation.name", confirmationRecordVo.getName());

                    log.info("转正记录单次提醒：{}", ext);
                    messageService.sendMsgNoticeEvent(msgConfigDto.getBid(), Lists.list(confirmationRecordVo.getEmpId()), ext, "confirmation", 0);
                }
            });

        } else if (msgConfigDto.getFunc() == NotificationMethodEnum.CYCLE) {
            //循环；
            //  提前：转正日期   大于等于   当前日期 - 提前天数。
            //  当天：转正日期   等于  当前日期。
            //  延后：转正日期   小于等于  当前日期 + 延后天数。
            long finalSendDate = sendDate;
            allConfirmation.forEach(confirmationRecordVo -> {
                if ((cycleType == NotificationCycleTypeEnum.ADVANCE && confirmationRecordVo.getConfirmationDate() >= finalSendDate) ||
                        (cycleType == NotificationCycleTypeEnum.TODAY && confirmationRecordVo.getConfirmationDate() == finalSendDate) ||
                        (cycleType == NotificationCycleTypeEnum.DELAY && confirmationRecordVo.getConfirmationDate() <= finalSendDate)) {
                    //发送消息；
                    Map<String, String> ext = new HashMap<>();
                    ext.put("confirmation.empId", confirmationRecordVo.getEmpId());
                    ext.put("confirmation.confirmationDate", String.valueOf(confirmationRecordVo.getConfirmationDate()));
                    ext.put("confirmation.name", confirmationRecordVo.getName());
                    log.info("转正记录循环提醒：{}", ext);
                    messageService.sendMsgNoticeEvent(msgConfigDto.getBid(), Lists.list(confirmationRecordVo.getEmpId()), ext, "confirmation", 0);
                }
            });
        }
    }

    /**
     * 获取 需要的审批通过的转正记录；
     *
     * @return
     */
    private List<ConfirmationRecordVo> getAllConfirmation() {
        ConfirmationRecordSearchDto searchDto = new ConfirmationRecordSearchDto();
        searchDto.setApprovalStatus("APPROVE");
        int total = 0;
        searchDto.setPageNo(1);
        searchDto.setPageSize(100);
        List<ConfirmationRecordVo> result = new ArrayList<>();
        do {
            PageResult<ConfirmationRecordVo> page = confirmationRecordService.page(searchDto);
            if (total == 0) {
                total = page.getTotal();
            }
            List<ConfirmationRecordVo> items = page.getItems();
            result.addAll(items);
            searchDto.setPageNo(searchDto.getPageNo() + 1);
        } while ((searchDto.getPageNo() - 1) * 100 < total);

        return result;
    }


}
