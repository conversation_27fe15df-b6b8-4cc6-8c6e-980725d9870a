package com.caidaocloud.hr.service.confirmation.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.ScheduleFeignClient;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationConfigService;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.domain.entity.EnabledConfirmationPropInfo;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hr.service.dto.growthrecord.GrowthRecordDto;
import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpSalaryChangeService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryChangeDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpSalaryChangeDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.DataSourceEnum;
import com.caidaocloud.hr.service.enums.growthrecord.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.enums.system.RegularStatusEnum;
import com.caidaocloud.hr.service.growthrecord.application.event.publish.GrowthRecordPublish;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhou
 * @date 2023/6/13
 */
@Service
@Slf4j
public class EmpConfirmationService {
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private ConfirmationConfigService confirmationConfigService;
    @Resource
    private EmpSalaryChangeDomainService empSalaryChangeDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private ScheduleFeignClient scheduleFeignClient;
    @Resource
    private GrowthRecordPublish growthRecordPublish;
    @Resource
    private ConfirmationEsService confirmationEsService;
    @Resource
    private EmpSalaryChangeService empSalaryChangeService;

    private static final Pattern pattern = Pattern.compile("salary\\$(.)*_enable");

    public static final String MQ_TOPIC = "EMP_CONFIRMATION";

    public void updateEmpInfoWithConfirmationApply(DataSimple apply) {
        long timestamp = DateUtil.getCurrentTimestamp();
        ConfirmationType type = fetchConfirmationType(apply);
        log.info("updateEmpInfoWithConfirmationApply, confirmation type={},bid={}", type, apply.getId());
        switch (type) {
            case ON_TIME_CONFIRMATION:
            case EARLY_CONFIRMATION:
            case DELAY_CONFIRMATION:
                doWorkingConfirmation(apply, timestamp, type);
                break;
            case NOT_CONFIRMATION:
                doNotConfirmation(apply);
                break;
            default:
        }
    }

    /**
     * 处理指定时间转正操作；
     *
     * @param apply
     * @param timestamp
     * @param type
     */
    private void doWorkingConfirmation(DataSimple apply, long timestamp, ConfirmationType type) {
        log.info("working confirmation");
        Long effectiveDate = fetchEffectiveDate(type, apply);
        if (effectiveDate == null) {
            log.error("Confirmation date is empty,bid={}", apply.getId());
            throw new ServerException("Confirmation date is empty");
        }
        if (timestamp >= effectiveDate) {
            doUpdate(apply);
        } else {
            updateConfirmationStatus(apply, RegularStatusEnum.ON_CONFIRMATION_STATUS);
            log.info("create schedule task,bid={},timestamp={}", apply.getId(), effectiveDate);
            scheduleFeignClient.addSchedule(new ScheduleTaskDto(SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId(), MQ_TOPIC, apply.getId(), apply.getId(), effectiveDate));
        }
    }

    private void doNotConfirmation(DataSimple apply) {
        updateConfirmationStatus(apply, RegularStatusEnum.NOT_CONFIRMATION_STATUS);
        SimplePropertyValue defId = (SimplePropertyValue) apply.getProperties().get("defId");
        createGrowthRecord(apply, confirmationConfigService.one(defId.getValue()));
    }

    private void updateConfirmationStatus(DataSimple apply, RegularStatusEnum confirmationStatus) {
        String empId = ((SimplePropertyValue) apply.getProperties().get("main$empId")).getValue();
        EmpWorkInfoDo workInfo = empWorkInfoService.getEmpWorkInfo(empId, System.currentTimeMillis());
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(confirmationStatus.getIndex().toString());
        workInfo.setConfirmationStatus(enumSimple);
        //empWorkInfoService.update(workInfo, null);
        workInfo.getProperties().add("confirmationStatus", enumSimple);
        empWorkInfoService.updateNowAndNext(workInfo, null);
    }

    private void doUpdate(DataSimple apply) {
        String defId = ((SimplePropertyValue) apply.getProperties().get("defId")).getValue();
        val businessKey = String.format("%s_CONFIRMATION-%s", apply.getId(), defId);
        ConfirmationConfig config = confirmationConfigService.one(defId);
        // 修改任职异动信息，并创建成长记录
        updateWorkInfoWithConfirmationApply(config, apply);
        // 修改薪资异动信息
        saveOrUpdateSalaryInfoWithConfirmationApply(config, apply, businessKey);
    }

    private void saveOrUpdateSalaryInfoWithConfirmationApply(ConfirmationConfig config, DataSimple apply, String businessKey) {
        var properties = apply.getProperties();
        var empId = ((SimplePropertyValue) apply.getProperties().get("main$empId")).getValue();
        val processCode = ((SimplePropertyValue) properties.getOrDefault("processCode", new SimplePropertyValue(""))).getValue();
        var empSalary = new EmpSalaryChangeDo()
                .setEmpId(empId)
                .setEffectiveDate(fetchConfirmationDate(apply))
                .setDataSource(DataSourceEnum.CONFIRMATION.name())
                .setProcessNo(processCode);
        var ext = doExtMap(apply, config.getSalaryProps(), empSalary, "salary$");
        empExtFieldService.doCusExtProps(empSalary.getDoIdentifier(), ext, empSalary);
        empSalaryChangeService.handleSalaryChange(empId, empSalary);
    }

    @Deprecated
    private void saveOrUpdateSalaryInfoWithConfirmationApply(ConfirmationConfig config, DataSimple apply) {
        if (!checkSalaryEnable(apply)) {
            log.info("Salary enable is false,bid={}", apply.getId());
            return;
        }
        String empId = ((SimplePropertyValue) apply.getProperties().get("main$empId")).getValue();
        Long timestamp = fetchConfirmationDate(apply);
        List<EmpSalaryChangeDo> empSalaryList = empSalaryChangeDomainService.getEmpSalaryList(empId);
        EmpSalaryChangeDo empSalary = findEmpSalary(empSalaryList, timestamp);
        if (empSalary == null) {
            log.info("转正：insert薪资记录");
            empSalary = new EmpSalaryChangeDo();
            empSalary.setEmpId(empId);
            empSalary.setEffectiveDate(timestamp);
            empSalary.setDataStartTime(empSalary.getEffectiveDate());
            Map<String, Object> ext = doExtMap(apply, config.getSalaryProps(), empSalary, "salary$");
            empExtFieldService.doCusExtProps(empSalary.getDoIdentifier(), ext, empSalary);
            empSalaryChangeDomainService.insertEmpSalaryChange(empSalary);
            return;
        }
        log.info("转正：update薪资记录：{}", empSalary);
        // caidom-2716 转正修改薪资方式调整；
        Map<String, Object> ext = doExtMap(apply, config.getSalaryProps(), empSalary, "salary$");
        empExtFieldService.doCusExtProps(empSalary.getDoIdentifier(), ext, empSalary);
        empSalaryChangeDomainService.update(empSalary);
    }

    private boolean checkSalaryEnable(DataSimple apply) {
        for (Map.Entry<String, PropertyValue> entry : apply.getProperties().entrySet()) {
            if (pattern.matcher(entry.getKey()).find()) {
                if (Boolean.parseBoolean(((SimplePropertyValue) entry.getValue()).getValue())) {
                    return true;
                }
            }
        }
        return false;
    }

    private EmpSalaryChangeDo findEmpSalary(List<EmpSalaryChangeDo> empSalaryList, Long timestamp) {
        for (EmpSalaryChangeDo empSalaryChangeDo : empSalaryList) {
            if (ObjectUtil.nullSafeEquals(timestamp, empSalaryChangeDo.getEffectiveDate())) {
                return empSalaryChangeDo;
            }
        }
        return null;
    }

    private void updateWorkInfoWithConfirmationApply(ConfirmationConfig config, DataSimple apply) {
        String empId = ((SimplePropertyValue) apply.getProperties().get("main$empId")).getValue();
        ConfirmationType type = fetchConfirmationType(apply);
        Long effectiveDate = fetchEffectiveDate(type, apply);
        if (effectiveDate == null) {
            throw new ServerException("Effective date of confirmation is empty");
        }
        List<EmpWorkInfoDo> list = SpringUtil.getBean(EmpWorkInfoDomainService.class)
                .gerEmpWorkInfoRange(empId, effectiveDate, null);
        // 排序
        list.sort(Comparator.comparingLong(AbstractData::getDataStartTime));
        EmpWorkInfoDo workInfo = list.get(0);
        workInfo.setDataStartTime(effectiveDate);
        workInfoUpdated(config, apply, workInfo, type);
//        for (int i = 1; i < list.size(); i++) {
//            workInfoUpdated(config, apply, list.get(i), type);
//        }
    }

    private void workInfoUpdated(ConfirmationConfig config, DataSimple apply, EmpWorkInfoDo workInfo, ConfirmationType type) {
        // 任职异动信息
        Map<String, Object> ext = doExtMap(apply, config.getWorkProps(), workInfo, "work$");
        if (type == ConfirmationType.DELAY_CONFIRMATION) {
            Long endDate = fetchProbationPeriodEndDate(apply);
            workInfo.setProbationPeriodEndDate(endDate);
            workInfo.getProperties().add("probationPeriodEndDate", String.valueOf(endDate));
        } else {
            Long confirmationDate = fetchConfirmationDate(apply);
            workInfo.setConfirmationDate(confirmationDate);
            workInfo.getProperties().add("confirmationDate", String.valueOf(confirmationDate));
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(RegularStatusEnum.REGULAR_STATUS.getIndex().toString());
            workInfo.setConfirmationStatus(enumSimple);
            workInfo.getProperties().add("confirmationStatus", enumSimple);
        }
//        empWorkInfoService.update(workInfo, ext);
        empWorkInfoService.updateNowAndNext(workInfo, ext);
        createGrowthRecord(apply, config);
    }

    private Long fetchConfirmationDate(DataSimple apply) {
        SimplePropertyValue propertyValue = (SimplePropertyValue) apply.getProperties()
                .get("other$confirmationDate");
        return propertyValue != null ? Long.valueOf(propertyValue.getValue()) : null;
    }

    private Long fetchEffectiveDate(ConfirmationType type, DataSimple apply) {
        if (type == ConfirmationType.NOT_CONFIRMATION || type == ConfirmationType.DELAY_CONFIRMATION) {
            return DateUtil.getCurrentTimestamp();
        }
        return fetchConfirmationDate(apply);
    }


    private Long fetchProbationPeriodEndDate(DataSimple apply) {
        SimplePropertyValue propertyValue = (SimplePropertyValue) apply.getProperties()
                .get("other$probationPeriodEndDate");
        return propertyValue != null ? Long.valueOf(propertyValue.getValue()) : null;
    }


    private ConfirmationType fetchConfirmationType(DataSimple apply) {
        EnumSimple propertyValue = (EnumSimple) apply.getProperties()
                .get("other$confirmationType");
        if (propertyValue == null) {
            throw new ServerException("Type of confirmation is empty");
        }
        return ConfirmationType.getByName(propertyValue.getValue());
    }

    public void createGrowthRecord(DataSimple apply, ConfirmationConfig config) {
        ConfirmationType confirmationType = fetchConfirmationType(apply);
        String empId = ((SimplePropertyValue) apply.getProperties().get("main$empId")).getValue();
        log.info("createGrowthRecord,confirmation type={},bid={},empId={}", confirmationType, apply.getId(), empId);

        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
        growthRecordDto.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
        growthRecordDto.setEmpId(empId);
        growthRecordDto.setBusinessEventType(BusinessEventTypeEnum.CONFIRMATION.toString());
        growthRecordDto.setDataChangeSourceId(config.getBid());
        growthRecordDto.setDataChangeTitle(config.getName());
        growthRecordDto.setCreateBy(apply.getCreateBy());
        String[] propertyKey;
        switch (confirmationType) {
            case EARLY_CONFIRMATION:
            case ON_TIME_CONFIRMATION:
                Long confirmationDate = fetchConfirmationDate(apply);
                growthRecordDto.setEffectiveDate(confirmationDate);
                propertyKey = new String[]{"main$", "other$", "work$", "old_work$"};
                break;
            case DELAY_CONFIRMATION:
                propertyKey = new String[]{"main$", "other$", "work$", "old_work$"};
                growthRecordDto.setEffectiveDate(DateUtil.getCurrentTimestamp());
                break;
            case NOT_CONFIRMATION:
                propertyKey = new String[]{"main$", "other$"};
                growthRecordDto.setEffectiveDate(DateUtil.getCurrentTimestamp());
                break;
            default:
                throw new ServerException("Action not support");
        }
        //CAIDOM-2277 转正的成长记录 记录转生生效之后的数据；
        //获取对应日期 生效的员工数据；
        EmpWorkInfoDo data = empWorkInfoService.getEmpWorkInfo(empId,
                growthRecordDto.getEffectiveDate() == null ? DateUtil.getCurrentTimestamp() : growthRecordDto.getEffectiveDate());

        Map<String, GrowthRecordDto.DataItem> dataItemMap = new HashMap<>();
        for (Map.Entry<String, PropertyValue> entry : apply.getProperties().entrySet()) {
            for (String key : propertyKey) {
                if (entry.getKey().startsWith(key)) {
                    String prop = entry.getKey().split("\\$")[1];
                    prop = prop.replaceAll("_txt", "Txt");
                    if ("attachment".equals(prop) || "workflowEnabled".equals(prop)) {
                        continue;
                    }
                    String value = getValue(entry);
                    if (entry.getKey().startsWith("other$")) {
                        addConfirmationDataItem(dataItemMap, prop, value);
                    } else {
                        entry.setValue(data.getProperties().get(prop));
                        value = getValue(entry);
                        addWorkDataItem(dataItemMap, prop, value, entry.getKey().startsWith("old_work$"));
                    }
                    break;
                }
            }

        }
        growthRecordDto.setDataList(new ArrayList<>(dataItemMap.values()));
        growthRecordPublish.publish(growthRecordDto);
    }

    private void addConfirmationDataItem(Map<String, GrowthRecordDto.DataItem> dataItemMap, String key, String value) {
        GrowthRecordDto.DataItem item = dataItemMap.computeIfAbsent(key, GrowthRecordDto.DataItem::new);
        if ("confirmationType".equals(key)) {
            value = ConfirmationType.getTextByName(value);
        }
        item.setNewValue(value);
        item.setText(EnabledConfirmationPropInfo.EnabledConfirmationProp.getTextByName(StringUtil.humpToUnderline(key)));
    }

    private void addWorkDataItem(Map<String, GrowthRecordDto.DataItem> dataItemMap, String key, String value, boolean isOldValue) {
        GrowthRecordDto.DataItem item = dataItemMap.computeIfAbsent(key, GrowthRecordDto.DataItem::new);
        if (isOldValue) {
            item.setValue(value);
            if (item.getNewValue() == null) {
                item.setNewValue(value);
            }
        } else {
            item.setNewValue(value);
        }
    }


    /**
     * 获取属性中的值
     *
     * @param currentEntry 当前属性
     * @return String
     */
    private String getValue(Map.Entry<String, PropertyValue> currentEntry) {
        if (currentEntry.getValue() instanceof SimplePropertyValue) {
            return ((SimplePropertyValue) currentEntry.getValue()).getValue();
        } else if (currentEntry.getValue() instanceof DictSimple) {
            return ((DictSimple) currentEntry.getValue()).getValue();
        } else if (currentEntry.getValue() instanceof EnumSimple) {
            return ((EnumSimple) currentEntry.getValue()).getValue();
        } else if (currentEntry.getValue() instanceof Address) {
            return ((Address) currentEntry.getValue()).doText();
        } else if (currentEntry.getValue() instanceof EmpSimple) {
            return ((EmpSimple) currentEntry.getValue()).getEmpId();
        } else if (currentEntry.getValue() instanceof JobGradeRange) {
            return ((JobGradeRange) currentEntry.getValue()).getStartGrade();
        } else if (currentEntry.getValue() instanceof ComponentPropertyValue) {
            log.warn("value:{}", currentEntry);
        } else {
            log.warn("[other type component]value:{}", currentEntry);
        }
        return null;
    }


    private Map<String, Object> doExtMap(DataSimple dataSimple, List<ConfirmationChangeFieldDef> defList, DataSimple data, String type) {
        Map<String, Object> ext = new HashMap<>();
        Map<String, ConfirmationChangeFieldDef> collect = defList.stream().collect(Collectors.toMap(ConfirmationChangeFieldDef::getProperty, prop -> prop, (k1, k2) -> k1));
        NestPropertyValue properties = data.getProperties();
        NestPropertyValue props = dataSimple.getProperties();
        props.forEach((prop, pv) -> {
            if (null == prop || prop.startsWith("old_") || !prop.startsWith(type)) {
                return;
            }
            prop = prop.replace(type, "");
            ConfirmationChangeFieldDef changeFieldDef = collect.get(prop);
            if (null == changeFieldDef) {
                return;
            }

            prop = prop.replace("_txt", "Txt");
            PropertyValue propValue = properties.get(prop);
            setSystemField(data, prop, pv);

            if (StringUtil.isNotEmpty(changeFieldDef.getDisplayProperty())) {
                String displayProperty = changeFieldDef.getDisplayProperty();
                propValue = properties.get(displayProperty);
                setSystemField(data, changeFieldDef.getDisplayProperty(),
                        props.get(type + changeFieldDef.getDisplayProperty()));
            }

            if (null == propValue) {
                return;
            }
            setFieldType(pv, propValue);
            setFieldValue(ext, prop, pv, propValue);
        });
        return ext;
    }

    private void setFieldValue(Map<String, Object> ext, String prop, PropertyValue pv, PropertyValue metaPv) {
        if (metaPv instanceof DictSimple) {
            String value = ((DictSimple) pv).getValue();
            if (StringUtil.isNotEmpty(value)) {
                ext.put(prop, value);
            }
            return;
        }

        if (isSetField(pv)) {
            ext.put(prop, pv);
        }
    }

    private void setFieldType(PropertyValue pv, PropertyValue propValue) {
        if (pv instanceof SimplePropertyValue) {
            ((SimplePropertyValue) pv).setType(((SimplePropertyValue) propValue).getType());
        }
    }

    private void setSystemField(DataSimple data, String prop, PropertyValue pv) {
        try {
            Field field = data.getClass().getDeclaredField(prop);
            field.setAccessible(true);
            data.getProperties().add(prop, pv);
            if (pv instanceof SimplePropertyValue) {
                String value = ((SimplePropertyValue) pv).getValue();
                if (StringUtil.isEmpty(value)) {
                    return;
                }
                field.set(data, ConvertUtils.convert(value, field.getType()));
                return;
            }

            if (isSetField(pv)) {
                field.set(data, pv);
            }
        } catch (Exception e) {
            log.error("field prop={} set err,{}", prop, e.getMessage());
        }
    }

    private boolean isSetField(PropertyValue pv) {
        if (pv instanceof SimplePropertyValue) {
            if (StringUtil.isEmpty(((SimplePropertyValue) pv).getValue())) {
                return false;
            }
        } else if (pv instanceof DictSimple) {
            if (StringUtil.isEmpty(((DictSimple) pv).getValue())) {
                return false;
            }
        } else if (pv instanceof EnumSimple) {
            if (StringUtil.isEmpty(((EnumSimple) pv).getValue())) {
                return false;
            }
        } else if (pv instanceof EmpSimple) {
            if (StringUtil.isEmpty(((EmpSimple) pv).getEmpId())) {
                return false;
            }
        } else if (pv instanceof Address) {
            if (StringUtil.isEmpty(((Address) pv).getValue())) {
                return false;
            }
        } else if (pv instanceof JobGradeRange) {
            if (null == ((JobGradeRange) pv).getIsRange()) {
                return false;
            }
        }
        return true;
    }

    public void doUpdate(String applyId) {
        doUpdate(confirmationEsService.getConfirmationApply(applyId));
    }
}
