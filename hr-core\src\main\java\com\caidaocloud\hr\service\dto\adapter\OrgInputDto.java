package com.caidaocloud.hr.service.dto.adapter;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrgInputDto {
    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("组织全称")
    private String fullName;

    @ApiModelProperty("组织简称")
    private String name;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("上级组织bid")
    private String pid;

    @ApiModelProperty("上级组织名称")
    private String pidTxt;

    @ApiModelProperty("组织类型")
    private String orgType;

    @ApiModelProperty("责任人")
    private EmpSimple leaderEmp;

    @ApiModelProperty("负责人岗位bid")
    private String leaderPost;

    @ApiModelProperty("负责人岗位名称")
    private String leaderPostTxt;
}
