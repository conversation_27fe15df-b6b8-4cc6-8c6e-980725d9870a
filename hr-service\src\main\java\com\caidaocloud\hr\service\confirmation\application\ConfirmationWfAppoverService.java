package com.caidaocloud.hr.service.confirmation.application;

import com.caidaocloud.hr.service.confirmation.application.enums.ConfirmationApproverType;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.util.FunUtil;
import com.caidaocloud.hr.service.workflow.interfaces.vo.WfApproverVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
@Service
@Slf4j
public class ConfirmationWfAppoverService {
	@Autowired
	private ConfirmationService confirmationService;
	@Autowired
	private EmpWorkInfoService empWorkInfoService;
	@Autowired
	private OrgService orgService;

	public String getWfApprover(String empId, String businessKey, String code, String value) {
		log.info("getWfApprover empId={}, businessKey={}, code={}, value={}", empId, businessKey, code, value);
		code = StringUtils.substringBefore(code, "_");
		ConfirmationApproverType approverType = ConfirmationApproverType.fromCode(code);
		switch(approverType) {
			case APPROVER_HRBP:
				return getApproverHrbp(businessKey);
			case APPROVER_X_HRBP:
				if(StringUtil.isEmpty(value)){
					value = "0";
				}
				if("0".equals(value)){
					return getApproverHrbp(businessKey);
				}
				return getApproverHrbpX(businessKey, value);
			case APPROVER_ORG_LEADER:
				if(StringUtil.isEmpty(value)){
					value = "0";
				}
				return getApproverOrgLeaderX(businessKey, value, true);
			case APPROVER_X_LEADER:
				if(StringUtil.isEmpty(value)){
					value = "0";
				}
				return getApproverLeaderX(businessKey, value);
			case APPROVER_X_ORG_LEADER:
				if(StringUtil.isEmpty(value)){
					value = "0";
				}
				return getApproverOrgLeaderX(businessKey, value, false);
			default:
				return "";
			}
	}

	private String getApproverHrbp(String businessKey){
		DataSimple data = confirmationService.getConfirmationDataByBusinessId(businessKey);
		NestPropertyValue props = null;
		if(null == data || null == (props = data.getProperties()) || props.isEmpty()){
			return "";
		}

		SimplePropertyValue organize = (SimplePropertyValue) props.get("work$organize");
		if(null == organize || StringUtil.isEmpty(organize.getValue())){
			return "";
		}

		EmpWorkInfoDo hrbp = empWorkInfoService.getOrganizeHrbp(organize.getValue(), System.currentTimeMillis());
		if(null == hrbp || StringUtil.isEmpty(hrbp.getEmpId())){
			return "";
		}

		return FastjsonUtil.toJson(Lists.list(new WfApproverVo(hrbp.getEmpId(), hrbp.getName(), hrbp.getEmpStatus())));
	}


	private String getApproverHrbpX(String businessKey, String layerCount){
		DataSimple data = confirmationService.getConfirmationDataByBusinessId(businessKey);
		NestPropertyValue props = null;
		if(null == data || null == (props = data.getProperties()) || props.isEmpty()){
			return "";
		}

		SimplePropertyValue organize = (SimplePropertyValue) props.get("work$organize");
		if(null == organize || StringUtil.isEmpty(organize.getValue())){
			return "";
		}

		OrgDo orgData = orgService.getOrgById(organize.getValue(), System.currentTimeMillis());
		if(null == orgData || StringUtil.isEmpty(orgData.getBid())){
			return "";
		}

		TreeParent pid = orgData.getPid();
		if(null == pid || StringUtil.isEmpty(pid.getPath())){
			return "";
		}

		String paths = pid.getPath();
		String [] split = paths.split("/");
		int count = Integer.valueOf(layerCount);
		EmpWorkInfoDo hrbp = empWorkInfoService.getOrganizeHrbp(split[count - 1], System.currentTimeMillis());
		if(null == hrbp || StringUtil.isEmpty(hrbp.getEmpId())){
			return "";
		}
		return FastjsonUtil.toJson(Lists.list(new WfApproverVo(hrbp.getEmpId(), hrbp.getName(), hrbp.getEmpStatus())));
	}


	/**
	 * @param businessKey
	 * @param layerCount
	 * @param isTop 为 true，代表从上往下找，false 代表从下往上找
	 * @return
	 */
	private String getApproverOrgLeaderX(String businessKey, String layerCount, boolean isTop){
		DataSimple data = confirmationService.getConfirmationDataByBusinessId(businessKey);
		NestPropertyValue props = null;
		if(null == data || null == (props = data.getProperties()) || props.isEmpty()){
			return "";
		}

		SimplePropertyValue organize = (SimplePropertyValue) props.get("work$organize");
		if(null == organize || StringUtil.isEmpty(organize.getValue())){
			return "";
		}

		OrgDo orgData = orgService.getOrgById(organize.getValue(), System.currentTimeMillis());
		if(null == orgData || StringUtil.isEmpty(orgData.getBid())){
			return "";
		}

		if("0".equals(layerCount)){
			return emp2approver(orgData.getLeaderEmp());
		}

		TreeParent pid = orgData.getPid();
		if(null == pid || StringUtil.isEmpty(pid.getPath())){
			return "";
		}

		String paths = pid.getPath();
		String [] split = paths.split("/");
		int count = Integer.valueOf(layerCount);
		int index = isTop ? count - 1 : split.length - count;
		orgData = orgService.getOrgById(split[index], System.currentTimeMillis());
		if(null == orgData || StringUtil.isEmpty(orgData.getBid())){
			return "";
		}

		return emp2approver(orgData.getLeaderEmp());
	}


	private String getApproverLeaderX(String businessKey, String value) {
		DataSimple data = confirmationService.getConfirmationDataByBusinessId(businessKey);
		log.info("ApproverLeaderX data={}", FastjsonUtil.toJson(data));
		NestPropertyValue props = null;
		if(null == data || null == (props = data.getProperties()) || props.isEmpty()){
			return "";
		}

		EmpSimple leadEmpId = (EmpSimple) props.get("work$leadEmpId");
		if(null == leadEmpId || StringUtil.isEmpty(leadEmpId.getEmpId())){
			return "";
		}

		long dataTime = System.currentTimeMillis();
		EmpWorkInfoDo empWorkInfo = empWorkInfoService.getEmpWorkInfo(leadEmpId.getEmpId(), dataTime);
		if(null == empWorkInfo || StringUtil.isEmpty(empWorkInfo.getEmpId())){
			return "";
		}

		if("0".equals(value)){
			return FastjsonUtil.toJson(Lists.list(new WfApproverVo(empWorkInfo.getEmpId(), empWorkInfo.getName(), empWorkInfo.getEmpStatus())));
		}

		return getApproverLeaderX(empWorkInfo.getLeadEmpId(), dataTime, Integer.valueOf(value) - 1);
	}

	private String getApproverLeaderX(EmpSimple leader, long dataTime, int x){
		if(x < 0 || null == leader || StringUtil.isEmpty(leader.getEmpId()) || x > 10){
			// 上级领导层级超过10层，将不提供支持
			return "";
		}

		if(0 == x){
			EmpWorkInfoDo ewi = empWorkInfoService.getEmpWorkInfo(leader.getEmpId(), dataTime);
			EnumSimple es = FunUtil.getValue(ewi, EmpWorkInfoDo::getEmpStatus);
			return FastjsonUtil.toJson(Lists.list(new WfApproverVo(leader.getEmpId(), leader.getName(), es)));
		}

		EmpWorkInfoDo empWorkInfo = empWorkInfoService.getEmpWorkInfo(leader.getEmpId(), dataTime);
		if(null == empWorkInfo || StringUtil.isEmpty(empWorkInfo.getEmpId())){
			return "";
		}

		return getApproverLeaderX(empWorkInfo.getLeadEmpId(), dataTime, x - 1);
	}


	private String emp2approver(EmpSimple emp){
		if(null == emp || StringUtil.isEmpty(emp.getEmpId())){
			return "";
		}
		EmpWorkInfoDo ewi = empWorkInfoService.getEmpWorkInfo(emp.getEmpId(), System.currentTimeMillis());
		EnumSimple es = FunUtil.getValue(ewi, EmpWorkInfoDo::getEmpStatus);
		return FastjsonUtil.toJson(Lists.list(new WfApproverVo(emp.getEmpId(), emp.getName(), es)));
	}

}
