package com.caidaocloud.hr.service.contract.application.service;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import javax.annotation.Resource;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.domain.entity.LastContractDo;
import com.caidaocloud.hr.service.contract.domain.service.LastContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContractRelationService extends BaseServiceImpl<LastContractDo, ContractQueryDto> {
    private static SnowflakeUtil snowFlake = new SnowflakeUtil(1L, 1L);
    @Resource
    private LastContractDomainService lastContractDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private Locker locker;

    private static final String CONTRACT_RELATION_LOCK_KEY = "CONTRACT_RELATION_LOCK_KEY_%s";
    private static final int BATCH_SIZE = 100;


    @Override
    protected BaseDomainService getDomainService() {
        return lastContractDomainService;
    }

    public void initEmpRelation() {
        String tenantId = UserContext.getTenantId();
        // 加锁
        Lock lock = locker.getLock(String.format(CONTRACT_RELATION_LOCK_KEY, tenantId));
        try {
            boolean locked= lock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                // 获取锁失败
                log.info("tryLock contract relation fail,tenantId={}", tenantId);
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.PARALLEL_TASK_ALREADY_EXISTS));
            }

            ContractQueryDto page = new ContractQueryDto();
            page.setPageSize(BATCH_SIZE);
            int pageNo = 0;
            PageResult<LastContractDo> contractPage;
            do {
                page.setPageNo(++pageNo);
                contractPage = lastContractDomainService.getLastContractPage(page);
                for (LastContractDo item : contractPage.getItems()) {
                    empWorkInfoDomainService.saveContractRelation(item.getOwner().getEmpId(), item.getBid());
                }
            }
            while (pageNo * BATCH_SIZE < contractPage.getTotal());
        } catch (Exception e){
            log.error("failed to init contract relation", e);
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.FAILED_INIT_CONTRACT_RELATION));
        }finally {
            lock.unlock();
        }

    }
}
