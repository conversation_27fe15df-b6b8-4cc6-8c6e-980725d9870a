package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.core.dto.SyncOnBoarDingDto;
import com.caidaocloud.hr.core.dto.UserBaseInfoDto;
import com.caidaocloud.hr.core.dto.UserSyncDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserFeignFallBack implements UserFeignClient {

    @Override
    public Result batchSyncOnBoarDing(List<SyncOnBoarDingDto> dtoList) {
        return Result.fail("导入候选人 调用 批量接入候选人接口 batchSyncOnBoarDing error");

    }

    @Override
    public Result batchSyncOfficial(List<SyncOnBoarDingDto> dtoList) {
        return Result.fail("导入候选人 调用 员工转正 batchSyncOfficial error");
    }

    @Override
    public Result<?> deleteUserInfo(String empId, String tenantId) {
        return Result.fail("删除用户信息失败");
    }

    /**
     * 查询用户信息
     * @return
     * @param userId
     */
    @Override
    public Result<UserBaseInfoDto> getUserById(Long userId) {
        return Result.fail();
    }

    @Override
    public Result stopUser(String empId) {
        return Result.fail("停用用户信息失败");
    }

    @Override
    public Result<Boolean> syncUserInfo(UserSyncDto dto) {
        return Result.fail("同步用户失败");
    }

    @Override
    public Result<List<UserBaseInfoDto>> getUserByIds(List<Long> userIds) {
        return Result.fail();
    }
}
