package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class EmpSalaryChangeSaveDto {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("生效日期")
    private Long effectiveDate;

    @ApiModelProperty("薪资类型（此字段后续不再使用，请使用empSalaryType字典字段）")
    @Deprecated
    private String salaryType;

    @ApiModelProperty("员工薪资类型")
    private String empSalaryType;

    @ApiModelProperty("薪资")
    private String salary;

    @ApiModelProperty("薪资比例")
    private Float salaryRatio;

    @ApiModelProperty("试用期薪资")
    private String probationSalary;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("异动类型")
    private String changeType;

    @ApiModelProperty("异动类型名称")
    private String changeTypeTxt;

    @ApiModelProperty("异动原因")
    private String changeReason;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();


    @ApiModelProperty("生效结束日期")
    private Long effectiveEndDate;
}
