package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.WorkFlowFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.HyperlinkUtil;
import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.application.enums.*;
import com.caidaocloud.hr.service.contract.domain.entity.*;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchAutoRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.enums.system.BaseEmpType;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.NO_DATA_EXIST;
import static com.caidaocloud.hr.service.contract.application.enums.ContractHideStatus.HIDE;
import static com.caidaocloud.hr.service.contract.application.enums.ContractHideStatus.SHOW;

@Slf4j
@Service
public class ContractDomainService extends BaseDomainServiceImpl<ContractDo, ContractQueryDto> {
    @Resource
    private ContractDo contractDo;
    @Resource
    private WorkplaceDo workplaceDo;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Resource
    private ContractTypeSetDo contractTypeSetDo;
    @Resource
    private LastContractDo lastContractDo;
    @Resource
    private EmpContractTypeSetRelDo empContractTypeSetRelDo;
    @Resource
    private ContractSetConditionDo contractSetConditionDo;
    @Resource
    private ContinueLetterDomainService continueLetterDomainService;
    @Resource
    private CacheService cacheService;
    @Resource
    private ContractTypeSetDomainService contractTypeSetDomainService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private WorkFlowFeignClient workFlowFeignClient;

    // 批量续签最大数量
    private static final int BATCH_RENEWAL_LIMIT = 100;

    @Value("${env.domain.url:}")
    private String envDomainUrl;

    @Override
    public BaseDomainDo<ContractDo> getDoService() {
        return contractDo;
    }

    public PageResult<ContractDo> selectPage(ContractQueryDto dto) {
        doContractQuery(dto);
        return contractDo.selectPage(dto);
    }

    public PageResult<ContractDo> selectRecordPage(ContractQueryDto dto) {
        doContractQuery(dto);
        return contractDo.selectRecordPage(dto);
    }

    public List<ContractDo> getLinkContracts(List<String> ids) {
        return contractDo.getLinkContracts(ids);
    }

    public List<ContractDo> getLinkContracts(List<String> ids, List<String> approvalStatus) {
        return contractDo.getLinkContracts(ids, approvalStatus);
    }

    private void doContractQuery(ContractQueryDto dto) {
        if (Objects.equals("0", dto.getOrganize())) {
            dto.setOrganize(null);
        }
    }

    public List<ContractDo> getEmpCurrentContract(List<String> empList, String approvalStatus) {
        return contractDo.getEmpCurrentContract(empList, approvalStatus);
    }

    public ContractDo getEmpLastContract(String empId, String approvalStatus) {
        return contractDo.getEmpLastContract(empId, approvalStatus);
    }

    public List<ContractDo> getEmpContractByStatus(String empId, String approvalStatus, List<String> contractStatus) {
        return contractDo.getEmpContractByStatus(empId, approvalStatus, contractStatus);
    }

    public List<ContractDo> getEmpHistoryContract(List<String> empList, String approvalStatus) {
        return contractDo.getEmpHistoryContract(empList, approvalStatus);
    }

    public List<ContractDo> getContractSignList(List<String> empList) {
        if (CollectionUtils.isEmpty(empList)) {
            return Lists.list();
        }
        return contractDo.getContractSignList(empList);
    }

    public void deleteByEmpIds(List<String> empIds) {
        if (CollectionUtils.isNotEmpty(empIds)) {
            contractDo.deleteByEmpIds(empIds);
        }
    }

    public PageResult<ContractDo> getList(String approvalStatus, ContractQueryDto queryDto) {
        return contractDo.getList(approvalStatus, queryDto);
    }

    public List<ContractDo> getInoperativeContract(List<String> empList, String approvalStatus, String contractStatus) {
        return contractDo.getInoperativeContract(empList, approvalStatus, contractStatus);
    }

    /**
     * 批量获取员工未生效的合同的数据
     *
     * @return
     */
    public List<ContractDo> getInoperativeContract(List<String> empList) {
        return contractDo.getInoperativeContract(empList, ApprovalStatusEnum.PASSED.getIndex().toString()
                , ContractStatusEnum.IN_EFFECTIVE.getIndex());
    }

    public List<ContractDo> getUnderApprovalContract(List<String> empList) {
        return contractDo.getInoperativeContract(empList, ApprovalStatusEnum.IN_APPROVAL.getIndex().toString(), "");
    }

    @Override
    public void linkDataConvert(ContractDo data) {
        // 获取合同设置的合同类型
        if (data.getContractTypeSet() != null) {
            ContractTypeSetDo contractTypeSetDo = this.contractTypeSetDo.getByBid(data.getContractTypeSet());
            PreCheck.preCheckNotNull(contractTypeSetDo, LangUtil.getMsg(NO_DATA_EXIST));
            data.setContractSettingType(contractTypeSetDo.getContractType());
            // 修复异动合同主体后，合同记录丢失员工合同类别问题 by 周旺
            data.setContractType(contractTypeSetDo.getContractClass());
            data.setContractTypeSetTxt(Optional.ofNullable(contractTypeSetDo.getContractType()).map(DictSimple::getText).orElse(""));
        } else if (data.getContractSettingType() != null) {
            data.setContractTypeSetTxt(Optional.ofNullable(data.getContractSettingType()).map(DictSimple::getText).orElse(""));
        }
    }

    public List<ContractDo> getContractByContractNo(List<String> contractNos) {
        return contractDo.getContractByContractNo(contractNos);
    }

    public List<ContractDo> getContractByWorkNo(List<String> workNos) {
        return contractDo.getContractByWorkNo(workNos);
    }

    public String insert(ContractDo data) {
        return save(data).getBid();
    }

    public String transferSave(ContractDo data) {
        String bid = contractDo.save(data);
        data.setBid(bid);
        LastContractDo lastData = this.lastContractDo.syncFromContract(data);
        return lastData.getBid();
    }

    public String update(ContractDo data, boolean... isLastContract) {
        String bid = contractDo.update(data);
        LastContractDo lastContract = this.lastContractDo.syncFromContract(data);
        if (lastContract != null) {
            empWorkInfoDo.saveContractRelation(lastContract.getOwner().getEmpId(), lastContract.getBid());
        }
        if (ArrayUtils.isNotEmpty(isLastContract) && isLastContract[0]) {
            return lastContract.getBid();
        }
        return bid;
    }

    public void updateContinueContract(String contineContractBid, ApprovalStatusEnum status) {
        if (status == null) {
            return;
        }
        EnumSimple continueApprovalStatus = new EnumSimple();
        switch (status) {
            case IN_APPROVAL:
                continueApprovalStatus.setValue("IN_PROCESS");
                break;
            case PASSED:
                continueApprovalStatus.setValue("APPROVE");
                break;
            case REJECTED:
                continueApprovalStatus.setValue("REFUSE");
                break;
            case REVOKE:
                continueApprovalStatus.setValue("REVOKE");
                break;
            default:
                continueApprovalStatus.setValue("ENDING");
        }
        var optional = this.lastContractDo.getByContractId(contineContractBid);
        if (!optional.isPresent()) {
            log.info("not found last contract, contineContractBid={}", contineContractBid);
            return;
        }
        var lastContract = optional.get().setContinueApprovalStatus(continueApprovalStatus);
        this.lastContractDo.updateById(lastContract);
    }

    public String onlyUpdate(ContractDo data) {
        String bid = contractDo.onlyUpdate(data);
        lastContractDo.syncFromContract(data);
        return bid;
    }

    public List<ContractDo> getLatestList(List<String> empList) {
        return contractDo.getLatestList(empList);
    }

    public PageResult<ContractDo> selectContractUser(ContractQueryDto dto) {
        if (Objects.equals("0", dto.getOrganize())) {
            dto.setOrganize(null);
        }
        return contractDo.selectContractUser(dto);
    }

    public long countSignTime(EmpSimple emp) {
        return contractDo.countSignTime(emp.getEmpId());
    }

    public PageResult<ContractDo> getLatestList(int pageNo, int pageSize) {
        return contractDo.getLatestList(pageNo, pageSize);
    }

    /**
     * 批量获取员工的合同签订次数
     *
     * @param empIds
     * @return
     */
    public Map<String, Long> countSignTimes(List<String> empIds) {
        return contractDo.countSignTimes(empIds);
    }

    @Override
    public ContractDo save(ContractDo data) {
        setRequiredField(data, true);
        linkDataConvert(data);
        //改签不计为一次合同签订次数
        if (StringUtils.isEmpty(data.getBid()) && data.getSignType() != null && !SignTypeEnum.CHANGE.getCode().equals(data.getSignType().getValue())) {
            data.setSignTime(Math.toIntExact(countSignTime(data.getOwner())) + 1);
        }
        data.setLastContract(linkLastContract(data.getOwner().getEmpId()).map(AbstractData::getBid).orElse(null));
        // 合同期（年）处理
        disposeContractYear(data);
        contractDo.save(data);
        LastContractDo lastContract = this.lastContractDo.syncFromContract(data);
        // 员工任职信息
        if (lastContract != null) {
            empWorkInfoDo.saveContractRelation(lastContract.getOwner().getEmpId(), lastContract.getBid());
        }
        return data;
    }

    private Optional<ContractDo> linkLastContract(String empId) {
        List<ContractDo> reList = contractDo.getEmpCurrentContract(Lists.list(empId), ApprovalStatusEnum.PASSED.getIndex()
                .toString());
        if (CollectionUtils.isEmpty(reList)) {
            return Optional.empty();
        }
        long nowDate = System.currentTimeMillis();
        ContractDo ret = reList.stream()
                .filter(cd -> {
                    cd.calcStatus();
                    return null != cd.getStartDate() && cd.getStartDate() <= nowDate
                            && null != cd.getEndDate() && nowDate <= cd.getEndDate()
                            && ContractStatusEnum.EFFECTIVE.getIndex().equals(cd.getContractStatus().getValue());
                })
                .findFirst().orElse(reList.get(0));
        if (null != ret) {
            ret.convertContractStatus();
        }
        return Optional.of(ret);
    }

    private void disposeContractYear(ContractDo data) {
        if (null == data.getContractPeriod() || data.getContractPeriod() == 0) {
            return;
        }
        if (Objects.nonNull(data.getContractYear())) {
            return;
        }
        BigDecimal contractYear = new BigDecimal(data.getContractPeriod()).divide(new BigDecimal(12), 0, RoundingMode.DOWN);
        data.setContractYear(contractYear);
    }

    public ContractDo onlySave(ContractDo data) {
        setRequiredField(data, true);
        linkDataConvert(data);
        data.setSignTime(Math.toIntExact(countSignTime(data.getOwner())) + 1);
        contractDo.onlySave(data);
        LastContractDo lastContract = this.lastContractDo.syncFromContract(data);
        // 员工任职信息
        if (lastContract != null) {
            empWorkInfoDo.saveContractRelation(lastContract.getOwner().getEmpId(), lastContract.getBid());
        }
        return data;
    }


    public PageResult<ContractDo> selectApprovalRecordPage(ContractQueryDto dto) {
        doContractQuery(dto);
        return contractDo.selectApprovalRecordPage(dto);
    }

    public List<BatchAutoRenewalDto> batchAutoRenewalCheck(List<String> empIds) {
        if (empIds == null || empIds.isEmpty()) {
            log.warn("emp list is empty");
            return Lists.list();
        }
        PreCheck.preCheckArgument(empIds.size() > BATCH_RENEWAL_LIMIT, LangUtil.getMsg(MsgCodeConstant.BATCH_RENEWAL_MAX_THRESHOLD));

        // 校验未生效、审判中合同
        Set<String> inoperativeSet = getInoperativeContract(empIds).stream().map(contract -> contract.getOwner().getEmpId()).collect(Collectors.toSet());
        Set<String> underApprovalSet = getUnderApprovalContract(empIds).stream().map(contract -> contract.getOwner().getEmpId()).collect(Collectors.toSet());
        empIds = empIds.stream()
                .filter(empId -> !(inoperativeSet.contains(empId) || underApprovalSet.contains(empId)))
                .collect(Collectors.toList());

        if (empIds.isEmpty()) {
            log.info("all emp cannot renewal");
            return Lists.list();
        }
        log.info("Renewable emp>>>>>>{}", empIds);

        // 校验合同设置匹配
        List<LastContractDo> lastContracts = lastContractDo.getByEmp(empIds);
        List<BatchAutoRenewalDto> relList = checkContractTypeSetRel(lastContracts, SignTypeEnum.RENEW);

        if (relList.isEmpty()) {
            log.info("No matching ContractTypeSet found");
            return Lists.list();
        }
        // empIds = relList.stream().map(EmpContractTypeSetRelDo::getEmpId).collect(Collectors.toList());
        log.info("Found matching contractTypeSet>>>>>>{}", relList);


        // 校验合同期限
        List<String> typeSetIds = relList.stream().map(BatchAutoRenewalDto::getContractTypeSet).collect(Collectors.toList());
        // Map<String, ContractTypeSetDo> typeSetMap = contractTypeSetDo.selectByIds(typeSetIds).stream().collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));
        Map<String, ContractSetConditionDo> conditionMap = contractSetConditionDo.selectByTypeSetIds(typeSetIds).stream().collect(Collectors.toMap(ContractSetConditionDo::getContractTypeSet, obj -> obj, (a, b) -> a));
        List<BatchAutoRenewalDto> list = relList.stream().peek(renewalDto -> {
            ContractSetConditionDo condition;
            if ((condition = conditionMap.get(renewalDto.getContractTypeSet())) != null) {
                BeanUtils.copyProperties(condition, renewalDto);
            }
        }).filter(renewalDto -> {
            if (renewalDto.getContractTypeSet() == null || renewalDto.getPeriodType() == null) {
                return false;
            }
            // 若合同设置为固定期限，且合同期限为null，返回false
            return !PeriodTypeEnum.FIXED.getCode().equals(renewalDto.getPeriodType().getValue())
                    || renewalDto.getContractPeriod() != null;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 根据上一份合同的合同公司，签订类型获取自动续签的合同设置
     *
     * @param lastContracts 上一份合同集合
     * @param signType      签订类型
     * @return
     */
    private List<BatchAutoRenewalDto> checkContractTypeSetRel(List<LastContractDo> lastContracts, SignTypeEnum signType) {
        Map<String, LastContractDo> contractMap = lastContracts.stream()
                .collect(Collectors.toMap(contract -> contract.getOwner().getEmpId(), contract -> contract, (a, b) -> a));
        List<String> empIds = Lists.list(contractMap.keySet());

        // 获取合同设置的合同公司和签订类型
        List<EmpContractTypeSetRelDo> empRelList = empContractTypeSetRelDo.selectByEmp(empIds);
        if (empRelList.isEmpty()) {
            return Lists.list();
        }
        List<String> typeSetIds = empRelList.stream().map(EmpContractTypeSetRelDo::getContractTypeSet).collect(Collectors.toList());
        Map<String, ContractTypeSetDo> typeSetMap = contractTypeSetDo.selectByIds(typeSetIds).stream().collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));


        Map<String, List<EmpContractTypeSetRelDo>> empRelMap = empRelList.stream().collect(Collectors.groupingBy(EmpContractTypeSetRelDo::getEmpId));
        return empIds.stream().filter(emp -> {
            List<EmpContractTypeSetRelDo> relList = empRelMap.get(emp);
            // 用户匹配的合同设置为空
            if (relList == null) {
                return false;
            }
            String companyId = contractMap.get(emp).getCompany();
            // 根据上一份合同的合同公司，签订类型获取自动续签的合同设置
            relList.removeIf(rel -> {
                ContractTypeSetDo contractTypeSetDo;
                if ((contractTypeSetDo = typeSetMap.get(rel.getContractTypeSet())) == null) {
                    log.warn("rel's  contractTypeSet '{}' is deleted", rel.getContractTypeSet());
                    return true;
                }
                // 续签筛选支持全部公司
                boolean filterCompany = !contractTypeSetDo.getCompany().contains("-1")
                        && !contractTypeSetDo.getCompany().contains(companyId);
                if (filterCompany || !contractTypeSetDo.getSignType().contains(signType.getCode())) {
                    return true;
                }
                return false;
            });
            // 当只匹配到一个合同设置时，才能自动续签
            return relList.size() == 1;
        }).map(emp -> {
            EmpContractTypeSetRelDo rel = empRelMap.get(emp).get(0);
            BatchAutoRenewalDto dto = ObjectConverter.convert(rel, BatchAutoRenewalDto.class);
            ContractTypeSetDo typeSet;
            if ((typeSet = typeSetMap.get(rel.getContractTypeSet())) != null) {
                BeanUtils.copyProperties(typeSet, dto);
                // 设置合同类别
                dto.setContractType(typeSet.getContractClass());
                // 设置合同类型
                dto.setContractSettingType(typeSet.getContractType());
                // 设置合同类型名称
                dto.setContractTypeSetTxt(typeSet.getContractType().getText());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 批量新签校验
     *
     * @param empIds
     * @return
     */
    public List<BatchAutoRenewalDto> batchAutoNewlySignedCheck(List<String> empIds) {
        if (empIds == null || empIds.isEmpty()) {
            log.warn("emp list is empty");
            return Lists.list();
        }
        PreCheck.preCheckArgument(empIds.size() > BATCH_RENEWAL_LIMIT, LangUtil.getMsg(MsgCodeConstant.BATCH_NEWLYSIGNED_MAX_THRESHOLD));
        // 校验未生效、审判中合同
        Set<String> inoperativeSet = getInoperativeContract(empIds).stream().map(contract -> contract.getOwner().getEmpId()).collect(Collectors.toSet());
        Set<String> underApprovalSet = getUnderApprovalContract(empIds).stream().map(contract -> contract.getOwner().getEmpId()).collect(Collectors.toSet());
        empIds = empIds.stream()
                .filter(empId -> !(inoperativeSet.contains(empId) || underApprovalSet.contains(empId)))
                .collect(Collectors.toList());

        if (empIds.isEmpty()) {
            log.info("all emp cannot renewal");
            return Lists.list();
        }
        log.info("Renewable emp>>>>>>{}", empIds);

        // 校验合同设置匹配
        List<BatchAutoRenewalDto> relList = checkContractTypeNewlySignedRel(empIds, SignTypeEnum.NEW);

        if (relList.isEmpty()) {
            log.info("No matching ContractTypeSet found");
            return Lists.list();
        }
        log.info("Found matching contractTypeSet>>>>>>{}", relList);


        // 校验合同期限
        List<String> typeSetIds = relList.stream().map(BatchAutoRenewalDto::getContractTypeSet).collect(Collectors.toList());
        Map<String, ContractSetConditionDo> conditionMap = contractSetConditionDo.selectByTypeSetIds(typeSetIds).stream().collect(Collectors.toMap(ContractSetConditionDo::getContractTypeSet, obj -> obj, (a, b) -> a));
        log.info("found matching conditionMap>>>>>>{}", FastjsonUtil.toJson(conditionMap));

        List<BatchAutoRenewalDto> list = relList.stream().peek(renewalDto -> {
            ContractSetConditionDo condition;
            if ((condition = conditionMap.get(renewalDto.getContractTypeSet())) != null) {
                BeanUtils.copyProperties(condition, renewalDto);
            }
        }).filter(renewalDto -> {
            if (renewalDto.getContractTypeSet() == null || renewalDto.getPeriodType() == null) {
                return false;
            }
            // 若合同设置为固定期限，且合同期限为null，返回false
            return !PeriodTypeEnum.FIXED.getCode().equals(renewalDto.getPeriodType().getValue())
                    || renewalDto.getContractPeriod() != null;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 校验新签合同
     *
     * @param empIds
     * @param signType
     * @return
     */
    private List<BatchAutoRenewalDto> checkContractTypeNewlySignedRel(List<String> empIds, SignTypeEnum signType) {
        List<EmpWorkInfoDo> workInfoList = empWorkInfoDo.getEmpListByEmpIds(empIds, System.currentTimeMillis());
        Map<String, String> contractMap = workInfoList.stream().filter(workInfoDo -> null != workInfoDo &&
                StringUtils.isNotEmpty(workInfoDo.getCompany())).collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, EmpWorkInfoDo::getCompany, (a, b) -> a));
        // 获取合同设置的合同公司和签订类型
        List<EmpContractTypeSetRelDo> empRelList = empContractTypeSetRelDo.selectByEmp(empIds);
        if (empRelList.isEmpty()) {
            return Lists.list();
        }
        List<String> typeSetIds = empRelList.stream().map(EmpContractTypeSetRelDo::getContractTypeSet).collect(Collectors.toList());
        Map<String, ContractTypeSetDo> typeSetMap = contractTypeSetDo.selectByIds(typeSetIds).stream().collect(Collectors.toMap(AbstractData::getBid, obj -> obj, (a, b) -> a));
        Map<String, ContractSetConditionDo> setConditionDoMap = contractSetConditionDo.selectByTypeSetIds(typeSetIds).stream().collect(Collectors.toMap(ContractSetConditionDo::getContractTypeSet, st -> st, (a, b) -> a));
        Map<String, List<EmpContractTypeSetRelDo>> empRelMap = empRelList.stream().collect(Collectors.groupingBy(EmpContractTypeSetRelDo::getEmpId));
        return empIds.stream().filter(emp -> {
            List<EmpContractTypeSetRelDo> relList = empRelMap.get(emp);
            // 用户匹配的合同设置为空
            if (relList == null) {
                return false;
            }
            String companyId = contractMap.get(emp);
            // 根据上一份合同的合同公司，签订类型获取自动续签的合同设置
            relList.removeIf(rel -> {
                ContractTypeSetDo contractTypeSetDo;
                if ((contractTypeSetDo = typeSetMap.get(rel.getContractTypeSet())) == null) {
                    log.warn("rel's  contractTypeSet '{}' is deleted", rel.getContractTypeSet());
                    return true;
                }
                // 筛选公司条件支持全部
                boolean filterCompany = !contractTypeSetDo.getCompany().contains("-1")
                        && !contractTypeSetDo.getCompany().contains(companyId);
                if (filterCompany || !contractTypeSetDo.getSignType().contains(signType.getCode())) {
                    return true;
                }
                return false;
            });
            return relList.size() == 1;
        }).map(emp -> {
            EmpContractTypeSetRelDo rel = empRelMap.get(emp).get(0);
            BatchAutoRenewalDto dto = ObjectConverter.convert(rel, BatchAutoRenewalDto.class);
            ContractTypeSetDo typeSet;
            if ((typeSet = typeSetMap.get(rel.getContractTypeSet())) != null) {
                BeanUtils.copyProperties(typeSet, dto);
                // 设置合同类别
                dto.setContractType(typeSet.getContractClass());
                // 设置合同类型
                dto.setContractSettingType(typeSet.getContractType());
                // 设置合同类型名称
                dto.setContractTypeSetTxt(typeSet.getContractType().getText());
            }
            ContractSetConditionDo setConditionDo;
            if ((setConditionDo = setConditionDoMap.get(rel.getContractTypeSet())) != null) {
                dto.setProbationDeadline(setConditionDo.getProbationDeadline());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 已签合同中导入的合同信息不在审批记录中显示
     *
     * @param contractDo
     */
    public void setHideStatus(ContractDo contractDo) {
        if (contractDo.getIsHideInApproval() == null) {
            EnumSimple enumSimple = new EnumSimple();
            contractDo.setIsHideInApproval(enumSimple);
        }
        contractDo.getIsHideInApproval().setValue(HIDE.getCode());
    }

    /**
     * 1，是否需要审批=是 应该要进入到审批记录中
     * 2，是否需要审批不等于是 不需要进入
     *
     * @param contractDo
     */
    public void setShowStatus(ContractDo contractDo) {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(SHOW.getCode());
        enumSimple.setText(SHOW.getMark());
        contractDo.setIsHideInApproval(enumSimple);
    }

    public PageResult<ContractDo> getContractPage(ContractStatusQueryDto queryDto) {
        return contractDo.getContractPage(queryDto);
    }

    public Long calcEndDate(BatchAutoRenewalDto renewalDto, EmpWorkInfoDo empWorkInfoDo, Long startDate) {
        ContractSetConditionDo conditionDo = ObjectConverter.convert(renewalDto, ContractSetConditionDo.class);
        return calcEndDate(conditionDo, empWorkInfoDo, startDate, conditionDo.getContractPeriod());
    }

    public Long calcEndDate(ContractSetConditionDo conditionDo, EmpWorkInfoDo empWorkInfoDo, Long startDate, Integer contractPeriod) {
        // 无固定期限，设置结束时间为9999-12-31
        if (PeriodTypeEnum.NO_FIXED.getCode().equals(conditionDo.getPeriodType().getValue())) {
            return com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.MAX_TIMESTAMP;
        }
        Long endDate = com.caidaocloud.util.DateUtil.timeCalculate(com.caidaocloud.util.DateUtil.formatDate(startDate), null, null, contractPeriod, -1);
        EndDateTypeEnum endDateType = EndDateTypeEnum.getByCode(conditionDo.getEndDateType().getValue());
        switch (endDateType) {
            case MONTH:
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(new Date(endDate));
                // 获得本月最后一天
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = calendar.getTime().getTime();
                break;
            case SEASON:
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(new Date(endDate));
                //计算季度数：由于月份从0开始，即1月份的Calendar.MONTH值为0,所以计算季度的第三个月份只需 月份 / 3 * 3 + 2
                endCalendar.add(Calendar.MONTH, (endCalendar.get(Calendar.MONTH) / 3) * 3 + 2 - endCalendar.get(Calendar.MONTH));
                endCalendar.set(Calendar.DAY_OF_MONTH, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = endCalendar.getTime().getTime();
                break;

            //caidao - 1959 合同结束日期 增加 年度最后有一天；
            //todo: 年度最后一天操作；
            case YEAR:
                Calendar yearCalendar = Calendar.getInstance();
                yearCalendar.setTime(new Date(endDate));
                yearCalendar.set(Calendar.MONTH, Calendar.DECEMBER); // 12月
                yearCalendar.set(Calendar.DAY_OF_MONTH, yearCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)); // 获取当月最大天数
                endDate = yearCalendar.getTime().getTime();
                break;
            case DEFAULT:
            default:
        }
        if (Optional.ofNullable(conditionDo.getBaseExpectGraduateDate()).orElse(false)) {
            // 用工类型是实习生时，合同结束日期不能大于预计毕业日期
            if (empWorkInfoDo != null && BaseEmpType.getInterns().stream().map(BaseEmpType::getCode).collect(Collectors.toList())
                    .contains(empWorkInfoDo.getEmpType().getCode())) {
                return Math.min(empWorkInfoDo.getExpectGraduateDate(), endDate);
            }
        }
        if (Optional.ofNullable(conditionDo.getBaseRetireDate()).orElse(false)) {
            // 校验退休日期
            if (empWorkInfoDo != null && empWorkInfoDo.getRetireDate() != null) {
                return Math.min(empWorkInfoDo.getRetireDate(), endDate);
            }
        }
        return endDate;
    }

    /**
     * 创建合同意向书
     *
     * @param contract
     * @param template
     * @param attachment
     * @return
     */
    @PaasTransactional
    public String createContinueLetter(ContractDo contract, ContinueContractTemplateDto template, Attachment attachment) {
        contract.getContinueStatus().setValue(ContinueStatus.INITIATED.name());
        String continueLetter = continueLetterDomainService.createContinueLetter(contract, template, attachment);
        contract.setContinueLetter(continueLetter);
        update(contract);
        return continueLetter;
    }

    /**
     * 生成消息通知变量
     * @param contract         合同
     * @param workInfo         员工任职信息
     * @param currentTimestamp
     * @return
     */
    public Map<String, String> genContractNoticeParam(ContractDo contract, EmpWorkInfoDo workInfo, long currentTimestamp) {
        List<Pair<EmpWorkInfoDo,ContractDo>> list = new ArrayList<>();
        return genContractNoticeParam(contract, workInfo, currentTimestamp, list);
    }

    /**
     * 生成消息通知变量
     *
     * @param contract         合同
     * @param workInfo         员工姓名
     * @param currentTimestamp 当前时间
     * @param list               合并通知变量
     * @return
     */
    public Map<String, String> genContractNoticeParam(ContractDo contract, EmpWorkInfoDo workInfo, long currentTimestamp,List<Pair<EmpWorkInfoDo,ContractDo>> list) {
        Map<String, String> map = new HashMap<>();
        // 开始日期
        String startDate = DateUtil.format(contract.getStartDate(), "yyyy年MM月dd日");
        String startDateEn = DateUtil.formatDate(contract.getStartDate());
        // 结束日期
        String endDate = DateUtil.format(contract.getEndDate(), "yyyy年MM月dd日");
        String endDateEn = DateUtil.formatDate(contract.getEndDate());
        // 合同结束日期
        map.put("contract.endDate", endDate);
        map.put("contract.endDate.en", endDateEn);
        // 合同编号
        map.put("contract.contractNo", contract.getContractNo());
        // 合同开始日期
        map.put("contract.startDate", startDate);
        map.put("contract.startDate.en", startDateEn);
        // 合同到期天数
        map.put("contract.contractDays", String.valueOf(TimeUnit.MILLISECONDS.toDays(contract.getEndDate() - currentTimestamp)));
        // 合同类型
        DictSimple contractType = contract.getContractSettingType();
        SysParamDictDto dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", contractType.getValue())), SysParamDictDto.class);
        if (dictDto != null) {
            map.put("contract.contractType.cn", dictDto.getDictChnName());
            map.put("contract.contractType.en", dictDto.getDictEngName());
        } else {
            log.warn("未找到合同类型：{}", contractType.getValue());
        }
        // 下一份合同
        genNextContractParam(map, contract, workInfo);
        // 续签审批链接
        genApprovalLink(map, workInfo);
        // 合同到期人员汇总
        list.add(Pair.pair(workInfo,contract));
        return map;
    }

    private void genApprovalLink(Map<String, String> map, EmpWorkInfoDo workInfo) {
        ContractQueryDto dto = new ContractQueryDto();
        dto.setStatus("0");
        dto.setEmpId(workInfo.getEmpId());
        PageResult<ContractDo> result = selectRecordPage(dto);
        if (result.getTotal()==0) {
            log.warn("未查询到审批中合同信息，empId={}", workInfo.getEmpId());
            return;
        }
        if (result.getTotal()>1) {
            log.warn("查询到多个审批中合同信息，empId={}", workInfo.getEmpId());
        }
        ContractDo contractDo = result.getItems().get(0);
        Result<String> workFlowResult = workFlowFeignClient.getBusinessKeyBy(contractDo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_RENEW);
        String businessKey = Optional.ofNullable(workFlowResult).filter(it -> it.isSuccess() && it.getData() != null)
                .map(Result::getData).orElse(contractDo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_RENEW);
        map.put("contract.approvalLink", HyperlinkUtil.generateHyperlink(envDomainUrl + "/approval-node?businessKey=" + businessKey,"链接"));
        map.put("contract.approvalLink.en", HyperlinkUtil.generateHyperlink(envDomainUrl + "/approval-node?businessKey=" + businessKey,"link"));
    }

    private void genNextContractParam(Map<String, String> map, ContractDo contract, EmpWorkInfoDo workInfo) {
        String empId = contract.getOwner().getEmpId();
        List<EmpContractTypeSetRelDo> contractTypeList = empContractTypeSetRelDo.getEmpContractTypeList(empId);
        if (contractTypeList.isEmpty()) {
            return;
        }
        List<String> bidList = Sequences.sequence(contractTypeList).map(EmpContractTypeSetRelDo::getContractTypeSet)
                .toList();
        // 过滤停用的合同设置
        List<ContractTypeSetDo> list = Sequences.sequence(contractTypeSetDo.selectByIds(bidList))
                .filter(contractTypeSet -> StatusEnum.isEnable(contractTypeSet.getStatus())).toList();
        if (list.size() != 1) {
            return;
        }
        // 获取下一份合同的合同类型
        ContractTypeSetDo nextContract = list.get(0);
        SysParamDictDto dictDto = FastjsonUtil.toObject(cacheService.getValue(String.format("DICT_%s", nextContract.getContractType().getValue())), SysParamDictDto.class);
        if (dictDto != null) {
            map.put("contract.next.contractType.cn", dictDto.getDictChnName());
            map.put("contract.next.contractType.en", dictDto.getDictEngName());
        }
        else {
            log.warn("未找到合同类型：{}",nextContract.getContractType().getValue());
        }
        // 计算下一份合同的结束日期
        ContractSetConditionDo condition = contractSetConditionDo.getByTypeSetBid(nextContract.getBid());
        Long endDate = contractSetConditionDo.calcEndDate(condition, workInfo, contract.getEndDate() + TimeUnit.DAYS.toMillis(1));
        map.put("contract.next.endDate", DateUtil.format(endDate, "yyyy年MM月dd日"));
        map.put("contract.next.endDate.en", DateUtil.formatDate(endDate));
    }


    /**
     * 校验合同时间
     * 改签：开始时间要大于上份的开始时间小于上份合同结束时间+1，结束时间不受限制。
     * 续签：开始时间要等于上份合同的结束时间+1，结束时间不受限制
     *
     * @param contractDo 合同
     * @param signType   签订类型
     * @param startDate  新合同开始时间
     * @param endDate    新合同结束时间
     */
    public void checkContractDate(ContractDo contractDo, SignTypeEnum signType, long startDate, long endDate) {
        if (endDate <= startDate) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30016"));
        }
        long endDate_ = contractDo.getEndDate() + TimeUnit.DAYS.toMillis(1);
        switch (signType) {
            case RENEW:
                if (startDate != endDate_) {
                    throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80014"));
                }
                return;
            case CHANGE:
                if (!(contractDo.getStartDate() < startDate && startDate < endDate_)) {
                    throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80014"));
                }
                return;
            case NEW:
            default:
                return;
        }
    }

    @Override
    public ContractDo getById(String bid) {
        return contractDo.getByBid(bid);
    }

    public void doContractByLastDay(Long lastDay) {
        // 查询合同解除日期、终止日期小于 lastDay 且合同状态为生效中的合同数据
        List<ContractDo> dataList = contractDo.getContractByLastDay(lastDay);
        dataList.forEach(data -> {
            if (null != data.getTerminationDate()) {
                // 已终止
                data.getContractStatus().setValue(ContractStatusEnum.TERMINATED.getIndex());
            } else if (null != data.getDissolveDate()) {
                // 已解除
                data.getContractStatus().setValue(ContractStatusEnum.RELEASED.getIndex());
            }
            try {
                contractDo.updateContractDo(data);
                lastContractDo.syncFromContract(data);
            } catch (Exception e) {
                log.error("doContractByLastDay err, lastDay={}, contract={}, errMsg={}",
                        lastDay, FastjsonUtil.toJson(data), e.getMessage(), e);
            }
        });
        if (100 == dataList.size()) {
            doContractByLastDay(lastDay);
        }
    }

    public List<ContractDo> getContractByInApproval(List<String> empList) {
        return contractDo.getContractByInApproval(empList);
    }

    public ContractDo selectByLoseEfficacy(ContractDo data) {
        return contractDo.selectByLoseEfficacy(data);
    }

    public List<ContractDo> getArchiveData(BasePage page) {
        return contractDo.getArchiveData(page);
    }

/**
 * 年度最后一天调试；
 */
//    public static void main(String[] args) {
//
//        Calendar currCal = Calendar.getInstance();
//        int currentYear = currCal.get(Calendar.YEAR);
//        Calendar endYearcalendar = Calendar.getInstance();
//        endYearcalendar.clear();
//        endYearcalendar.set(Calendar.YEAR, currentYear);
//        endYearcalendar.roll(Calendar.DAY_OF_YEAR, -1);
//        long currYearLast = endYearcalendar.getTime().getTime();
//
//        System.out.println(currYearLast);
//
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(new Date(endDate));
//        // 获得本月最后一天
//        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
//        endDate = calendar.getTime().getTime();
//
//    }
}
