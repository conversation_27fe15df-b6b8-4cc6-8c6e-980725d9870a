package com.caidaocloud.hr.service.contract.infrastructure.config.workflow;

import com.caidaocloud.workflow.annotation.WfSeqCondition;
import com.caidaocloud.workflow.enums.WfSeqConditionCallTypeEnum;
import com.caidaocloud.workflow.enums.WfSeqConditionOperatorEnum;
import com.caidaocloud.workflow.enums.WfValueComponentEnum;
import org.springframework.stereotype.Component;

/**
 * 工作流-流程条件序列流注册
 *
 * <AUTHOR>
 */
@Component
public class WorkFlowSequenceConfig {

    @WfSeqCondition(name = "合同类型",
            code = "CONTRACT_TYPE_SET",
            operators = {WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE},
            type = WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
            address = "/api/hr/contract/workflow/sequence/v1/data",
            component = WfValueComponentEnum.DATA_SOURCE,
            serviceId = "caidaocloud-hr-service",
            dataSourceAddress = "/api/bcc/dict/common/v1/dict/getEnableDictList?belongModule=Employee&typeCode=ContractType",
            funcCode = {"ContractNewSign", "ContractRenew", "ContractAmendment"})
    public void contractTypeTemplateType() {
    }
}
