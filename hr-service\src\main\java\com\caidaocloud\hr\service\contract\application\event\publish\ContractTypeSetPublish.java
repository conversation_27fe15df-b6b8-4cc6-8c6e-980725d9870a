package com.caidaocloud.hr.service.contract.application.event.publish;

import com.caidaocloud.hr.service.contract.application.event.dto.ContractTypeSetChangeMessageDto;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractTypeSetRefreshMessageDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/21
 */
@Slf4j
@Service
public class ContractTypeSetPublish {
    private final static String CONTRACT_EXCHANGE = "hr.contract.fac.direct.exchange";
    private final static String CONTRACT_ROUTING_KEY = "routingKey.hr.contract";
    private final static String CONTRACT_REFRESH_ROUTING_KEY = "routingKey.hr.contracttypeset.refresh";

    @Resource
    private MqMessageProducer<ContractMessage> producer;

    public void contractTypeSetChanged(ContractTypeSetChangeMessageDto message){
        ContractMessage contract = new ContractMessage();
        contract.setBody(FastjsonUtil.toJson(message));
        contract.setExchange(CONTRACT_EXCHANGE);
        contract.setRoutingKey(CONTRACT_ROUTING_KEY);
        log.info("ContractMessage={}", contract.getBody());
        producer.publish(contract);
    }

    public void contractTypeSetChanged(String bid) {
        contractTypeSetChanged(new ContractTypeSetChangeMessageDto(UserContext.getTenantId(), bid));
    }

    public void publishContractTypeSetRefresh(String tenantId, String bid, String process){
        ContractTypeSetRefreshMessageDto message = new ContractTypeSetRefreshMessageDto();
        message.setBid(bid);
        message.setProcess(process);
        message.setTenantId(tenantId);
        ContractMessage contract = new ContractMessage();
        contract.setBody(FastjsonUtil.toJson(message));
        contract.setExchange(CONTRACT_EXCHANGE);
        contract.setRoutingKey(CONTRACT_REFRESH_ROUTING_KEY);
        log.info("ContractTypeSetRefresh ContractMessage={}", contract.getBody());
        producer.publish(contract);
    }
}
