package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.hr.service.contract.application.service.ContractSignService;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractApprovalSignDto;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/contract/sign/v1")
@Api(value = "/api/hr/contract/sign/v1", description = "合同签订", tags = "v1.7")
public class ContractSignController {

    @Resource
    private ContractSignService contractSignService;

    @ApiOperation("审批通过")
    @PostMapping("/approve")
    public Result approve(@RequestBody ContractApprovalSignDto dto) {
        contractSignService.updateContractApproval(dto, WfTaskActionEnum.APPROVE);
        return Result.ok();
    }

    @ApiOperation("审批拒绝")
    @PostMapping("/refuse")
    public Result refuse(@RequestBody ContractApprovalSignDto dto) {
        contractSignService.updateContractApproval(dto, WfTaskActionEnum.REFUSE);
        return Result.ok();
    }

    @ApiOperation("审批撤回")
    @PostMapping("/revoke")
    public Result revoke(@RequestBody ContractApprovalSignDto dto) {
        contractSignService.revoke(dto);
        return Result.ok();
    }


    @PostMapping("/callback")
    @ApiOperation("工作流-回调")
    public Result callback(@RequestBody WfCallbackResultDto callback) {
        log.info("ContractSignController callback param ={}", FastjsonUtil.toJson(callback));
        contractSignService.callback(callback.getTenantId(), callback.getBusinessKey(), callback.getCallbackType());
        return Result.ok(true);
    }

    @PostMapping("/callbackAndStart")
    @ApiOperation("工作流-回调")
    public Result callbackAndStart(@RequestBody WfCallbackResultDto callback) {
        log.info("ContractSignController callback param ={}", FastjsonUtil.toJson(callback));
        contractSignService.callbackAndStart(callback.getTenantId(), callback.getBusinessKey(), callback.getCallbackType());
        return Result.ok(true);
    }

    @GetMapping("/workflow/detail")
    @ApiOperation("工作流详情")
    public Result detail(@RequestParam String businessKey) {
        return Result.ok(contractSignService.detailVo(businessKey));
    }

    @ApiOperation(value = "分组匹配条件")
    @GetMapping("/condition")
    public Result<List> conditionList() {
        /*List<ConditionItem> list = contractSignService.getConditionList();
        return Result.ok(ObjectConverter.convertList(list, ConditionVo.class));*/
        return Result.ok(contractSignService.listMatchCondition());
    }


    @ApiOperation(value = "角色组匹配条件")
    @GetMapping("/roleCondition")
    public Result<List> roleCondition() {

        return Result.ok(contractSignService.listRoleCondition());
    }


}
