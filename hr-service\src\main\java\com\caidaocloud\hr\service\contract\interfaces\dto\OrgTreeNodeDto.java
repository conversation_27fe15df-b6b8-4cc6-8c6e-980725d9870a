package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrgTreeNodeDto {
    @ApiModelProperty("数据ID")
    private String id;
    @ApiModelProperty("组织名称")
    private String text;
    @ApiModelProperty("子组织")
    private List<OrgTreeNodeDto> list = Lists.newArrayList();
}
