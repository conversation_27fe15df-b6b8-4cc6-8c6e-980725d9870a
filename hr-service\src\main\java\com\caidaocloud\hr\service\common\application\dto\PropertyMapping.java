package com.caidaocloud.hr.service.common.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.Data;

/**
 * 属性映射规则
 * <AUTHOR>
 * @date 2023/2/7
 */
@Data
public class PropertyMapping {
	/**
	 * 映射源property
	 */
	private String source;

	/**
	 * 映射目标property
	 */
	private String target;
	/**
	 * 属性类型
	 */
	private PropertyDataType dataType;

	/**
	 * 为空是否覆盖表单
	 */
	private Boolean isOverwriteWithEmpty = false;


}
