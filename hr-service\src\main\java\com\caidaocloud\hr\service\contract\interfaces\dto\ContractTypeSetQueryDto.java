package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Data
@ApiModel("合同设置查询DTO")
public class ContractTypeSetQueryDto extends BasePage {
    @ApiModelProperty("合同类型名称搜索")
    private List<String> contractType;
    private String status;
}
