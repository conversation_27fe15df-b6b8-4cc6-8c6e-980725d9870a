package com.caidaocloud.hr.service.contract.application.exp.service;

import com.caidaocloud.hr.service.contract.interfaces.dto.ConditionExpDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.val;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
public abstract class AbsContractExp implements IBaseExp {

    Sequence<Map<String, String>> findLastContract(List<Long> empIds, ConditionExpDto exp){
        // val list = ConditionTreeDto.queryAll(exp.getIdentifier(),
        //         DataFilter.in("ownerEmpId", Sequences.sequence(empIds).map(it -> String.valueOf(it)).toList())
        //                 .andEq("deleted", Boolean.FALSE.toString()));

        List<Map<String, String>> result = Lists.list();
        getInfoAll(exp.getIdentifier(), DataFilter.eq("deleted", Boolean.FALSE.toString()), result, "owner.empId", "endDate");
        return Sequences.sequence(result);
    }

    public static void getInfoAll(String identifier, DataFilter filter, List<Map<String,String>> empIdList, String... props){
        long dataTime = System.currentTimeMillis();
        int i = 1;
        DataQuery dataQuery = DataQuery.identifier(identifier);
        do{
            dataQuery.limit(5000, i);
            val page = dataQuery.filterProperties(filter, Lists.list(props), dataTime);
            if(null == page || null == page.getItems()){
                break;
            }

            empIdList.addAll(page.getItems());

            if(page.getTotal() / 5000 + (page.getTotal() % 5000 > 0 ? 1 : 0) <= i){
                break;
            }

            i++;
        } while(true);
    }
}
