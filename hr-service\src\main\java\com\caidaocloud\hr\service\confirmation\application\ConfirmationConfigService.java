package com.caidaocloud.hr.service.confirmation.application;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationConfigDto;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationAppliedBy;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationConfigStatus;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.transfer.application.service.TransferDefService;
import com.caidaocloud.hr.service.transfer.interfaces.vo.ChangeFieldDefVo;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.workflow.dto.WfMetaFunNameDto;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConfirmationConfigService {
    @Autowired
    private ConfirmationWfRegisterService wfRegisterService;
    @Autowired
    private MetadataService metadataService;
    @Autowired
    private TransferDefService transferDefService;

    public String create(ConfirmationConfigDto config) {
        return FastjsonUtil.convertObject(config, ConfirmationConfig.class).create();
    }

    public void update(ConfirmationConfigDto config) {
        val entity = FastjsonUtil.convertObject(config, ConfirmationConfig.class);
        entity.update();
        if(entity.getStatus().equals(ConfirmationConfigStatus.ENABLED)){
            wfRegisterService.register(entity);
        }
        FastjsonUtil.convertObject(config, ConfirmationConfig.class).update();
    }

    @Resource
    private WfOperateFeignClient wfOperateFeignClient;

    public void updateName(ConfirmationConfigDto config) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        String bid = config.getBid();
        String name = config.getName();
        ConfirmationConfig one = one(bid);
        one.setName(name);
        one.setUpdateBy(String.valueOf(user.getUserId()));
        one.setUpdateTime(System.currentTimeMillis());
        DataUpdate.identifier(one.getIdentifier()).update(one);
        //同步流程 信息；
        WfMetaFunNameDto dto = new WfMetaFunNameDto();
        dto.setName(name);
        dto.setCode("CONFIRMATION-" + bid);
        dto.setTenantId(user.getTenantId());
        wfOperateFeignClient.updateFunctionName(dto);
        wfOperateFeignClient.updateFunName(dto);
    }

    public List<ConfirmationConfig> list(ConfirmationAppliedBy applyBy, boolean showDisabled) {
        return ConfirmationConfig.listAll().stream()
                .filter(it -> applyBy.equals(it.getAppliedBy()) && (showDisabled || !it.getStatus().equals(ConfirmationConfigStatus.DISABLED))).sorted(
                        Comparator.comparingInt(c -> c.getStatus().ordinal())).collect(Collectors.toList());
    }

    public ConfirmationConfig one(ConfirmationAppliedBy applyType) {
        ConfirmationConfig config = ConfirmationConfig.listAll().stream()
                .filter(it -> applyType.equals(it.getAppliedBy()) && it.getStatus().equals(ConfirmationConfigStatus.ENABLED))
                .findFirst().orElseThrow(() -> new ServerException("配置未启用"));

        return config;
    }

    public ConfirmationConfig one(String configId) {
        ConfirmationConfig config = ConfirmationConfig.listAll().stream()
                .filter(it -> it.getBid().equals(configId))
                .findFirst().orElseThrow(() -> new ServerException("配置不存在"));

        return config;
    }

    public void enable(String bid) {
        ConfirmationConfig.enable(bid);
        wfRegisterService.register(one(bid));
        //registerWorkflow(bid);
    }

    /**
     * 修改转正配置多语言 接口
     * @param configId
     */
    public void updateConfig(String configId) {
        ConfirmationConfig config = ConfirmationConfig.listAll().stream()
                .filter(it -> it.getBid().equals(configId))
                .findFirst().orElseThrow(() -> new ServerException("配置不存在"));
        MetadataVo metadataVoForWork = metadataService.getMetadata("entity.hr.EmpWorkInfo");
        // 员工信息
        List<MetadataPropertyVo> empStandList = metadataVoForWork.getStandardProperties();
        empStandList.addAll(metadataVoForWork.getCustomProperties());

        List<MetadataPropertyVo> displayWorkInfos = config.getDisplayWorkInfos();

        List<MetadataPropertyVo> displayWorkInfosNew= Lists.list();
        for (MetadataPropertyVo metadataPropertyVo : empStandList) {
            for (MetadataPropertyVo displayWorkInfo : displayWorkInfos) {
                if (metadataPropertyVo.getProperty().equals(displayWorkInfo.getProperty())){
                    displayWorkInfo.setI18nName(metadataPropertyVo.getI18nName());
                    displayWorkInfosNew.add(displayWorkInfo);
                    break;
                }
            }
        }
        config.setDisplayWorkInfos(displayWorkInfosNew);

        //任职信息
        List<ChangeFieldDefVo> work = transferDefService.getAllModeProps("WORK");
        config.setWorkProps( ObjectConverter.convertList(work, ConfirmationChangeFieldDef.class));
        //薪资
        List<ChangeFieldDefVo> salary = transferDefService.getAllModeProps("SALARY");
        for (ConfirmationChangeFieldDef salaryProp : config.getSalaryProps()) {
            for (ChangeFieldDefVo changeFieldDefVo : salary) {
                if (changeFieldDefVo.getProperty().equals(salaryProp.getProperty())){
                    salaryProp.setI18nName(changeFieldDefVo.getI18nName());
                }
            }

        }


        val entity = FastjsonUtil.convertObject(config, ConfirmationConfig.class);
        log.info("update entity:{}",entity.toString());
        entity.update();
    }
}
