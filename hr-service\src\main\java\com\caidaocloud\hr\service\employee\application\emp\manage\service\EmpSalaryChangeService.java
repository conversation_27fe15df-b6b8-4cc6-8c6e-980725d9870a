package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.DataSimpleUtil;
import com.caidaocloud.hr.service.common.infrastructure.utils.UserInfoUtil;
import com.caidaocloud.hr.service.dto.EmpSalaryChangeSaveDto;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.common.tool.LogChangeDataUtil;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryChangeDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpSalaryChangeDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpSalaryChangeDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpSalaryChangeListVo;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpSalaryChangeVo;
import com.caidaocloud.hr.service.enums.DataSourceEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Triple;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmpSalaryChangeService extends BaseServiceImpl<EmpSalaryChangeDo, EmpSalaryChangeDto> {
    private final static String IDENTIFIER = "entity.hr.EmpSalaryChange";
    @Resource
    private EmpSalaryChangeDomainService empSalaryChangeDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private UserFeignClient userFeignClient;

    @Override
    protected BaseDomainService getDomainService() {
        return empSalaryChangeDomainService;
    }

    public List<EmpSalaryChangeListVo> getListByEmpId(EmpSalaryChangeDto dto) {
        PageResult<EmpSalaryChangeDo> page = empSalaryChangeDomainService.getPage(dto, ObjectConverter.convert(dto, EmpSalaryChangeDo.class));
        log.info("emp salary record={}", page.getItems());
        List<EmpSalaryChangeListVo> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(page.getItems())) {
            // 判断是否需要查询自定义字段
            MetadataVo metadata = null;
            if (dto.isIfQueryExt()) {
                metadata = metadataService.getMetadata(IDENTIFIER);
            }

            MetadataVo finalMetadata = metadata;
            page.getItems().forEach(st -> {
                EmpSalaryChangeListVo vo = ObjectConverter.convert(st, EmpSalaryChangeListVo.class);
                if (st.getSalaryRatio() != null || st.getProbationSalary() != null) {
                    vo.setSalaryRatio(st.getSalaryRatio());
                    vo.setProbationSalary(st.getProbationSalary());
                }
                if (st.getEffectiveDate() != null) {
                    vo.setEffectiveDateTimestamp(st.getEffectiveDate());
                }
                if (dto.isIfQueryExt()) {
                    Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(finalMetadata, st);
                    vo.setExt(ext);
                }
                list.add(vo);
            });
        }
        List<EmpSalaryChangeListVo> result = !list.isEmpty() ?
                list.stream().sorted(Comparator.comparing(EmpSalaryChangeListVo::getEffectiveDate)
                        .reversed()).collect(Collectors.toList()) : list;
        //CAIDOM-2595 数据展示调整；
        if (!result.isEmpty()) {
            val updateUserIds = result.stream().filter(e -> StringUtils.isNotBlank(e.getUpdateBy()) && !StringUtils.equals(e.getUpdateBy(), "null")).map(e -> Long.valueOf(e.getUpdateBy())).distinct().collect(Collectors.toList());
            if (!updateUserIds.isEmpty()) {
                Map<String, String> empIdToName = UserInfoUtil.getUsername(updateUserIds);
                for (EmpSalaryChangeListVo empSalaryChangeListVo : result) {
                    if (StringUtil.isNotEmpty(empSalaryChangeListVo.getDataSource())) {
                        empSalaryChangeListVo.setDataSource(DataSourceEnum.getNameByCode(empSalaryChangeListVo.getDataSource()));
                    }
                    if (StringUtil.isNotEmpty(empSalaryChangeListVo.getUpdateBy())
                            && !empIdToName.isEmpty()
                            && empIdToName.containsKey(empSalaryChangeListVo.getUpdateBy())) {
                        empSalaryChangeListVo.setUpdateBy(empIdToName.get(empSalaryChangeListVo.getUpdateBy()));
                    }
                }
            }
        }
        return result;
    }

    public EmpSalaryChangeVo getBasicInfo(String empId, Long dataTime) {
        List<EmpSalaryChangeDo> dataList = empSalaryChangeDomainService.getEmpSalaryList(empId);
        List<EmpSalaryChangeDo> changeDos = dataList.stream().sorted(Comparator.comparing(EmpSalaryChangeDo::getEffectiveDate)).filter(empSalaryChangeDo -> empSalaryChangeDo.getEffectiveDate() <= dataTime).collect(Collectors.toList());
        EmpSalaryChangeVo vo = new EmpSalaryChangeVo();
        if (!changeDos.isEmpty()) {
            log.info("薪资当前时间记录：{}", changeDos.get(changeDos.size() - 1));
            EmpSalaryChangeDo data = changeDos.get(changeDos.size() - 1);
            vo = ObjectConverter.convert(data, EmpSalaryChangeVo.class);
            Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
            vo.setExt(ext);
        }
        return vo;
    }

    public void insert(EmpSalaryChangeDo data) {
        empSalaryChangeDomainService.insertEmpSalaryChange(data);
    }

    public void updateBasicInfo(EmpSalaryChangeSaveDto dto) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        EmpWorkInfoDo workInfo = empWorkInfoDomainService.getEmpWorkInfo(dto.getEmpId(), System.currentTimeMillis());
        EmpSalaryChangeDo newData = ObjectConverter.convert(dto, EmpSalaryChangeDo.class);
        Long effectiveEndDate = dto.getEffectiveEndDate();

        DictSimple empSalaryType = new DictSimple();
        empSalaryType.setValue(dto.getEmpSalaryType());
        newData.setEmpSalaryType(empSalaryType);
        newData.setTenantId(user.getTenantId());
        EnumSimple salaryTypeSimple = new EnumSimple();
        salaryTypeSimple.setValue(dto.getSalaryType());
        newData.setSalaryType(salaryTypeSimple);
        //操作人
        newData.setUpdateBy(UserContext.getUserName());
        empExtFieldService.doCusExtProps(newData.getDoIdentifier(), dto.getExt(), newData);

        if (effectiveEndDate != null && effectiveEndDate > 0 && effectiveEndDate > dto.getEffectiveDate()) {
            effectDateUpdate(dto, user, newData, workInfo);
            // CAIDOM-2595 录入 生效结束日期；
            List<EmpSalaryChangeDo> listByTimeSlot = empSalaryChangeDomainService.getListByTimeSlot(newData, effectiveEndDate);
            if (!listByTimeSlot.isEmpty()) {
                effectEndDateUpdate(dto, user, workInfo, newData, listByTimeSlot);
            }
        } else {
            effectDateUpdate(dto, user, newData, workInfo);
        }
    }

    /**
     * 根据生效结束日期 进行更新
     *
     * @param dto
     * @param user
     * @param workInfo
     * @param newData
     * @param listByTimeSlot
     */
    private void effectEndDateUpdate(EmpSalaryChangeSaveDto dto, SecurityUserInfo user, EmpWorkInfoDo workInfo, EmpSalaryChangeDo newData, List<EmpSalaryChangeDo> listByTimeSlot) {
        //操作人
        newData.setUpdateBy(UserContext.getUserId());
        for (EmpSalaryChangeDo oldData : listByTimeSlot) {
            log.info("原数据bid：{}", oldData.getBid());
            Long effectiveDate = oldData.getEffectiveDate();
            String id = oldData.getId();
            String bid = oldData.getBid();
            DataFilter baseFilter = getBaseFilter(oldData.getEmpId(), effectiveDate);
            String identifier = oldData.getIdentifier();
            List<DataSimple> oldItems = DataQuery.identifier(identifier).filter(baseFilter, DataSimple.class).getItems();
            BeanUtil.copyProperties(newData, oldData, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
            oldData.setEffectiveDate(effectiveDate);
            oldData.setSalaryRatio(dto.getSalaryRatio());
            oldData.setProbationSalary(dto.getProbationSalary());
            oldData.setDataSource(DataSourceEnum.EMP.name());
            oldData.setUpdateTime(System.currentTimeMillis());
            oldData.setUpdateBy(String.valueOf(user.getUserId()));
            oldData.setId(id);
            oldData.setBid(bid);
            log.info("当前修改bid:{} 数据：{}", oldData.getBid(), oldData);
            empSalaryChangeDomainService.update(oldData);
            List<DataSimple> newItems = DataQuery.identifier(identifier).filter(baseFilter, DataSimple.class).getItems();
            String info = LogChangeDataUtil.getChangeInfo(oldItems.get(0), newItems.get(0));
            LogRecordContext.putVariable("change", info);
            LogRecordContext.putVariable("name", workInfo.getName());
            LogRecordContext.putVariable("workno", workInfo.getWorkno());
            if (info.length() > 0) {
                LogRecordContext.putVariable("condition", true);
            }
        }
    }

    /**
     * 没有生效结束日期 的 薪资记录更新；
     *
     * @param dto
     * @param user
     * @param newData
     */
    private void effectDateUpdate(EmpSalaryChangeSaveDto dto, SecurityUserInfo user, EmpSalaryChangeDo newData, EmpWorkInfoDo workInfo) {

        EmpSalaryChangeDo oldData = empSalaryChangeDomainService.getOneByEffectDate(dto.getEmpId(), dto.getEffectiveDate());
        DataFilter baseFilter = getBaseFilter(dto.getEmpId(), dto.getEffectiveDate());
        String identifier = EmpSalaryChangeDo.IDENTIFIER;

        //操作人

        newData.setUpdateBy(UserContext.getUserId());

        if (oldData != null && oldData.getEffectiveDate() != null && oldData.getEffectiveDate().equals(dto.getEffectiveDate())) {
            List<DataSimple> oldItems = DataQuery.identifier(identifier).filter(baseFilter, DataSimple.class).getItems();
            BeanUtil.copyProperties(newData, oldData, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
            oldData.setSalaryRatio(dto.getSalaryRatio());
            oldData.setProbationSalary(dto.getProbationSalary());
            oldData.setDataSource(DataSourceEnum.EMP.name());
            oldData.setUpdateTime(System.currentTimeMillis());
            oldData.setUpdateBy(String.valueOf(user.getUserId()));
            log.info("bid:{},修改数据：{}", oldData.getBid(), oldData);
            empSalaryChangeDomainService.update(oldData);
            List<DataSimple> newItems = DataQuery.identifier(identifier).filter(baseFilter, DataSimple.class).getItems();
            LogRecordContext.putVariable("change", LogChangeDataUtil.getChangeInfo(oldItems.get(0), newItems.get(0)));
            LogRecordContext.putVariable("name", workInfo.getName());
            LogRecordContext.putVariable("workno", workInfo.getWorkno());
        } else {
            newData.setEffectiveDate(dto.getEffectiveDate());
            newData.setDataSource(DataSourceEnum.EMP.name());
            newData.setUpdateTime(System.currentTimeMillis());
            newData.setUpdateBy(String.valueOf(user.getUserId()));
            log.info("bid:{},新增数据：{}", newData.getBid(), newData);
            empSalaryChangeDomainService.save(newData);
            List<DataSimple> newItems = DataQuery.identifier(identifier).filter(baseFilter, DataSimple.class).getItems();
            LogRecordContext.putVariable("change", LogChangeDataUtil.getChangeInfo(null, newItems.get(0)));
            LogRecordContext.putVariable("name", workInfo.getName());
            LogRecordContext.putVariable("workno", workInfo.getWorkno());
        }
        String st = (String) LogRecordContext.getVariable("change");
        if (st.length() > 0) {
            LogRecordContext.putVariable("condition", true);
        }
    }

    private DataFilter getBaseFilter(String empId, Long effectiveDate) {
        return DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("empId", empId)
                .andEq("effectiveDate", String.valueOf(effectiveDate))
                ;
    }

    public void onlyUpdBasicInfo(EmpSalaryChangeSaveDto dto) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        EmpSalaryChangeDo newData = ObjectConverter.convert(dto, EmpSalaryChangeDo.class);
        DictSimple empSalaryType = new DictSimple();
        empSalaryType.setValue(dto.getEmpSalaryType());
        newData.setEmpSalaryType(empSalaryType);
        //操作人

        newData.setUpdateBy(UserContext.getUserId());
        empExtFieldService.doCusExtProps(newData.getDoIdentifier(), dto.getExt(), newData);
        EmpSalaryChangeDo oldData = empSalaryChangeDomainService.getListByEffectDate(dto.getEmpId());
        if (oldData != null && oldData.getBid() != null) {
            BeanUtil.copyProperties(newData, oldData, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
            oldData.setDataSource(DataSourceEnum.EMP.name());
            oldData.setUpdateBy(String.valueOf(user.getUserId()));
            oldData.setUpdateTime(System.currentTimeMillis());
            empSalaryChangeDomainService.update(oldData);
        } else {
            newData.setDataSource(DataSourceEnum.EMP.name());
            newData.setUpdateBy(String.valueOf(user.getUserId()));
            newData.setUpdateTime(System.currentTimeMillis());
            empSalaryChangeDomainService.save(newData);
        }
    }

    public EmpSalaryChangeDo save(EmpSalaryChangeSaveDto dto) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        EmpSalaryChangeDo data = ObjectConverter.convert(dto, EmpSalaryChangeDo.class);
//        EnumSimple salaryTypeSimple = new EnumSimple();
//        salaryTypeSimple.setValue(dto.getSalaryType());
//        data.setSalaryType(salaryTypeSimple);

        DictSimple empSalaryType = new DictSimple();
        empSalaryType.setValue(dto.getEmpSalaryType());
        data.setEmpSalaryType(empSalaryType);
        data.setDataSource(DataSourceEnum.EMP.name());
        data.setUpdateBy(String.valueOf(user.getUserId()));
        data.setUpdateTime(System.currentTimeMillis());
        //操作人

        data.setUpdateBy(UserContext.getUserId());
        //doConverter(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empSalaryChangeDomainService.save(data);
        return data;
    }

    public void softDelete(String bid) {
        EmpSalaryChangeDo esc = empSalaryChangeDomainService.getById(bid);
        if (null == esc || StringUtil.isEmpty(esc.getBid())) {
            return;
        }
        esc.setDeleted(true);
        empSalaryChangeDomainService.update(esc);
    }

    /**
     * 处理薪资记录逻辑
     *
     * @param empId
     * @param newEmpSalaryChange
     */
    public void handleSalaryChange(String empId, EmpSalaryChangeDo newEmpSalaryChange) {
        if (StringUtils.isBlank(empId) || newEmpSalaryChange == null) {
            log.info("parameters are illegal, empId={} newEmpSalaryChange={}", empId, FastjsonUtil.toJson(newEmpSalaryChange));
            return;
        }
        var empSalaryList = empSalaryChangeDomainService.getEmpSalaryList(empId);
        if (CollectionUtils.isEmpty(empSalaryList)) {
            empSalaryChangeDomainService.save(newEmpSalaryChange);
            return;
        }
        if (DataSourceEnum.CONFIRMATION.name().equals(newEmpSalaryChange.getDataSource()) && StringUtils.isBlank(newEmpSalaryChange.getSalary())) {
            val onboardingEmpSalaryChangeList = empSalaryList.stream().filter(e -> "ONBOARDING".equals(e.getDataSource())).sorted(Comparator.comparing(EmpSalaryChangeDo::getEffectiveDate).reversed()).collect(Collectors.toList());
            EmpSalaryChangeDo empSalaryChange = null;
            if (CollectionUtils.isNotEmpty(onboardingEmpSalaryChangeList)) {
                empSalaryChange = onboardingEmpSalaryChangeList.get(0);
            } else {
                empSalaryChange = empSalaryList.stream().sorted(Comparator.comparing(EmpSalaryChangeDo::getEffectiveDate).reversed()).findFirst().get();
            }
            if (Objects.isNull(empSalaryChange.getSalaryRatio())) {
                log.info("salaryRatio is empty, empId={} salaryRatio={} dataSource={}", empId, empSalaryChange.getSalaryRatio(), newEmpSalaryChange.getDataSource());
                return;
            }
            val bigDecimal = new BigDecimal(empSalaryChange.getSalary());
            String result = bigDecimal.divide(new BigDecimal(empSalaryChange.getSalaryRatio()), 2, BigDecimal.ROUND_UP).toString();
            String[] split = result.split("\\.");
            if (split.length > 1 && split[1].equals("00")) {
                result = split[0];
            }
            BeanUtils.copyProperties(empSalaryChange, newEmpSalaryChange, "id", "bid", "effectiveDate", "dataSource", "processNo", "properties", "updateTime", "createTime");
            newEmpSalaryChange.setSalary(result);
            newEmpSalaryChange.getProperties().add("salary", result);
        }
        var empSalaryChangeTriple = findEmpSalary(empSalaryList, newEmpSalaryChange.getEffectiveDate());
        val isMinEmpSalaryChange = newEmpSalaryChange.getEffectiveDate() != null && empSalaryChangeTriple.first().longValue() > newEmpSalaryChange.getEffectiveDate().longValue();
        if (isMinEmpSalaryChange || (empSalaryChangeTriple.second() == null && empSalaryChangeTriple.third() == null)) {
            empSalaryChangeDomainService.save(newEmpSalaryChange);
            return;
        }
        if (empSalaryChangeTriple.third() != null) {
            DataSimpleUtil.copyNewValue(empSalaryChangeTriple.third(), newEmpSalaryChange, "bid", "createBy", "updateBy", "empId", "effectiveDate", "updateTime");
            empSalaryChangeDomainService.updateById(empSalaryChangeTriple.third());
            return;
        }
        DataSimpleUtil.copyOldValueWhenNull(empSalaryChangeTriple.second(), newEmpSalaryChange, "bid", "createBy", "updateBy", "empId", "effectiveDate", "updateTime", "createTime");
        empSalaryChangeDomainService.save(newEmpSalaryChange);
    }

    /**
     * @param empSalaryList
     * @param effectiveDate
     * @return first:最小有效期 second:前一个有效期 third:当前有效期
     */
    private Triple<Long, EmpSalaryChangeDo, EmpSalaryChangeDo> findEmpSalary(List<EmpSalaryChangeDo> empSalaryList, Long effectiveDate) {
        empSalaryList = empSalaryList.stream().filter(e -> Objects.nonNull(e.getEffectiveDate()))
                .sorted(Comparator.comparing(EmpSalaryChangeDo::getEffectiveDate)).collect(Collectors.toList());
        val minEffectiveDate = empSalaryList.get(0).getEffectiveDate();
        EmpSalaryChangeDo pre = null;
        for (int i = 0; i < empSalaryList.size(); i++) {
            var empSalaryChange = empSalaryList.get(i);
            val b = effectiveDate.longValue() > empSalaryChange.getEffectiveDate().longValue() && (pre == null || empSalaryChange.getEffectiveDate().longValue() > pre.getEffectiveDate().longValue());
            if (Objects.equals(effectiveDate, empSalaryChange.getEffectiveDate())) {
                if (i > 0) {
                    return Triple.triple(minEffectiveDate, empSalaryList.get(i - 1), empSalaryChange);
                }
                return Triple.triple(minEffectiveDate, null, empSalaryChange);
            } else if (b) {
                pre = empSalaryChange;
                continue;
            }
        }
        return Triple.triple(minEffectiveDate, pre, null);
    }
}