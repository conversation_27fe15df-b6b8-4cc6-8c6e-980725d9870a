package com.caidaocloud.hr.service.util;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;

import java.util.List;
import java.util.stream.Collectors;

public final class ConditionExpUtil {
    public static List<String> containChild(String simpleValue, Long dataTime){
        if(StringUtil.isEmpty(simpleValue)){
            return Lists.list();
        }

        String [] sValues = simpleValue.split(",");
        DataFilter sdFilter = DataFilter.eq("bid", sValues[0]).orRegex("pid$path", sValues[0]);
        for (int i = 1; i < sValues.length; i++) {
            sdFilter = sdFilter.orEq("bid", sValues[i]).orRegex("pid$path", sValues[i]);
        }
        return doContainChild(sdFilter, dataTime);
    }

    private static List<String> doContainChild(DataFilter sdFilter, Long dataTime){
        DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                .and(sdFilter)
                .andEq("deleted", Boolean.FALSE.toString());
        PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hr.Org")
                .decrypt().specifyLanguage()
                .queryInvisible().limit(5000, 1)
                .filter(filter, DataSimple.class, dataTime);
        if(null == pageResult || null == pageResult.getItems() || pageResult.getItems().isEmpty()){
            return Lists.list();
        }

        return pageResult.getItems().stream().map(ds -> ds.getBid()).distinct().collect(Collectors.toList());
    }

    public static List<String> containChild(String simpleValue){
        return containChild(simpleValue, System.currentTimeMillis());
    }
}
