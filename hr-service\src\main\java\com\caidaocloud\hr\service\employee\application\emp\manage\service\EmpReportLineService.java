package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.dto.EmpReportLineDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.event.publish.MsgTypeRefreshPublish;
import com.caidaocloud.hr.service.employee.application.event.publish.ReportLinePublish;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpReportLineDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpConcurrentPostDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpReportLineDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.OrganizeReportDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.PersonnelReportDto;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.search.application.service.EmpWorkInfoSearchService;
import com.caidaocloud.hr.service.transfer.application.service.TransferEsService;
import com.caidaocloud.hr.service.util.FunUtil;
import com.caidaocloud.hr.service.workflow.interfaces.vo.WfApproverVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.util.*;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.NO_DATA_EXIST;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EmpReportLineService {
    @Resource
    private EmpReportLineDomainService empReportLineDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private EmpWorkInfoSearchService empWorkInfoSearchService;
    @Resource
    private EmpConcurrentPostDomainService empConcurrentPostDomainService;
    @Autowired
    private MsgTypeRefreshPublish msgTypeRefreshPublish;
    @Resource
    private TransferEsService transferEsService;

    public EmpReportLineDo getByEmpIdAndOrgAndPost(String empId, String organize, String post) {
        return empReportLineDomainService.selectByEmpIdAndOrgAndPost(empId, organize, post, System.currentTimeMillis());
    }

    public String save(EmpReportLineDto dto) {
        EmpReportLineDo data = ObjectConverter.convert(dto, EmpReportLineDo.class);
        doConvert(dto, data);
        return empReportLineDomainService.save(data);
    }

    public void update(EmpReportLineDto dto) {
        EmpReportLineDo data = ObjectConverter.convert(dto, EmpReportLineDo.class);
        doConvert(dto, data);
        empReportLineDomainService.update(data);
    }

    private void doConvert(EmpReportLineDto source, EmpReportLineDo target) {
        long dataStartTime = System.currentTimeMillis();
        if (source.getOrganize() != null) {
            OrgDo org = orgDomainService.selectById(source.getOrganize(), dataStartTime);
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30049));
            target.setOrganizeCode(org.getCode());
            target.setOrganizeTxt(org.getName());
        }
        if (source.getPost() != null) {
            PostDo post = postDomainService.selectById(source.getPost(), dataStartTime);
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30109));
            target.setPostCode(post.getCode());
            target.setPostTxt(post.getName());
        }

        if (source.getLeaderOrganize() != null) {
            OrgDo org = orgDomainService.selectById(source.getLeaderOrganize(), dataStartTime);
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32045));
            target.setLeaderOrganizeCode(org.getCode());
            target.setLeaderOrganizeTxt(org.getName());
        }

        if (source.getLeaderPost() != null) {
            PostDo post = postDomainService.selectById(source.getLeaderPost(), dataStartTime);
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32046));
            target.setLeaderPostCode(post.getCode());
            target.setLeaderPostTxt(post.getName());
        }
    }

    private void doConvertPersistObject(EmpWorkInfoDo empWorkInfo) {
        if (empWorkInfo.getLeaderOrganize() != null) {
            OrgDo org = orgDomainService.selectById(empWorkInfo.getLeaderOrganize(), empWorkInfo.getDataStartTime());
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32045));
            empWorkInfo.setLeaderOrganizeTxt(org.getName());
        }

        if (empWorkInfo.getLeaderPost() != null) {
            PostDo post = postDomainService.selectById(empWorkInfo.getLeaderPost(), empWorkInfo.getDataStartTime());
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32046));
            empWorkInfo.setLeaderPostTxt(String.format(HrConstant.NAME_CODE_FORMAT, post.getName(), post.getCode()));
        }
    }

    public List<EmpWorkInfoDo> disposeEmpWorkInfo(List<PersonnelReportDto> personList, Boolean majorPost, Long dataTime) {
        List<EmpWorkInfoDo> updEmpWorkInfoDos = new ArrayList<>();
        if (CollectionUtils.isEmpty(personList)) {
            return updEmpWorkInfoDos;
        }
        long workDataStartTime;
        // majorPost:true 任职（主岗）,dataTime精确到天,false 兼岗,dataTime精确到秒.
        if (majorPost) {
            workDataStartTime = dataTime;
        } else {
            workDataStartTime = dataTime + 1L;
        }
        personList.forEach(emp -> {
            if (emp.getMajorPost()) {
                EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(emp.getEmpId(), emp.getDataStartTime());
                PreCheck.preCheckArgument(null == empWorkInfo || null == empWorkInfo.getBid(), LangUtil.getMsg(NO_DATA_EXIST));
                // 主岗信息处理,时间轴切片
                empWorkInfo.setDataStartTime(workDataStartTime > emp.getDataStartTime() ? workDataStartTime : emp.getDataStartTime());

                empWorkInfo.setLeaderOrganize(emp.getNewOrganize());
                empWorkInfo.setLeadEmpId(emp.getNewLeaderEmp());
                empWorkInfo.setLeaderPost(emp.getNewPost());

                // todo 处理组织、岗位中文名称,查询时间节点为（编辑任职：页面时间轴选择时间，编辑兼岗：兼岗结束时间）
                doConvertPersistObject(empWorkInfo);

                updEmpWorkInfoDos.add(empWorkInfo);
            }
        });
        return updEmpWorkInfoDos;
    }

    public List<EmpConcurrentPostDo> disposeConcurrentPost(List<PersonnelReportDto> personList, Boolean majorPost, Long dataTime) {
        List<EmpConcurrentPostDo> updConcurrentPostDos = new ArrayList<>();
        if (CollectionUtils.isEmpty(personList)) {
            return updConcurrentPostDos;
        }
        long conDataStartTime;
        // majorPost:true 任职（主岗）,dataTime精确到天,false 兼岗,dataTime精确到秒.
        if (majorPost) {
            conDataStartTime = dataTime - 1L;
        } else {
            conDataStartTime = dataTime;
        }
        personList.forEach(emp -> {
            if (!emp.getMajorPost()) {
                EmpConcurrentPostDo dbData = empConcurrentPostDomainService.getEmpConcurrentPost(emp.getBid(), System.currentTimeMillis());
                PreCheck.preCheckArgument(null == dbData || null == dbData.getBid(), LangUtil.getMsg(NO_DATA_EXIST));
                // 当前编辑数据时间轴节点为兼岗结束时间
                dbData.setDataStartTime(dbData.getEndDate());

                // 开始时间 <= 时间轴切片节点 < 结束时间，进行时间切片
                if (emp.getDataStartTime() <= conDataStartTime && conDataStartTime < emp.getDataEndTime()) {
                    // 新增一条新兼岗记录
                    EmpConcurrentPostDo data = new EmpConcurrentPostDo();
                    BeanUtil.copyProperties(dbData, data, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy", "deleted");
                    data.setLeaderOrganize(emp.getNewOrganize());
                    data.setPostSuperior(emp.getNewLeaderEmp());
                    data.setLeaderPost(emp.getNewPost());
                    // 新增兼岗数据 = 上条兼岗数据结束时间 + 1秒
                    data.setStartDate(conDataStartTime + 1L);
                    updConcurrentPostDos.add(data);


                    dbData.setEndDate(conDataStartTime);
                    // 时间切片，时间节点为兼岗结束时间
                    dbData.setDataStartTime(conDataStartTime);
                    updConcurrentPostDos.add(dbData);
                } else if (emp.getDataStartTime() > conDataStartTime) {
                    dbData.setLeaderOrganize(emp.getNewOrganize());
                    dbData.setPostSuperior(emp.getNewLeaderEmp());
                    dbData.setLeaderPost(emp.getNewPost());
                    updConcurrentPostDos.add(dbData);
                } else {
                    log.info("data---->{}", emp);
                }
            }
        });
        return updConcurrentPostDos;
    }

    public List<OrgDo> disposeOrgInfo(List<OrganizeReportDto> organizeList, Boolean majorPost, Long dataTime) {
        List<OrgDo> orgList = new ArrayList<>();
        if (CollectionUtils.isEmpty(organizeList)) {
            return orgList;
        }
        long orgDataStartTime;
        // majorPost:true 任职（主岗）,dataTime精确到天,false 兼岗,dataTime精确到秒.
        if (majorPost) {
            orgDataStartTime = dataTime;
        } else {
            orgDataStartTime = dataTime + 1L;
        }

        organizeList.forEach(org -> {
            OrgDo dbData = orgDomainService.selectById(org.getBid(), org.getDataStartTime());
            PreCheck.preCheckArgument(null == dbData || null == dbData.getBid(), LangUtil.getMsg(NO_DATA_EXIST));
            dbData.setCustomOrgRoles(ObjectConverter.convertList(dbData.getCustomOrgRoles(), CustomOrgRoleDo.class));
            dbData.setLeaderOrganize(org.getNewOrganize());
            dbData.setLeaderEmp(org.getNewLeaderEmp());
            dbData.setLeaderPost(org.getNewPost());
            dbData.setDataStartTime(orgDataStartTime);
            orgList.add(dbData);
        });
        return orgList;
    }

    public void updateReportLine(List<String> empList) {
        if (CollectionUtils.isEmpty(empList)) {
            return;
        }
        List<EmpReportLineDto> empReportLines = new ArrayList<>();
        long dataStartTime = System.currentTimeMillis();

        // 删除原有汇报线数据
        empReportLineDomainService.deleteByEmpId(empList);

        // 主岗数据
        List<EmpWorkInfoDo> empWorkList = empWorkInfoDomainService.getEmpListByEmpIds(empList, dataStartTime);
        empWorkList.stream().filter(emp -> !StringUtils.isEmpty(emp.getOrganize()) && !StringUtils.isEmpty(emp.getPost())).forEach(emp -> {
            EmpReportLineDto dto = new EmpReportLineDto();
            BeanUtil.copyProperties(emp, dto, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                    "deleted", "dataStartTime", "dataEndTime");
            dto.setLeadEmp(emp.getLeadEmpId());
            dto.setMajorPost(true);
            empReportLines.add(dto);
        });

        Map<String, EmpWorkInfoDo> empMap = empWorkList.stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, re -> re, (k1, k2) -> k1));
        // 兼岗数据
        List<EmpConcurrentPostDo> list = empConcurrentPostDomainService.getByEmpIds(empList, dataStartTime);
        if (!CollectionUtils.isEmpty(list)) {
            // 过滤生效中的数据
            list = list.stream().filter(con -> dataStartTime >= con.getStartDate()
                    && dataStartTime <= con.getEndDate())
                    .collect(Collectors.toList());

        }
        list.forEach(con -> {
            EmpWorkInfoDo workInfo = empMap.get(con.getEmpId());
            EmpReportLineDto dto = new EmpReportLineDto();
            BeanUtil.copyProperties(con, dto, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                    "deleted", "dataStartTime", "dataEndTime");
            dto.setLeadEmp(con.getPostSuperior());
            dto.setMajorPost(false);
            dto.setWorkno(workInfo.getWorkno());
            dto.setName(workInfo.getName());
            dto.setEnName(workInfo.getEnName());
            empReportLines.add(dto);
        });

        EmpReportLineService erlService = SpringUtil.getBean(EmpReportLineService.class);
        // 新增汇报线数据
        empReportLines.forEach(line -> erlService.save(line));
        log.info("汇报线信息修改:{}条", empReportLines.size());
    }


    public void updateEmpAndOrg(List<EmpWorkInfoDo> empWorkInfoDos, List<EmpConcurrentPostDo> concurrentPostDos, List<OrgDo> orgDos) {
        // 更新主岗信息
        empWorkInfoDos.forEach(emp ->
                {
                    EmpWorkInfoDo beforeEmpWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(emp.getEmpId(), emp.getDataStartTime());
                    empWorkInfoDomainService.update(emp, false);
                    try {
                        msgTypeRefreshPublish.sendRefreshMsgTemplate(emp.getEmpId());
                    } catch (Exception e) {
                        log.error("send message template refresh msg failed, {}", e.getMessage());
                    }
                    EmpWorkInfoDo afterEmpWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(emp.getEmpId(), emp.getDataStartTime());
                    empWorkInfoDomainService.doProcessGrowthRecord(beforeEmpWorkInfo, afterEmpWorkInfo);
                }
        );

        // 修改兼岗信息处理
        concurrentPostDos.forEach(con -> {
            if (StringUtils.isEmpty(con.getBid())) {
                empConcurrentPostDomainService.save(con);
            } else {
                empConcurrentPostDomainService.update(con);
            }
            empWorkInfoSearchService.dataChangeSyncEs(con.getEmpId(), empConcurrentPostDomainService.selectList(con.getEmpId()));
        });

        // 更新组织
        orgDos.forEach(org ->
                orgDomainService.update(org, null)
        );
        log.info("任职信息修改:{}条,兼岗信息修改:{}条,组织修改:{}条", empWorkInfoDos.size(), concurrentPostDos.size(), orgDos.size());
    }

    public String getWfTransferApprovers(String businessKey, String value){
        String applyId = org.apache.commons.lang3.StringUtils.substringBefore(businessKey, "_");
        DataSimple dataSimple = transferEsService.getTransfer(applyId);
        NestPropertyValue props = null;
        if(null == dataSimple || null == (props = dataSimple.getProperties())){
            return "";
        }

        EmpSimple leadEmp = (EmpSimple) props.get("work$leadEmpId");
        if(null == leadEmp || StringUtil.isEmpty(leadEmp.getEmpId())){
            return "";
        }

        SimplePropertyValue leaderOrganize = (SimplePropertyValue) props.get("work$leaderOrganize");
        String organize = null;
        if(null != leaderOrganize && !StringUtil.isEmpty(leaderOrganize.getValue())){
            organize = leaderOrganize.getValue();
        }
        if(StringUtil.isEmpty(organize)){
            leaderOrganize = (SimplePropertyValue) props.get("old_work$leaderOrganize");
            organize = null != leaderOrganize ? leaderOrganize.getValue() : organize;
        }
        String post = null;
        SimplePropertyValue leaderPost = (SimplePropertyValue) props.get("work$leaderPost");
        if(null != leaderPost && !StringUtil.isEmpty(leaderPost.getValue())){
            post = leaderPost.getValue();
        }
        if(StringUtil.isEmpty(post)){
            leaderPost = (SimplePropertyValue) props.get("old_work$leaderPost");
            post = null != leaderPost ? leaderPost.getValue() : post;
        }

        SimplePropertyValue effectiveDate = (SimplePropertyValue) dataSimple.getProperties().get("other$effectiveDate");
        Long effDate = null != effectiveDate && StringUtil.isNotEmpty(effectiveDate.getValue()) ?
            Long.valueOf(effectiveDate.getValue()) : System.currentTimeMillis();
        return getWfReportLineApprover(leadEmp, value, organize, post, effDate);
    }

    public String getWfApprover(String empId, String value) {
        long nowDate = System.currentTimeMillis();
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, nowDate);
        EmpSimple leadEmp = null;
        if(null == empWorkInfo || null == (leadEmp = empWorkInfo.getLeadEmpId()) || StringUtil.isEmpty(leadEmp.getEmpId())){
            return "";
        }

        return getWfReportLineApprover(leadEmp, value, empWorkInfo.getLeaderOrganize(), empWorkInfo.getLeaderPost(), nowDate);
    }

    private String getWfReportLineApprover(EmpSimple leadEmp, String value, String organize, String post, Long nowDate){
        if(StringUtil.isEmpty(value)){
            value = "0";
        }

        if("0".equals(value) || "1".equals(value)){
            EmpWorkInfoDo ewi = empWorkInfoDomainService.getEmpWorkInfo(leadEmp.getEmpId(), nowDate);
            EnumSimple es = FunUtil.getValue(ewi, EmpWorkInfoDo::getEmpStatus);
            WfApproverVo wfrla = new WfApproverVo(leadEmp.getEmpId(), leadEmp.getName(), es);
            return FastjsonUtil.toJson(Lists.list(wfrla));
        }

        if(StringUtil.isEmpty(organize) || StringUtil.isEmpty(post)){
            return "";
        }

        int count = Integer.valueOf(value) - 1;
        String empId = leadEmp.getEmpId();
        for (int i = 0; i < count; i++) {
            EmpReportLineDo emp = empReportLineDomainService.selectByEmpIdAndOrgAndPost(empId, organize, post, nowDate);
            if(null == emp || null == (leadEmp = emp.getLeadEmp()) || StringUtil.isEmpty(leadEmp.getEmpId())){
                return "";
            }
            empId = leadEmp.getEmpId();
            organize = emp.getLeaderOrganize();
            post = emp.getLeaderPost();
        }

        EmpWorkInfoDo ewi = empWorkInfoDomainService.getEmpWorkInfo(leadEmp.getEmpId(), nowDate);
        EnumSimple es = FunUtil.getValue(ewi, EmpWorkInfoDo::getEmpStatus);
        return FastjsonUtil.toJson(Lists.list(new WfApproverVo(leadEmp.getEmpId(), leadEmp.getName(), es)));
    }

    public void reportLineRefresh(Long dataTime){
        reportLineRefresh(null, dataTime);
    }

    @PaasTransactional
    public void reportLineRefresh(String empId, Long dataTime){
        Map<String, EmpWorkInfoDo> workInfoDoMap = mainPostRefresh(empId, dataTime);
        // 同步每日兼岗信息；
        List<EmpConcurrentPostDo> empConcurrentPostDos = empConcurrentPostDomainService.selectListByOnlyTime(empId, dataTime);
        empReportLineDomainService.deleteBatch(empId, false);
        for (EmpConcurrentPostDo empConcurrentPostDo : empConcurrentPostDos) {
            EmpReportLineDto empReportLineDto = ObjectConverter.convert(empConcurrentPostDo, EmpReportLineDto.class);
            empReportLineDto.setMajorPost(Boolean.FALSE);
            empReportLineDto.setLeadEmp(empConcurrentPostDo.getPostSuperior());
            if (workInfoDoMap.containsKey(empConcurrentPostDo.getEmpId())) {
                EmpWorkInfoDo empWorkInfoDo = workInfoDoMap.get(empConcurrentPostDo.getEmpId());
                empReportLineDto.setWorkno(empWorkInfoDo.getWorkno());
                empReportLineDto.setName(empWorkInfoDo.getName());
                empReportLineDto.setEnName(empWorkInfoDo.getEnName());
            }
            try {
                save(empReportLineDto);
            } catch (Exception e){
                log.error("Failed to save the part-time reporting line, empReportLineDto={}, errMsg={}",
                        FastjsonUtil.toJson(empReportLineDto), e.getMessage(), e);
            }
        }
    }

    private Map<String, EmpWorkInfoDo> mainPostRefresh(Long dataTime){
        return mainPostRefresh(null, dataTime);
    }

    private Map<String, EmpWorkInfoDo> mainPostRefresh(String empId, Long dataTime){
        //同步每日主岗信息；(存在错误数据无法转换)
        List<EmpWorkInfoDo> empWorkInfoDos = loadMainPostEmpList(empId, dataTime);
        // 清空上级汇报表；
        empReportLineDomainService.deleteBatch(empId, true);
        Map<String, EmpWorkInfoDo> workInfoDoMap = empWorkInfoDos.stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, empWorkInfoDo -> empWorkInfoDo, (k1, k2) -> k1));
        for (EmpWorkInfoDo empWorkInfoDo : empWorkInfoDos) {
            EmpReportLineDto empReportLineDto = ObjectConverter.convert(empWorkInfoDo, EmpReportLineDto.class);
            empReportLineDto.setMajorPost(Boolean.TRUE);
            empReportLineDto.setLeadEmp(empWorkInfoDo.getLeadEmpId());
            try {
                save(empReportLineDto);
            } catch (Exception e){
                log.error("Failed to save the main reporting line, empReportLineDto={}, errMsg={}",
                        FastjsonUtil.toJson(empReportLineDto), e.getMessage(), e);
            }
        }
        empWorkInfoDos = null;
        return workInfoDoMap;
    }

    private List<EmpWorkInfoDo> loadMainPostEmpList(String empId, Long dataTime){
        if(null == empId){
            return empWorkInfoDomainService.getEmpWorkInfoByTime(dataTime);
        }

        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        return Lists.list(empWorkInfo);
    }
}
