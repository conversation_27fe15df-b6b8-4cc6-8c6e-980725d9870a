package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractTemplateRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/17
 **/
@Data
public class ContinueContractTemplateDo extends BaseDomainDoImpl<ContinueContractTemplateDo> {
    /**
     * 名称国际化
     */
    private String i18nName;
    /**
     * 模版说明国际化
     */
    private String i18nDesc;
    /**
     * 条件
     */
    private String condition;
    /**
     * 页面提示说明
     */
    private String tips;
    /**
     * 附件
     */
    private Attachment attachment;
    /**
     * 合同信息字段
     */
    private List<MetadataPropertyDto> contractFields;
    /**
     * 员工续签意向 存的是字段值code RenewIntention
     */
    private String feedbacks;

    private static final String IDENTIFIER = "entity.hr.continue.Contract";

    @Override
    public BaseRepository<ContinueContractTemplateDo> getRepository() {
        return SpringUtil.getBean(IContinueContractTemplateRepository.class);
    }

    private static BaseRepository<ContinueContractTemplateDo> repository() {
        return SpringUtil.getBean(IContinueContractTemplateRepository.class);
    }

    @Override
    public String getIdentifier() {
        return ContinueContractTemplateDo.IDENTIFIER;
    }

    @Override
    public String getDoIdentifier() {
        return ContinueContractTemplateDo.IDENTIFIER;
    }

    public static void save(ContinueContractTemplateDo contractTemplateDo) {
        repository().insert(contractTemplateDo);
    }

    public static void update(ContinueContractTemplateDo contractTemplateDo) {
        repository().updateById(contractTemplateDo);
    }

    public ContinueContractTemplateDto convertDto() {
        var convert = ObjectConverter.convert(this, ContinueContractTemplateDto.class);
        if (StringUtils.isNotBlank(this.condition)) {
            convert.setCondition(FastjsonUtil.toObject(this.condition, ConditionTree.class));
        }
        if (StringUtils.isNotBlank(this.i18nName)) {
            Map<String, String> map = FastjsonUtil.toObject(this.i18nName, Map.class);
            convert.setI18nName(map);
        }
        if (StringUtils.isNotBlank(this.i18nDesc)) {
            Map<String, String> map = FastjsonUtil.toObject(this.i18nDesc, Map.class);
            convert.setI18nDesc(map);
        }
        if (StringUtils.isNotBlank(this.feedbacks)) {
            this.feedbacks = this.feedbacks.replace("\\\"", "")
                    .replace("[", "")
                    .replace("]", "")
                    .replace("\"", "");
            convert.setFeedbacks(Splitter.on(",").splitToList(this.feedbacks));
        }
        return convert;
    }
}
