package com.caidaocloud.hr.service.contract.application.event.subscribe;

import java.util.Map;

import com.caidaocloud.hr.service.contract.application.service.ContractExpireNoticeService;
import com.caidaocloud.hr.service.contract.application.service.ContractSignService;
import com.caidaocloud.hr.service.temination.application.event.dto.ScheduleTaskMsg;
import com.caidaocloud.hr.service.temination.application.writeback.ContractWriteBackService;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApply;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/3/10
 */
@Component
@Slf4j
public class ContractScheduleSubscriber {
	@Autowired
	private MsgNoticeService msgNoticeService;
	@Autowired
	private ContractSignService contractSignService;

	@RabbitHandler
	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "caidaocloud.hr.contract.expire", durable = "true"),
					exchange = @Exchange(value = "schedule.task.exchange." + ContractExpireNoticeService.TOPIC),
					key = {"routingKey.schedule.task." + ContractExpireNoticeService.TOPIC}
			)
	)
	public void process(String msg) {
		log.info("Subscribe contract expire notice,msg={}", msg);
		ScheduleTaskMsg scheduleTaskMsg = FastjsonUtil.toObject(msg, ScheduleTaskMsg.class);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(scheduleTaskMsg.getUserInfo().getTenantId());
			userInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			String[] taskId = scheduleTaskMsg.getTaskId().split("_");
			String msgId = taskId[0], empId = taskId[1], ext = scheduleTaskMsg.getTaskDetail();
			msgNoticeService.sendMsgNoticeEvent(msgId, Lists.list(empId), FastjsonUtil.toObject(ext, Map.class), "hr", 0);
		}catch (Exception e){
			log.info("contract expire task error,{}", e.getMessage(), e);
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	@RabbitHandler
	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "caidaocloud.hr.contract.start", durable = "true"),
					exchange = @Exchange(value = "schedule.task.exchange.CONTRACT_START"),
					key = {"routingKey.schedule.task.CONTRACT_START" }
			)
	)
	public void contractStartProcess(String msg) {
		log.info("Subscribe contract start notice,msg={}", msg);
		ScheduleTaskMsg scheduleTaskMsg = FastjsonUtil.toObject(msg, ScheduleTaskMsg.class);
		try {
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(scheduleTaskMsg.getUserInfo().getTenantId());
			userInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			String contractId = scheduleTaskMsg.getTaskDetail();
			contractSignService.startMsg(contractId);
		}catch (Exception e){
			log.info("contract start task error,{}", e.getMessage(), e);
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}
}
