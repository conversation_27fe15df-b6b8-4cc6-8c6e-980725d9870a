package com.caidaocloud.hr.service.confirmation.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationEsService;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationAppliedBy;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationConfigStatus;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationPropertyConfig;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationConfigStatus;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeFieldDef;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class ConfirmationConfig extends DataSimple {

    private static String IDENTIFIER = "entity.hr.ConfirmationConfig";

    private ConfirmationAppliedBy appliedBy;

    private ConfirmationConfigStatus status;

    private String name;

    private String description;

    private String processCode;

    @DisplayAsArray
    private List<MetadataPropertyVo> displayWorkInfos = Lists.list();

    @DisplayAsArray
    private List<ConfirmationChangeFieldDef> salaryProps = Lists.list();

    @DisplayAsArray
    private List<ConfirmationChangeFieldDef> workProps = Lists.list();

    private String formDefId;

    @DisplayAsArray
    private List<EnabledConfirmationPropInfo> enabledConfirmationProps = Lists.list();

    public static List<ConfirmationConfig> listAll() {
        return DataQuery.identifier(IDENTIFIER).limit(500, 1).filter(DataFilter.eq("tenantId",
                SecurityUserUtil.getSecurityUserInfo().getTenantId()), ConfirmationConfig.class).getItems();
    }

    public static void enable(String bid) {
        List<ConfirmationConfig> configs = listAll();
        ConfirmationConfig config = configs.stream().filter(it -> it.getBid().equals(bid)).findFirst()
                .orElseThrow(() -> new ServerException("配置不存在"));
        if (config.status.equals(ConfirmationConfigStatus.ENABLED)) {
            throw new ServerException("配置已开启");
        }
        config.status = ConfirmationConfigStatus.ENABLED;
        DataUpdate.identifier(IDENTIFIER).update(config);
        SpringUtil.getBean(ConfirmationEsService.class).syncConfirmationDef(config);
        val type = config.appliedBy;
        configs.stream().filter(it ->
                it.getStatus().equals(ConfirmationConfigStatus.ENABLED) &&
                        it.getAppliedBy().equals(type) &&
                        !it.getBid().equals(bid)).forEach(it -> {
            it.status = ConfirmationConfigStatus.DISABLED;
            DataUpdate.identifier(IDENTIFIER).update(it);
        });
    }

    public String create() {
        val user = SecurityUserUtil.getSecurityUserInfo();
        setCreateBy(String.valueOf(user.getUserId()));
        setCreateTime(System.currentTimeMillis());
        setUpdateBy(getCreateBy());
        setUpdateTime(getCreateTime());
        status = ConfirmationConfigStatus.NOT_ENABLED;
        return DataInsert.identifier(IDENTIFIER).insert(this);
    }

    private static void checkRemovedProperties(List<ConfirmationChangeFieldDef> existedProp, List<ConfirmationChangeFieldDef> editedProp){
        if(null != existedProp && !existedProp.isEmpty()){
            if(null == editedProp){
                throw new ServerException("转正字段不允许删除");
            }
            for(ConfirmationChangeFieldDef propertyConfig : existedProp){
                editedProp.stream().filter(newPropertyConfig->
                                StringUtils.equals(newPropertyConfig.getProperty(),propertyConfig.getProperty()))
                        .findFirst().orElseThrow(()->new ServerException("转正字段不允许删除"));
            }
        }
    }

    public void update() {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        ConfirmationConfig one = DataQuery.identifier(IDENTIFIER).one(getBid(), ConfirmationConfig.class);
        if(one.status.equals(ConfirmationConfigStatus.ENABLED)){
            if(null != one.getEnabledConfirmationProps() && !one.getEnabledConfirmationProps().isEmpty()){
                if(null == this.getEnabledConfirmationProps()){
                    throw new ServerException("转正标准字段不允许更新");
                }
                for(EnabledConfirmationPropInfo propertyConfig : one.getEnabledConfirmationProps()){
                    this.getEnabledConfirmationProps().stream().filter(newPropertyConfig->
                            newPropertyConfig.getEnabled() == propertyConfig.getEnabled())
                            .findFirst().orElseThrow(()->new ServerException("转正标准字段不允许更新"));
                }
            }
            checkRemovedProperties(one.getSalaryProps(), salaryProps);
            checkRemovedProperties(one.getWorkProps(), workProps);
            if(!StringUtils.equals(one.formDefId, formDefId)){
                throw new ServerException("自定义设置不允许更新");
            }
        }else if(one.status.equals(ConfirmationConfigStatus.DISABLED)){
            throw new ServerException("配置当前状态不允许更新");
        }else if(one.status.equals(ConfirmationConfigStatus.NOT_ENABLED)){
            //do nothing
        }
        if (!one.appliedBy.equals(appliedBy)) {
            throw new ServerException("配置类型不允许更新");
        }
        BeanUtils.copyProperties(one, this,
                "name", "description",
                "displayWorkInfos", "salaryProps", "workProps",
                "formDefId", "enabledConfirmationProps");
        setUpdateBy(String.valueOf(user.getUserId()));
        setUpdateTime(System.currentTimeMillis());
        //status = ConfirmationConfigStatus.NOT_ENABLED;
        DataUpdate.identifier(IDENTIFIER).update(this);
    }

    public List<ConfirmationChangeFieldDef> fetchAllBusinessField() {
        List<ConfirmationChangeFieldDef> list = new ArrayList<>();
        list.addAll(salaryProps);
        list.addAll(workProps);
        for (EnabledConfirmationPropInfo property : enabledConfirmationProps) {
            list.add(new ConfirmationChangeFieldDef("other", property.getEnabled()
                    .name(), property.getEnabled().text, property.getEnabled().dataType));
        }
        return list;
    }

    public static ConfirmationConfig getOne(String bid) {
        val config = listAll().stream().filter(it -> it.getBid().equals(bid)).findFirst()
                .orElseThrow(() -> new ServerException("配置不存在"));
        return config;
    }
}
