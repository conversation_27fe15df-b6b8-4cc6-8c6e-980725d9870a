package com.caidaocloud.hr.service.archive.infrastructure.provider;

import com.caidaocloud.hr.service.archive.annotation.DynamicIndex;
import com.caidaocloud.hr.service.common.infrastructure.utils.BeanReflectionUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.zxp.esclientrhl.annotation.ESID;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.enums.DataType;
import org.zxp.esclientrhl.util.IndexTools;
import org.zxp.esclientrhl.util.MappingData;
import org.zxp.esclientrhl.util.MetaData;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 21/6/2024 1:35 下午
 */
@Slf4j
@Component
@Primary
@AllArgsConstructor
public class CusDynamicIndexTool extends IndexTools {
    private DynamicIndexProvider indexProvider;

    @Override
    public MetaData getMetaData(Class<?> clazz) {
        MetaData metaData = super.getMetaData(clazz);
        boolean dynamicIndexMode = clazz.isAnnotationPresent(DynamicIndex.class);
        if (dynamicIndexMode) {
            DynamicIndex dynamicIndex = clazz.getAnnotation(DynamicIndex.class);
            if (dynamicIndex.enable()) {
                metaData.setIndexname(indexProvider.getTenantIndex(metaData.getIndexname()));
                if (metaData.getSearchIndexNames() != null) {
                    String[] tenantSearchIndexes = Arrays.stream(metaData.getSearchIndexNames()).map(it -> indexProvider.getTenantIndex(it))
                            .toArray(String[]::new);
                    metaData.setSearchIndexNames(tenantSearchIndexes);
                }
            }
        }
        return metaData;
    }

    @Override
    public MappingData[] getMappingData(Class<?> clazz) {
        var fieldList = BeanReflectionUtil.getBeanPropertyField(clazz);
        MappingData[] mappingDataList = new MappingData[fieldList.size()];
        for (int i = 0; i < fieldList.size(); i++) {
            if (fieldList.get(i).getName().equals("serialVersionUID")) {
                continue;
            }
            mappingDataList[i] = getMappingData(fieldList.get(i));
        }
        return mappingDataList;
    }

    @Override
    public MappingData getMappingData(Field field) {
        if (field == null) {
            return null;
        }
        field.setAccessible(true);
        MappingData mappingData = new MappingData();
        mappingData.setField_name(field.getName());
        if (field.getAnnotation(ESMapping.class) != null) {
            ESMapping esMapping = field.getAnnotation(ESMapping.class);
            mappingData.setDatatype(getType(esMapping.datatype()));
            mappingData.setAnalyzer(esMapping.analyzer().toString());
            mappingData.setNgram(esMapping.ngram());
            mappingData.setIgnore_above(esMapping.ignore_above());
            mappingData.setSearch_analyzer(esMapping.search_analyzer().toString());
            if (mappingData.getDatatype().equals("text")) {
                mappingData.setKeyword(esMapping.keyword());
            } else {
                mappingData.setKeyword(false);
            }
            mappingData.setSuggest(esMapping.suggest());
            mappingData.setAllow_search(esMapping.allow_search());
            mappingData.setCopy_to(esMapping.copy_to());
            mappingData.setNested_class(esMapping.nested_class());
            if (!StringUtils.isEmpty(esMapping.null_value())) {
                mappingData.setNull_value(esMapping.null_value());
            }
        } else {
            mappingData.setKeyword(false);
            if (field.getAnnotation(ESID.class) != null) {
                mappingData.setDatatype(getType(DataType.keyword_type));
            } else {
                if (field.getType() == String.class) {
                    mappingData.setDatatype(getType(DataType.text_type));
                    mappingData.setKeyword(true);
                } else if (field.getType() == Short.class || field.getType() == short.class) {
                    mappingData.setDatatype(getType(DataType.short_type));
                } else if (field.getType() == Integer.class || field.getType() == int.class) {
                    mappingData.setDatatype(getType(DataType.integer_type));
                } else if (field.getType() == Long.class || field.getType() == long.class) {
                    mappingData.setDatatype(getType(DataType.long_type));
                } else if (field.getType() == Float.class || field.getType() == float.class) {
                    mappingData.setDatatype(getType(DataType.float_type));
                } else if (field.getType() == Double.class || field.getType() == double.class) {
                    mappingData.setDatatype(getType(DataType.double_type));
                } else if (field.getType() == BigDecimal.class) {
                    mappingData.setDatatype(getType(DataType.double_type));
                } else if (field.getType() == Boolean.class || field.getType() == boolean.class) {
                    mappingData.setDatatype(getType(DataType.boolean_type));
                } else if (field.getType() == Byte.class || field.getType() == byte.class) {
                    mappingData.setDatatype(getType(DataType.byte_type));
                } else if (field.getType() == Date.class) {
                    mappingData.setDatatype(getType(DataType.date_type));
                } else if (field.getType() == Map.class || field.getType() == NestPropertyValue.class) {
                    mappingData.setDatatype("object");
                } else {
                    mappingData.setDatatype(getType(DataType.text_type));
                    mappingData.setKeyword(true);
                }
            }
            mappingData.setAnalyzer("standard");
            mappingData.setNgram(false);
            mappingData.setIgnore_above(256);
            mappingData.setSearch_analyzer("standard");
            mappingData.setSuggest(false);
            mappingData.setAllow_search(true);
            mappingData.setCopy_to("");
            mappingData.setNested_class(null);
        }
        return mappingData;
    }

    private static String getType(DataType dataType) {
        return dataType.toString().replaceAll("_type", "");
    }
}