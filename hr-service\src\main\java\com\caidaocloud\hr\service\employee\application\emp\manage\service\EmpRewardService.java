package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpRewardDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpRewardDomainService;
import com.caidaocloud.hr.service.dto.EmpRewardDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Service
public class EmpRewardService {
    @Resource
    private EmpRewardDomainService empRewardDomainService;

    @Resource
    private EmpExtFieldService empExtFieldService;
    public List<EmpRewardDo> getRewardList(String empId) {
        return  empRewardDomainService.getRewardList(empId);
    }

    public EmpRewardDo getReward(String bid) {
        return empRewardDomainService.getReward(bid);
    }

    /**
     * 新增奖惩信息
     * @param dto 奖惩信息dto
     */
    public String save(EmpRewardDto dto) {
        EmpRewardDo data = ObjectConverter.convert(dto, EmpRewardDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empRewardDomainService.save(data);
    }

    private void doConvert(EmpRewardDto source, EmpRewardDo target) {
        if (StringUtils.isNotEmpty(source.getType())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getType());
            target.setType(enumSimple);
        }
    }

    public void update(EmpRewardDto dto) {
        EmpRewardDo data = ObjectConverter.convert(dto, EmpRewardDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empRewardDomainService.update(data);
    }

    public void delete(String bid) {
        empRewardDomainService.delete(bid);
    }
}
