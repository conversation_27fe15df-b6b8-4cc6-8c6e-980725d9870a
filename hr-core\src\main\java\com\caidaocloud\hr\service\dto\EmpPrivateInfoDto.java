package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ProvinceCity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工个人基本信息DTO")
public class EmpPrivateInfoDto {
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("员工英文名")
    private String enName;
    @ApiModelProperty("性别")
    private String sex;
    @ApiModelProperty("国籍")
    private String nationality;
    @ApiModelProperty("民族")
    private String nation;
    @ApiModelProperty("籍贯")
    private Address birthPlace;
    @ApiModelProperty("籍贯(弃用)")
    private ProvinceCity nativePlace;
    @ApiModelProperty("户口类型")
    private String familyType;
    @ApiModelProperty("户籍地址")
    private String permanentAddress;
    @ApiModelProperty("出生日期")
    private Long birthDate;
    @ApiModelProperty("年龄")
    private Integer divisionAge;
    @ApiModelProperty("婚姻状态")
    private String maritalStatus;
    @ApiModelProperty("生育状态")
    private String fertilityStatus;
    @ApiModelProperty("政治面貌")
    private String politicalOutlook;
    @ApiModelProperty("手机号")
    private PhoneSimple phone;
    @ApiModelProperty("员工个人邮箱")
    private String email;
    @ApiModelProperty("通讯地址")
    private String postalAddress;
    @ApiModelProperty("证件类型")
    private String cardType;
    @ApiModelProperty("证件号")
    private String cardNo;
    @ApiModelProperty("证件有效日期")
    private Long cardEffectiveDate;
    @ApiModelProperty("是否残疾")
    private Boolean disability;
    @ApiModelProperty("监护人姓名")
    private String guardianName;
    @ApiModelProperty("监护人手机")
    private PhoneSimple guardianPhone;
    @ApiModelProperty("监护人邮箱")
    private String guardianEmail;
    @ApiModelProperty("残疾人证等级")
    private String disabilityLevelType;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
