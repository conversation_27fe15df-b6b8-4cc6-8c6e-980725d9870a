package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.transfer.application.service.TransferService;
import com.caidaocloud.hr.service.transfer.interfaces.vo.TransferApplyVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 异动文件归档
 * created by: FoAng
 * create time: 13/6/2024 4:55 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class TransferArchiveProcessor extends AbsArchiveProcessor {

    private TransferService transferService;

    @Override
    public String businessLine() {
        return ArchiveStandardLine.TRANSFER.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        TransferApplyVo applyVo = transferService.detailVo(businessId);
        if (applyVo == null) {
            log.error("[archive] fetch transfer data error, businessId:{}", businessId);
            return archiveDataList;
        }
        return filterArchiveList(Lists.newArrayList(buildArchiveData(applyVo)));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        List<DataSimple> transferList = transferService.getArchiveData(page);
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(transferList)) {
            for (DataSimple dataSimple: transferList) {
                TransferApplyVo applyVo = transferService.convertApplyData(dataSimple, false);
                archiveDataList.add(buildArchiveData(applyVo));
            }
        }
        return filterArchiveList(archiveDataList);
    }

    public ArchiveData buildArchiveData(TransferApplyVo applyVo) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setBusinessLine(ArchiveStandardLine.TRANSFER.getDesc());
        archiveData.setBusinessType(applyVo.getTypeIdTxt());
        archiveData.setBusinessId(applyVo.getId());
        archiveData.setEmpId(applyVo.getEmp().getEmpId());
        archiveData.setArchiveFiles(fetchArchiveFiles(applyVo));
        archiveData.setEventTime(applyVo.getCreateTime());
        return archiveData;
    }

    public List<ArchiveFile> fetchArchiveFiles(TransferApplyVo apply) {
        List<ArchiveFile> archiveFiles = Lists.newArrayList();
        if (StringUtils.isNotEmpty(apply.getFormValueId())) {
            ArchiveFile formFile = fetchFormFile(apply.getFormId(), apply.getFormValueId());
            archiveFiles.add(formFile);
        }
        if (apply.getFiles() != null) {
            archiveFiles.add(BeanUtil.convert(apply.getFiles(), ArchiveFile.class));
        }
        return archiveFiles.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
