package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.caidaocloud.hr.service.dto.EmpOtherContractDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpOtherContractDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpOtherContractDomainService;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class EmpOtherContractService {
    @Resource
    private EmpOtherContractDomainService empOtherContractDomainService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    public List<EmpOtherContractDo> getEmpOtherContractList(String empId) {
        return empOtherContractDomainService.getEmpOtherContractList(empId);
    }

    public EmpOtherContractDo getDetail(String bid) {
        return empOtherContractDomainService.getDetail(bid);
    }

    public String save(EmpOtherContractDto dto) {
        EmpOtherContractDo data = ObjectConverter.convert(dto, EmpOtherContractDo.class);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empOtherContractDomainService.save(data);
    }

    public void update(EmpOtherContractDto dto) {
        List<EmpOtherContractDo> dataList = empOtherContractDomainService.getEmpOtherContractList(dto.getEmpId());
        EmpOtherContractDo oldData = new EmpOtherContractDo();
        if (CollectionUtils.isNotEmpty(dataList)) {
            oldData = dataList.stream().sorted(Comparator.comparingLong(EmpOtherContractDo::getStartDate).reversed()).collect(Collectors.toList()).get(0);
        }
        EmpOtherContractDo newData = ObjectConverter.convert(dto, EmpOtherContractDo.class);
        empExtFieldService.doCusExtProps(newData.getDoIdentifier(), dto.getExt(), newData);
        if (oldData != null && oldData.getBid() != null) {
            BeanUtil.copyProperties(newData, oldData, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
            empOtherContractDomainService.update(oldData);
        } else {
            empOtherContractDomainService.save(newData);
        }
    }

    public void delete(String bid) {
        empOtherContractDomainService.delete(bid);
    }

    public EmpOtherContractDo getBasicInfo(String empId) {
       return empOtherContractDomainService.getListByEffectDate(empId);
    }
}
