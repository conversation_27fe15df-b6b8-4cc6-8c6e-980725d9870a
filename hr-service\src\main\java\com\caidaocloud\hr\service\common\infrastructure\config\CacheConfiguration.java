package com.caidaocloud.hr.service.common.infrastructure.config;

import com.jarvis.cache.serializer.FastjsonSerializer;
import com.jarvis.cache.serializer.ISerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CacheConfiguration {
    @Bean
    public ISerializer<Object> serialize() {
        Object res = new FastjsonSerializer();
        return (ISerializer) res;
    }
}