package com.caidaocloud.hr.service.vo.adapter;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrgDataOutVo {
    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织全称")
    private String fullName;

    @ApiModelProperty("组织简称")
    private String name;

    @ApiModelProperty("上级组织")
    private TreeParent pid;

    @ApiModelProperty("责任人")
    private EmpSimple leaderEmp;

    @ApiModelProperty("责任人岗位code")
    private String leaderEmpPost;

    @ApiModelProperty("hrbp")
    private EmpSimple hrbpEmp;
}
