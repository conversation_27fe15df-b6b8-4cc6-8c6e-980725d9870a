package com.caidaocloud.hr.service.confirmation.domain.enums;

public enum ConfirmationType {
    // 不转正
    NOT_CONFIRMATION("不转正"),
    // 延迟转正
    DELAY_CONFIRMATION("延迟转正"),
    // 提前转正
    EARLY_CONFIRMATION("提前转正"),
    // 按期转正
    ON_TIME_CONFIRMATION("按期转正");

    public final String text;

    ConfirmationType(String text) {
        this.text = text;
    }


    public static ConfirmationType getByName(String name) {
        for (ConfirmationType type : ConfirmationType.values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static String getTextByName(String name) {
        ConfirmationType confirmationType = getByName(name);
        return confirmationType == null ? null : confirmationType.text;
    }
}
