package com.caidaocloud.hr.service.contract.interfaces.vo;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchRecord;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
@ApiModel(description = "续约意向书批量发起批次vo")
public class ContinueLetterBatchVo {
	@ApiModelProperty(value = "bid")
	private String bid;

	@ApiModelProperty("发起人员")
	private String name = "人员列表";

	@ApiModelProperty(value = "创建人")
	private String createBy;

	@ApiModelProperty(value = "创建人姓名")
	private String createByName;

	@ApiModelProperty(value = "创建时间")
	private Long createTime;
	
	@ApiModelProperty(value = "匹配进度")
	private EnumSimple matchStatus;

	@ApiModelProperty(value = "匹配成功数")
	private int matchSucceed = 0;

	@ApiModelProperty(value = "匹配失败数")
	private int matchFailed = 0;

	@ApiModelProperty(value = "发起状态")
	private EnumSimple startStatus;

	@ApiModelProperty(value = "发起成功数")
	private int startSucceed = 0;

	@ApiModelProperty(value = "发起失败数")
	private int startFailed = 0;
}
