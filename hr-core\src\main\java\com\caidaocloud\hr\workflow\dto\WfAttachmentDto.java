package com.caidaocloud.hr.workflow.dto;

import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WfAttachmentDto {
    /**
     * 文件类型
     */
    private WfAttachmentTypeEnum type;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * Attachment 转 WfAttachmentDto
     *
     * @param attachFile
     * @return
     */
    public static List<WfAttachmentDto> doConvertAttachment(Attachment attachFile) {
        if (null == attachFile || CollectionUtils.isEmpty(attachFile.getUrls()) ||
                CollectionUtils.isEmpty(attachFile.getNames()) || attachFile.getUrls().size() != attachFile.getNames().size()) {
            return Lists.newArrayList();
        }
        List<String> names = attachFile.getNames();
        List<String> urls = attachFile.getUrls();
        List<WfAttachmentDto> fileList = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            fileList.add(new WfAttachmentDto(WfAttachmentTypeEnum.file, urls.get(i), names.get(i)));
        }
        return fileList;
    }
}
