package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.hr.service.common.application.dto.paas.ProvinceAndCityDto;
import com.caidaocloud.hr.service.common.application.feign.DictFeignClient;
import com.caidaocloud.hr.service.temination.application.dto.FormDefMetadataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 信息收集值转换工具类
 *
 * <AUTHOR>
 * @date 2022/9/17
 **/
@Slf4j
@Component
public class FormDataConvertValueUtil {
    @Autowired
    private DictFeignClient dictFeignClient;
    @Autowired
    private IMetadataFeign metadataFeign;
    private final String PAAS_PROVINCE_IDETIFIER = "entity.hrpaas.Province";
    private final String PAAS_CITY_IDETIFIER = "entity.hrpaas.City";

    /**
     * 将value转换成文本
     *
     * @param value
     * @param collectProp
     * @return
     */
    public Object convertValueToText(Object value, FormDefMetadataDto formDefMetadata) {
        if (value == null) {
            return value;
        }
        switch (formDefMetadata.getDataType()) {
            case Dict:
                return handleDict(formDefMetadata.getDatasource(), value);
            case Enum:
                return handleEnum(formDefMetadata, value);
            case PROVINCE_CITY:
                return handleProvinceCity(value);
            case Timestamp:
                return handleTimestamp(value);
            case Boolean:
                return handleBoolean(value);
            case Address:
                return handleAddress(value);
            case Attachment:
                return ((Attachment) value).getNames();
            case Emp:
                return handleEmp(value);
            default:
                return value;
        }
    }

    private Object handleDict(String dataSource, Object value) {
        if (log.isDebugEnabled()) {
            log.debug("prepare to run handleDict method, dataSource={} value={}", dataSource, value);
        }
        dataSource = StringUtils.defaultString(dataSource, "");
        if (dataSource.contains("/")) {
            dataSource = dataSource.substring(dataSource.indexOf("typeCode=") + "typeCode=".length());
        }
        var result = dictFeignClient.getEnableDictList(dataSource, "Employee");
        if (result.isSuccess()) {
            var optional = result.getData().stream().filter(e -> e.getValue().equals(value)).map(e -> e.getText()).findFirst();
            if (optional.isPresent()) {
                return optional.get();
            }
            log.info("not found text of value, dataSource={} value={}", dataSource, value);
        } else {
            log.info("request dict service fail, dataSource={} value={}", dataSource, value);
        }
        return null;
    }

    private Object handleEnum(FormDefMetadataDto formDefMetadata, Object value) {
        List<PropertyEnumDefDto> enumDef = formDefMetadata.getEnumDef();
        for (PropertyEnumDefDto propertyEnumDefDto : enumDef) {
            if (propertyEnumDefDto.getValue().equals(value)) {
                return propertyEnumDefDto.getDisplay();
            }
        }
        return "";
    }

    private Object forEarchMetaPropertyList(List<MetadataPropertyVo> list, String property, Object value) {
        if (!CollectionUtils.isEmpty(list)) {
            var optional = list.stream()
                    .filter(e -> e.getDataType() == PropertyDataType.Enum && e.getProperty().equals(property))
                    .map(e -> e.getEnumDef()).findFirst();
            List<PropertyEnumDefDto> propertyEnumDefList = null;
            if (optional.isPresent() && !CollectionUtils.isEmpty(propertyEnumDefList = optional.get())) {
                for (PropertyEnumDefDto propertyEnumDefDto : propertyEnumDefList) {
                    if (value != null && propertyEnumDefDto.getValue().equalsIgnoreCase(value.toString())) {
                        return propertyEnumDefDto.getDisplay();
                    }
                }
            }
        }
        return null;
    }

    private Object handleProvinceCity(Object value) {
        if (value == null) {
            log.info("value is empty");
            return value;
        }
        if (value instanceof Map) {
            Map map = (Map) value;
            var provinceName = map.computeIfPresent("provinceId", (k, v) -> {
                return queryProvinceAndCity(PAAS_PROVINCE_IDETIFIER, v.toString());
            });
            var cityName = map.computeIfPresent("cityId", (k, v) -> {
                return queryProvinceAndCity(PAAS_CITY_IDETIFIER, v.toString());
            });
            if (!Objects.isNull(provinceName) && !Objects.isNull(cityName)) {
                return String.format("%s/%s", provinceName, cityName);
            } else if (!Objects.isNull(provinceName)) {
                return provinceName;
            } else if (!Objects.isNull(cityName)) {
                return cityName;
            }
            log.info("not found name of province and city, value={}", FastjsonUtil.toJson(value));
        }
        return value;
    }

    private String queryProvinceAndCity(String identifer, String bid) {
        var provinceAndCityDto = DataQuery.identifier(identifer)
                .decrypt()
                .specifyLanguage()
                .queryInvisible()
                .oneOrNull(bid, ProvinceAndCityDto.class);
        if (provinceAndCityDto == null) {
            return null;
        }
        return provinceAndCityDto.getName();
    }

    private String handleTimestamp(Object value) {
        if (value == null || StringUtils.isBlank(value.toString()) || "null".equals(value.toString())) {
            return null;
        }
        return DateUtil.formatDate(Long.parseLong(value.toString()));
    }

    public String handleAddress(Object value) {
        if (value == null) {
            return "";
        }
        Address address = null;
        if (value instanceof String) {
            address = new Address();
            var split = String.valueOf(value).split("/");
            if (split.length == 4) {
                address.setProvince(StringUtils.isBlank(split[0]) ? null : Long.valueOf(split[0]));
                address.setCity(StringUtils.isBlank(split[1]) || "null".equals(split[1]) ? null : Long.valueOf(split[1]));
                address.setArea(StringUtils.isBlank(split[2]) || "null".equals(split[2]) ? null : Long.valueOf(split[2]));
                address.setAddress(split[3]);
            }
        } else if (value instanceof Map) {
            Map map = (Map) value;
            if (map.containsKey("addressTxt")) {
                return String.valueOf(map.get("addressTxt"));
            }
            address = FastjsonUtil.convertObject(value, Address.class);
        } else {
            address = FastjsonUtil.convertObject(value, Address.class);
        }
        address.doValue();
        try {
            QueryInfoCache.init();
            Address.doAddress(address);
        } finally {
            QueryInfoCache.clear();
        }

        ArrayList<String> textList = Lists.newArrayList(address.getProvinceTxt(), address.getCityTxt(), address.getAreaTxt());
        String text = textList.stream().filter(txt -> StringUtils.isNotEmpty(txt)).collect(Collectors.joining("/"));
        return text;
    }

    public String handleBoolean(Object value) {
        if (value == null) {
            return "否";
        }
        Boolean b = Boolean.valueOf(value.toString());
        if (b) {
            return "是";
        } else {
            return "否";
        }
    }

    private String handleEmp(Object value) {
        if (value == null || StringUtils.isBlank(value.toString()) || "null".equals(value.toString())) {
            return "";
        }
        EmpSimple empSimple = FastjsonUtil.convertObject(value, EmpSimple.class);
        return StringUtils.isNotBlank(empSimple.getWorkno()) ? String.format("(%s)%s", empSimple.getWorkno(), empSimple.getName()) : empSimple.getName();
    }
}