package com.caidaocloud.hr.service.employee.application.dataimport.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.TmpOrgImportPo;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.DictDto;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class TmpOrgImportService {
    @Autowired
    private OrgService orgService;
    @Autowired
    private DictService dictService;

    private final  String MODULE ="M_ORG",TYPE="ArchitectureType", CODE = "Administration";

    public void importOrg(MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        try {
            List<TmpOrgImportPo> importData = ExcelImportUtil.importExcel(file.getInputStream(), TmpOrgImportPo.class, params);
            Iterator<TmpOrgImportPo> iterator = importData.iterator();
            List<String> worknos = new ArrayList<>(500);

            Result dictRes = dictService.getEnableDictListAllInfo(TYPE, MODULE);
            Object data = dictRes.getData();
            List<DictDto> list = FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), DictDto.class);
            String schemaType = null;
            for (DictDto dict : list) {
                if(CODE.equals(dict.getCode())){
                    schemaType = dict.getValue();
                }
            }
            while (iterator.hasNext()) {
                TmpOrgImportPo po = iterator.next();
                OrgDo orgDo = new OrgDo();
                orgDo.setFullName(po.getOrgName());
                orgDo.setCostCenterId(null);
                orgDo.setOrgType(null);
                orgDo.setName(po.getOrgName());
                orgDo.setCode(po.getOrgCode());
                orgDo.setDataStartTime(DateUtil.getCurrentTimestamp());
                orgDo.setPid(null);
                orgDo.setUpdateSchema(false);
                orgDo.setVirtual(false);
                orgDo.setCustomOrgRoles(Lists.newArrayList());

                DictSimple dictSimple = new DictSimple();
                dictSimple.setValue(schemaType);
                orgDo.setSchemaType(dictSimple);
                orgService.saveOrg(orgDo, null);
            }
        } catch (Exception e) {
            log.error("import emp info occur error ,msg = {}", e.getMessage(), e);
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_32014"));
        }
    }
}
