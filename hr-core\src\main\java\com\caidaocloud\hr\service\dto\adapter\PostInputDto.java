package com.caidaocloud.hr.service.dto.adapter;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PostInputDto {
    private String bid;

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 岗位编码
     */
    private String code;

    private Long dateTime;

    /**
     * 所属组织ID
     */
    private String orgId;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 关联职务或基准岗位，0：未配置，1：职务，2：基准岗位
     */
    private Integer relation;

    /**
     * 关联基准岗位ID
     */
    private String benchmarkPositionId;

    /**
     * 关联基准岗位名称
     */
    private String benchmarkPositionName;
}
