package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.hr.service.contract.domain.entity.EmpContractTypeSetRelDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

public interface IEmpContractTypeSetRelRepository extends BaseRepository<EmpContractTypeSetRelDo> {

    List<EmpContractTypeSetRelDo> getEmpContractTypeList(String empId, String identifier);

    void batchInsert(String identifier, List<EmpContractTypeSetRelDo> data);

    void deleteByTypeSetBid(String doIdentifier, String bid);

    List<EmpContractTypeSetRelDo> selectByEmpId(String identifier, List<String> empIds);
}
