package com.caidaocloud.hr.service.confirmation.interfaces.vo;

import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ConfirmationApplyVo implements Serializable {
    @ApiModelProperty("主键Id")
    private String id;

    @ApiModelProperty("申请员工")
    private EmpSimple emp;

    @ApiModelProperty("转正配置ID")
    private String defId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("挂载表单ID")
    private String formDefId;

    @ApiModelProperty("表单formValueId")
    private String formValueId;

    @ApiModelProperty("开启审批")
    private boolean approval;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("创建时间")
    private long createTime;

    @ApiModelProperty("更新时间")
    private long updateTime;

    @ApiModelProperty("转正数据")
    private List<ConfirmationChangeField> confirmations = Lists.newArrayList();

    @ApiModelProperty("薪资数据")
    private List<ConfirmationChangeField> salary = Lists.newArrayList();

    @ApiModelProperty("任职数据")
    private List<ConfirmationChangeField> work = Lists.newArrayList();

    @ApiModelProperty("工作流id")
    private String businessKey;
}
