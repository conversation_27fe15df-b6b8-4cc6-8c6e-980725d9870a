package com.caidaocloud.hr.core.feign;

import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class WorkFlowFeignFallBack implements WorkFlowFeignClient {
    @Override
    public Result<Boolean> checkDefEnabled(String funCode) {
        return Result.fail();
    }

    @Override
    public Result<String> getBusinessKeyBy(String businessKey) {
        return Result.fail();
    }
}
