package com.caidaocloud.hr.service.employee.application.dataimport.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConRecordImportDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.EmpConRecordImportPo;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpConRecordImportService extends DataImportService<EmpConRecordImportDo,EmpConRecordImportPo> {

    private final static String IDENTIFIER = "entity.hr.Contract";
    @Resource
    private EmpConRecordImportDo empConRecordImportDo;
    @Resource
    private CompanyService companyService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private DictService dictService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private ContractService contractService;
    @Resource
    private CacheService cacheService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private ContractEsignService contractEsignService;
    @Resource
    private OrgService orgService;
    @Autowired
    private IWfRegisterFeign iWfRegisterFeign;


    private static Map<String, Map<String, KeyValue>> staticMap = new HashMap<>();
    private static Map<String, Map<String, PropertyEnumDefDto>> staticPropMap = new HashMap<>();
    private static Map<String, Map<String, CompanyDo>> companyMaps = new HashMap<>();
    private static Map<String, Map<String, ContractTypeSetDo>> contractTypeSetMap = new HashMap<>();

    private static String businessCode = "EMP_CON_APPROVAL_IMPORT";

    private static final String ContractTypeDicCode = "ContractType";

    private static final String SignTypeProp = "signType", PeriodTypeProp = "periodType", ApprovalStatus = "approvalStatus";

    private static final String CompanyList = "CompanyList", ContractTypeSetList = "ContractTypeSetList";

    @Override
    public String getExcelCode(){
        return businessCode;
    }

    @Override
    public List<EmpConRecordImportDo> getPoDataFromExcel(InputStream inputStream) {
        return empConRecordImportDo.getEmpConRecordImportDoFromExcel(inputStream);
    }

    @Override
    public List<EmpConRecordImportDo> batchInsertUpdateData(List<EmpConRecordImportDo> conRecordList) {
        if(null == conRecordList || conRecordList.size() == 0){
            return new ArrayList<>();
        }
        List<EmpConRecordImportDo> errorList = new ArrayList<>();
        List<String> contractNos = new ArrayList<>();
        List<String> workNos = new ArrayList<>();
        for(EmpConRecordImportDo con :conRecordList){
            contractNos.add(con.getContractNo());
            workNos.add(con.getWorkno());
        }
        List<EmpBasicInfoDo> basicInfoList = empBasicInfoService.getEmpBasicInfoListByWorkNo(workNos, System.currentTimeMillis());
        List<EmpWorkInfoDo> workInfoList = empWorkInfoDomainService.getEmpListByWorkNos(workNos);
        List<ContractDo> contractList = contractService.getContractByContractNo(contractNos);

        Map<String, ContractDo> oldConMap = contractList.stream().collect(Collectors.toMap(ContractDo::getContractNo, obj -> obj, (A, B) -> A));
        Map<String, EmpBasicInfoDo> oldEmpInfoMap = basicInfoList.stream().collect(Collectors.toMap(EmpBasicInfoDo::getWorkno, obj -> obj, (A, B) -> A));
        Map<String, EmpWorkInfoDo> oldWorkInfoMap = workInfoList.stream().collect(Collectors.toMap(EmpWorkInfoDo::getWorkno, obj -> obj, (A, B) -> A));

        // 查询组织信息
        Map<String, OrgDo> orgInfoMap = orgService.selectOrgMapByIds(workInfoList.stream().filter(empWorkInfoDo -> empWorkInfoDo.getOrganize() != null)
                .map(EmpWorkInfoDo::getOrganize).collect(Collectors.toList()), System.currentTimeMillis());


        if(null == contractList || contractList.size() == 0){
            return insertEmpConRecordImportList(conRecordList,oldEmpInfoMap,oldWorkInfoMap, orgInfoMap);
        }
        List<EmpConRecordImportDo> insertList = new ArrayList<>();
        List<EmpConRecordImportDo> updateList = new ArrayList<>();
        for(EmpConRecordImportDo importDo : conRecordList){
            ContractDo contractDo = oldConMap.get(importDo.getContractNo());
            if(contractDo == null){
                insertList.add(importDo);
            } else {
                importDo.setBid(contractDo.getBid());
                updateList.add(importDo);
            }
        }
        errorList.addAll(insertEmpConRecordImportList(insertList,oldEmpInfoMap,oldWorkInfoMap,orgInfoMap));
        errorList.addAll(updateConRecordImportList(updateList,oldEmpInfoMap,oldWorkInfoMap,oldConMap,orgInfoMap));
        return errorList;
    }


    private void contractOpenWorkflow(ContractDo contractDo) {
        String funcCode = "";
        if (SignTypeEnum.NEW.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_NEW_SIGN;
        }
        if (SignTypeEnum.CHANGE.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_AMENDMENT;
        }
        if (SignTypeEnum.RENEW.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_RENEW;
        }
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        workflowDto.setFuncCode(funcCode);
        workflowDto.setApplicantId(contractDo.getOwner().getEmpId());
        workflowDto.setBusinessId(contractDo.getBid());
        workflowDto.setApplicantName(contractDo.getOwner().getName());
        // 业务单据事件时间
        workflowDto.setEventTime(System.currentTimeMillis());
        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("import contractOpenWorkflow beginWorkflow err,{}", e.getMessage(), e);
        }
        log.info("import contractOpenWorkflow wfResult:{}", FastjsonUtil.toJson(wfResult));
        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());

    }


    private List<EmpConRecordImportDo> updateConRecordImportList(List<EmpConRecordImportDo> updateList,Map<String, EmpBasicInfoDo> oldEmpInfoMap,
                                                                 Map<String, EmpWorkInfoDo> oldWorkInfoMap,Map<String, ContractDo> oldConMap,Map<String, OrgDo> orgInfoMap) {
        List<EmpConRecordImportDo> errorList = new ArrayList<>();
        Long dataTime = DateUtil.getCurrentTimestamp();
        for(EmpConRecordImportDo importDo : updateList){
            try {
                ContractDo contractDo = oldConMap.get(importDo.getContractNo());
                BeanUtils.copyProperties(importDo, contractDo);
                doConvertEmpInfo(importDo,oldEmpInfoMap,oldWorkInfoMap,contractDo,orgInfoMap);
                contractDo.calcStatus();
                contractDomainService.update(contractDo);

                // 审批通过的数据流转到电子签待发起列表
                if(null != contractDo.getApprovalStatus() &&
                        ApprovalStatusEnum.PASSED.getIndex().toString().equals(contractDo.getApprovalStatus().getValue())){
                    contractEsignService.contractCirculation(contractDo, oldEmpInfoMap, oldWorkInfoMap, dataTime);
                }
                if ("是".equals(importDo.getNeedApprove())) {
                    contractOpenWorkflow(contractDo);
                }
            }catch (Exception e){
                log.error("审批记录导入异常--> error:{},{}", e.getMessage(), e);
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }
        return errorList;
    }

    private List<EmpConRecordImportDo> insertEmpConRecordImportList(List<EmpConRecordImportDo> insertList,Map<String, EmpBasicInfoDo> oldEmpInfoMap,
                                                                    Map<String, EmpWorkInfoDo> oldWorkInfoMap,Map<String, OrgDo> orgInfoMap) {
        List<EmpConRecordImportDo> errorList = new ArrayList<>();
        String contractBid = null;
        Long dataTime = DateUtil.getCurrentTimestamp();
        for(EmpConRecordImportDo importDo : insertList){
            try {
                ContractDo contractDo = ObjectConverter.convert(importDo, ContractDo.class);
                doConvertEmpInfo(importDo, oldEmpInfoMap, oldWorkInfoMap, contractDo,orgInfoMap);
                contractDo.calcStatus();
                contractBid = contractDomainService.insert(contractDo);

                // 审批通过的数据流转到电子签待发起列表
                if(null != contractDo.getApprovalStatus() &&
                        ApprovalStatusEnum.PASSED.getIndex().toString().equals(contractDo.getApprovalStatus().getValue())){
                    contractDo.setBid(contractBid);
                    contractEsignService.contractCirculation(contractDo, oldEmpInfoMap, oldWorkInfoMap, dataTime);
                }
                if ("是".equals(importDo.getNeedApprove())) {
                    contractOpenWorkflow(contractDo);
                }
            }catch (Exception e){
                log.error("审批记录导入异常--> error:{},{}", e.getMessage(), e);
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }
        return errorList;
    }

    private void doConvertEmpInfo(EmpConRecordImportDo importDo, Map<String, EmpBasicInfoDo> oldEmpInfoMap, Map<String, EmpWorkInfoDo> oldWorkInfoMap, ContractDo contractDo,Map<String, OrgDo> orgInfoMap) {
        EmpBasicInfoDo basicInfo = oldEmpInfoMap.get(importDo.getWorkno());
        EmpWorkInfoDo workInfo = oldWorkInfoMap.get(importDo.getWorkno());
        if(basicInfo == null || StringUtils.isEmpty(basicInfo.getWorkno())){
            throw new RuntimeException("导入数据异常，未找到人员工号");
        }

        if (!basicInfo.getName().equals(importDo.getName())) {
            throw new RuntimeException("工号与姓名不匹配");
        }

        if (contractDo.getOwner() != null && !contractDo.getOwner().getWorkno().equals(importDo.getWorkno())) {
            throw new RuntimeException("与系统该合同编号对应人员工号不匹配");
        }
        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId(basicInfo.getBid());
        empSimple.setWorkno(basicInfo.getWorkno());
        empSimple.setName(basicInfo.getName());
        contractDo.setOwner(empSimple);

        if(StringUtils.isNotBlank(importDo.getSignTimeTxt())){
            contractDo.setSignTime(Integer.valueOf(importDo.getSignTimeTxt()));
        }
        contractDo.setOrganize(basicInfo.getOrganize());
        contractDo.setOrganizeCode(Optional.ofNullable(orgInfoMap.get(basicInfo.getOrganize())).orElse(new OrgDo()).getCode());
        contractDo.setOrganizeTxt(basicInfo.getOrganizeTxt());
        contractDo.setEmpStatus(basicInfo.getEmpStatus());
        contractDo.setPost(basicInfo.getPost());
        contractDo.setPostTxt(basicInfo.getPostTxt());
        if(workInfo != null){
            contractDo.setJob(workInfo.getJob());
            contractDo.setJobTxt(workInfo.getJobTxt());
            contractDo.setHireDate(workInfo.getHireDate());
            contractDo.setEmpType(workInfo.getEmpType());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Temporal str = LocalDate.parse(sdf.format(importDo.getStartDate()));
        Temporal end = LocalDate.parse(sdf.format(importDo.getEndDate()));
        // 方法返回为相差月份
        if (!"9999/12/31".equals(importDo.getEndDateTxt())) {
            Long contractPeriod = ChronoUnit.MONTHS.between(str, end);
            contractDo.setContractPeriod(contractPeriod.intValue());
        }
    }

    private void setEmptyTips(EmpConRecordImportDo con, String tip){
        con.setCheckEmpty(true);
        if(null == con.getCheckEmptyTips()){
            con.setCheckEmptyTips(tip);
        }else {
            con.setCheckEmptyTips(con.getCheckEmptyTips() + "，" + tip);
        }
    }

    @Override
    public boolean checkEmptyProp(EmpConRecordImportDo con) {
        if(StringUtils.isEmpty(con.getName())){
            setEmptyTips(con, "姓名不能为空");
        }
        if(StringUtils.isEmpty(con.getWorkno())){
            setEmptyTips(con, "工号不能为空");
        }
        if(StringUtils.isEmpty(con.getSignTypeTxt())){
            setEmptyTips(con, "签订类型不能为空");
        }
        if(StringUtils.isEmpty(con.getCompanyTxt())){
            setEmptyTips(con, "合同公司不能为空");
        }
        if(StringUtils.isEmpty(con.getContractNo())){
            setEmptyTips(con, "合同编号不能为空");
        }
        if(StringUtils.isEmpty(con.getContractSettingTypeTxt())){
            setEmptyTips(con, "合同类型不能为空");
        }
//        if(StringUtils.isEmpty(con.getPeriodTypeTxt())){
//            setEmptyTips(con, "合同期限不能为空");
//        }
        if(StringUtils.isEmpty(con.getStartDateTxt())){
            setEmptyTips(con, "合同开始时间不能为空");
        }
        if(StringUtils.isEmpty(con.getApprovalStatusTxt())){
            setEmptyTips(con, "审批状态不能为空");
        }
        return true;
    }

    @Override
    public boolean checkEmptyMark(EmpConRecordImportDo data) {
        return data.isCheckEmpty();
    }

    @Override
    public boolean installProp(EmpConRecordImportDo con) {
        String tenantId = getTenantId();
//        Map<String, KeyValue> conTypeMap = staticMap.get(getMapKeyWithTenantId(ContractTypeDicCode, tenantId));
//        Map<String, PropertyEnumDefDto> periodTypeMap = staticPropMap.get(getMapKeyWithTenantId(PeriodTypeProp, tenantId));
        Map<String, PropertyEnumDefDto> signTypeMap = staticPropMap.get(getMapKeyWithTenantId(SignTypeProp, tenantId));
        Map<String, PropertyEnumDefDto> approvalStatusMap = staticPropMap.get(getMapKeyWithTenantId(ApprovalStatus, tenantId));
        Map<String, CompanyDo> companyMap = companyMaps.get(getMapKeyWithTenantId(CompanyList, tenantId));
        Map<String, ContractTypeSetDo> contractTypeSetDoMap = contractTypeSetMap.get(getMapKeyWithTenantId(ContractTypeSetList, tenantId));

        try {
            installCompanyProp(con, companyMap);
            installConTypeProp(con, contractTypeSetDoMap);
            installSignTypeProp(con, signTypeMap);
//        installPeriodTypeProp(con, periodTypeMap);
            installApprovalStatusProp(con, approvalStatusMap);
            installDate(con);
        } catch (Exception e) {
            log.error("[审批记录导入] installProp error, msg: {}", e.getMessage(), e);
            setEmptyTips(con, "审批记录导入数据转换异常，请联系管理员");
        }
        if(con.isCheckEmpty()){
            return false;
        }
        return true;
    }

    private void installDate(EmpConRecordImportDo con) {
        DateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        try {
            con.setStartDate(format.parse(con.getStartDateTxt()).getTime());
            con.setEndDate(format.parse(con.getEndDateTxt()).getTime());
            con.setProbationPeriodEndDate(Optional.ofNullable(con.getProbationPeriodEndDateTxt()).map(it -> {
                try {
                    return format.parse(it).getTime();
                } catch (ParseException e) {
                    throw new ServerException("试用期截止日期转换异常");
                }
            }).orElse(null));
            con.setProbationPeriod(con.getProbationPeriod());
            if (con.getStartDate() >= con.getEndDate()) {
                throw new ServerException("合同日期范围错误");
            }
            EnumSimple simple = new EnumSimple();
            simple.setValue("0");
            if ("9999/12/31".equals(con.getEndDateTxt())) {
                simple.setValue("1");
            }
            con.setPeriodType(simple);
        } catch (ParseException e) {
            setEmptyTips(con, "日期转换异常");
            log.error("import conRecord Data parse error:{}", e.getMessage());
        } catch (ServerException e) {
            setEmptyTips(con, e.getMessage());
        }
    }

    private void installApprovalStatusProp(EmpConRecordImportDo con, Map<String, PropertyEnumDefDto> approvalStatusMap) {
        if(null != approvalStatusMap && null != approvalStatusMap.get(con.getApprovalStatusTxt())){
            PropertyEnumDefDto status = approvalStatusMap.get(con.getApprovalStatusTxt());
            EnumSimple approvalStatus = new EnumSimple();
            approvalStatus.setText(status.getDisplay());
            approvalStatus.setValue(status.getValue());
            con.setApprovalStatus(approvalStatus);
        } else {
            setEmptyTips(con, "审批状态不存在");
        }
    }

    private void installPeriodTypeProp(EmpConRecordImportDo con, Map<String, PropertyEnumDefDto> periodTypeMap) {
        if(null != periodTypeMap && null != periodTypeMap.get(con.getPeriodTypeTxt())){
            PropertyEnumDefDto status = periodTypeMap.get(con.getPeriodTypeTxt());
            EnumSimple periodType = new EnumSimple();
            periodType.setText(status.getDisplay());
            periodType.setValue(status.getValue());
            con.setPeriodType(periodType);
        } else {
            setEmptyTips(con, "合同期限类型不存在");
        }
    }

    private void installSignTypeProp(EmpConRecordImportDo con, Map<String, PropertyEnumDefDto> signTypeMap) {
        if(null != signTypeMap && null != signTypeMap.get(con.getSignTypeTxt())){
            PropertyEnumDefDto status = signTypeMap.get(con.getSignTypeTxt());
            EnumSimple signType = new EnumSimple();
            signType.setText(status.getDisplay());
            signType.setValue(status.getValue());
            con.setSignType(signType);
        } else {
            setEmptyTips(con, "签订类型不存在");
        }
    }

    private void installConTypeProp(EmpConRecordImportDo con, Map<String, ContractTypeSetDo> contractTypeSetDoMap) {
        if(null != contractTypeSetDoMap && null != contractTypeSetDoMap.get(con.getContractSettingTypeTxt())){
            ContractTypeSetDo contractTypeSetDo = contractTypeSetDoMap.get(con.getContractSettingTypeTxt());

            //合同类型
            con.setContractSettingType(contractTypeSetDo.getContractType());

            con.setContractTypeSet(contractTypeSetDo.getBid());
            con.setContractTypeSetTxt(con.getContractSettingTypeTxt());

            //合同类别
            con.setContractType(contractTypeSetDo.getContractClass());
        }else {
            setEmptyTips(con, "合同类型不存在");
        }
    }

    private void installCompanyProp(EmpConRecordImportDo con, Map<String, CompanyDo> companyMap) {
        if(null != companyMap && !companyMap.isEmpty() && null != companyMap.get(con.getCompanyTxt())){
            String companyId = companyMap.get(con.getCompanyTxt()).getBid();
            String code = companyMap.get(con.getCompanyTxt()).getOrganizationCode();
            con.setCompany(companyId);
            con.setCompanyCode(code);
        }else {
            setEmptyTips(con, "公司不存在");
        }
    }
    private String getMapKeyWithDictId(String dictId){
        String dictKey = String.format("DICT_%s",dictId);
        String value = cacheService.getValue(dictKey);
        Map map = FastjsonUtil.toObject(value, Map.class);
        return map.get("dictCode").toString();
    }

    protected String getMapKeyWithTenantId(String key, String tenantId){
        return String.format("%s_%s", key, tenantId);
    }

    @Override
    public void initProperty(){
        String tenantId = UserContext.getTenantId();
//        staticMap.put(getMapKeyWithTenantId(ContractTypeDicCode, tenantId), getEmployDictMap(ContractTypeDicCode));
        staticPropMap.put(getMapKeyWithTenantId(SignTypeProp, tenantId), getEmpMetadataPropertyMap(SignTypeProp));
//        staticPropMap.put(getMapKeyWithTenantId(PeriodTypeProp, tenantId), getEmpMetadataPropertyMap(PeriodTypeProp));
        staticPropMap.put(getMapKeyWithTenantId(ApprovalStatus, tenantId), getEmpMetadataPropertyMap(ApprovalStatus));
        companyMaps.put(getMapKeyWithTenantId(CompanyList, tenantId), getCompanyMap());
        contractTypeSetMap.put(getMapKeyWithTenantId(ContractTypeSetList, tenantId), getContractTypeSetMap());
    }

    private Map<String, KeyValue> getEmployDictMap(String typeCode){
        Map<String, KeyValue> map = new HashMap<>();
        List<KeyValue> list = dictService.getEnableDictList(typeCode,"Employee");
        for(KeyValue kv:list){
            map.put(kv.getText(), kv);
        }
        return map;
    }

    private Map<String, PropertyEnumDefDto> getEmpMetadataPropertyMap(String prop){
        Map<String, PropertyEnumDefDto> map = new HashMap<>();
        MetadataPropertyVo vo = metadataService.getPropertyDef(IDENTIFIER, prop);
        for(PropertyEnumDefDto dto: vo.getEnumDef()){
            map.put(dto.getDisplay(), dto);
        }
        return map;
    }

    /**
     * 查询公司信息
     * @return
     */
    private Map<String, CompanyDo> getCompanyMap(){
        List<CompanyDo> companyList = companyService.selectList();
        Map<String, CompanyDo> map = new HashMap<>();
        for(CompanyDo companyDo : companyList){
            map.put(companyDo.getCompanyName(), companyDo);
        }
        return map;
    }

    /**
     * 查询合同设置信息
     * @return
     */
    private Map<String, ContractTypeSetDo> getContractTypeSetMap(){
        List<ContractTypeSetDo> contractTypeSetDos = contractTypeSetService.selectList();
        Map<String, ContractTypeSetDo> map = new HashMap<>();
        for(ContractTypeSetDo data : contractTypeSetDos){
            map.put(data.getContractType().getText(), data);
        }
        return map;
    }

    @Override
    public void operateAfterImport() {
        // 处理合同签订次数
        contractService.countSignTime();
    }
}
