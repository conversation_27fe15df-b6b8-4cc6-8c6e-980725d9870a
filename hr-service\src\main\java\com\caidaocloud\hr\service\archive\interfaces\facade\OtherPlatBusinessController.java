package com.caidaocloud.hr.service.archive.interfaces.facade;

import com.caidaocloud.hr.service.archive.interfaces.dto.OtherPlatBusinessDto;
import com.caidaocloud.hr.service.archive.interfaces.vo.OtherPlatBusinessVo;
import com.caidaocloud.hr.service.archive.service.OtherPlatBusinessService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/onboarding/v1/other/platform/business")
@Api(value = "第三方平台业务映射", tags = "第三方平台业务映射")
public class OtherPlatBusinessController {
    @Resource
    private OtherPlatBusinessService otherPlatBusinessService;

    @PostMapping("/save")
    @ApiOperation("保存")
    public Result<Boolean> save(@RequestBody List<OtherPlatBusinessDto> list) {
        otherPlatBusinessService.save(list);
        return Result.ok();
    }

    @GetMapping("/list")
    @ApiOperation("查询")
    public Result<List<OtherPlatBusinessVo>> list() {
        return Result.ok(ObjectConverter.convertList(otherPlatBusinessService.selectList(), OtherPlatBusinessVo.class));
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除")
    public Result<Boolean> delete(@RequestParam("bid") String bid) {
        otherPlatBusinessService.delete(bid);
        return Result.ok();
    }
}
