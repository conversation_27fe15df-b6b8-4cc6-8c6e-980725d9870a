package com.caidaocloud.hr.service.contract.interfaces.facade;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.InitiateStatus;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.OrgEmpTreeQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchAutoRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractEmpQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.*;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hr/contract/v1")
@Api(value = "/api/hr/contract/v1", description = "合同管理", tags = "v1.7")
public class ContractController {
    @Resource
    private ContractService contractService;

    @ApiOperation("新增合同")
    @PostMapping("/save")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-{signType{#dto.signType.value}}合同", category = "新增", success = "新增了{{#dto.owner.name}}的合同")
    public Result saveContract(@RequestBody ContractDto dto) {
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("续签合同")
    @PostMapping("/renewalContract")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "续签", success = "续签了{{#dto.owner.name}}的合同")
    public Result renewalContract(@RequestBody ContractDto dto) {
        // 上一份合同不能为空；未维护上一份合同
        // Not maintaining the previous contract
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getLastContract()), LangUtil.getMsg(80021));
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("改签合同")
    @PostMapping("/amendmentContract")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-改签合同", category = "改签", success = "改签了{{#dto.owner.name}}的合同")
    public Result amendmentContract(@RequestBody ContractDto dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getLastContract()), LangUtil.getMsg(80021));
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("候选人流转合同")
    @PostMapping("/movingContract")
    public Result movingContract(@RequestBody ContractDto dto) {
        // 入职过来的数据因入职流程程中做过了电子签署，因此合同数据流转过来时，需关闭电子文件签署
        dto.setCloseEsign(true);
        contractService.movingContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("员工信息-合同信息-新增或编辑合同")
    @PostMapping("/empSaveContract")
    @LogRecordAnnotation(category = "编辑", success = "编辑了{{#name}}的合同信息", menu = "人事-员工信息-合同信息", condition = "{{#condition}}")
    public Result empSaveContract(@RequestBody ContractDto nowContractDto) {
        if (nowContractDto.getOwner() != null) {
            LogRecordContext.putVariable("name", nowContractDto.getOwner().getName());
        }
        contractService.insertOrUpdateContract(nowContractDto);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("更新合同")
    @PostMapping("/update")
    public Result updContract(@RequestBody ContractDto dto) {
        contractService.updateById(ObjectConverter.convert(dto, ContractDo.class));
        return Result.ok(true);
    }

    @ApiOperation("删除合同")
    @DeleteMapping("/delContract")
    @LogRecordAnnotation(category = "删除", success = "删除了{workInfo{#contract.owner.empId}}的{{#contract.contractTypeSetTxt + '(' + #contract.contractNo + ')'}}数据", menu = "人事-员工信息-合同信息", condition = "{{#_ret.success}}")
    public Result delContract(@RequestParam("bid") String bid) {
        ContractDo contractDo = contractService.getById(bid);
        if (contractDo != null) {
            LogRecordContext.putVariable("contract", contractDo);
            contractService.delete(bid);
        }
        return Result.ok(true);
    }

    @ApiOperation("获取合同详情")
    @GetMapping("/getDetail")
    public Result<ContractVo> getDetail(@RequestParam("bid") String bid) {
        return Result.ok(ObjectConverter.convert(contractService.getById(bid), ContractVo.class));
    }

    @ApiOperation("合同列表")
    @PostMapping("/list")
    public Result<PageResult<ContractVo>> getList(@RequestBody ContractQueryDto queryDto) {
        return Result.ok(contractService.getSignedContractPage(queryDto));
    }

    @ApiOperation("生效中和未生效的合同列表 ")
    @PostMapping("/contractList")
    public Result<PageResult<ContractVo>> getContractList(@RequestBody ContractQueryDto queryDto) {

        //生效中和未生效的
//        queryDto.setContractStatusEnumList(Lists.newArrayList(ContractStatusEnum.EFFECTIVE, ContractStatusEnum.IN_EFFECTIVE));
        return Result.ok(contractService.getContractPage(queryDto));
    }

    @ApiOperation("续签意向列表")
    @PostMapping("continue/list")
    public Result<PageResult<ContinueSignContractVo>> getContinueList(@RequestBody ContractQueryDto queryDto) {
        // 过滤未生效合同
        queryDto.setContractStatus(ContractStatusEnum.IN_EFFECTIVE);
        var pageResult = contractService.getLastList(queryDto);
        long nowDateTime = DateUtil.getCurrentTimestamp();
        boolean isZh = LangUtil.chineseLocale();
        final Map<String, String> statusMap = new HashMap<>();
        final Map<String, String> dictMap = new HashMap<>();
        List<ContinueSignContractVo> newContractList = pageResult.getItems().stream().map(data -> {
            var vo = ObjectConverter.convert(data, ContinueSignContractVo.class);
            vo.convertEmpStatus(isZh);
            vo.convertContinueApprovalStatus(isZh);
            vo.convertFeedback(isZh, dictMap);
            if (vo.getEndDate() != null) {
                Long num = vo.getEndDate() - nowDateTime;
                long lastDay = num / 86400000;
                vo.changeStatus(lastDay, statusMap);
                vo.setContractDays(String.valueOf(lastDay));
            }
            vo.setContinueStatus(ContinueStatus.getByName(data.getContinueStatus().getValue()));
            // 发起签署状态默认值
            if (vo.getInitiateStatus() == null || vo.getInitiateStatus().getValue() == null) {
                EnumSimple enumSimple = new EnumSimple();
                enumSimple.setValue(String.valueOf(InitiateStatus.initiate));
                vo.setInitiateStatus(enumSimple);
            }
            vo.setName(data.getOwner().getName());
            vo.setWorkno(data.getOwner().getWorkno());
            String businessKey = vo.getContinueApprovalStatus() == null ||
                    StringUtils.isBlank(vo.getContinueApprovalStatus().getValue()) ? "" : String.format("%s_CONTINUECONTRACT", data.getBid());
            vo.setBusinessKey(businessKey);
            return vo;
        }).collect(Collectors.toList());
        return ResponseWrap.wrapResult(new PageResult(newContractList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("续签意向合同详情")
    @GetMapping("last/detail")
    public Result<ContinueSignContractVo> getDetailOfLastContract(@RequestParam("bid") String bid) {
        Optional<ContractDo> contractByBid = contractService.getLastContractByBid(bid);
        if (contractByBid.isPresent()) {
            ContractDo data = contractByBid.get();
            ContinueSignContractVo vo = ObjectConverter.convert(data, ContinueSignContractVo.class);
            if (data.getContinueStatus() != null && StringUtils.isNotBlank(data.getContinueStatus().getValue())) {
                vo.setContinueStatus(ContinueStatus.getByName(data.getContinueStatus().getValue()));
            }
            vo.setName(String.format("%s(%s)", data.getOwner().getName(), data.getOwner().getWorkno()));
            if (vo.getEndDate() != null) {
                vo.setContractDays(String.valueOf(TimeUnit.MILLISECONDS.toDays(vo.getEndDate() - DateUtil.getCurrentTimestamp())));
            }
            return Result.ok(vo);
        }
        return Result.ok(new ContinueSignContractVo());
    }


    private List<ContractVo> getNewContractList(List<ContractExportVo> pageResult, Map<String, ContractExportVo> empContractDaysMap) {
        List<ContractVo> newContractList = ObjectConverter.convertList(pageResult, ContractVo.class);
        List<String> empList = newContractList.stream().map(cVo -> cVo.getOwner().getEmpId()).distinct().collect(Collectors.toList());
        Map<String, EmpWorkInfoDo> workMap = contractService.getEmpWorkInfoMap(empList);
        long nowDateTime = DateUtil.getCurrentTimestamp();
        newContractList.forEach(contractVo -> {
            String empId = contractVo.getOwner().getEmpId();
            if (contractVo.getEndDate() != null) {
                Long num = contractVo.getEndDate() - nowDateTime;
                contractVo.setContractDays(String.valueOf(num / 86400000));
            }
            if (empContractDaysMap != null) {
                ContractExportVo exportVo = new ContractExportVo();
                exportVo.setContractDays(contractVo.getContractDays());
                empContractDaysMap.put(empId, exportVo);
            }
            EmpWorkInfoDo emp = workMap.get(empId);
            if (null != emp) {
                contractVo.setCompany(emp.getCompany());
                contractVo.setCompanyTxt(emp.getCompanyTxt());
                contractVo.setHireDate(emp.getHireDate());
                contractVo.setEmpStatus(emp.getEmpStatus());
                contractVo.setOrganize(emp.getOrganize());
                contractVo.setOrganizeTxt(emp.getOrganizeTxt());
                contractVo.setJob(emp.getJob());
                contractVo.setJobTxt(emp.getJobTxt());
                contractVo.setEmpType(emp.getEmpType());
                contractVo.setPost(emp.getPost());
                contractVo.setPostTxt(emp.getPostTxt());
                contractVo.setWorkHour(emp.getWorkHour());
                contractVo.setWorkplace(emp.getWorkplace());
                contractVo.setWorkplaceTxt(emp.getWorkplaceTxt());
            }
        });
        return newContractList;
    }

    @ApiOperation("获取当前合同信息")
    @GetMapping("/getEmpCurrentContract")
    public Result getEmpCurrentContract(@RequestParam("empId") String empId) {
        return Result.ok(ObjectConverter.convert(contractService.getCurrentContract(empId), ContractVo.class));
    }

    @ApiOperation("获取上份合同信息")
    @GetMapping("/getEmpLastContract")
    public Result getEmpLastContract(@RequestParam("empId") String empId) {
        return Result.ok(ObjectConverter.convert(contractService.getEmpLastContract(empId), ContractVo.class));
    }

    @ApiOperation("获取历史合同信息")
    @GetMapping("/getEmpHistoryContract")
    public Result<List<ContractVo>> getEmpHistoryContract(@RequestParam("empId") String empId) {
        List<ContractDo> reList = contractService.getEmpHistoryContract(empId);
        if (null == reList) {
            reList = new ArrayList<>();
        }
        List<ContractVo> collect = reList.stream().map(cd -> {
            ContractVo convert = ObjectConverter.convert(cd, ContractVo.class);
            convert.convertContractStatus();
            return convert;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @ApiOperation("合同记录列表")
    @PostMapping("/getContractRecords")
    public Result<PageResult<ContractRecordsVo>> getContractRecords(@RequestBody ContractQueryDto dto) {
        dto.setStatus("1");
        return Result.ok(contractService.selectRecordPage(dto));
    }

    @ApiOperation("导出合同记录列表")
    @PostMapping("/exportContractRecords")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-合同记录", category = "导出", success = "导出了合同记录")
    public void exportContractRecords(@RequestBody ContractQueryDto dto, HttpServletResponse response) {
        dto.setStatus("1");
        PageResult<ContractExportVo> page = contractService.selectExportRecordPage(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : contractService.installContractRecordExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        try {
            ExcelUtils.downloadDataMapExcel(colList, CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installDataList(page.getItems(), colList), "导出合同记录表", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }

    @ApiOperation("审批记录列表")
    @PostMapping("/getApprovalRecords")
    public Result<PageResult<ContractRecordsVo>> getApprovalRecords(@RequestBody ContractQueryDto dto) {
        return Result.ok(contractService.selectApprovalRecordPage(dto));
    }

    @ApiOperation("审批记录导出")
    @PostMapping("/exportApproval")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-审批记录", category = "导出", success = "导出了合同信息")
    public void exportApproval(@RequestBody ContractQueryDto dto, HttpServletResponse response) {
        PageResult<ContractExportVo> page = contractService.selectExportApprovalRecordPage(dto);

        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : contractService.installApprovalTagProperty()) {
            colList.add(new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15));
        }
        List<Map<String, Object>> dataList = CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installDataList(page.getItems(), colList);

        try {
            ExcelUtils.downloadDataMapExcel(colList, dataList, "审批记录", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }

    @ApiOperation("已签合同列表导出")
    @PostMapping("/exportContractList")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "导出", success = "导出了合同信息")
    public void exportContractList(@RequestBody ContractQueryDto queryDto, HttpServletResponse response) {
        queryDto.setPageNo(1);
        queryDto.setPageSize(5000);
        var contractList = contractService.selectExportDataForSign(queryDto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty property : contractService.initContractExportTagProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(property.getPropertyTxt(), property.getProperty(), 15);
            exprortEntity.setOrderNum(property.getOrder());
            colList.add(exprortEntity);
        }
        try {
            ExcelUtils.downloadDataMapExcel(colList, CollectionUtils.isEmpty(contractList) ? Lists.newArrayList() : installDataList(contractList, colList), "已签合同", response);
        } catch (Exception e) {
            log.error("download approval List excel err.{}", e.getMessage(), e);
        }
    }

    private List<Map<String, Object>> installDataList(List<ContractExportVo> data, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (ContractExportVo vo : data) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }

    private List<Map<String, Object>> installNewlySignedDataList(List<EmpWorkInfoNewlySignedVo> data, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (EmpWorkInfoNewlySignedVo vo : data) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }

    private List<Map<String, Object>> installEmpList(List<EmpWorkInfoVo> data, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (EmpWorkInfoVo vo : data) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }

    @ApiOperation(value = "新增和续签合同员工选择器")
    @PostMapping("/searchUserList")
    public Result<SearchUserVo> searchUserList(@RequestBody ContractEmpQueryDto dto) {
        SearchUserVo searchUserVo = new SearchUserVo();

        // 员工选择器中关键字字段名为keyword
        ContractQueryDto queryDto = ObjectConverter.convert(dto, ContractQueryDto.class);
        queryDto.setKeyword(dto.getKeyword());

        searchUserVo.setUserList(ObjectConverter.convertList(contractService.searchUserList(queryDto), ContractEmpTreeNodeVo.class));
        searchUserVo.setUserCount(null != searchUserVo.getUserList() ? searchUserVo.getUserList().size() : 0);
        return Result.ok(searchUserVo);
    }

    @ApiOperation(value = "获取合同选人员工树列表")
    @PostMapping("/fetchDeptAndEmp")
    public Result<OrgEmpTreeVo> fetchOrgEmpTree(@RequestBody OrgEmpTreeQueryDto queryDto) {
        OrgEmpTreeVo treeVo = ObjectConverter.convert(contractService.fetchOrgEmpTree(queryDto), OrgEmpTreeVo.class);
        return Result.ok(treeVo);
    }

    @ApiOperation(value = "批量续签")
    @PostMapping("/batchRenewal")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "批量续签", success = "批量续签了合同")
    public Result batchRenewal(@RequestBody BatchRenewalDto renewalDto) {
        contractService.batchRenewal(renewalDto);
        return Result.ok(true);
    }

    @ApiOperation(value = "批量自动续签校验")
    @PostMapping("/batchAutoRenewalCheck")
    public Result<BatchAutoRenewalVo> batchAutoRenewalCheck(@RequestBody List<String> empIds) {
        List<BatchAutoRenewalDto> relList = contractService.batchAutoRenewalCheck(empIds);
        return Result.ok(new BatchAutoRenewalVo(empIds.size() - relList.size(),
                relList.stream().map(BatchAutoRenewalDto::getEmpId).collect(Collectors.toList()),
                relList.size()));
    }

    @ApiOperation(value = "批量自动续签")
    @PostMapping("/batchAutoRenewal")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "批量自动续签", success = "批量自动续签合同")
    public Result batchAutoRenewal(@RequestBody List<String> empIds) {
        contractService.batchAutoRenewal(empIds);
        return Result.ok(true);
    }

    @ApiOperation("新签合同列表")
    @PostMapping("/newlySignedList")
    public Result<PageResult<EmpWorkInfoNewlySignedVo>> newlySignedList(@RequestBody ContractQueryDto queryDto) {
        return ResponseWrap.wrapResult(contractService.newlySignedList(queryDto));
    }

    @ApiOperation("导出新签合同列表")
    @PostMapping("/exportNewlySignedList")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-新签合同", category = "导出", success = "导出了新签合同")
    public void exportNewlySignedList(@RequestBody ContractQueryDto dto, HttpServletResponse response) {
        dto.setPageNo(1);
        dto.setPageSize(5000);
        PageResult<EmpWorkInfoNewlySignedVo> page = contractService.newlySignedList(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : contractService.installNewlySignedExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        List<Map<String, Object>> dataList = CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installNewlySignedDataList(page.getItems(), colList);
        try {
            ExcelUtils.downloadDataMapExcel(colList, dataList, "新签合同", response);
        } catch (Exception e) {
            log.error("download NewlySignedList excel error.{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "新签署合同选人员工树列表")
    @PostMapping("/fetchNewlySignedEmp")
    public Result<OrgEmpTreeVo> fetchNewlySignedEmp(@RequestBody OrgEmpTreeQueryDto queryDto) {
        OrgEmpTreeVo treeVo = ObjectConverter.convert(contractService.fetchNewlySignedEmp(queryDto), OrgEmpTreeVo.class);
        return Result.ok(treeVo);
    }

    @ApiOperation(value = "批量自动新签校验")
    @PostMapping("/batchAutoNewlySignedCheck")
    public Result<BatchAutoRenewalVo> batchAutoNewlySignedCheck(@RequestBody List<String> empIds) {
        List<BatchAutoRenewalDto> relList = contractService.batchAutoNewlySignedCheck(empIds);
        return Result.ok(new BatchAutoRenewalVo(empIds.size() - relList.size(),
                relList.stream().map(BatchAutoRenewalDto::getEmpId).collect(Collectors.toList()),
                relList.size()));
    }

    @ApiOperation(value = "批量自动新签")
    @PostMapping("/batchAutoNewlySigned")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-新签合同", category = "批量自动新签", success = "批量自动新签了合同")
    public Result batchAutoNewlySigned(@RequestBody List<String> empIds) {
        contractService.batchAutoNewlySigned(empIds);
        return Result.ok(true);
    }

    @ApiOperation(value = "新签合同员工选择器")
    @PostMapping("/searchUserNewlySignedList")
    public Result<SearchUserVo> searchUserNewlySignedList(@RequestBody ContractEmpQueryDto dto) {
        SearchUserVo searchUserVo = new SearchUserVo();
        // 员工选择器中关键字字段名为keyword
        ContractQueryDto queryDto = ObjectConverter.convert(dto, ContractQueryDto.class);
        queryDto.setKeyword(dto.getKeyword());
        searchUserVo.setUserList(ObjectConverter.convertList(contractService.searchNewlySignedList(queryDto), ContractEmpTreeNodeVo.class));
        searchUserVo.setUserCount(null != searchUserVo.getUserList() ? searchUserVo.getUserList().size() : 0);
        return Result.ok(searchUserVo);
    }

    @ApiOperation("新签合同")
    @PostMapping("/newlySignedSave")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-新签合同", category = "新增", success = "新增了{{#dto.owner.name}}的合同")
    public Result newlySignedSave(@RequestBody ContractDto dto) {
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("按照自动规则生成合同编码")
    @GetMapping("/getNextContractNo")
    public Result<String> getNextContractNo(@RequestParam(required = false, value = "contractNo") String contractNo) {
        return Result.ok(contractService.getNextContractNo(contractNo));
    }

    @ApiOperation("终止合同")
    @PostMapping("/termination")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "终止", success = "终止了{{#dto.owner.name}}的合同")
    public Result termination(@RequestBody ContractDto dto) {
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("解除合同")
    @PostMapping("/dissolve")
    @LogRecordAnnotation(menu = "人事-合同管理-合同签订-已签合同", category = "解除", success = "解除了{{#dto.owner.name}}的合同")
    public Result dissolve(@RequestBody ContractDto dto) {
        contractService.saveContract(dto);
        return Result.ok(true);
    }

    @ApiOperation("合同续签流程回调")
    @PostMapping("continue/callback")
    public Result continueCallback(@RequestBody WfCallbackResultDto callback) {
        contractService.updateApprovalStatusOfContineContract(callback);
        return Result.ok();
    }

}