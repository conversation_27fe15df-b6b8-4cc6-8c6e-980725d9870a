package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.params.ExcelForEachParams;
import cn.afterturn.easypoi.excel.export.styler.IExcelExportStyler;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.emp.fieldset.handler.WraptextHandler;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.enums.system.RegularStatusEnum;
import com.caidaocloud.hr.service.handler.ExcelStyleHandler;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.search.application.dto.EmpSearchInfoDto;
import com.caidaocloud.hr.service.search.application.dto.EntityDataChangeDto;
import com.caidaocloud.hr.service.search.application.service.EmpWorkInfoSearchService;
import com.caidaocloud.hr.service.search.infrastructure.es.EsExportScrollQueryWrapper;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.util.EntityDataUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.ConvertValueUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.hrpaas.paas.common.service.DynamicService;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.employee.application.common.constant.Constant.DEFAULT_ES_EXPORT_SIZE;
import static com.caidaocloud.hr.service.employee.application.common.constant.Constant.EMP_CARD_DYNAMIC_CODEE_KEY;
import static com.caidaocloud.hr.service.employee.application.common.constant.Constant.EMP_DYNAMIC_CODEE_KEY;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/6/18
 */
@Service
@Slf4j
public class EmpDynamicService {
	@Resource
	private MetadataService metadataService;
	@Resource
	private EmpPrivateInfoDomainService empPrivateInfoDomainService;
	@Resource
	private OrgService orgService;
	@Resource
	private EmpSearchInfoRepository empSearchInfoRepository;
	@Resource
	private DynamicService dynamicService;
	@Resource
	private EmpWorkInfoSearchService empWorkInfoSearchService;
	@Resource
	private DynamicFeignClient dynamicFeignClient;
	@Resource
	private ConvertValueUtil convertValueUtil;
	@Resource
	private EmpWorkInfoService empWorkInfoService;
	@Resource
	private OrgDomainService orgDomainService;
	@Value("${postTxt.showCode:enabled}")
	private String postTxtShowCode;

	public List<MetadataVo> loadEmpColumns(){
		List<MetadataVo> defaultList = Lists.list(metadataService.getMetadata("entity.hr.EmpWorkInfo"),
				metadataService.getMetadata("entity.hr.EmpPrivateInfo"),
				metadataService.getMetadata("entity.hr.LastContract"));
		defaultList.add(businessColumns());
		return defaultList;
	}

	private MetadataVo businessColumns() {
		MetadataVo metadataVo = new MetadataVo();
		metadataVo.setIdentifier("Emp");
		metadataVo.setName("业务字段");

		MetadataPropertyVo propertyVo = new MetadataPropertyVo();
		propertyVo.setProperty("organizePathTxt");
		propertyVo.setName("组织全路径");
		propertyVo.setDataType(PropertyDataType.String);
		metadataVo.setStandardProperties(Lists.list(propertyVo));
		return metadataVo;
	}

	public void buildEmpEsData(EntityDataChangeDto<EmpSearchInfoDto> entityDataChangeDto, List concurrentPostList, Map<String, RegularStatusEnum> regularMap,
		Map<String, EmpStatusEnum> statusMap, EmpSearchInfoDto empData, List empTags) {
		installPrivateInfo(empData, regularMap, statusMap);
		installOrganizePath(empData);
		queryConcurrentPostAndTagsByEs(concurrentPostList, empData.getEmpId(), entityDataChangeDto, empTags);
		empData.setConcurrentPost(concurrentPostList);
		empData.setEmpTags(empTags);
		installDynamicColumn(empData);
	}

	private void installDynamicColumn(EmpSearchInfoDto empData) {
		Map<String, List<MetadataPropertyDto>> tableConfig = dynamicService.dynamicTableConfigLoad(EMP_DYNAMIC_CODEE_KEY);
		Map<String, List<MetadataPropertyDto>> cardConfig = dynamicService.dynamicTableConfigLoad(EMP_CARD_DYNAMIC_CODEE_KEY);
		for (Map.Entry<String, List<MetadataPropertyDto>> entry : cardConfig.entrySet()) {
			tableConfig.merge(entry.getKey(), entry.getValue(), (oldValue, newValue) -> {oldValue.addAll(newValue);return oldValue;});
		}
		tableConfig.remove("Emp");
		Map<String, Object> dynamicData = dynamicService.loadDynamicData(tableConfig, empData.getEmpId(), empData.getDataStartTime(), Maps.map("entity.hr.LastContract", "owner$empId"));
		empData.setDynamicColumn(dynamicData);
	}

	private void installPrivateInfo(EmpSearchInfoDto dto, Map<String, RegularStatusEnum> regularStatusMap, Map<String, EmpStatusEnum> statusMap){
		try {
			dto.setEmpStatusText(null==statusMap.get(dto.getEmpStatusValue())?null:statusMap.get(dto.getEmpStatusValue()).getName());
			dto.setConfirmationStatusText(null==regularStatusMap.get(String.valueOf(dto.getConfirmationStatus()))?null:regularStatusMap.get(String.valueOf(dto.getConfirmationStatus())).getName());
			EmpPrivateInfoDo empPrivateInfo = empPrivateInfoDomainService.getByEmpId(dto.getEmpId());
			if (log.isDebugEnabled()) {
				log.debug("load empPrivateInfo data={}", FastjsonUtil.toJson(empPrivateInfo));
			}
			if(null!=empPrivateInfo && null!=empPrivateInfo.getSex()){
				dto.setSex(empPrivateInfo.getSex().getValue());
				dto.setSexText(empPrivateInfo.getSex().getText());
			}
		} catch (Exception e){
			log.error("Failed to query employee private information,{}", e.getMessage(), e);
		}
	}

	private void installOrganizePath(EmpSearchInfoDto empData) {
		String organizePath = getOrganizePath(empData.getOrganize(), System.currentTimeMillis());
		organizePath = StringUtil.isEmpty(organizePath) ? empData.getOrganize() : String.format("%s/%s", organizePath, empData.getOrganize());
		empData.setOrganizePath(organizePath);
	}

	public String getOrganizePath(String bid, Long dataTime) {
		String organizePath = "";
		if (StringUtil.isEmpty(bid)) {
			return organizePath;
		}

		OrgDo orgDo = orgService.getOrgById(bid, dataTime);
		if (null == orgDo) {
			return organizePath;
		}

		TreeParent pid = orgDo.getPid();
		if (null != pid && StringUtil.isNotEmpty(pid.getPath())) {
			organizePath = pid.getPath();
		}
		return organizePath;
	}

	private void queryConcurrentPostAndTagsByEs(List concurrentPostList, String empId, EntityDataChangeDto changeDto, List empTags) {
		if(!concurrentPostList.isEmpty() || !StringUtil.isEmpty(changeDto.getDataId())){
			return;
		}
		changeDto.setDataId(empId);
		QueryBuilder queryBuilder = QueryBuilders.termQuery("empId.keyword", empId);
		try {
			// 查询 ES 数据
			List<EmpSearchInfoPo> empSearchInfoPos = empSearchInfoRepository.searchMore(queryBuilder, 1);
			if(null != empSearchInfoPos && !empSearchInfoPos.isEmpty()){
				if(null!=empSearchInfoPos.get(0).getConcurrentPost()) {
					// 兼岗数据
					concurrentPostList.addAll(empSearchInfoPos.get(0).getConcurrentPost());
				}
				if(null != empSearchInfoPos.get(0).getEmpTags()) {
					// 标签数据
					empTags.addAll(empSearchInfoPos.get(0).getEmpTags());
				}
			}
		}
		catch (Exception e){
			if (e instanceof ElasticsearchStatusException && ((ElasticsearchStatusException) e).status()== RestStatus.NOT_FOUND){
				log.warn("Es index not inited,msg={}",e.getMessage());
				return;
			}
			log.error("queryConcurrentPostAndTagsByEs err,{}", e.getMessage(), e);
			throw new ServerException("query Concurrent Post And empTags By Es err");
		}
	}

	public PageResult<Map> dynamicTable(EmpPageQueryDto dto) {
		List<MetadataPropertyDto> propertyDtoList = dynamicFeignClient.dynamicTableLoad(EMP_DYNAMIC_CODEE_KEY)
				.getData();
		return dynamicTable(dto, propertyDtoList);
	}
	public PageResult<Map> dynamicTable(EmpPageQueryDto dto,List<MetadataPropertyDto> propertyDtoList) {
		PageResult<EmpSearchInfoPo> pageResult = empWorkInfoSearchService.selectPage(dto);
		List<Map<String, Object>> list = formatTableData(dto, propertyDtoList, pageResult.getItems());
		return new PageResult(list, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
	}

	private List<Map<String, Object>> formatTableData(EmpPageQueryDto dto, List<MetadataPropertyDto> propertyDtoList, List<EmpSearchInfoPo> esList) {
		if (esList.isEmpty()) {
			return new ArrayList<>();
		}
		List<String> orgIdList = esList.stream().filter(po -> null != po && StringUtil.isNotEmpty(po.getOrganize())).map(EmpSearchInfoPo::getOrganize).collect(Collectors.toList());
		Map<String, String> fullPathMap = empWorkInfoService.getOrgFullPath(orgIdList, dto.getDateTime());
		String organizeFullPathFormat = "%s/%s/%s",
				organizePathFormat = "%s/%s",
				organizeFullPathTxt = "", tenantName = orgDomainService.getTenantName();
		List<Map<String, Object>> list = Sequences.sequence(esList).map(data -> {
			Map<String, Object> map = new HashMap<>();
			map.put("empId", data.getEmpId());
			map.put("name", data.getName());
			map.put("workno", data.getWorkno());
			if (data.getDynamicColumn() != null) {
				map.putAll(convertPropertyValue(data.getDynamicColumn(), propertyDtoList));
			}
			if(!"enabled".equals(postTxtShowCode)){
				String postTxt = (String) map.get("<EMAIL>");
				if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
					postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
					map.put("<EMAIL>", postTxt);
				}
			}
			map.put("organizePathTxt@Emp", empWorkInfoService.formateOrgPath(fullPathMap, organizeFullPathFormat, organizePathFormat, tenantName, data));
			return map;
		}).toList();
		return list;
	}


	public PageResult<Map> dynamicCard(EmpPageQueryDto dto) {
		List<MetadataPropertyDto> propertyDtoList = dynamicFeignClient.dynamicTableLoad(EMP_CARD_DYNAMIC_CODEE_KEY)
				.getData();
		return dynamicCard(dto, propertyDtoList);
	}

	public PageResult<Map> dynamicCard(EmpPageQueryDto dto,List<MetadataPropertyDto> propertyDtoList) {
		PageResult<EmpSearchInfoPo> pageResult = empWorkInfoSearchService.selectPage(dto);
		List<Map<String, Object>> list = Sequences.sequence(pageResult.getItems()).map(data -> {
			Map<String, Object> map = new HashMap<>();
			map.put("empId", data.getEmpId());
			map.put("name", data.getName());
			map.put("workno", data.getWorkno());
			map.put("photo", data.getPhoto());
			map.put("empStatus", data.getEmpStatusValue());
			map.put("resignationStatus", data.getResignationStatus());
			if (data.getDynamicColumn() != null) {
				map.putAll(convertPropertyValue(data.getDynamicColumn(), propertyDtoList));
			}
			String postTxt = (String) map.get("<EMAIL>");
			if(!"enabled".equals(postTxtShowCode)){
				if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
					postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
					map.put("<EMAIL>", postTxt);
				}
			}
			return map;
		}).toList();
		return new PageResult(list, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
	}

	private Map<String,Object> convertPropertyValue(Map<String, Object> dynamicColumn, List<MetadataPropertyDto> propertyDtoList) {
		Map<String, Object> map = new HashMap<>();
		for (MetadataPropertyDto propertyDto : propertyDtoList) {
			MetadataPropertyVo propertyVo = ObjectConverter.convert(propertyDto, MetadataPropertyVo.class);
			PropertyValue value = convertValueUtil.convertData2PropertyValue(dynamicColumn.get(propertyVo.getProperty()), propertyVo);
			if (value == null || StringUtil.isEmpty(value.toText())) {
				map.put(propertyVo.getProperty(), null);
			}
			else {
				if (propertyVo.getDataType().name().equals("Emp")){
					map.put(propertyVo.getProperty(),  value);
					log.info("propertyVo.getProperty() :emp :"+value);
				}else {
					map.put(propertyVo.getProperty(), value instanceof Attachment ? value : value.toText());
				}

			}
		}
		return map;
	}
//	//基于 CAIDAOM-3081 单独处理emp值
//	public PropertyValue convertData2PropertyValue(Object value, MetadataPropertyVo metadataProperty) {
//		PropertyDataType dataType = metadataProperty.getDataType();
//		switch(dataType) {
//			case Dict:
//				if (value instanceof Map) {
//					return (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), DictSimple.class);
//				}
//
//				return null == value ? new DictSimple() : DictSimple.doDictSimple(Objects.toString(value, (String)null));
//			case Enum:
//				if (value instanceof Map) {
//					return (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), EnumSimple.class);
//				} else {
//					EnumSimple enumSimple = new EnumSimple();
//					if (null == value) {
//						return enumSimple;
//					}
//
//					enumSimple.setValue(Objects.toString(value));
//					if (org.apache.commons.collections.CollectionUtils.isNotEmpty(metadataProperty.getEnumDef())) {
//						PropertyEnumDefDto defDto = (PropertyEnumDefDto)metadataProperty.getEnumDef().stream().filter((it) -> {
//							return it.getValue().equals(enumSimple.getValue());
//						}).findFirst().orElse((Object)null);
//						enumSimple.setText(defDto != null ? defDto.getDisplay() : enumSimple.getValue());
//					}
//
//					return enumSimple;
//				}
//			case PROVINCE_CITY:
//			case Timestamp:
//			case Boolean:
//			default:
//				if (dataType.isComponent()) {
//					throw new ServerException("Data type not support");
//				} else if (value instanceof Map) {
//					return (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), SimplePropertyValue.class);
//				} else {
//					if (dataType.isArray()) {
//						SimplePropertyValue propertyValue = new SimplePropertyValue();
//						propertyValue.setArrayValues((List)FastjsonUtil.convertObject(value, List.class));
//						return propertyValue;
//					}
//
//					return new SimplePropertyValue(Objects.toString(null == value ? "" : value));
//				}
//			case Address:
//				return (PropertyValue)(null == value ? new Address() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), Address.class));
//			case Attachment:
//				return (PropertyValue)(null == value ? new Attachment() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), Attachment.class));
//			case Emp:
//				return (PropertyValue)(null == value ? new EmpSimple() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), EmpSimple.class));
//			case Job_Grade_Range:
//				return (PropertyValue)(null == value ? new JobGradeRange() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), JobGradeRange.class));
//			case Phone:
//				return (PropertyValue)(null == value ? new PhoneSimple() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), PhoneSimple.class));
//			case PID:
//				return (PropertyValue)(null == value ? new TreeParent() : (PropertyValue)FastjsonUtil.toObject(FastjsonUtil.toJson(value), TreeParent.class));
//		}
//	}

	public EmpSearchInfoDto buildEmpEsData(EmpWorkInfoDo data, MetadataVo metadata, Map<String, EmpStatusEnum> statusMap, Map<String, RegularStatusEnum> regularMap) {
	val entityData = data.toPersistData(metadata);
	EmpSearchInfoDto esData =  EntityDataUtil.convertEntityData(entityData, EmpSearchInfoDto.class);
	buildEmpEsData(new EntityDataChangeDto<>(), new ArrayList(), regularMap, statusMap, esData, new ArrayList());
	return esData;
	}

	public  void dynamicTableExport(EmpPageQueryDto dto, HttpServletResponse response) {
		List<MetadataPropertyDto> source = dynamicFeignClient.dynamicTableLoad(EMP_DYNAMIC_CODEE_KEY).getData();
		UserDynamicConfig userDynamicConfig = dynamicFeignClient.userDynamicTableLoad(EMP_DYNAMIC_CODEE_KEY).getData();
		List<String> checkedList = userDynamicConfig.getCheckedList();
		List<String> sortedList = userDynamicConfig.getSortedList();
		if (!checkedList.isEmpty() && !sortedList.isEmpty()) {
			source = Sequences.sequence(source)
					.filter(propertyDto -> checkedList.contains(propertyDto.getProperty()))
					.sortBy(Comparator.comparingInt(p -> sortedList.indexOf(p.getProperty()))).toList();
		}

		List<MetadataPropertyDto> propertyDtoList = source;
		List<ExcelExportEntity> columnList = buildDynamicColumn(propertyDtoList);
		ExportParams exportParams = new ExportParams();
		exportParams.setStyle(WraptextHandler.class);
		exportParams.setDataHandler(new ExcelDataHandlerDefaultImpl() {
			@Override
			public String[] getNeedHandlerFields() {
				return Sequences.sequence(propertyDtoList).filter(p -> p.getDataType() == PropertyDataType.Timestamp)
						.map(MetadataPropertyDto::getName).toArray(String.class);
			}

			@Override
			public Object exportHandler(Object obj, String name, Object value) {
				if (value == null) {
					return null;
				}
				return DateUtil.formatDate(Long.valueOf(value.toString()));
			}
		});

		EsExportScrollQueryWrapper<EmpSearchInfoPo> queryWrapper = new EsExportScrollQueryWrapper<>(empWorkInfoSearchService.pageQueryBuilder(dto), EmpSearchInfoPo.class, DEFAULT_ES_EXPORT_SIZE);
		Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, columnList, new IExcelExportServer() {
			@Override
			public List<Object> selectListForExcelExport(Object queryParams, int page) {
				List<EmpSearchInfoPo> esList = queryWrapper.query();
				return Sequences.sequence(formatTableData(dto, propertyDtoList, esList))
						.map(data -> {
							for (Object key : data.keySet()) {
								Object value = data.get(key);
								if (value instanceof PropertyValue) {
									data.put((String) key, ((PropertyValue) value).toText());
								}
								if ("<EMAIL>".equals(key) && value !=null) {
									List<EmpCostCenterDto> cc = FastjsonUtil.toList(FastjsonUtil.toJson(value), EmpCostCenterDto.class);
									data.put((String) key, formatCostCenterTxt(cc));
								}
							}
							return (Object) data;
						}).toList();
			}
		}, dto);
		try {
			ExcelUtils.downLoadSXSSFExcel("员工信息", response, workbook);
		}
		catch (IOException e) {
			log.error("导出员工信息失败", e);
			throw new ServerException("导出员工信息失败", e);
		}
	}


	public static String formatCostCenterTxt(List<EmpCostCenterDto> costCenters) {
		if (costCenters == null || costCenters.isEmpty()) {
			return "";
		}

		if (costCenters.size() == 1) {
			EmpCostCenterDto costCenter = costCenters.get(0);
			return costCenter.getCostTxt() != null ? costCenter.getCostTxt() : "";
		}

		StringBuilder sb = new StringBuilder();
		for (EmpCostCenterDto costCenter : costCenters) {
			String costTxt = costCenter.getCostTxt() != null ? costCenter.getCostTxt() : "";
			String proportion = costCenter.getProportionScale() != null
					? String.format("%.0f%%", costCenter.getProportionScale())
					: "0%";
			sb.append(costTxt).append(" - ").append(proportion).append("\n");
		}
		return sb.toString().trim();
	}

	private List<ExcelExportEntity> buildDynamicColumn(List<MetadataPropertyDto> propertyDtoList) {
		List<ExcelExportEntity> list = Lists.list(
				new ExcelExportEntity("姓名", "name"),
				new ExcelExportEntity("工号", "workno")
		);
		list.addAll(Sequences.sequence(propertyDtoList)
				.map(p -> {
					ExcelExportEntity entity = new ExcelExportEntity(p.getName(), p.getProperty());
					entity.setWrap("<EMAIL>".equals(entity.getKey()));
					return entity;
				})
				.toList());
		return list;
	}
}
