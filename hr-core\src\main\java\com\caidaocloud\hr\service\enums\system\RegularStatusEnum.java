package com.caidaocloud.hr.service.enums.system;

public enum RegularStatusEnum {
    REGULAR_STATUS(0, "已转正"),
    TEMPORARY_STATUS(1, "试用期"),
    NOT_CONFIRMATION_STATUS(2, "不转正"),
    ON_CONFIRMATION_STATUS(3, "转正中")
    ;

    private Integer index;
    private String name;

    RegularStatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (RegularStatusEnum c : RegularStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
