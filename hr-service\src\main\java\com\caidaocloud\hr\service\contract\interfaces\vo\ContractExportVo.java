package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ContractExportVo {
    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("入职日期")
    private String hireDate;

    @ApiModelProperty("员工状态")
    private String empStatus;

    @ApiModelProperty("任职组织")
    private String organizeTxt;

    @ApiModelProperty("任职组织编码")
    private String organizeCode;

    @ApiModelProperty("职务")
    private String jobTxt;

    @ApiModelProperty("岗位")
    private String postTxt;

    @ApiModelProperty("用工类型")
    private String empType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("公司ID")
    private String company;

    @ApiModelProperty("合同公司")
    private String companyTxt;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("签订类型")
    private String signType;

    @ApiModelProperty("签订次数")
    private Integer signTime;

    @ApiModelProperty("合同开始日期")
    private String startDate;

    @ApiModelProperty("合同结束日期")
    private String endDate;

    @ApiModelProperty("合同终止日期")
    private String terminationDate;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("合同状态")
    private String contractStatus;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("流程状态")
    private String signProcessStatus;

    @ApiModelProperty("合同附件")
    private Attachment attachFile;

    @ApiModelProperty("合同类型设置名称")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;

    @ApiModelProperty("合同到期天数")
    private String contractDays;

    @ApiModelProperty("合同发起日期")
    private String launchDate;
}
