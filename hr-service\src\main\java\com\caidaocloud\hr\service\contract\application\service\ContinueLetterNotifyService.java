package com.caidaocloud.hr.service.contract.application.service;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;

import org.springframework.stereotype.Service;

/**
 * 续签意向消息补发
 * <AUTHOR>
 * @date 2024/5/23
 */
@Service
public class ContinueLetterNotifyService {
	@Resource
	private ContinueLetterService continueLetterService;
	@Resource
	private ContractDomainService contractDomainService;

	public void notice(List<String> contractIds){
		List<ContractDo> contractList = contractDomainService.selectByIds(contractIds);
		for (ContractDo data : contractList) {
			continueLetterService.continueLetterNotify(data);
		}
	}
}
