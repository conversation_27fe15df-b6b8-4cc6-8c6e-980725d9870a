package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import java.util.List;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueLetterBatchRepository;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.ContinueLetterBatchPo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.googlecode.totallylazy.Sequences;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@Repository
public class ContinueLetterBatchRepositoryImpl implements IContinueLetterBatchRepository {
	@Override
	public ContinueLetterBatchDo insert(ContinueLetterBatchDo data) {
		ContinueLetterBatchPo po = ContinueLetterBatchPo.fromEntity(data);
		String dataId = DataInsert.identifier(po.getIdentifier()).insert(po);
		data.setBid(dataId);
		return data;
	}

	@Override
	public int updateById(ContinueLetterBatchDo data) {
		ContinueLetterBatchPo po = ContinueLetterBatchPo.fromEntity(data);
		DataUpdate.identifier(data.getIdentifier()).update(po);
		return 0;
	}

	@Override
	public PageResult<ContinueLetterBatchDo> selectPage(BasePage page, ContinueLetterBatchDo data) {
		PageResult<ContinueLetterBatchPo> result = DataQuery.identifier(data.getIdentifier()).decrypt()
				.specifyLanguage().queryInvisible()
				.limit(page.getPageSize(), page.getPageNo()).filter(DataFilter.eq("tenantId", data.getTenantId())
						.andNe("deleted", Boolean.TRUE.toString()), ContinueLetterBatchPo.class);
		List<ContinueLetterBatchDo> list = Sequences.sequence(result.getItems())
				.map(ContinueLetterBatchPo::toEntity).toList();

		return new PageResult<>(list, result.getPageNo(), result.getPageSize(), result.getTotal());
	}

	@Override
	public ContinueLetterBatchDo selectById(String id, String identifier) {
		ContinueLetterBatchPo po = DataQuery.identifier(identifier).decrypt().specifyLanguage()
				.queryInvisible()
				.oneOrNull(id, ContinueLetterBatchPo.class);
		return po == null ? null : po.toEntity();
	}

}
