package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduExperienceDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpEduExperienceDomainService;
import com.caidaocloud.hr.service.dto.EmpEduExperienceDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Service
public class EmpEduExperienceService {
    @Resource
    private EmpEduExperienceDomainService empEduExperienceDomainService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    public List<EmpEduExperienceDo> getEmpExpList(String empId) {
        return  empEduExperienceDomainService.getList(empId);
    }

    public EmpEduExperienceDo getEmpExp(String bid) {
        return empEduExperienceDomainService.getEduExperience(bid);
    }
    /**
     * 新增教育经历
     * @param dto
     */
    public void save(EmpEduExperienceDto dto) {
        EmpEduExperienceDo data = ObjectConverter.convert(dto, EmpEduExperienceDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empEduExperienceDomainService.save(data);
    }

    private void doConvert(EmpEduExperienceDto source, EmpEduExperienceDo target) {
        if (StringUtils.isNotEmpty(source.getDegree())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getDegree());
            target.setDegree(dictSimple);
        }

        if (StringUtils.isNotEmpty(source.getBackground())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getBackground());
            target.setBackground(dictSimple);
        }
    }

    public void update(EmpEduExperienceDto dto) {
        EmpEduExperienceDo data = ObjectConverter.convert(dto, EmpEduExperienceDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empEduExperienceDomainService.update(data);
    }



    public void delete(String bid) {
        empEduExperienceDomainService.delete(bid);
    }
}
