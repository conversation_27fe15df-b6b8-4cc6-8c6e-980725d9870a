package com.caidaocloud.hr.service.employee.application.common.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

public class BaseServiceImpl<T extends DataSimple, D extends BasePage> implements BaseService<T, D> {
    protected BaseDomainService getDomainService() {
        return null;
    }

    @Override
    public BaseDomainDo getDoService() {
        return null;
    }

    @Override
    public T getDetail(T data) {
        return (T) getDomainService().getDetail(data);
    }

    @Override
    public T getOne(T data) {
        return null;
    }

    @Override
    public List<T> getAll(T data) {
        return null;
    }

    @Override
    public T getById(String bid) {
        T data = (T) getDomainService().getById(bid);
        return data;
    }

    @Override
    public PageResult<T> getPage(D query) {
        return getDomainService().getPage(query);
    }

    @Override
    public PageResult<T> getPage(D query, T data) {
        return getDomainService().getPage(query, data);
    }

    @Override
    public void delete(String bid) {
        getDomainService().delete(bid);
    }

    @Override
    public void softDelete(String bid) {

    }

    @Override
    public T save(T data) {
        return (T) getDomainService().save(data);
    }

    @Override
    public void batchSave(List<T> data) {

    }

    @Override
    public T saveOrUpdateObj(Object data) {
        T t = dto2do(data);
        checkObj(t);
        convert(t, data);
        return saveOrUpdate(t);
    }

    @Override
    public List<T> selectByIds(List<String> ids) {
        return getDomainService().selectByIds(ids);
    }

    @Override
    public List<T> selectList() {
        return getDomainService().selectList();
    }

    protected void convert(T data, Object dto) {
    }

    protected void checkObj(T data) {

    }

    @Override
    public T saveOrUpdate(T data) {
        return StringUtil.isEmpty(data.getBid()) ? save(data) : updateById(data);
    }

    @Override
    public T updateById(T data) {
        return (T) getDomainService().updateById(data);
    }

    @Override
    public List<T> selectList(T data) {
        return getDomainService().selectList(data);
    }

    @Override
    public T dto2do(Object data) {
        return ObjectConverter.convert(data, getDataClazz());
    }

    protected Class<T> getDataClazz() {
        Type type = getClass().getGenericSuperclass();
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            return (Class<T>) parameterizedType.getActualTypeArguments()[0];
        }

        return null;
    }
}
