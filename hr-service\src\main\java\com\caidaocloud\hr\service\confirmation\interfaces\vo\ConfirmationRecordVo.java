package com.caidaocloud.hr.service.confirmation.interfaces.vo;

import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@ApiModel("转正记录Vo")
public class ConfirmationRecordVo {
    private String id;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("英文名")
    private String enName;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;
    @ApiModelProperty("转正日期")
    private Long confirmationDate;
    @ApiModelProperty("转正类型")
    private EnumSimple confirmationType;
    @ApiModelProperty("所属组织")
    private String organize;
    /**
     * 所属组织名称
     */
    @ApiModelProperty("所属组织名称")
    private String organizeTxt;
    @ApiModelProperty("岗位")
    private String post;
    @ApiModelProperty("岗位名称")
    private String postTxt;
    /**
     * 用工类型\员工类型
     */
    @ApiModelProperty("用工类型")
    private DictSimple empType;
    /**
     * 直接上级
     */
    @ApiModelProperty("直接上级")
    private EmpSimple leader;
    @ApiModelProperty("审批状态")
    private String approvalStatus;
    @ApiModelProperty("业务busKey")
    private String businessKey;
    @ApiModelProperty("重新发起单据id")
    private String reapplyId;
    private String defId;
    @ApiModelProperty("电子签状态，0 未发起 1 已发起")
    private String esign;

    public static ConfirmationRecordVo map2Vo(Map<String, Object> map, String postTxtShowCode){
        ConfirmationRecordVo recordVo = new ConfirmationRecordVo();
        recordVo.setId((String) map.get("id"));
        recordVo.setDefId((String) map.get("defId"));
        recordVo.setEmpId((String) map.get("main$empId"));
        recordVo.setWorkno((String) map.get("main$workno"));
        recordVo.setName((String) map.get("main$name"));
        recordVo.setEnName((String) map.get("main$enName"));
        recordVo.setHireDate(convertLong((String) map.get("main$hireDate")));
        // 试用期截止日期
        recordVo.setProbationPeriodEndDate(convertLong((String) map.get("main$probationPeriodEndDate")));
        // 转正日期
        recordVo.setConfirmationDate(convertLong((String) map.get("other$confirmationDate")));
        // 转正类型
        EnumSimple ct = new EnumSimple();
        ct.setValue((String) map.get("other$confirmationType"));
        ct.setText(StringUtil.isEmpty(ct.getValue()) ? null : ConfirmationType.getByName(ct.getValue()).text);
        recordVo.setConfirmationType(ct);
        recordVo.setOrganize((String) map.get("main$organize"));
        recordVo.setOrganizeTxt((String) map.get("main$organizeTxt"));
        recordVo.setPost((String) map.get("main$post"));
        String postTxt = (String) map.get("main$postTxt");
        if(!"enabled".equals(postTxtShowCode)){
            if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
            }
        }
        recordVo.setPostTxt(postTxt);
        String mainEmpType = (String) map.get("main$empType");
        // 用工类型
        recordVo.setEmpType(DictSimple.doDictSimple(mainEmpType));
        String leaderEmpId = (String) map.get("main$leadEmpId_empId"),
            leaderWorkno = (String) map.get("main$leadEmpId_workno"),
            leaderName = (String) map.get("main$leadEmpId_name"),
            leaderEnName = (String) map.get("main$leadEmpId_enName");
        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId(leaderEmpId);
        empSimple.setEnName(leaderEnName);
        empSimple.setWorkno(leaderWorkno);
        empSimple.setName(leaderName);
        recordVo.setLeader(empSimple);
        // 审批状态
        recordVo.setApprovalStatus((String) map.get("other$approvalStatus"));
        recordVo.setBusinessKey(recordVo.getId() + "_CONFIRMATION-" + recordVo.getDefId());
        recordVo.setReapplyId((String) map.get("reapplyId"));
        recordVo.setEsign((String) map.get("esign"));
        return recordVo;
    }

    private static Long convertLong(String dateStr){
        if(StringUtil.isBlank(dateStr)){
            return null;
        }

        return Long.valueOf(dateStr);
    }
}
