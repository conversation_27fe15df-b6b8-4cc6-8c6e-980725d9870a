package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpExtFieldDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.ExtFieldDto;
import com.caidaocloud.hr.service.util.ExtFieldUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EmpExtFieldService {
    @Resource
    private MetadataService metadataService;
    @Resource
    private EmpExtFieldDomainService empExtFieldDomainService;

    public EmpWorkInfoDto saveWorkInfo(ExtFieldDto fields) {
        EmpWorkInfoDto workInfo = new EmpWorkInfoDto();
        workInfo.setEmpId(fields.getEmpId());
        fields.setIdentifier("entity.hr.EmpWorkInfo");
        fields.setBid(null);
        ExtFieldDto extFieldDto = saveExtField(fields);
        workInfo.setExt(extFieldDto.getExt());
        return workInfo;
    }

    public ExtFieldDto saveExtField(ExtFieldDto fields) {
        Map<String, Object> ext = null;
        if ((StringUtil.isEmpty(fields.getEmpId()) && StringUtil.isEmpty(fields.getBid())) || null == (ext = fields.getExt()) || ext.isEmpty()) {
            return fields;
        }

        Map<String, Object> data = getCusExtProps(fields.getIdentifier(), ext);
        if (null == data) {
            return fields;
        }
        fields.setExt(data);

        if (StringUtil.isNotEmpty(fields.getEmpId())) {
            empExtFieldDomainService.updateExtFieldByEmpId(fields.getEmpId(), fields.getIdentifier(), fields.getDataTime(), data);
        } else {
            empExtFieldDomainService.updateExtFieldByBid(fields.getBid(), fields.getIdentifier(), fields.getDataTime(), data);
        }

        return fields;
    }

    public ExtFieldDto getExtField(ExtFieldDto fields) {
        boolean searchEmp = StringUtil.isEmpty(fields.getEmpId());
        if (searchEmp && StringUtil.isEmpty(fields.getBid())) {
            return fields;
        }

        DataSimple dataSimple = !searchEmp ?
                empExtFieldDomainService.getExtFieldByEmpId(fields.getEmpId(), fields.getIdentifier(), fields.getDataTime()) :
                empExtFieldDomainService.getExtFieldByBid(fields.getBid(), fields.getIdentifier(), fields.getDataTime());

        if (null == dataSimple) {
            return fields;
        }

        Map<String, Object> data = getEmpCustomPropertyValue(fields.getIdentifier(), dataSimple);
        fields.setExt(data);
        return fields;
    }

    /**
     * 查询员工自定义字段数据
     *
     * @param metadata
     * @param dataSimple
     * @return
     */
    public Map<String, Object> getEmpCustomPropertyValue(MetadataVo metadata, DataSimple dataSimple) {
        Map<String, Object> data = new HashMap<>();

        List<MetadataPropertyVo> cusPropList;
        if (null == metadata || null == (cusPropList = metadata.getCustomProperties()) || cusPropList.isEmpty()) {
            return data;
        }

        // 解析自定义字段
        Map<String, MetadataPropertyVo> fieldMap = cusPropList.stream()
                .collect(Collectors.toMap(MetadataPropertyVo::getProperty, prop -> prop, (k1, k2) -> k1));

        ExtFieldUtil.doExtField(cusPropList, fieldMap, dataSimple, data);
        return data;
    }



    /**
     * 查询员工自定义字段数据
     *
     * @param identifier
     * @param dataSimple
     * @return
     */
    public Map<String, Object> getEmpCustomPropertyValue(String identifier, DataSimple dataSimple) {
        Map<String, Object> data = new HashMap<>();

        // 查询模型定义
        MetadataVo metadata = metadataService.getMetadata(identifier);
        getEmpCustomPropertyValue(metadata, dataSimple);
        return data;
    }

    public Map<String, MetadataPropertyVo> getCusField(String identifier, List<MetadataPropertyVo> cusPropList) {
        // 查询模型定义
        MetadataVo metadata = metadataService.getMetadata(identifier);
        List<MetadataPropertyVo> list = null;
        if (null == metadata || null == (list = metadata.getCustomProperties()) || list.isEmpty()) {
            return Maps.newHashMap();
        }
        cusPropList.addAll(list);
        // 解析自定义字段
        Map<String, MetadataPropertyVo> fieldMap = cusPropList.stream()
                .collect(Collectors.toMap(MetadataPropertyVo::getProperty, prop -> prop, (k1, k2) -> k1));
        return fieldMap;
    }

    public Map<String, Object> getCusExtProps(String identifier, Map<String, Object> ext) {
        if (MapUtils.isEmpty(ext)) {
            return null;
        }

        MetadataVo metadata = metadataService.getMetadata(identifier);
        List<MetadataPropertyVo> cusPropList = null;
        if (null == metadata || null == (cusPropList = metadata.getCustomProperties()) || cusPropList.isEmpty()) {
            return null;
        }

        Map<String, MetadataPropertyVo> fieldMap = cusPropList.stream()
                .collect(Collectors.toMap(MetadataPropertyVo::getProperty, prop -> prop, (k1, k2) -> k1));

        Map<String, Object> data = new HashMap<>();
        ExtFieldUtil.doExtObj(ext, fieldMap, data);

        return data;
    }

    public void doCusExtProps(String identifier, Map<String, Object> ext, DataSimple dataSimple) {
        Map<String, Object> data = getCusExtProps(identifier, ext);
        if (MapUtils.isEmpty(data)) {
            return;
        }

        ExtFieldUtil.doProperties(data, dataSimple);
    }

    public Map<String, Object> getEmpCustomPropertyValueSimple(MetadataVo metadataVo, DataSimple dataSimple) {
        Map<String, Object> ext = getEmpCustomPropertyValue(metadataVo, dataSimple);
        Map<String, Object> simpleExt = new HashMap<>();
        for (Map.Entry<String, Object> entry : ext.entrySet()) {
            Object value = entry.getValue();
            // 如果字段的值实现了接口 A，执行特殊处理
            if (value instanceof PropertyValue) {
                simpleExt.put(entry.getKey(), ((PropertyValue) value).toText());
            } else {
                simpleExt.put(entry.getKey(), value);
            }
        }
        return simpleExt;
    }

}
