package com.caidaocloud.hr.service.contract.application.event.subscribe;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractTypeSetChangeMessageDto;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractTypeSetRefreshMessageDto;
import com.caidaocloud.hr.service.contract.application.service.EmpContractTypeSetRelService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ContractTypeSetSubscriber {
    @Resource
    private EmpContractTypeSetRelService empContractTypeSetRelService;

    @RabbitHandler
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.hr.contract.queue", durable = "true"),
            exchange = @Exchange(value = "hr.contract.fac.direct.exchange"),
            key = {"routingKey.hr.contract"}
        )
    )
    public void process(String message) {
        log.info("Subscribe ContractTypeSetChangeMessage={}", message);
        try {
            ContractTypeSetChangeMessageDto contractMessage = FastjsonUtil.toObject(message, ContractTypeSetChangeMessageDto.class);
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(contractMessage.getTenantId());
            userInfo.setEmpId(0L);
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(contractMessage.getTenantId());
            user.setStaffId(0L);
            user.setUserid(0);
            user.setEmpid(0);
            UserContext.setCurrentUser(user);
            empContractTypeSetRelService.initEmpContractRel(contractMessage.getBid(), null);
        } catch (Exception ex) {
            log.error("process ContractMessage err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    @RabbitHandler
    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = "caidaocloud.hr.contracttypeset.queue", durable = "true"),
            exchange = @Exchange(value = "hr.contract.fac.direct.exchange"),
            key = {"routingKey.hr.contracttypeset.refresh"}
        )
    )
    public void processContractTypeSetRefresh(String message) {
        log.info("Subscribe ContractTypeSetRefreshMessage={}", message);
        SecurityUserInfo userInfo = new SecurityUserInfo();
        String bid, tenantId;
        try {
            ContractTypeSetRefreshMessageDto refreshMessage = FastjsonUtil.toObject(message, ContractTypeSetRefreshMessageDto.class);
            tenantId = refreshMessage.getTenantId();
            userInfo.setTenantId(tenantId);
            userInfo.setEmpId(0L);
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(tenantId);
            user.setStaffId(0L);
            user.setUserid(0);
            user.setStaffId(0L);
            UserContext.setCurrentUser(user);
            bid = refreshMessage.getBid();
            if (bid != null) {
                empContractTypeSetRelService.initEmpContractRel(bid, refreshMessage.getProcess());
            } else {
                empContractTypeSetRelService.initEmpContractRel(refreshMessage.getProcess());
            }
        } catch (Exception ex) {
            log.error("process ContractTypeSetRefreshMessage err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }
}
