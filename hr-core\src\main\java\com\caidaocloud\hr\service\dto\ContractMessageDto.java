package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hr.service.enums.contract.ContractEventType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import lombok.Data;

@Data
public class ContractMessageDto {
    private Long userId;

    private String tenantId;
    /**
     * 合同ID
     */
    private String contract;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;

    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;

    /**
     * 合同类别
     */
    private DictSimple contractType;

    /**
     * 签订类型
     */
    private EnumSimple signType;

    /**
     * 审批状态
     */
    private EnumSimple approvalStatus;

    /**
     * 合同签署人
     */
    private EmpSimple emp;

    /**
     * 手机号码
     */
    private PhoneSimple phone;

    /**
     * 公司邮箱
     */
    private String companyEmail;

    /**
     * 员工类型
     */
    private DictSimple empType;

    /**
     * 任职组织
     */
    private String organize;

    /**
     * 任职组织名称
     */
    private String organizeTxt;

    /**
     * 职务
     */
    private String job;

    /**
     * 职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 合同公司
     */
    private String company;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 国籍
     */
    private DictSimple nationality;

    /**
     * 是否残疾
     */
    private Boolean disability;

    /**
     * 监护人姓名
     */
    private String guardianName;

    /**
     * 监护人手机
     */
    private PhoneSimple guardianPhone;

    /**
     * 监护人邮箱
     */
    private String guardianEmail;

    /**
     * 工作地ID
     */
    private String workplace;

    /**
     * 工作地名称
     */
    private String workplaceTxt;

    /**
     * 工时制
     */
    private EnumSimple workHour;

    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;

    private ContractEventType eventType;
}
