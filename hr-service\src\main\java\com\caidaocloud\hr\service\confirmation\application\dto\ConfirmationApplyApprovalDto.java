package com.caidaocloud.hr.service.confirmation.application.dto;

import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationApplyDto;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ConfirmationApplyApprovalDto extends ConfirmationApplyDto {

    private String businessKey;

    private String taskId;

    private String comment;
    // 更新的字段名
    private List<String> writableFields = Lists.list();
}
