package com.caidaocloud.hr.service.common.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class JobConfiguration implements CommandLineRunner {
    private RestHighLevelClient restHighLevelClient;
    private ScheduledExecutorService scheduledThreadPool = Executors.newScheduledThreadPool(1);

    public JobConfiguration(RestHighLevelClient restHighLevelClient) {
        this.restHighLevelClient = restHighLevelClient;
    }

    private void job() {
        scheduledThreadPool.schedule(() -> {
            RequestOptions options = RequestOptions.DEFAULT.toBuilder().build();
            try {
                restHighLevelClient.ping(options);
            } catch (IOException e) {
                log.info("es ping occur error", e);
            }
        }, 30, TimeUnit.SECONDS);
    }

    @Override
    public void run(String... args) throws Exception {
        job();
    }
}