package com.caidaocloud.hr.service.common.infrastructure.manager.cal;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计算器管理器
 */
@Service
@Slf4j
public class PriorCalculatorManager {
    @Resource
    private List<IPriorCalculator> calculators;
    private final Map<String, IPriorCalculator> calculatorMap = new HashMap<>();
    
    /**
     * 初始化，将所有计算器实现注册到映射表中
     */
    @PostConstruct
    public void init() {
        if (calculators != null && !calculators.isEmpty()) {
            for (IPriorCalculator calculator : calculators) {
                calculatorMap.put(calculator.type(), calculator);
                log.info("register handler: {}", calculator.type());
            }
        }
    }

    /**
     * 根据指定类型获取计算器
     * 
     * @param type 计算器类型
     * @return 工龄计算器
     */
    public IPriorCalculator getCalculator(String type) {
        IPriorCalculator calculator = calculatorMap.get(type);
        if (calculator == null) {
            return null;
        }
        return calculator;
    }
    
    /**
     * 执行计算
     * 
     * @param workOverviewDo 工作概况数据
     */
    public void calculate(String type, Object obj) {
        IPriorCalculator calculator = getCalculator(type);
        if (calculator != null) {
            calculator.cal(obj);
        } else {
            log.warn("not match calculator, type={}", type);
        }
    }
} 