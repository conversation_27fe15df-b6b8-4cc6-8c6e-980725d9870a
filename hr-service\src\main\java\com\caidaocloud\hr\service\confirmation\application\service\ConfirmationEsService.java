package com.caidaocloud.hr.service.confirmation.application.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.common.base.DataScopeService;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationApply;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.hr.service.dto.auth.EsOperate;
import com.caidaocloud.hr.service.dto.auth.EsScopeQuery;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.search.application.service.AbstractEsService;
import com.caidaocloud.hr.service.util.DataSimpleUtil;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.transfer.infrastructure.constant.TransferConstant.*;

@Slf4j
@Service
public class ConfirmationEsService extends AbstractEsService {
    @Resource
    private DataScopeService dataScopeService;

    /**
     * 同步转正配置配置定义对应的ES动态数据列
     */
    public void syncConfirmationDef(ConfirmationConfig def) {
        String tenantId = UserContext.getTenantId();
        if (esDataRepository.existsIndex(getIndex(tenantId))) {
            // 索引已存在，则不处理
            return;
        }

        def.setTenantId(tenantId);
        final Map<String, Object> defMap = new HashMap<>();
        // 处理员工展示信息
        doDisplayWorkInfoField(defMap, def.getDisplayWorkInfos());
        // 处理薪资信息
        doFieldDef(def.getSalaryProps(), defMap);
        // 处理任职信息
        doFieldDef(def.getWorkProps(), defMap);
        // 处理转正信息
        doConfirmationField(defMap);
        // 生成动态映射索引
        convertEsDef(defMap, tenantId);
    }

    private void doFieldDef(List<ConfirmationChangeFieldDef> fieldDefList, Map<String, Object> defMap) {
        fieldDefList.forEach(def -> {
            String prop = String.join("$", def.getType(), def.getProperty());
            if (defMap.containsKey(prop)) {
                return;
            }
            PropertyDataType dataType = null == def.getDataType() ? PropertyDataType.String : def.getDataType();
            addDefMapProp(defMap, dataType, prop);
        });
    }

    private void doConfirmationField(Map<String, Object> defMap) {
        // 试用期截止日期
        addDefMap(defMap, keyword, "other$probationPeriodEndDate");
        // 转正日期
        addDefMap(defMap, keyword, "other$confirmationDate");
        // 转正类型
        addDefMap(defMap, keyword, "other$confirmationType");
        // 附件
        addDefMapProp(defMap, PropertyDataType.Attachment, "other$attachment");
        addDefMap(defMap, keyword, "other$remark");
        addDefMap(defMap, keyword, "other$pre");
        // 审批状态
        addDefMap(defMap, keyword, "other$approvalStatus");
    }

    private void doDisplayWorkInfoField(Map<String, Object> defMap, List<MetadataPropertyVo> propList) {
        // 工号
        addDefMap(defMap, keyword, "main$workno");
        // 姓名
        addDefMap(defMap, keyword, "main$name");
        // 员工empId
        addDefMap(defMap, keyword, "main$empId");
        // 员工enName
        addDefMap(defMap, keyword, "main$enName");
        // 入职日期
        addDefMap(defMap, longType, "main$hireDate");
        // 试用期截止日期
        addDefMap(defMap, longType, "main$probationPeriodEndDate");
        // 转正日期
        addDefMap(defMap, longType, "main$confirmationDate");
        // 转正类型
        addDefMap(defMap, keyword, "main$confirmationType");
        // 所属组织
        addDefMap(defMap, longType, "main$organize");
        // 所属组织名称
        addDefMap(defMap, keyword, "main$organizeTxt");
        // 岗位
        addDefMap(defMap, longType, "main$post");
        // 岗位名称
        addDefMap(defMap, keyword, "main$postTxt");
        // 用工类型
        addDefMap(defMap, keyword, "main$empType");
        // 转正工作地
        addDefMap(defMap, keyword, "main$workplace");
        // 直接上级
        addEmpDefMap("main$leader", defMap);
        if (null == propList || propList.isEmpty()) {
            return;
        }

        propList.forEach(mp -> {
            String prop = String.join("$", "main", mp.getProperty());
            if (defMap.containsKey(prop)) {
                return;
            }
            PropertyDataType dataType = null == mp.getDataType() ? PropertyDataType.String : mp.getDataType();
            addDefMapProp(defMap, dataType, prop);
        });
    }

    private void addDefMapProp(Map<String, Object> defMap, PropertyDataType dataType, String prop) {
        switch (dataType) {
            case Integer:
                addDefMap(defMap, integerType, prop);
                break;
            case Timestamp:
                addDefMap(defMap, longType, prop);
                break;
            case Attachment:
                defMap.put(OLD_MARK + prop + "_names", typeMapping(keyword));
                defMap.put(prop + "_names", typeMapping(keyword));
                defMap.put(OLD_MARK + prop + "_urls", typeMapping(keyword));
                defMap.put(prop + "_urls", typeMapping(keyword));
                break;
            case Emp:
                defMap.put(OLD_MARK + prop + "_empId", typeMapping(keyword));
                defMap.put(prop + "_empId", typeMapping(keyword));
                defMap.put(OLD_MARK + prop + "_workno", typeMapping(keyword));
                defMap.put(prop + "_workno", typeMapping(keyword));
                defMap.put(OLD_MARK + prop + "_name", typeMapping(keyword));
                defMap.put(prop + "_name", typeMapping(keyword));
                defMap.put(OLD_MARK + prop + "_enName", typeMapping(keyword));
                defMap.put(prop + "_enName", typeMapping(keyword));
                break;
            default:
                defMap.put(OLD_MARK + prop, typeMapping(keyword));
                defMap.put(prop, typeMapping(keyword));
                break;
        }
    }

    private void convertEsDef(Map<String, Object> defMap, String tenantId) {
        defMap.put("tenantId", typeMapping(keyword));
        defMap.put("typeId", typeMapping(longType));
        defMap.put("defId", typeMapping(longType));
        defMap.put("createTime", typeMapping(longType));
        defMap.put("updateTime", typeMapping(longType));
        // 挂载的表单id
        defMap.put("formDefId", typeMapping(longType));
        // 发起电子签署
        defMap.put("esgin", typeMapping(keyword));
        // 挂载的表单对应的数据id
        defMap.put("formValueId", typeMapping(longType));
        String mappingJson = esDataRepository.addPropsDynamicTemplate(defMap);
        log.info("update index mappingJson={}", mappingJson);
        esDataRepository.updateDynamicIndex(mappingJson, getIndex(tenantId));
    }

    public PageResult<Map> getPageMapList(BasePage basePage) {
        UserInfo userInfo = UserContext.preCheckUser();
        ConfirmationRecordSearchDto searchDto = (ConfirmationRecordSearchDto) basePage;
        String tenantId = userInfo.getTenantId();
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(getIndex(tenantId));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.size(basePage.getPageSize());
        sourceBuilder.from((basePage.getPageNo() - 1) * basePage.getPageSize());
        sourceBuilder.sort("updateTime", SortOrder.DESC);

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchQuery("tenantId", tenantId));
        if (StringUtil.isNotEmpty(searchDto.getKeywords())) {
            BoolQueryBuilder kwBoolQuery = QueryBuilders.boolQuery().
                    should(QueryBuilders.matchQuery("main$name", searchDto.getKeywords())).
                    should(QueryBuilders.matchQuery("main$workno", searchDto.getKeywords())).
                    should(QueryBuilders.wildcardQuery("main$name", "*" + searchDto.getKeywords() + "*")).
                    should(QueryBuilders.wildcardQuery("main$workno", "*" + searchDto.getKeywords() + "*"))
                    .minimumShouldMatch(1);
            boolQuery.must(kwBoolQuery);
        }
        doSearchFilters(basePage.getFilters(), boolQuery);
        // 审批状态
        if (StringUtil.isNotEmpty(searchDto.getApprovalStatus())) {
            boolQuery.must(QueryBuilders.matchQuery("other$approvalStatus", searchDto.getApprovalStatus()));
        }
        if (searchDto.getApprovalStatusInclude() != null) {
            boolQuery.must(QueryBuilders.termsQuery("other$approvalStatus", searchDto.getApprovalStatusInclude()));
        }
        if (StringUtil.isNotEmpty(searchDto.getOrganize())) {
            boolQuery.must(QueryBuilders.termQuery("main$organize", searchDto.getOrganize()));
        }
        if (StringUtil.isNotEmpty(searchDto.getDefId())) {
            boolQuery.must(QueryBuilders.termQuery("defId", searchDto.getDefId()));
        }
        if (StringUtil.isNotEmpty(searchDto.getEmpId())) {
            boolQuery.must(QueryBuilders.termQuery("main$empId", searchDto.getEmpId()));
        }
        // 是否显示已撤销
        if (!searchDto.isShowRevoke()) {
            boolQuery.mustNot(QueryBuilders.matchQuery("other$approvalStatus", WfProcessStatusEnum.REVOKE.value));
        }

        if (null != searchDto.getStartConfirmationDate() && null != searchDto.getEndConfirmationDate()) {
            boolQuery.must(QueryBuilders.rangeQuery("other$confirmationDate")
                    .gte(searchDto.getStartConfirmationDate())
                    .lte(searchDto.getEndConfirmationDate()));
        }

        if (StringUtil.isNotEmpty(searchDto.getConfirmationFutureDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("other$confirmationDate").gt(searchDto.getConfirmationFutureDate()));
        }

        //caidao - 1787 校验 异动后 组织/岗位 信息；

        //异动后 组织信息；
        if (StringUtil.isNotEmpty(searchDto.getNewOrganize())) {
            boolQuery.must(QueryBuilders.termQuery("work$organize_enable", true));
            boolQuery.must(QueryBuilders.termQuery("work$organize", searchDto.getNewOrganize()));
        }

        //异动后 岗位信息；
        if (StringUtil.isNotEmpty(searchDto.getNewPost())) {
            boolQuery.must(QueryBuilders.termQuery("work$post_enable", true));
            boolQuery.must(QueryBuilders.termQuery("work$post", searchDto.getNewPost()));
        }

        doDataScope(userInfo.getUserId(), boolQuery);
        sourceBuilder.query(boolQuery);
        searchRequest.source(sourceBuilder);
        SearchHits hits = esDataRepository.searchRequest(searchRequest);

//        //1787 转正排查；
//        log.info("*** 测试 ： 转正流程数据：{}", hits.getHits());

        if (null == hits) {
            return new PageResult(new ArrayList(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }

        List<Map<String, Object>> itemList = new ArrayList<>();
        hits.forEach(hit -> {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            if (null == dataMap || dataMap.isEmpty()) {
                return;
            }
            itemList.add(dataMap);
        });

//        log.info("*** 测试转正：{}", itemList);

        return new PageResult(itemList, basePage.getPageNo(), basePage.getPageSize(), (int) hits.getTotalHits().value);
    }

    private void doDataScope(Long userId, BoolQueryBuilder boolQuery) {
        List<AuthRoleScopeFilterDetail> dataList = dataScopeService.getScopeList("ENTITY_HR_EMP_CONFIRMATION", userId, "");
        Map<AuthRoleScopeRestriction, EsScopeQuery> scopeMap = new HashMap();
        //查看自己创建的数据
        scopeMap.put(AuthRoleScopeRestriction.CREATED_BY_MYSELF, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("createBy"));
        ////按用工类型（参数：下拉用工类型，支持多选）
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_EMP_TYPE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$empType"));
        // 指定合同公司
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_COMPANY, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$company"));
        //// 按HRBP 查看（选择所属组织的对应HRBP等于**的员工）
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_HRBP, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$organize"));
        // 指定工作地（参数：下拉多选工作地，按照员工身上的工作地圈人）
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_WORKPLACE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$workplace"));

        // 查看本组织及下级组织
        scopeMap.put(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("main$organizePath"));
        // 查看本组织
        scopeMap.put(AuthRoleScopeRestriction.MY_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$organize"));
        // 查看指定组织
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$organize"));
        // 查看指定组织及下级组织
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("main$organizePath"));

        // 按部门负责人查看
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_LEADER, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$organize"));
        // 查看直接下级
        scopeMap.put(AuthRoleScopeRestriction.DIRECT_SUBORDINATE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$empId"));
        // 查看指定员工（选择指定员工，支持多选）
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_EMP, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("main$empId"));

        dataScopeService.getScopeQuery(dataList, boolQuery, scopeMap);
    }

    private void doSearchFilters(List<FilterElement> filters, BoolQueryBuilder boolQuery) {
        if (null == filters || filters.isEmpty()) {
            return;
        }

        filters.forEach(filter -> {
            if (null == filter.getValue()) {
                return;
            }

            String value = filter.getValue().toString();
            if ("".equals(value)) {
                return;
            }

            switch (filter.getOp()) {
                case in:
                    boolQuery.must(QueryBuilders.termsQuery(filter.getProp(), value.split(",")));
                    break;
                case bt:
                    String[] split = filter.getValue().toString().split(",");
                    boolQuery.must(QueryBuilders.rangeQuery(filter.getProp()).gte(split[0]).lte(split[1]));
                    break;
            }
        });
    }

    @Override
    protected PageResult<DataSimple> getPageList(BasePage basePage) {
        return null;
    }

    @Override
    protected DataSimple map2DataSimple(Map<String, Object> dataMap) {
        if (null == dataMap) {
            return new DataSimple();
        }
        String defId = (String) dataMap.get("defId");
        ConfirmationConfig defConfig = ConfirmationConfig.getOne(defId);
        return map2DataSimple(dataMap, defConfig);
    }

    private DataSimple map2DataSimple(Map<String, Object> dataMap, ConfirmationConfig defConfig) {
        DataSimple dataSimple = new DataSimple();
        if (null == dataMap || dataMap.isEmpty()) {
            return dataSimple;
        }

        if (log.isDebugEnabled()) {
            log.debug("Confirmation map2DataSimple dataMap = {}", FastjsonUtil.toJson(dataMap));
        }

        Map<String, ConfirmationChangeFieldDef> defMap = convertDefMap(defConfig);
        Map<String, String> baseMap = DataSimpleUtil.simpleMap;
        dataMap.forEach((k, pv) -> {
            if (baseMap.containsKey(k)) {
                // 如果是 DataSimple 中的 id、tenantId、createTime、createBy、updateBy、deleted、updateTime 等字段，则直接赋值
                addBaseData(dataSimple, k, pv);
                return;
            }

            ConfirmationChangeFieldDef fieldDef = defMap.get(k);
            // 标准字段
            if (null != fieldDef) {
                addDataSimple(dataSimple, fieldDef, k, pv, dataMap, StringUtil.EMPTY);
                return;
            } else if (k.endsWith(TXT_FIELD_MARK)) {
                dataSimple.getProperties().add(k, Objects.toString(pv, null));
                return;
            } else if (k.endsWith(ENABLE_MARK)) {
                dataSimple.getProperties().add(k, Objects.toString(pv, null));
                return;
            } else {
                // 其他字段，other以及
                String[] props = k.split(UNDERLINE);
                if (props.length > 1) {
                    if (OLD_FIELD_MARK.equals(props[0])) {
                        String property = props[1];
                        fieldDef = defMap.get(property);
                        addDataSimple(dataSimple, fieldDef, property, pv, dataMap, OLD_FIELD_MARK + UNDERLINE);
                        return;
                    }
                    fieldDef = defMap.get(props[0]);
                    putDataSimple(fieldDef, dataSimple, props[0], pv, dataMap);
                    return;
                }
            }

            putDataSimple(fieldDef, dataSimple, k, pv, dataMap);
        });
        return dataSimple;
    }

    private void putDataSimple(ConfirmationChangeFieldDef fieldDef, DataSimple dataSimple,
                               String k, Object pv, Map<String, Object> dataMap) {
        if (null == fieldDef) {
            dataSimple.getProperties().add(k, Objects.toString(pv, null));
        } else {
            addDataSimple(dataSimple, fieldDef, k, pv, dataMap, StringUtil.EMPTY);
        }
    }

    public DataSimple getConfirmationApply(String dataId) {
        return getEsDataById(dataId);
    }

    /**
     * 查询已审批完成
     * @param page
     * @return
     */
    public List<DataSimple> getArchiveData(BasePage page) {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(getIndex(tenantId));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.from(page.getPageNo() + page.getPageSize());
        sourceBuilder.size(page.getPageSize());
        sourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("other$approvalStatus", "APPROVE"));
        sourceBuilder.query(boolQuery);
        searchRequest.source(sourceBuilder);
        SearchHits searchHits = esDataRepository.searchRequest(searchRequest);
        if (searchHits.getHits() != null) {
            return Arrays.stream(searchHits.getHits()).map(it -> map2DataSimple(it.getSourceAsMap())).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public boolean checkIndexExist() {
        String tenantId = UserContext.getTenantId();
        return esDataRepository.existsIndex(getIndex(tenantId));
    }

    @Override
    protected String getIndex(String tenantId) {
        String index = String.format("confirmation_%s", tenantId);
        return index;
    }

    public void saveConfirmationApply(ConfirmationApply applyDo) {
        saveEsData(applyDo);
    }

    public void deleteConfirmationApply(String dataId) {
        deleteEsData(dataId);
    }

    public void updateConfirmationApply(ConfirmationApply applyDo) {
        updateEsData(applyDo);
    }

    public void updateConfirmationApply(DataSimple applyDo) {
        updateEsData(applyDo);
    }

    private Map<String, ConfirmationChangeFieldDef> convertDefMap(ConfirmationConfig defConfig) {
        Map<String, ConfirmationChangeFieldDef> defMap = new HashMap<>();
        // 试用期截止日期
        defMap.put("other$probationPeriodEndDate", new ConfirmationChangeFieldDef().setProperty("probationPeriodEndDate").setDataType(PropertyDataType.Timestamp));
        // 转正日期
        defMap.put("other$confirmationDate", new ConfirmationChangeFieldDef().setProperty("confirmationDate").setDataType(PropertyDataType.Timestamp));
        // 转正类型
        defMap.put("other$confirmationType", new ConfirmationChangeFieldDef()
                .setProperty("confirmationType")
                .setDataType(PropertyDataType.Enum)
                .setEnumDef(FastjsonUtil.toList("[{\"display\":\"不转正\",\"value\":\"NOT_CONFIRMATION\"},{\"display\":\"延迟转正\",\"value\":\"DELAY_CONFIRMATION\"},{\"display\":\"提前转正\",\"value\":\"EARLY_CONFIRMATION\"},{\"display\":\"按期转正\",\"value\":\"ON_TIME_CONFIRMATION\"}]", PropertyEnumDefDto.class)));
        // 附件
        defMap.put("other$attachment", new ConfirmationChangeFieldDef().setProperty("attachment").setDataType(PropertyDataType.Attachment));
        // 备注
        defMap.put("other$remark", new ConfirmationChangeFieldDef().setProperty("remark").setDataType(PropertyDataType.String));
        defMap.put("other$approvalStatus", new ConfirmationChangeFieldDef().setProperty("approvalStatus").setDataType(PropertyDataType.Enum));

        defConfig.getDisplayWorkInfos().forEach(mp -> {
            defMap.put("main$" + mp.getProperty(),
                    new ConfirmationChangeFieldDef().setProperty(mp.getProperty())
                            .setDataType(mp.getDataType()).setEnumDef(mp.getEnumDef()));
        });

        defConfig.getWorkProps().forEach(ccf -> {
            defMap.put(ccf.getType() + "$" + ccf.getProperty(), ccf);
        });

        defConfig.getSalaryProps().forEach(ccf -> {
            defMap.put(ccf.getType() + "$" + ccf.getProperty(), ccf);
        });

        return defMap;
    }
}
