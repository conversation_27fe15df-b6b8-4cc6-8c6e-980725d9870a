package com.caidaocloud.hr.service.agreement.domain.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.agreement.domain.entity.UserAgreementDo;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * created by: FoAng
 * create time: 14/10/2024 1:39 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserAgreementDomainService extends BaseDomainServiceImpl<UserAgreementDo, BasePage> {

    private UserAgreementDo userAgreementDo;

    @Override
    public BaseDomainDo<UserAgreementDo> getDoService() {
        return userAgreementDo;
    }

    public PageResult<UserAgreementDo> getSummaryList(BasePage page) {
        return userAgreementDo.getSummaryList(page);
    }

    public void changeStatusByType(String bid, String type, Integer status) {
        userAgreementDo.changeStatusByType(bid, type, status);
    }

    public UserAgreementDo detailByType(String type) {
        return userAgreementDo.detailByType(type);
    }
}
