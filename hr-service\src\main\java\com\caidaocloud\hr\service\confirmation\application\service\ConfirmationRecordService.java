package com.caidaocloud.hr.service.confirmation.application.service;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationRecordExportDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.temination.application.FormService;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConfirmationRecordService {
    @Resource
    private ConfirmationEsService confirmationEsService;
    @Resource
    private FormService formService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    public PageResult<ConfirmationRecordVo> page(ConfirmationRecordSearchDto searchDto) {
        PageResult<Map> pageMapList = confirmationEsService.getPageMapList(searchDto);
        List<ConfirmationRecordVo> dataList = new ArrayList<>(10);
        pageMapList.getItems().forEach(dataMap -> {
            dataList.add(ConfirmationRecordVo.map2Vo(dataMap, postTxtShowCode));
        });
        return new PageResult(dataList, searchDto.getPageNo(), searchDto.getPageSize(), pageMapList.getTotal());
    }

    public void exportQuery(ConfirmationRecordSearchDto query, HttpServletResponse response) {
        query.setPageSize(10000);
        log.info("Confirmation Record Export List queryDto = {}", FastjsonUtil.toJson(query));
        Map<String, FormDefDto> formDefMap = new HashMap<>();
        List<Map> exportList = getDataList(query, formDefMap);
        List<ExcelExportEntity> colList = getExcelExportEntityList(formDefMap);
        try {
            ExcelUtils.downloadDataListMapExcel(colList, exportList, "转正记录", response);
        } catch (Exception e) {
            log.error("download export Query List excel err.{}", e.getMessage(), e);
        }
    }

    private List<Map> getDataList(ConfirmationRecordSearchDto query, Map<String, FormDefDto> formDefMap){
        PageResult<Map> pageMapList = confirmationEsService.getPageMapList(query);
        List<Map> exportList = pageMapList.getItems().stream()
            .map(map -> {
                Map dataMap = FastjsonUtil.convertObject(ConfirmationRecordExportDto.map2Dto(map, postTxtShowCode), Map.class);
                String formDefId = (String) map.get("formDefId"), formValueId = (String) map.get("formValueId");
                if(StringUtil.isEmpty(formDefId) || StringUtil.isEmpty(formValueId)){
                    return dataMap;
                }

                Map formDataMap = formService.getFormDataMap(formDefId, formValueId);
                if(null != formDataMap && !formDataMap.isEmpty()){
                    FormDefDto formDef = getFormDefByCache(formDefMap, formDefId);
                    formService.convertFormValue(formDataMap, formDef);
                    dataMap.putAll(formDataMap);
                }

                return dataMap;
            }).collect(Collectors.toList());
        return exportList;
    }

    private FormDefDto getFormDefByCache(Map<String, FormDefDto> formDefMap, String formDefId){
        FormDefDto formDef = formDefMap.get(formDefId);
        if(null != formDef){
            return formDef;
        }

        formDef = formService.getFormDefById(formDefId);
        formDefMap.put(formDefId, formDef);
        return formDef;
    }

    public List<ExcelExportEntity> getExcelExportEntityList(Map<String, FormDefDto> formDefMap) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        colList.add(getExcelExportEntity("工号", "workno", 1));
        colList.add(getExcelExportEntity("姓名", "name", 2));
        colList.add(getExcelExportEntity("入职日期", "hireDate", 3));
        colList.add(getExcelExportEntity("试用期截止日期", "probationPeriodEndDate", 4));
        colList.add(getExcelExportEntity("转正日期", "confirmationDate", 5));
        colList.add(getExcelExportEntity("转正类型", "confirmationType", 6));
        colList.add(getExcelExportEntity("任职组织", "organizeTxt", 7));
        colList.add(getExcelExportEntity("岗位", "postTxt", 8));
        colList.add(getExcelExportEntity("用工类型", "empType", 9));
        colList.add(getExcelExportEntity("直接上级姓名", "leaderName", 10));
        colList.add(getExcelExportEntity("直接上级工号", "leaderWorkNo", 11));
        colList.add(getExcelExportEntity("直接上级英文名", "leaderEnName", 12));
        colList.add(getExcelExportEntity("审批状态", "approvalStatus", 13));

        Set<String> keySet = new HashSet<>();
        formDefMap.forEach((k, v) -> {
            if(keySet.contains(k)){
                return;
            }

            v.getProperties().forEach(fdm -> {
                if(keySet.contains(fdm.getName()) || keySet.contains(fdm.getProperty())){
                    return;
                }

                colList.add(getExcelExportEntity(fdm.getName(), fdm.getProperty(), colList.size() + 1));
                keySet.add(fdm.getName());
                keySet.add(fdm.getProperty());
            });

            keySet.add(k);
        });

        return colList;
    }

    private ExcelExportEntity getExcelExportEntity(String text, String key, int order){
        ExcelExportEntity entity = new ExcelExportEntity(text, key, 20);
        entity.setOrderNum(order);
        return entity;
    }

}
