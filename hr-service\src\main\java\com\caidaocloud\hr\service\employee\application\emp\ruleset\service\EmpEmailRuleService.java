package com.caidaocloud.hr.service.employee.application.emp.ruleset.service;

import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.vo.EmpEmailRuleVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EmpEmailRuleService {
    @Resource
    private ITenantFeignClient tenantFeignClient;

    public EmpEmailRuleVo getRuleDetail() {
        Result<String> result =  tenantFeignClient.getKv("EMAIL_GENDER_RULE_" + UserContext.getTenantId());
        if(null == result || !result.isSuccess()){
            return new EmpEmailRuleVo();
        }
        return FastjsonUtil.toObject(result.getData(), EmpEmailRuleVo.class);
    }
}
