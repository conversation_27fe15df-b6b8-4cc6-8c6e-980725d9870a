package com.caidaocloud.hr.service.contract.application.enums;

/**
 * <AUTHOR>
 * @date 2022/5/24
 */

/**
 * 合同设置，合同结束日期
 */
public enum EndDateTypeEnum {
    DEFAULT("0", "默认"),
    MONTH("1","结束月最后一天"),
    SEASON("2","季度末最后一天"),
    YEAR("3","年度最后一天")
    ;


    private final String code;
    private final String text;

    EndDateTypeEnum(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static EndDateTypeEnum getByCode(String code) {
        for (EndDateTypeEnum v : EndDateTypeEnum.values()) {
            if (v.getCode().equals(code)) {
                return v;
            }
        }
        return DEFAULT;
    }
}
