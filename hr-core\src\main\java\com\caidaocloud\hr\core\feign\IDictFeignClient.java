package com.caidaocloud.hr.core.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "caidaocloud-business-config-center",
        fallback = DictFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "hrCoreDictFeignClient")
public interface IDictFeignClient {
    @GetMapping("/api/bcc/dict/common/v1/dict/getEnableDictList")
    Result getEnableDictList(@RequestParam("typeCode") String typeCode,
                             @RequestParam(name = "belongModule", required = false) String belongModule);
}
