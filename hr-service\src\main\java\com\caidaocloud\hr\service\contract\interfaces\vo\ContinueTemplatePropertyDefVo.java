package com.caidaocloud.hr.service.contract.interfaces.vo;

import java.util.List;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/18
 */
@ApiModel("续签字段定义")
@Data
public class ContinueTemplatePropertyDefVo {
	@ApiModelProperty("bid")
	private String bid;
	@ApiModelProperty("名称")
	private String name;
	@ApiModelProperty("页面提示说明")
	private String tips;
	@ApiModelProperty("员工信息")
	private List<MetadataPropertyVo> empInfo;
	@ApiModelProperty("合同信息")
	private List<MetadataPropertyVo> contract;
	@ApiModelProperty("附件")
	private Attachment attachment;
	@ApiModelProperty("员工续签意向")
	private List<String> feedbacks;
}
