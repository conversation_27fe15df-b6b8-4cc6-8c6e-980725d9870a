package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/3/19
 */
@Data
@ApiModel("续签意向书创建dto")
public class ContinueLetterCreateDto {
	@ApiModelProperty("合同id")
	private String contractId;
	@ApiModelProperty("续签意向书模板id")
	private String templateId;
	@ApiModelProperty("附件")
	private Attachment attachment;
}
