package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.organization.application.org.dto.LabelDataDto;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgTreeData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.google.common.collect.Sets;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/10
 **/
public class TreeDataUtil {
    public static void handleIsHiden(List noFilterList, List filter) {
        HashSet<String> existBid = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(filter)) {
            doConvertSet(filter, existBid);
        }
        handleWhichIsHiden(noFilterList, existBid);
    }

    private static <T extends DataSimple> void doConvertSet(List list, Set<String> existBid) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.stream().filter(e -> {
            var isTrue = false;
            if (e instanceof TreeData) {
                isTrue = ((TreeData) e).getData() != null;
            } else if (e instanceof OrgTreeData) {
                isTrue = ((OrgTreeData<?>) e).getData() != null;
            }
            return isTrue;
        }).forEach(e -> {
            if (e instanceof TreeData) {
                existBid.add(((TreeData) e).getData().getBid());
            } else if (e instanceof OrgTreeData) {
                Object bid = ((OrgTreeData<Map>) e).getData().get("bid");
                existBid.add(bid != null ? String.valueOf(bid) : "");
            }
        });
        Object filterList = null;
        if (list.get(0) instanceof TreeData) {
            filterList = ((List<TreeData>) list).stream().filter(e -> CollectionUtils.isNotEmpty(((TreeData) e).getChildren()))
                    .map(e -> ((TreeData) e).getChildren()).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (list.get(0) instanceof OrgTreeData) {
            filterList = ((List<OrgTreeData>) list).stream().filter(e -> CollectionUtils.isNotEmpty(((OrgTreeData) e).getChildren()))
                    .map(e -> ((OrgTreeData) e).getChildren()).flatMap(Collection::stream).collect(Collectors.toList());
        }
        doConvertSet((List) filterList, existBid);
    }

    private static <T extends DataSimple, S> void handleWhichIsHiden(List list, HashSet<String> existBid) {
        if (CollectionUtils.isNotEmpty(list) && list.get(0) instanceof TreeData) {
            for (TreeData<T> jobTypeDoTreeData : (List<TreeData<T>>) list) {
                if (jobTypeDoTreeData.getData() != null && Objects.nonNull(existBid) && !existBid.contains(jobTypeDoTreeData.getData().getBid())) {
                    if (jobTypeDoTreeData.getData() instanceof DataEntity) {
                        ((DataEntity) jobTypeDoTreeData.getData()).setIsHidden(true);
                    } else if (jobTypeDoTreeData.getData() instanceof LabelDataDto) {
                        ((LabelDataDto) jobTypeDoTreeData.getData()).setIsHidden(true);
                    }
                } else if (jobTypeDoTreeData.getData() != null) {
                    if (jobTypeDoTreeData.getData() instanceof DataEntity) {
                        ((DataEntity) jobTypeDoTreeData.getData()).setIsHidden(false);
                    } else if (jobTypeDoTreeData.getData() instanceof LabelDataDto) {
                        ((LabelDataDto) jobTypeDoTreeData.getData()).setIsHidden(false);
                    }
                }
                if (CollectionUtils.isNotEmpty(jobTypeDoTreeData.getChildren())) {
                    handleWhichIsHiden(jobTypeDoTreeData.getChildren(), existBid);
                }
            }
        } else if (CollectionUtils.isNotEmpty(list) && list.get(0) instanceof OrgTreeData) {
            for (OrgTreeData<S> orgTreeData : (List<OrgTreeData<S>>) list) {
                if (orgTreeData.getData() != null && orgTreeData.getData() instanceof Map) {
                    Object bid = ((Map<String, Object>) orgTreeData.getData()).get("bid");
                    if (bid != null && Objects.nonNull(existBid) && !existBid.contains(String.valueOf(bid))) {
                        ((Map) orgTreeData.getData()).put("isHidden", true);
                    } else {
                        ((Map) orgTreeData.getData()).put("isHidden", false);
                    }
                }
                if (CollectionUtils.isNotEmpty(orgTreeData.getChildren())) {
                    handleWhichIsHiden(orgTreeData.getChildren(), existBid);
                }
            }
        }
    }
}
