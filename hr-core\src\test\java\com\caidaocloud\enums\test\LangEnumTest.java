package com.caidaocloud.enums.test;

import com.caidaocloud.hr.service.enums.system.BaseEmpType;
import com.caidaocloud.hr.service.enums.system.EnumLangFactory;
import com.caidaocloud.hr.service.enums.system.WorkStatusEnum;

public class LangEnumTest {
    public static void main(String[] args) {
        EnumLangFactory.register("empType", BaseEmpType.FTI);
        EnumLangFactory.register("workStatus", WorkStatusEnum.CONCURRENT_POST);

        String enumTextByIndex = EnumLangFactory.getEnumTextByIndex("empType", "FTI", "cn");
        System.out.println(enumTextByIndex);

        enumTextByIndex = EnumLangFactory.getEnumTextByIndex("empType", "PartTime", "en");
        System.out.println(enumTextByIndex);

        enumTextByIndex = EnumLangFactory.getEnumTextByIndex("empType", "PartTime", "CN");
        System.out.println(enumTextByIndex);

        enumTextByIndex = EnumLangFactory.getEnumTextByIndex("empType", "sadasfd", "en");
        System.out.println(enumTextByIndex);

        enumTextByIndex = EnumLangFactory.getEnumTextByIndex("Asb_empType", "sadasfd", "en");
        System.out.println(enumTextByIndex);
    }
}
