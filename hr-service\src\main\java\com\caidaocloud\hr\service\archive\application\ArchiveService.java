package com.caidaocloud.hr.service.archive.application;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveEvent;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.archive.infrastructure.po.ArchiveEsData;
import com.caidaocloud.hr.service.archive.infrastructure.repository.ArchiveRepository;
import com.caidaocloud.hr.service.archive.properties.ArchiveProperty;
import com.caidaocloud.hr.service.archive.provider.IModuleProvider;
import com.caidaocloud.hr.service.archive.provider.feign.OnBoardingProviderFeign;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.zxp.esclientrhl.index.ElasticsearchIndex;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 5/6/2024 4:52 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ArchiveService {

    public static ConcurrentMap<String, IModuleProvider> providers = new ConcurrentHashMap<>();

    private EmpWorkInfoDomainService empWorkInfoDomainService;

    private CacheService cacheService;

    private ArchiveRepository archiveRepository;

    private ArchiveProperty archiveProperty;

    private ElasticsearchIndex<ArchiveEsData> elasticsearchIndex;

    private OrgService orgService;

    private DictService dictService;

    /**
     * 初始化文件归档
     */
    public void startArchiveInit() {
        boolean enable = archiveProperty.isEnable();
        boolean loadHistory = archiveProperty.isLoadHistory();
        try {
            if (enable && loadHistory && !elasticsearchIndex.exists(ArchiveEsData.class)) {
                elasticsearchIndex.createIndex(ArchiveEsData.class);
                for (ArchiveModule value : ArchiveModule.values()) {
                    IModuleProvider moduleProvider = providers.get(value.name());
                    if (moduleProvider == null) {
                        log.error("[archive] un resolve module");
                        return;
                    }
                    fetchModuleArchives(moduleProvider);
                }
                archiveRepository.refreshEsData();
            } else {
               log.info("[archive] archive is not enable or init already");
            }
        } catch (Exception e) {
            log.error("[archive] start archive with history error:{}", e.getMessage(), e);
            try {
                elasticsearchIndex.dropIndex(ArchiveEsData.class);
            } catch (Exception ex) {
                log.error("[archive] init archive error, rollback and del es index");
            }
        }
    }

    public void fetchModuleArchives(IModuleProvider moduleProvider) {
        String initMode = archiveProperty.getInitMode();
        if (initMode.equals("feign")) {
            fetchModulePageArchives(moduleProvider);
        } else if (initMode.equals("rabbitmq")) {
            log.info("[archive] fetch all archive by mq start....");
            moduleProvider.fetchAllArchiveData();
            log.info("[archive] fetch all archive by mq end....");
        } else {
            log.error("[archive] archive mode not present, mode:{}", initMode);
        }
    }


    private void fetchModulePageArchives(IModuleProvider moduleProvider) {
        BasePage page = new BasePage();
        page.setPageNo(1);
        page.setPageSize(20);
        List<ArchiveData> archiveList = null;
        do {
            log.info("[archive] fetch {} page archive data at page:{}", moduleProvider.module(), page.getPageNo());
            try {
                archiveList = moduleProvider.fetchPageArchiveData(page);
                page.setPageNo(page.getPageNo() + 1);
                log.info("[archive] save archive data: {}", FastjsonUtil.toJson(archiveList));
                batchSaveModuleArchive(archiveList);
            } catch (Exception e) {
                log.error("[archive] batch save archive data error, {}", e.getMessage(), e);
            }
        } while (CollectionUtils.isNotEmpty(archiveList));
    }

    private void batchSaveModuleArchive(List<ArchiveData> archiveData) {
        if (CollectionUtils.isNotEmpty(archiveData)) {
            List<ArchiveEsData> allEsData = Lists.newArrayList();
            Map<String, List<ArchiveData>> archiveDataMap = archiveData.stream()
                    .collect(Collectors.groupingBy(it -> String.format("%s_%s", it.getEmpType(), it.getEmpId())));
            archiveDataMap.keySet().forEach(it -> {
                List<ArchiveData> itemList = archiveDataMap.get(it);
                EmpWorkInfoDo empWorkInfoDo = fetchEmpWorkInfo(it, System.currentTimeMillis(), true);
                if (empWorkInfoDo == null) {
                    log.warn("[archive] batch save archive filter by un resolve emp work info, empId:{}, dataInfo:{}", it,
                            FastjsonUtil.toJson(itemList));
                    return;
                }
                ArchiveEsData templateData = FastjsonUtil.convertObject(empWorkInfoDo, ArchiveEsData.class);
                templateData.setOrganizePath(fetchOrgFullPath(empWorkInfoDo.getOrganize(), System.currentTimeMillis()));
                loadExtProperty(templateData, empWorkInfoDo, "REDate", "sapid");
                allEsData.addAll(combineArchiveData(itemList, templateData));
            });
            /*for (ArchiveData it : archiveData) {
                EmpWorkInfoDo empWorkInfoDo = fetchEmpWorkInfo(String.format("%s_%s", it.getEmpType(), it.getEmpId()), it.getEventTime());
                if (empWorkInfoDo == null) {
                    log.warn("[archive] batch save archive filter by un resolve emp work info, empId:{}, dataInfo:{}", it,
                            FastjsonUtil.toJson(it));
                    return;
                }
                ArchiveEsData templateData = ObjectConverter.convert(empWorkInfoDo, ArchiveEsData.class);
                templateData.setOrganizePath(fetchOrgFullPath(empWorkInfoDo.getOrganize(), System.currentTimeMillis()));
                loadExtProperty(templateData, empWorkInfoDo, "RESDate", "sapid");
                allEsData.addAll(combineArchiveData(Lists.newArrayList(it), templateData));
            }*/
            if (CollectionUtils.isNotEmpty(allEsData)) {
                archiveRepository.batchInsertArchiveData(allEsData);
            }
        }
    }


    private void loadExtProperty(ArchiveEsData archiveEsData, DataSimple dataSimple, String... properties) {
        NestPropertyValue propertyValue = dataSimple.getProperties();
        Field[] fields = ArchiveEsData.class.getDeclaredFields();
        Map<String, List<Field>> fieldsMap = Arrays.stream(fields).collect(Collectors.groupingBy(Field::getName));
        for (String property : properties) {
            if (fieldsMap.containsKey(property) && propertyValue.containsKey(property)) {
                Field field = fieldsMap.get(property).get(0);
                field.setAccessible(true);
                try {
                    Object itemValue = propertyValue.get(property);
                    if (itemValue != null) {
                        SimplePropertyValue convertValue = FastjsonUtil.convertObject(itemValue, SimplePropertyValue.class);
                        field.set(archiveEsData, convertValue.getValue());
                        log.info("[archive] set ext property value, key:{}, value:{}", property, convertValue.getValue());
                    }
                } catch (Exception e) {
                    log.error("[archive] do load ext field error, {}", e.getMessage(), e);
                }
            }
        }
    }

    private List<ArchiveEsData> combineArchiveData(List<ArchiveData> dataList, ArchiveEsData esData) {
        return Optional.ofNullable(dataList).map(it -> it.stream().map(o1 -> {
            List<ArchiveEsData> itemCombine = Lists.newArrayList();
            for (ArchiveFile archiveFile:
                    o1.getArchiveFiles()) {
                ArchiveEsData itemEsData = esData.clone();
                itemEsData.setBusinessId(o1.getBusinessId());
                itemEsData.setBusinessLine(o1.getBusinessLine());
                itemEsData.setBusinessType(o1.getBusinessType());
                itemEsData.setSubBusinessLine(o1.getSubBusinessLine());
                itemEsData.setArchiveEmpType(o1.getEmpType());
                itemEsData.setEmpId(o1.getEmpId());
                itemEsData.setEventTime(o1.getEventTime());
                itemEsData.setFileName(archiveFile.getFileName());
                if (StringUtils.isNotEmpty(archiveFile.getOpenContractId())) {
                    itemEsData.setOpenContractId(archiveFile.getOpenContractId());
                    itemEsData.setSignPlatform(archiveFile.getSignPlatform());
                    itemEsData.setSuffixType("pdf");
                    try {
                        itemEsData.setPrefix(Character.toString(PinyinUtil.getFirstLetter(itemEsData.getFileName().charAt(0))));
                    } catch (Exception e) {
                        log.error("[archive] convert pinyin prefix error, msg:{}", e.getMessage(), e);
                    }
                    itemCombine.add(itemEsData);
                } else {
                    itemCombine.addAll(combineMultiAttachment(itemEsData, archiveFile));
                }
            }
            return itemCombine;
        }).flatMap(Collection::stream).collect(Collectors.toList())).orElse(Lists.newArrayList());
    }

    private List<ArchiveEsData> combineMultiAttachment(ArchiveEsData esData, ArchiveFile archiveFile) {
        List<ArchiveEsData> combineFiles = Lists.newArrayList();
        if (archiveFile.getUrls().size() != archiveFile.getNames().size()) {
            log.error("[archive] attachment file error, {}", FastjsonUtil.toJson(archiveFile));
            return combineFiles;
        }
        archiveFile.getUrls().forEach(fileUrl -> {
            ArchiveEsData itemEsData = esData.clone();
            final int position = archiveFile.getUrls().indexOf(fileUrl);
            final String fileName = archiveFile.getNames().get(position);
            if (StringUtils.isNotEmpty(fileName) && StringUtils.isNotEmpty(fileUrl)) {
                itemEsData.setFileName(fetchFileName(archiveFile, position));
                itemEsData.setFileUrl(fileUrl);
                itemEsData.setSuffixType(StringUtils.substringAfterLast(fileUrl, "."));
                try {
                    itemEsData.setPrefix(Character.toString(PinyinUtil.getFirstLetter(itemEsData.getFileName().charAt(0))));
                } catch (Exception e) {
                    log.error("[archive] convert pinyin prefix error, msg:{}", e.getMessage(), e);
                }
                combineFiles.add(itemEsData);
            }
        });
        return combineFiles;
    }

    private String fetchFileName(ArchiveFile archiveFile, int position) {
        return Optional.ofNullable(archiveFile.getFileName())
                .map(it -> {
                    if (archiveFile.getUrls().size() == 1) {
                        return it;
                    } else {
                        return String.format("%s%s", it, position + 1);
                    }
                })
                .orElse(StringUtils.substringBeforeLast(archiveFile.getNames().get(position), "."));
    }


    /**
     * 文件归档事件处理
     * @param event 文件归档事件
     */
    public void dispatchArchiveEvent(ArchiveEvent event) {
        ArchivePolicy archivePolicy = event.getArchivePolicy();
        if (archivePolicy == ArchivePolicy.DELETE) {
            log.info("[archive] delete archive by businessLine:{}, businessId:{}", event.getBusinessLine(),
                    event.getBusinessId());
            archiveRepository.deleteArchiveData(event.getBusinessLine(), event.getBusinessId());
        } else {
            List<ArchiveData> archiveList;
            if (CollectionUtils.isNotEmpty(event.getArchiveList())) {
                archiveList = event.getArchiveList();
            } else {
                final String businessModule = event.getBusinessModule();
                IModuleProvider moduleProvider = providers.get(businessModule);
                if (moduleProvider == null) {
                    log.error("[archive] module provider is not suit, module:[{}]", businessModule);
                    return;
                }
                archiveList = moduleProvider.fetchArchiveData(event.getBusinessLine(), event.getBusinessId());
            }
            if (archivePolicy == ArchivePolicy.ADD_UPDATE && CollectionUtils.isEmpty(archiveList)) {
                // 删除历史已归档数据
                ArchiveStandardLine standardLine = null;
                try {
                    standardLine = ArchiveStandardLine.valueOf(event.getBusinessLine());
                } catch (IllegalArgumentException e) {
                    log.info("[archive] un resolve business line");
                }
                archiveRepository.deleteArchiveData(standardLine == null ? event.getBusinessLine() :
                        standardLine.getDesc(), event.getBusinessId());
            } else if (CollectionUtils.isNotEmpty(archiveList)) {
                for (ArchiveData archiveData:
                        archiveList) {
                    EmpWorkInfoDo empWorkInfoDo = fetchEmpWorkInfo(String.format("%s_%s", archiveData.getEmpType(), archiveData.getEmpId()),
                            archiveData.getEventTime(), false);
                    PreCheck.preCheckArgument(empWorkInfoDo == null, "归档员工信息不存在");
                    ArchiveEsData archiveEsData = BeanUtil.convert(empWorkInfoDo, ArchiveEsData.class);
                    archiveEsData.setOrganizePath(fetchOrgFullPath(empWorkInfoDo.getOrganize(), System.currentTimeMillis()));
                    loadExtProperty(archiveEsData, empWorkInfoDo, "REDate", "sapid");
                    List<ArchiveEsData> archiveEsDataList = combineArchiveData(Lists.newArrayList(archiveData), archiveEsData);
                    if (CollectionUtils.isEmpty(archiveEsDataList)) {
                        log.error("[archive] can't fetch archive files");
                        return;
                    }
                    if (archivePolicy == ArchivePolicy.ADD_ONLY) {
                        archiveRepository.batchInsertArchiveData(archiveEsDataList);
                    } else {
                        archiveRepository.batchSaveUpdateArchiveData(archiveEsDataList);
                    }
                }
            }
        }
    }

    private OnBoardingProviderFeign onBoardingProviderFeign;

    /**
     * 加载员工信息
     * @param empIdKey 员工ID
     * @param eventTime 事件时间
     */
    private EmpWorkInfoDo fetchEmpWorkInfo(String empIdKey, Long eventTime, boolean cache) {
        final String tenantId = UserContext.getTenantId();
        boolean entryType = StringUtils.substringBefore(empIdKey, "_").equals("1");
        final String empId = StringUtils.substringAfterLast(empIdKey, "_");
        if (cache) {
            String CACHE_KEY_ARCHIVE_EMP = "cache_archive_emp_%s_%s";
            return Optional.ofNullable(cacheService.getValue(String.format(CACHE_KEY_ARCHIVE_EMP, tenantId, empIdKey)))
                    .map(it -> FastjsonUtil.toObject(it, EmpWorkInfoDo.class))
                    .orElseGet(() -> {
                        EmpWorkInfoDo workInfoDo = getEmpWorkInfo(empId, entryType, eventTime);
                        if (workInfoDo != null) {
                            cacheService.cacheValue(String.format(CACHE_KEY_ARCHIVE_EMP, tenantId, empIdKey),
                                    FastjsonUtil.toJson(workInfoDo), 3600);
                        }
                        return workInfoDo;
                    });
        } else {
            return getEmpWorkInfo(empId, entryType, eventTime);
        }
    }

    private EmpWorkInfoDo getEmpWorkInfo(String empId, boolean entryType, Long eventTime) {
        EmpWorkInfoDo workInfoDo = null;
        if (entryType) {
            Result<Object> result = onBoardingProviderFeign.getWorkInfoByEmpId(empId);
            if (result.isSuccess() && result.getData() != null) {
                log.info("[archive] fetch entry empInfo:{}", result.getData());
                workInfoDo = FastjsonUtil.toObject(FastjsonUtil.toJson(result.getData()),
                        EmpWorkInfoDo.class);
            }
        } else {
            workInfoDo = empWorkInfoDomainService.getEmpWorkInfo(empId,
                    eventTime == null ? System.currentTimeMillis() : eventTime);
        }
        return workInfoDo;
    }

    private String fetchOrgFullPath(String orgId, Long eventTime) {
        final String tenantId = UserContext.getTenantId();
        String cacheOrgPathKey = String.format("cache_archive_org_%s_%s", tenantId, orgId);
        String parentPath = Optional.ofNullable(cacheService.getValue(cacheOrgPathKey))
                .orElseGet(() -> {
                    OrgDo orgDo = orgService.getOrgById(orgId, eventTime);
                    if (orgDo != null) {
                        TreeParent pid = orgDo.getPid();
                        if (null != pid ) {
                            cacheService.cacheValue(cacheOrgPathKey, pid.getPath(), 3600);
                            return pid.getPath();
                        }
                    }
                    return "";
                });
        return StringUtils.isEmpty(parentPath) ? orgId : String.format("%s/%s", parentPath, orgId);
    }


    /**
     * 获取当前所属业务
     */
    public List<String> getAllBusinessLine() {
        List<KeyValue> dictList = dictService.getEnableDictList("businessLine","Employee");
        return Optional.ofNullable(dictList).map(it -> it.stream().map(KeyValue::getText).distinct().collect(Collectors.toList()))
                .orElseGet(() -> Lists.newArrayList(ArchiveStandardLine.ONBOARDING, ArchiveStandardLine.REGULARIZATION,
                        ArchiveStandardLine.EMPLOYEE, ArchiveStandardLine.TRANSFER, ArchiveStandardLine.TERMINATE,
                        ArchiveStandardLine.ESIGN_PROOF).stream().map(ArchiveStandardLine::getDesc).collect(Collectors.toList()));
    }


}
