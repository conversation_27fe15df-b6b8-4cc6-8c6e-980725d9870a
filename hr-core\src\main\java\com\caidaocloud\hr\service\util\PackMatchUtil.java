package com.caidaocloud.hr.service.util;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.enums.system.MatchCondition;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.paas.match.ConditionExp;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
public class PackMatchUtil {
	private static ThreadLocal<List<ConditionDataVo>> MATCH_RULE = new ThreadLocal<>();

	/**
	 * {@link MatchCondition} 部分匹配条件的code和配置中心不同,兼容线上数据
	 */
	public final static Map<String, String> CODE_MAPPING = new HashMap<>();
	public final static Map<String, List<ConditionOperatorEnum>> OPERATORS_MAPPING = new HashMap<>();
	static {
		CODE_MAPPING.put(MatchCondition.ORG.getCode(), "organize");
		CODE_MAPPING.put("organize", "organize");

		CODE_MAPPING.put("workno", MatchCondition.WORK_NO.getCode());
		CODE_MAPPING.put(MatchCondition.WORK_NO.getCode(), "workno");

		CODE_MAPPING.put(MatchCondition.HEALTH_REQUIREMENT.getCode(), "rohExamination");
		CODE_MAPPING.put("rohExamination", MatchCondition.HEALTH_REQUIREMENT.getCode());

		CODE_MAPPING.put(MatchCondition.HEALTH_CARD.getCode(), "healthyCertificates");
		CODE_MAPPING.put("healthyCertificates", MatchCondition.HEALTH_CARD.getCode());

		CODE_MAPPING.put(MatchCondition.DORMITORY.getCode(), "accommodation");
		CODE_MAPPING.put("accommodation", MatchCondition.DORMITORY.getCode());
		OPERATORS_MAPPING.put("organize", Lists.list(ConditionOperatorEnum.CONTAIN_CHILD));
		OPERATORS_MAPPING.put("org", OPERATORS_MAPPING.get("organize"));
	}

	public static void set(List<ConditionDataVo> conditionDataVos) {
		MATCH_RULE.set(conditionDataVos);
	}

	public static void remove(){
		MATCH_RULE.remove();
	}

	public static boolean match(Map<String, String> preEmp, ConditionExp conditionExp) {
		ConditionDataVo condition = Sequences.sequence(MATCH_RULE.get())
				.find(data -> data.getCode()
						.equals(CODE_MAPPING.getOrDefault(conditionExp.getName(), conditionExp.getName())))
				.getOrThrow(new ServerException("错误的入职匹配条件"));
		String value = preEmp.get(condition.getQueryProperty());
		switch (conditionExp.getSymbol()) {
			case EQ:
				return StringUtils.equals(value, conditionExp.getSimpleValue());
			case NE:
				return !StringUtils.equals(value, conditionExp.getSimpleValue());
			case IN:
				if (StringUtils.isEmpty(conditionExp.getSimpleValue())) {
					return false;
				} else {
					return Lists.list(conditionExp.getSimpleValue().split(",")).contains(value);
				}
			case NOT_CONTAIN:
				if(StringUtils.isEmpty(conditionExp.getSimpleValue())){
					return !Objects.equals(value, conditionExp);
				}
				return !Lists.list(conditionExp.getSimpleValue().split(",")).contains(value);
			case CONTAIN_CHILD:
				return containChild(value, conditionExp.getSimpleValue());
			case IS_NULL:
				return StringUtils.isEmpty(value);
			case IS_NOT_NULL:
				return StringUtils.isNotEmpty(value);
			default:
				throw new ServerException("匹配条件存在不支持的比较符");
		}
	}

	public static List<ConditionOperatorEnum> findOperators(String prop){
		List<ConditionOperatorEnum> operators = OPERATORS_MAPPING.get(prop);
		if(null == operators || operators.isEmpty()){
			return Lists.list();
		}

		return operators;
	}

	private static boolean containChild(String organize, String simpleValue, Long dataTime){
		if(Objects.equals(organize, simpleValue)){
			return true;
		}

		if(null != simpleValue && null != organize && simpleValue.contains(organize)){
			return true;
		}

		if(null != simpleValue){
			String [] sValues = simpleValue.split(",");
			for (String sValue : sValues) {
				if(doContainChild(organize, sValue, dataTime)){
					return true;
				}
			}
		}
		return false;
	}

	private static boolean doContainChild(String organize, String simpleValue, Long dataTime){
		DataFilter filter = DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
				.and(DataFilter.eq("bid", simpleValue).orRegex("pid$path", simpleValue))
				.andEq("deleted", Boolean.FALSE.toString());
		PageResult<DataSimple> pageResult = DataQuery.identifier("entity.hr.Org")
				.decrypt().specifyLanguage()
				.queryInvisible().limit(5000, 1)
				.filter(filter, DataSimple.class, dataTime);
		if(null == pageResult || null == pageResult.getItems() || pageResult.getItems().isEmpty()){
			return false;
		}

		return null != pageResult.getItems().stream()
				.filter(it -> it.getBid().equals(organize))
				.findFirst().orElse(null);
	}

	private static boolean containChild(String organize, String simpleValue){
		return containChild(organize, simpleValue, System.currentTimeMillis());
	}
}
