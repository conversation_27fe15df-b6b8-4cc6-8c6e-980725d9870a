package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ProvinceCity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工个人银行卡信息DTO")
public class EmpBankDto {
    @ApiModelProperty("银行卡BID")
    private String bid;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("开户银行")
    private String bank;
    @ApiModelProperty("开户支行")
    private String subbranch;
    @ApiModelProperty("开户城市")
    private Address openingCity;
    @ApiModelProperty("开户城市(弃用)")
    private ProvinceCity address;
    @ApiModelProperty("开户人姓名")
    private String name;
    @ApiModelProperty("银行卡号")
    private String cardNumber;
    @ApiModelProperty("用途")
    private String purpose;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
