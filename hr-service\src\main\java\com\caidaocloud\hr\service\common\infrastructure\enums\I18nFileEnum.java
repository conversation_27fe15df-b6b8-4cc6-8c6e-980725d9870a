package com.caidaocloud.hr.service.common.infrastructure.enums;

import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;

import java.util.Locale;
import java.util.Map;

@Slf4j
public enum I18nFileEnum {
    name("Employee Name", "员工姓名", "従業員名"),
    organize("Belonging organization", "所属组织", "所属組織"),
    empType("Employment type", "用工类型", "雇用タイプ"),
    leadEmpId("Direct superior", "工时制", "直接上司"),
    hireDate("Date of Employment", "入职日期", "入社日"),
    probationPeriodEndDate("Trial period deadline", "试用期截止日期", "試用期間締切日"),
    workno("work no", "员工工号", "従業員番号"),
    postTxt("post", "岗位名称", "部署名"),
    jobGrade("Rank", "职级", "に順位を付ける"),
    confirmationDate("Date of Confirmation", "转正日期", "転正日"),
    workplaceTxt("workplace name", "工作地名称", "作業地名"),
    costCenters("Cost Center", "成本中心", "コストセンタ"),
    socialSecurity("Social security payment place", "社保缴纳地", "社会保険納付地。"),
    providentFund("Provident fund payment place", "公积金缴纳地", "公積金納付地。"),
    companyTxt("Contract company name", "合同公司名称", "契約会社名"),
    registerAddress("Contract registration location", "合同注册地", "契約登録地"),
    contractType("type of contract", "合同类型", "契約タイプ"),
    startDate("time-on", "开始时间", "開始時間"),
    endDate("End Time", "结束时间", "終了時間"),
    signType("Type of signing", "签订类型", "締結タイプ"),
    empSalaryType("Employee salary type", "员工薪资类型", "社員の給与の種類"),
    salary("salary", "薪资", "報酬"),
    htTest("htTest", "HT测试", "HTテスト"),
    reason("reason", "异动原因", "理由"),
    effectiveDate("effectiveDate", "生效日期", "有効日（ゆうこうび）"),
    approval("approval", "是否需审批", "承認が必要ですか"),
    files("files", "附件", "添付ファイル "),
    salaryModule("salary adjustment component", "调薪组件", "給与調整コンポーネント");

    public String en;
    public String zn;
    public String ja;


    I18nFileEnum(String en, String zn, String ja) {
        this.en = en;
        this.zn = zn;
        this.ja = ja;
    }

    public String getValue() {
        Locale locale = LangUtil.getLocale();
        return locale.getLanguage().equals("zh") ? this.zn : (locale.getLanguage().equals("ja") ? this.ja : this.en);
    }

    public Map<String, String> geti18nName() {
        return ImmutableMap.of("default", this.getValue(), "zh-CN", this.zn, "en-US", this.en, "ja-JP", this.ja);
    }
}