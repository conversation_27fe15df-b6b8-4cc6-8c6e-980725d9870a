package com.caidaocloud.hr.service.confirmation.domain.entity;

import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EnabledConfirmationPropInfo {


    private boolean required;

    private EnabledConfirmationProp enabled;

    public enum EnabledConfirmationProp{
        CONFIRMATION_TYPE("转正类型", PropertyDataType.Enum), CONFIRMATION_DATE("转正日期", PropertyDataType.Timestamp), PROBATION_PERIOD_END_DATE("试用期截止日期", PropertyDataType.Timestamp),
        ATTACHMENT("附件", PropertyDataType.Attachment), REMARK("备注", PropertyDataType.String), WORKFLOW_ENABLED("是否审批", PropertyDataType.Boolean);

        public final String text;
        public final PropertyDataType dataType;

        EnabledConfirmationProp(String text, PropertyDataType dataType) {
            this.text = text;
            this.dataType = dataType;
        }

        public static EnabledConfirmationProp getByName(String name) {
            for (EnabledConfirmationProp type : EnabledConfirmationProp.values()) {
                if (type.name().equalsIgnoreCase(name)) {
                    return type;
                }
            }
            return null;
        }

        public static String getTextByName(String name) {
            EnabledConfirmationProp prop = getByName(name);
            return prop == null ? null : prop.text;
        }
    }

    public EnabledConfirmationPropInfo(EnabledConfirmationProp enabled) {
        this.enabled = enabled;
        required = false;
    }
}
