package com.caidaocloud.hr.service.employee.application.event.subscribe;

import java.util.concurrent.RejectedExecutionException;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpDynamicRefreshService;
import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpDynamicService;
import com.caidaocloud.hrpaas.paas.common.event.DynamicColumnSaveEvent;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@Component
@Slf4j
public class DynamicColumnSavedSubscriber implements MessageHandler<DynamicColumnSaveEvent> {
	@Resource
	private EmpDynamicRefreshService empDynamicRefreshService;


	@Override
	public String topic() {
		return DynamicColumnSaveEvent.topic+"EMPLOYEE";
	}

	@Override
	public void handle(DynamicColumnSaveEvent message) throws Exception {
		String tenantId = message.getTenantId();
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId(tenantId);
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		try {

			empDynamicRefreshService.refresh();
		}
		catch (RejectedExecutionException e) {
			log.info("DynamicColumnSavedSubscriber rejectedExecutionException,message:{}", e.getMessage());
		}
		finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}
}
