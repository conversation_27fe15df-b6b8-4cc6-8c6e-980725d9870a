package com.caidaocloud.hr.service.search.infrastructure.es;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.SneakyThrows;
import org.elasticsearch.index.query.QueryBuilder;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.response.ScrollResponse;

/**
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Data
public  class EsExportScrollQueryWrapper<T> {

	private QueryBuilder queryBuilder;

	// private PageSortHighLight pageSortHighLight;
	private String scrollTime = "1m";


	private String scrollId;
	private boolean finished;
	private long total;

	private final Class<T> cls;
	private int pageSize;

	public EsExportScrollQueryWrapper(QueryBuilder queryBuilder, Class<T> cls, int pageSize) {
		this.queryBuilder = queryBuilder;
		this.cls = cls;
		this.pageSize = pageSize;
	}

	public List<T> query(){
		if (isFinished()) {
			return new ArrayList<>();
		}
		if (scrollId == null) {
			return init();
		}
		else {
			return nextScroll();
		}
	}

	@SneakyThrows
	public void clear(){
		ElasticsearchTemplate template = SpringUtil.getBean(ElasticsearchTemplate.class);
		template.clearScroll(scrollId);
	}

	@SneakyThrows
	private List<T> nextScroll() {
		ElasticsearchTemplate template = SpringUtil.getBean(ElasticsearchTemplate.class);
		ScrollResponse<T> response = template.queryScroll(cls, 1L, scrollId);
		if (response.getList().isEmpty()) {
			setFinished(true);
		}
		return response.getList();
	}

	@SneakyThrows
	private List<T> init() {
		ElasticsearchTemplate template = SpringUtil.getBean(ElasticsearchTemplate.class);
		// SearchRequest searchRequest = new SearchRequest("your_index_name");
		// searchRequest.scroll(scrollTime);
		// searchRequest.source(new SearchSourceBuilder().query(queryBuilder));
		ScrollResponse<T> response = template.createScroll(queryBuilder, cls, 1L, pageSize);
		this.scrollId = response.getScrollId();
		return response.getList();
	}
}
