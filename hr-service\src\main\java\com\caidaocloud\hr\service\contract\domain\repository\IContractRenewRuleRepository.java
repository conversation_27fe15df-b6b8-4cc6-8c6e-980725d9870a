package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRuleDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * created by: FoAng
 * create time: 29/11/2024 3:09 下午
 */
public interface IContractRenewRuleRepository extends BaseRepository<ContractRenewRuleDo> {

    List<Integer> listAdvanceDay(String identifier);
}
