package com.caidaocloud.hr.service.employee.domain.workExperience.service.impl;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkExperienceDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkOverviewDo;
import com.caidaocloud.hr.service.common.infrastructure.manager.cal.IPriorCalculator;
import com.caidaocloud.hr.service.employee.domain.workExperience.service.WorkExperienceDomainService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 加入本司前工龄计算器实现
 */
@Service
@Slf4j
public class WorkOverviewPriorServiceLenCalculatorImpl implements IPriorCalculator<WorkOverviewDo> {
    @Resource
    private WorkExperienceDomainService workExperienceDomainService;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    /**
     * 一年的天数
     */
    private static final BigDecimal DAYS_PER_YEAR = new BigDecimal("365");

    /**
     * 计算加入本司前工龄并更新工作概况
     *
     * @param workOverviewDo 工作概况数据
     */
    @Override
    public void cal(WorkOverviewDo workOverviewDo) {
        if (workOverviewDo == null || workOverviewDo.getEmpId() == null) {
            return;
        }
        // 获取员工工作经历列表
        List<WorkExperienceDo> workExperiences = workExperienceDomainService.selectList(workOverviewDo.getEmpId());
        val empWorkInfo = empWorkInfoDo.getEmpWorkInfo(workOverviewDo.getEmpId(), System.currentTimeMillis());
        if (Objects.isNull(empWorkInfo)) {
            log.info("not found empWorkInfo, empId={}", workOverviewDo.getEmpId());
        }
        // 计算加入本司前工龄
        BigDecimal priorServiceLen = calculatePriorServiceLen(workExperiences);
        workOverviewDo.getProperties().add("priorServiceLen", priorServiceLen.toString());
        workOverviewDo.setWorkAge(priorServiceLen.add(Objects.isNull(empWorkInfo) || Objects.isNull(empWorkInfo.getDivisionAge()) ? BigDecimal.ZERO : empWorkInfo.getDivisionAge()).setScale(1, RoundingMode.HALF_UP).toString());
    }

    /**
     * 获取计算器类型
     *
     * @return 计算器类型
     */
    @Override
    public String type() {
        return "priorServiceLen";
    }

    /**
     * 计算加入本司前工龄
     * 工龄 = 每段工作经历时长之和 / 365，四舍五入1位小数
     *
     * @param workExperiences 工作经历列表
     * @return 加入本司前工龄（单位：年，保留1位小数）
     */
    private BigDecimal calculatePriorServiceLen(List<WorkExperienceDo> workExperiences) {
        if (workExperiences == null || workExperiences.isEmpty()) {
            return BigDecimal.ZERO;
        }
        // 计算所有工作经历的总天数
        long totalDays = 0;
        for (WorkExperienceDo experience : workExperiences) {
            // 确保开始日期和结束日期都不为空
            if (experience.getStartDate() != null && experience.getEndDate() != null) {
                // 计算每段工作经历的天数（结束日期 - 开始日期 + 1天）
                long days = (experience.getEndDate() - experience.getStartDate()) / (24 * 60 * 60 * 1000) + 1;
                if (days > 0) {
                    totalDays += days;
                }
            }
        }
        // 将总天数转换为年数，四舍五入到1位小数
        if (totalDays > 0) {
            return new BigDecimal(totalDays).divide(DAYS_PER_YEAR, 1, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }
} 