package com.caidaocloud.hr.service.employee.application.emp.ruleset.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.dto.ruleset.EmpRuleSetDto;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.EmpRuleSetDomainService;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/2/17
 */
@Slf4j
@Service
public class EmpRuleSetService {
    @Resource
    private EmpRuleSetDomainService empRuleSetDomainService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Resource
    private Locker locker;
    /**
     * 自动生成工号redis key
     */
    private final String GENERATE_WORKNO_CACHE_KEY = "ONBOARDING_GENERATE_WORKNO_START_NUM";

    public void save(EmpRuleSetDto data) {
        var preRule = getRuleSet();
        empRuleSetDomainService.save(data);
        /*var lockKey = String.format("AUTO_GENERATE_WORKNO_KEY_%s", SecurityUserUtil.getSecurityUserInfo().getTenantId());
        var lock = locker.getLock(lockKey);
        try {
            try {
                var b = lock.tryLock(120, TimeUnit.SECONDS);
                if (b) {
                    handleWhetherClearCache(preRule, data);
                }
            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        } catch (InterruptedException e) {
            log.error("occur error get lock key", e);
            throw new ServerException(e.getMessage());
        }*/
    }

    /**
     * 处理是否需要清理缓存
     *
     * @param data
     */
    private void handleWhetherClearCache(EmpRuleSetDto preRule, EmpRuleSetDto currRule) {
        if (preRule == null || currRule == null) {
            log.warn("parameter is empty, preRule={} currRule={}", FastjsonUtil.toJson(preRule), FastjsonUtil.toJson(currRule));
            return;
        }
        var isClear = true;
        if (preRule != null && currRule != null) {
            var isEqStartValue = (preRule.getWorknoStartValue() != null && currRule.getWorknoStartValue() != null &&
                    preRule.getWorknoStartValue().longValue() == currRule.getWorknoStartValue().longValue()) ||
                    ((preRule.getWorknoStartValue() == null && currRule.getWorknoStartValue() != null) || (preRule.getWorknoStartValue() != null && currRule.getWorknoStartValue() == null));
            var isEqWorknoLength = (preRule.getWorknoLength() != null && currRule.getWorknoLength() != null &&
                    preRule.getWorknoLength().intValue() == currRule.getWorknoLength().intValue()) ||
                    ((preRule.getWorknoLength() == null && currRule.getWorknoLength() != null) || (preRule.getWorknoLength() != null && currRule.getWorknoLength() == null));
            isClear = !(isEqStartValue && isEqWorknoLength);
        }
        if (!isClear) {
            log.warn("handle clearing build workno cache, isClear={}", isClear);
            return;
        }
        var isExist = redisTemplate.hasKey(GENERATE_WORKNO_CACHE_KEY);
        var securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        var hashOperation = redisTemplate.opsForHash();
        if (isExist && hashOperation.hasKey(GENERATE_WORKNO_CACHE_KEY, securityUserInfo.getTenantId())) {
            hashOperation.delete(GENERATE_WORKNO_CACHE_KEY, securityUserInfo.getTenantId());
        }
    }

    public EmpRuleSetDto getRuleSet() {
        return empRuleSetDomainService.getRuleSet();
    }
}
