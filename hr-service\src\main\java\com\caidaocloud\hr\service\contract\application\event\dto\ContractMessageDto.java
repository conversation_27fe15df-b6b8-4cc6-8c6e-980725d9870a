package com.caidaocloud.hr.service.contract.application.event.dto;

import java.util.Objects;

import com.caidaocloud.hr.service.enums.contract.ContractEventType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import lombok.Data;

@Data
public class ContractMessageDto {
    private Long userId;

    private String tenantId;
    /**
     * 合同ID
     */
    private String contract;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;

    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;

    /**
     * 合同类别
     */
    private DictSimple contractType;

    /**
     * 签订类型
     */
    private EnumSimple signType;

    /**
     * 审批状态
     */
    private EnumSimple approvalStatus;

    /**
     * 合同签署人
     */
    private EmpSimple emp;

    /**
     * 手机号码
     */
    private PhoneSimple phone;

    /**
     * 公司邮箱
     */
    private String companyEmail;

    /**
     * 员工类型
     */
    private DictSimple empType;

    /**
     * 任职组织
     */
    private String organize;

    /**
     * 任职组织名称
     */
    private String organizeTxt;

    /**
     * 职务
     */
    private String job;

    /**
     * 职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 合同公司
     */
    private String company;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 国籍
     */
    private DictSimple nationality;

    /**
     * 是否残疾
     */
    private Boolean disability;

    /**
     * 监护人姓名
     */
    private String guardianName;

    /**
     * 监护人手机
     */
    private PhoneSimple guardianPhone;

    /**
     * 监护人邮箱
     */
    private String guardianEmail;

    /**
     * 工作地ID
     */
    private String workplace;

    /**
     * 工作地名称
     */
    private String workplaceTxt;

    /**
     * 工时制
     */
    private EnumSimple workHour;

    private long createTime;
    private String createBy;
    private long updateTime;
    private String updateBy;

    private ContractEventType eventType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractMessageDto that = (ContractMessageDto) o;
        return createTime == that.createTime && updateTime == that.updateTime && Objects.equals(userId, that.userId) && Objects.equals(tenantId, that.tenantId) && Objects.equals(contract, that.contract) && Objects.equals(contractNo, that.contractNo) && Objects.equals(contractTypeSetTxt, that.contractTypeSetTxt) && Objects.equals(contractSettingType, that.contractSettingType) && Objects.equals(contractType, that.contractType) && Objects.equals(signType, that.signType) && Objects.equals(approvalStatus, that.approvalStatus) && Objects.equals(emp, that.emp) && Objects.equals(phone, that.phone) && Objects.equals(companyEmail, that.companyEmail) && Objects.equals(empType, that.empType) && Objects.equals(organize, that.organize) && Objects.equals(organizeTxt, that.organizeTxt) && Objects.equals(job, that.job) && Objects.equals(jobTxt, that.jobTxt) && Objects.equals(post, that.post) && Objects.equals(postTxt, that.postTxt) && Objects.equals(company, that.company) && Objects.equals(companyTxt, that.companyTxt) && Objects.equals(nationality, that.nationality) && Objects.equals(disability, that.disability) && Objects.equals(guardianName, that.guardianName) && Objects.equals(guardianPhone, that.guardianPhone) && Objects.equals(guardianEmail, that.guardianEmail) && Objects.equals(workplace, that.workplace) && Objects.equals(workplaceTxt, that.workplaceTxt) && Objects.equals(workHour, that.workHour) && Objects.equals(createBy, that.createBy) && Objects.equals(updateBy, that.updateBy) && eventType == that.eventType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, tenantId, contract, contractNo, contractTypeSetTxt, contractSettingType, contractType, signType, approvalStatus, emp, phone, companyEmail, empType, organize, organizeTxt, job, jobTxt, post, postTxt, company, companyTxt, nationality, disability, guardianName, guardianPhone, guardianEmail, workplace, workplaceTxt, workHour, createTime, createBy, updateTime, updateBy, eventType);
    }
}
