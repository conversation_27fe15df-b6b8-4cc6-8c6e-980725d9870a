package com.caidaocloud.hr.service.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class WorkOverviewVo {
    @ApiModelProperty("工作概况ID")
    private String bid;
    /**
     * 员工ID
     */
    private String empId;

    @ApiModelProperty("首次工作日期")
    private Long firstWorkDate;

    @ApiModelProperty("工龄")
    private String workAge;

    @ApiModelProperty("工龄调整")
    private Float workAgeAdjust;

    @ApiModelProperty("是否连续工作")
    private EnumSimple continuousWorking;

    @ApiModelProperty("入职前最长连续工作月数")
    private Float continuousWorkingMonth;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
