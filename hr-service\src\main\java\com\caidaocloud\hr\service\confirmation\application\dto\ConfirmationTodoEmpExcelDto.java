package com.caidaocloud.hr.service.confirmation.application.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ExcelTarget("EmpConfirmationTodo")
public class ConfirmationTodoEmpExcelDto implements Serializable {

    @Excel(name = "工号")
    private String workno;
    @Excel(name = "姓名")
    private String name;
    @Excel(name = "入职日期", format = "yyyy-MM-dd")
    private Date hireDate;
    @Excel(name = "试用期截止日期", format = "yyyy-MM-dd")
    private Date probationPeriodEndDate;
    @Excel(name = "试用期到期天数")
    private Long probationPeriodDay;
    @Excel(name = "任职组织")
    private String organizeTxt;
    @Excel(name = "岗位")
    private String postTxt;
    @Excel(name = "用工类型")
    private String empTypeName;
    @Excel(name = "直接上级")
    private String leaderEmpName;
}
