package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工兼岗信息DTO")
public class EmpConcurrentPostDto {
    @ApiModelProperty("员工兼岗信息ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("兼岗组织")
    private String organize;

    @ApiModelProperty("兼岗组织名称")
    private String organizeTxt;

    @ApiModelProperty("兼任职务")
    private String job;

    @ApiModelProperty("兼任职务名称")
    private String jobTxt;

    @ApiModelProperty("兼任岗位")
    private String post;

    @ApiModelProperty("兼任岗位名称")
    private String postTxt;

    @ApiModelProperty("兼岗上级")
    private EmpSimple postSuperior;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("兼岗开始日期")
    private long startDate;

    @ApiModelProperty("兼岗结束日期")
    private long endDate;

    @ApiModelProperty("状态（0 兼岗中 1 兼岗结束 2 未生效）")
    private Integer status;

    @ApiModelProperty("兼岗上级组织ID")
    private String leaderOrganize;

    @ApiModelProperty("兼岗上级组织名称")
    private String leaderOrganizeTxt;

    @ApiModelProperty("兼岗上级岗位ID")
    private String leaderPost;

    @ApiModelProperty("兼岗上级岗位名称")
    private String leaderPostTxt;

    @ApiModelProperty("关联属性时间轴")
    private Long dataStartTime;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
