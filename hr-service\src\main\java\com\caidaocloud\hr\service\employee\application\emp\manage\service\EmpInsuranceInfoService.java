package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.dto.EmpInsuranceInfoDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInsuranceInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpInsuranceInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpInsuranceInfoVo;
import com.caidaocloud.hr.service.organization.application.company.service.PayUnitService;
import com.caidaocloud.hr.service.organization.domain.company.entity.PayUnitDo;
import com.caidaocloud.hr.service.transfer.application.feign.PayeignClient;
import com.caidaocloud.hr.service.transfer.application.feign.impl.PayFeignClientImpl;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
@Service
public class EmpInsuranceInfoService {
    @Resource
    private EmpInsuranceInfoDomainService empInsuranceInfoDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private PayUnitService payUnitService;
    @Resource
    private PayeignClient payeignClient;

    public void save(EmpInsuranceInfoDto dto) {
        EmpInsuranceInfoDo data = ObjectConverter.convert(dto, EmpInsuranceInfoDo.class);
        empInsuranceInfoDomainService.save(data);
    }


    public void update(EmpInsuranceInfoDto dto) {
        EmpInsuranceInfoDo data = ObjectConverter.convert(dto, EmpInsuranceInfoDo.class);
        if (StringUtil.isNotEmpty(data.getUnitBid())) {
            PayUnitDo payUnit = payUnitService.getPayUnitById(data.getUnitBid());
            data.setUnitName(payUnit!=null?payUnit.getUnitName():null);
        }
        BasePage page = new BasePage();
        page.setPageNo(0);
        if (StringUtil.isNotEmpty(data.getSocialPlanId())) {
            Result result = payeignClient.getSocialSecurityPolicies(page);
            if (result.getData() != null) {
                Map map = (Map) result.getData();
                List items = (List) map.get("items");
                if (items != null && items.size() > 0) {
                    for (Object o : items) {
                        Map o1 = (Map) o;
                        if (o1.get("ins_policy_id").equals(data.getSocialPlanId())) {
                            data.setSocialPlanName((String) o1.getOrDefault("ins_name",""));
                        }
                    }
                }
            }
        }
        if (StringUtil.isNotEmpty(data.getProvidentPlanId())) {
            Result result = payeignClient.getHfPlicyList(page);
            if (result.getData() != null) {
                Map map = (Map) result.getData();
                List items = (List) map.get("items");
                if (items != null && items.size() > 0) {
                    for (Object o : items) {
                        Map o1 = (Map) o;
                        if (o1.get("hf_policy_id").equals(data.getProvidentPlanId())) {
                            data.setProvidentPlanName((String) o1.getOrDefault("hf_name",""));
                        }
                    }
                }
            }
        }
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empInsuranceInfoDomainService.update(data);
    }

    public EmpInsuranceInfoDo getEmpInsuranceInfo(String empId) {
        return empInsuranceInfoDomainService.getInsuranceInfo(empId);
    }

    public EmpInsuranceInfoVo getDetail(String empId) {
        EmpInsuranceInfoDo data = getEmpInsuranceInfo(empId);
        if (null == data) {
            return new EmpInsuranceInfoVo();
        }

        EmpInsuranceInfoVo vo = ObjectConverter.convert(data, EmpInsuranceInfoVo.class);
        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
        vo.setExt(ext);
        return vo;
    }

    public Boolean hasEmpByPayUnit(String unitId) {
        List<EmpInsuranceInfoDo> insuranceInfos = empInsuranceInfoDomainService.getInsuranceInfosByPayUnitId(unitId);
        if(insuranceInfos != null && insuranceInfos.size() > 0) return true;
        return false;
    }
}
