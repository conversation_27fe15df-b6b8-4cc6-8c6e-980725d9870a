package com.caidaocloud.hr.service.employee.domain.workExperience.service.impl;

import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkOverviewDo;
import com.caidaocloud.hr.service.common.infrastructure.manager.cal.IPriorCalculator;
import com.caidaocloud.util.DateUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Service
public class DefaultWorkOverviewPriorCalculatorImpl implements IPriorCalculator<WorkOverviewDo> {
    private final float DEFAULT_ADJUST = 0F;

    @Override
    public void cal(WorkOverviewDo workOverviewDo) {
        if (Objects.isNull(workOverviewDo) || null == workOverviewDo.getFirstWorkDate()) {
            return;
        }
        long monthDiff = getMonthDiff(workOverviewDo.getFirstWorkDate(), DateUtil.getCurrentTimestamp());
        BigDecimal divide = new BigDecimal(monthDiff).divide(new BigDecimal("12"), 9, BigDecimal.ROUND_HALF_UP);
        divide = divide.add(BigDecimal.valueOf(Optional.ofNullable(workOverviewDo.getWorkAgeAdjust()).orElse(DEFAULT_ADJUST))).setScale(1, BigDecimal.ROUND_HALF_UP);
        workOverviewDo.setWorkAge(divide + "年");
    }

    private long getMonthDiff(long startDate, long endDate) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date firstWorkTime = new Date(startDate);
        Date todayTimestamp = new Date(endDate);
        String format1 = sf.format(firstWorkTime);
        String format2 = sf.format(todayTimestamp);
        return ChronoUnit.MONTHS.between(LocalDate.parse(format1), LocalDate.parse(format2));
    }

    @Override
    public String type() {
        return "defaultWorkOverview";
    }
}