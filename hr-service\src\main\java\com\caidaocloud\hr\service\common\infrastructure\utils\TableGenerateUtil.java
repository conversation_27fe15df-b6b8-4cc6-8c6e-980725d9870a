package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.util.FastjsonUtil;
import lombok.Getter;

import java.util.List;

/**
 * 表格内容解析生成
 * created by: FoAng
 * create time: 8/6/2023 2:08 下午
 */
public class TableGenerateUtil {

    public static final String symbol = "#";

    @Getter
    public enum TableStyle {

        /*转正人员汇总*/
        CONFIRM_SUMMARY(1, "姓名-工号-任职组织-岗位-试用期到期天数-试用期截止日期"),
        // 合同到期汇总
        CONTRACT_EXPIRE_SUMMARY(2, "姓名-工号-任职组织-岗位-合同到期日期"),
        CONTRACT_EXPIRE_SUMMARY_EN(3, "Name at Birth-Personnel Number-Organization Unit-Position(EN)-Contract End Date");

        int type;
        String header;

        TableStyle(int type, String header) {
            this.type = type;
            this.header = header;
        }
    }

    public static String buildHtmlContent(TableStyle style, List<String> data) {
        String headerStr = style.getHeader();
        String[] headerData = headerStr.split("-");
        final int columnCount = headerData.length;
        StringBuilder builder = new StringBuilder();
        builder.append("<table style=\"text-align: center; border-color:#b6ff00; border-collapse: collapse; border: 1px solid black;\">");
        // 添加表头
        builder.append("<tr>");
        for (String headerDatum : headerData) {
            builder.append("<th style=\"border: 1px solid black;\">")
                    .append(headerDatum)
                    .append("</th>");
        }
        builder.append("</tr>");
        // 添加表格内容
        builder.append("<tbody>");
        for (String rowStr : data) {
            builder.append("<tr>");
            String[] rowData = rowStr.split(symbol);
            for (int column = 0; column < columnCount; column++) {
                builder.append("<td style=\"border: 1px solid black;\">")
                        .append(rowData[column])
                        .append("</td>");
            }
            builder.append("</tr>");
        }
        builder.append("</tbody>");
        builder.append("</table>");
        return builder.toString();
    }
}
