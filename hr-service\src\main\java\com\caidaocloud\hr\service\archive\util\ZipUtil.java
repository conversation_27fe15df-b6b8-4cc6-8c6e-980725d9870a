package com.caidaocloud.hr.service.archive.util;

import com.aliyun.oss.common.utils.HttpHeaders;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Slf4j
public class ZipUtil {
    public static byte[] zipAndDownload(Map<String, List<ZipData>> map) throws IOException {
        try (ByteArrayOutputStream bas = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(bas)) {

            for (Map.Entry<String, List<ZipData>> mapEntry : map.entrySet()) {
                if (CollectionUtils.isNotEmpty(mapEntry.getValue())) {
                    zos.putNextEntry(new ZipEntry(mapEntry.getKey() + "/"));
                    for (ZipData zipData : mapEntry.getValue()) {
                        if (zipData.getInputStream() == null) {
                            continue;
                        }
                        ZipEntry entry = new ZipEntry(mapEntry.getKey() + "/" + zipData.getFileName());
                        zos.putNextEntry(entry);

                        // 将InputStream的内容写入ZIP条目
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zipData.getInputStream().read(buffer)) >= 0) {
                            zos.write(buffer, 0, length);
                        }
                        zos.closeEntry();
                    }
                }
            }
            return bas.toByteArray();
        } finally {
            // 确保所有流都被关闭
            for (Map.Entry<String, List<ZipData>> entry : map.entrySet()) {
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    for (ZipData zipData : entry.getValue()) {
                        if (zipData.getInputStream() != null) {
                            try {
                                zipData.getInputStream().close();
                            } catch (IOException e) {
                                log.error("InputStream close error:{},{}", e.getMessage(), e);
                            }
                        }
                    }
                }
            }
        }
    }

    public static void zipAndDownload(InputStream[] inputStreams, String[] entryNames, HttpServletResponse response, String zipName) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            // 设置HTTP响应头，
            setZipResponse(zipName, response);
            for (int i = 0; i < inputStreams.length; i++) {
                try (InputStream is = inputStreams[i]) {
                    // 创建ZIP条目
                    ZipEntry entry = new ZipEntry(entryNames[i]);
                    zos.putNextEntry(entry);

                    // 将InputStream的内容写入ZIP条目
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = is.read(buffer)) >= 0) {
                        zos.write(buffer, 0, length);
                    }
                    zos.closeEntry();
                }
            }
            // 完成ZIP文件写入
            zos.finish();
        } finally {
            // 确保所有流都被关闭
            for (InputStream is : inputStreams) {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                        log.error("zip create error:{},{}", e.getMessage(), e);
                    }
                }
            }
        }
    }

    public static void downloadZipByInputStream(InputStream inputStream, String zipName, HttpServletResponse response) throws IOException {
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            setZipResponse(zipName, response);
            try (ZipInputStream zipIn = new ZipInputStream(inputStream)) {
                ZipEntry zipEntry;
                while ((zipEntry = zipIn.getNextEntry()) != null) {
                    zipOut.putNextEntry(new ZipEntry(zipEntry.getName()));
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = zipIn.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, length);
                    }
                    zipIn.closeEntry();
                }
            }
            zipOut.finish();
        }
    }

    private static void setZipResponse(String zipName, HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/zip");
        String fileName = URLEncoder.encode(zipName, String.valueOf(StandardCharsets.UTF_8));
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
    }
}
