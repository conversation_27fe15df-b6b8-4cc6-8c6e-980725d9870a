package com.caidaocloud.hr.service.employee.application.emp.dto.resume;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ResumeEmpMergeConfig {
    private Map<String, List<Map<String, String>>> config;
    //排序字段，跟map的key保持一致
    private SortField mainSortField;
    //如果mainSortField排序完对应的字段的值有相同，需根据对相同的值再当前队列中secondSortList做二次排序
    private SortFieldVal secondSortList;
    private List<String> notBlankFiledList;

    @Data
    public static class SortField {
        private String field;
        private String dataType;
    }

    @Data
    public static class SortFieldVal {
        private String field;
        private List<FieldValSort> sortList;
    }

    @Data
    public static class FieldValSort {
        private int sort;
        private String val;
    }
}