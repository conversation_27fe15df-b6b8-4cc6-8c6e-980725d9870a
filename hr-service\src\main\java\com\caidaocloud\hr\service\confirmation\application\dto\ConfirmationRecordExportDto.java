package com.caidaocloud.hr.service.confirmation.application.dto;

import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationStatus;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class ConfirmationRecordExportDto {
    // 工号
    private String workno;
    // 姓名
    private String name;
    // 英文名
    private String enName;
    // 入职日期
    private String hireDate;
    // 试用期截止日期
    private String probationPeriodEndDate;
    // 转正日期
    private String confirmationDate;
    // 转正类型
    private String confirmationType;
    // 所属组织名称
    private String organizeTxt;
    // 岗位名称
    private String postTxt;
    /**
     * 用工类型\员工类型
     */
    private String empType;

    // 直接上级
    private String leaderName;
    // 直接上级
    private String leaderWorkNo;
    // 直接上级
    private String leaderEnName;

    // 审批状态
    private String approvalStatus;

    // 表单字段
    private Map<String, Object> fieldMap;

    public static ConfirmationRecordExportDto convertDto(ConfirmationRecordVo recordVo){
        ConfirmationRecordExportDto exportDto = new ConfirmationRecordExportDto();
        exportDto.setWorkno(recordVo.getWorkno())
            .setEnName(recordVo.getEnName())
            .setName(recordVo.getName())
            .setEmpType(Optional.ofNullable(recordVo.getEmpType()).map(et -> et.getText()).orElse(""))
            .setConfirmationType(Optional.ofNullable(recordVo.getConfirmationType()).map(et -> et.getText()).orElse(""))
            .setLeaderWorkNo(Optional.ofNullable(recordVo.getLeader()).map(leader -> leader.getWorkno()).orElse(""))
            .setLeaderName(Optional.ofNullable(recordVo.getLeader()).map(leader -> leader.getName()).orElse(""))
            .setLeaderEnName(Optional.ofNullable(recordVo.getLeader()).map(leader -> leader.getEnName()).orElse(""))
            .setPostTxt(recordVo.getPostTxt())
            .setOrganizeTxt(recordVo.getOrganizeTxt())
            .setConfirmationDate(Optional.ofNullable(recordVo.getConfirmationDate()).map(cd -> DateUtil.formatDate(cd)).orElse(""))
            .setHireDate(Optional.ofNullable(recordVo.getHireDate()).map(cd -> DateUtil.formatDate(cd)).orElse(""))
            .setProbationPeriodEndDate(Optional.ofNullable(recordVo.getProbationPeriodEndDate()).map(cd -> DateUtil.formatDate(cd)).orElse(""))
            .setApprovalStatus(TerminationStatus.valueOf(recordVo.getApprovalStatus()).name);
        return exportDto;
    }

    public static ConfirmationRecordExportDto map2Dto(Map<String, Object> map, String postTxtShowCode){
        ConfirmationRecordExportDto exportDto = new ConfirmationRecordExportDto();
        exportDto.setWorkno((String) map.get("main$workno"));
        exportDto.setName((String) map.get("main$name"));
        exportDto.setEnName((String) map.get("main$enName"));
        exportDto.setHireDate(convertDate((String) map.get("main$hireDate")));
        // 试用期截止日期
        exportDto.setProbationPeriodEndDate(convertDate((String) map.get("main$probationPeriodEndDate")));
        // 转正日期
        exportDto.setConfirmationDate(convertDate((String) map.get("other$confirmationDate")));
        // 转正类型
        String enumValue = (String) map.get("other$confirmationType");
        exportDto.setConfirmationType(StringUtil.isEmpty(enumValue) ? "" : ConfirmationType.getByName(enumValue).text);
        exportDto.setOrganizeTxt((String) map.get("main$organizeTxt"));

        String postTxt = (String) map.get("main$postTxt");
        if(!"enabled".equals(postTxtShowCode)){
            if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
            }
        }
        exportDto.setPostTxt(postTxt);
        String mainEmpType = (String) map.get("main$empType");
        // 用工类型
        exportDto.setEmpType(DictSimple.doDictSimple(mainEmpType).getText());
        String leaderWorkno = (String) map.get("main$leader_workno"),
            leaderName = (String) map.get("main$leader_name"),
            leaderEnName = (String) map.get("main$leader_enName");
        exportDto.setLeaderWorkNo(Optional.ofNullable(leaderWorkno).orElse(""))
        .setLeaderName(Optional.ofNullable(leaderName).orElse(""))
        .setLeaderEnName(Optional.ofNullable(leaderEnName).orElse(""));
        // 审批状态
        enumValue = (String) map.get("other$approvalStatus");
        exportDto.setApprovalStatus(StringUtil.isEmpty(enumValue) ? "" : TerminationStatus.valueOf(enumValue).name);
        return exportDto;
    }

    private static String convertDate(String dateStr){
        if(StringUtil.isBlank(dateStr)){
            return null;
        }

        return DateUtil.formatDate(Long.valueOf(dateStr));
    }
}
