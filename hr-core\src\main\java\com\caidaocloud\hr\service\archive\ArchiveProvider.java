package com.caidaocloud.hr.service.archive;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 4/6/2024 4:32 下午
 */
@Slf4j
public class ArchiveProvider {

    public static final ConcurrentMap<String, IArchiveProcessor> archiveProcessorMap = new ConcurrentHashMap<>();

    public static final ConcurrentMap<String, Map<String, Integer>> fetchCountMap = new ConcurrentHashMap<>();

    /**
     * 根据业务ID获取关联附件
     * @param businessLine 业务类型
     * @param businessId   业务ID
     */
    public List<ArchiveData> fetchArchive(String businessLine, String businessId) {
        IArchiveProcessor archiveProcessor = archiveProcessorMap.get(businessLine);
        if (archiveProcessor == null) {
            log.error("[archive] unResolve processor with businessLine[{}]", businessLine);
            return Lists.newArrayList();
        }
        return archiveProcessor.fetchArchiveData(businessId);
    }

    /**
     * 初始化获取分页归档文件
     * @param page 分页
     */
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        if (archiveProcessorMap.isEmpty()) {
            log.error("[archive] unResolve processor....");
            return Lists.newArrayList();
        }
        if (page.getPageNo() == 1) cleanFetchCount();
        return archiveProcessorMap.keySet().stream().map(it -> {
            List<ArchiveData> archiveDataList = Lists.newArrayList();
            try {
                SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
                if (userInfo == null || StringUtil.isEmpty(userInfo.getTenantId())) {
                    return archiveDataList;
                }
                String tenantId = userInfo.getTenantId();
                Map<String, Integer> countMap = fetchCountMap.getOrDefault(tenantId, Maps.newHashMap());
                if (countMap.containsKey(it) && countMap.get(it) >= 3) {
                    log.info("[archive] fetch empty count increase 3 count and return, businessLine:{}", it);
                    return archiveDataList;
                } else {
                    IArchiveProcessor processor = archiveProcessorMap.get(it);
                    archiveDataList = processor.fetchPageArchiveData(page);
                    if (CollectionUtils.isEmpty(archiveDataList)) {
                        Integer fetchCount = countMap.getOrDefault(it, 1);
                        countMap.put(it, fetchCount + 1);
                        log.info("[archive] fetch empty count increase count by businessLine:{}", it);
                        fetchCountMap.put(tenantId, countMap);
                    }
                    return archiveDataList;
                }
            } catch (Exception e) {
                log.error("[archive] fetch page data error, businessLine:{}", it);
                return archiveDataList;
            }
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public void cleanFetchCount() {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        if (userInfo != null) {
            String tenantId = userInfo.getTenantId();
            fetchCountMap.remove(tenantId);
        }
    }

    /**
     * 初始化获取归档文件
     */
    public void fetchAllArchiveData() {
        if (archiveProcessorMap.isEmpty()) {
            log.error("[archive] unResolve processor....");
        }
        archiveProcessorMap.values().forEach(it -> {
            try {
                it.fetchAllArchiveData();
            } catch (Exception e) {
                log.error("[archive] fetch all data error, businessLine:{}", it.businessLine());
            }
        });
    }

    /**
     * 同步删除归档附件
     * @param subBusinessLine 子业务线
     * @param businessId 对应业务ID
     * @param archiveFile 删除附件信息
     */
    public void dispatchArchiveDelEvent(String businessLine, String subBusinessLine, String businessId, ArchiveFile archiveFile) {
        ArchiveStandardLine standardLine = ArchiveStandardLine.indexDesc(businessLine);
        if (standardLine == null || archiveProcessorMap.isEmpty() || !archiveProcessorMap.containsKey(standardLine.name())) {
            log.error("[archive] unResolve processor....");
            return;
        }
        try {
            log.info("[archive] dispatch archive del event, subBusinessLine:{}, businessId:{}, file:{}",
                    subBusinessLine, businessId, FastjsonUtil.toJson(archiveFile));
            IArchiveProcessor processor = archiveProcessorMap.get(standardLine.name());
            processor.dispatchArchiveDelEvent(subBusinessLine, businessId, archiveFile);
        } catch (UnsupportedOperationException e) {
            log.error("[archive] not support action");
        }
    }
}
