package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterBatchRecord;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
public class ContinueLetterBatchExportPo  {
	@Excel(name = "工号", orderNum = "0")
	private String workno;

	@Excel(name = "姓名", orderNum = "1")
	private String name;

	@Excel(name = "入职日期", format = "yyyy-MM-dd", orderNum = "2")
	private String hireDate;

	@Excel(name = "员工状态", orderNum = "3")
	private String empStatus;

	@Excel(name = "任职组织", orderNum = "4")
	private String organizeTxt;

	@Excel(name = "职务", orderNum = "5")
	private String postTxt;

	@Excel(name = "岗位", orderNum = "6")
	private String jobTxt;

	@Excel(name = "用工类型", orderNum = "7")
	private String empType;

	@Excel(name = "合同编号", orderNum = "8")
	private String contractNo;

	@Excel(name = "合同公司", orderNum = "9")
	private String companyTxt;

	@Excel(name = "合同类型", orderNum = "10")
	private String contractTypeSetTxt;

	@Excel(name = "合同期限", orderNum = "11")
	private String periodType;

	@Excel(name = "合同开始日期", format = "yyyy-MM-dd", orderNum = "12")
	private String startDate;

	@Excel(name = "合同结束日期", format = "yyyy-MM-dd", orderNum = "13")
	private String endDate;

	@Excel(name = "合同到期天数", orderNum = "14")
	private String contractDays;

	@Excel(name = "合同状态", orderNum = "15")
	private String contractStatus;

	@Excel(name = "合同签订次数", orderNum = "16")
	private Integer signTime;
}
