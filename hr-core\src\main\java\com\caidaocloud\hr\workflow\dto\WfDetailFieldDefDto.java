package com.caidaocloud.hr.workflow.dto;

import com.caidaocloud.hr.workflow.enums.WfDetailFieldTypeEnum;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 工作流-审批中心-去处理、业务列表-详情 数据结构定义DTO
 *
 * <AUTHOR>
 * @Date 2022/11/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class WfDetailFieldDefDto {
    /**
     * 字段属性名
     */
    private String key;

    /**
     * 字段标签名称
     */
    private String text;

    /**
     * 字段数据
     * type=FILE,value=WfAttachmentDto或List<WfAttachmentDto>
     * type=EISGN_FILE,value=WfEsignDefDto
     * type=FORM,value=WfFormDefDto
     */
    private Object value;

    /**
     * 字段类型
     */
    private WfDetailFieldTypeEnum type;

    /**
     * 数据模型中的数据类型
     */
    private PropertyDataType dataType;

    public WfDetailFieldDefDto(String key, String text, WfDetailFieldTypeEnum type) {
        this.key = key;
        this.text = text;
        this.type = type;
    }

    public WfDetailFieldDefDto(String key, String text, Object value, WfDetailFieldTypeEnum type) {
        this.key = key;
        this.text = text;
        this.value = value;
        this.type = type;
    }
}
