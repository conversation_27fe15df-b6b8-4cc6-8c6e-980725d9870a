package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工其他合同信息DTO")
public class EmpOtherContractDto {
    @ApiModelProperty("员工其他合同ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("是否签署竞业协议")
    private Boolean signAgreement;

    @ApiModelProperty("竞业协议开始日期")
    private Long startDate;

    @ApiModelProperty("竞业协议结束日期")
    private Long endDate;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
