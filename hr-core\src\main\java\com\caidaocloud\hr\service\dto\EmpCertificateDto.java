package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工证书技能DTO")
public class EmpCertificateDto {
    @ApiModelProperty("员工证书技能ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("技能名称")
    private String skill;

    @ApiModelProperty("证书名称")
    private String certificate;

    @ApiModelProperty("熟练程度")
    private String proficiency;

    @ApiModelProperty("证书级别")
    private String certificateLevel;

    @ApiModelProperty("获得日期")
    private Long acquireDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
