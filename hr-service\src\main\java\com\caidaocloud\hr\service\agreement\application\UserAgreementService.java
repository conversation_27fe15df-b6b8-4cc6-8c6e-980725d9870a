package com.caidaocloud.hr.service.agreement.application;

import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.agreement.domain.entity.UserAgreementDo;
import com.caidaocloud.hr.service.agreement.domain.service.UserAgreementDomainService;
import com.caidaocloud.hr.service.agreement.interfaces.dto.UserAgreementDto;
import com.caidaocloud.hr.service.agreement.interfaces.vo.UserAgreementVo;
import com.caidaocloud.hr.service.enums.AgreementTypeEnum;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 14/10/2024 1:55 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserAgreementService {

    private UserAgreementDomainService userAgreementDomainService;

    public PageResult<UserAgreementVo> getUserAgreementVoList(BasePage page) {
        PageResult<UserAgreementDo> pageResult = userAgreementDomainService.getSummaryList(page);
        List<UserAgreementVo> agreementVos = pageResult.getItems().stream().map(this::convertAgreementVo).collect(Collectors.toList());
        return new PageResult<>(agreementVos, pageResult.getPageNo(), page.getPageSize(), page.getTotal());
    }

    public void saveUpdate(UserAgreementDto dto) {
        UserAgreementDo userAgreementDo = ObjectConverter.convert(dto, UserAgreementDo.class);
        userAgreementDo.setType(dto.getType().name());
        userAgreementDo.setI18Name(FastjsonUtil.toJson(dto.getI18Name()));
        userAgreementDo.setSummary(Optional.of(HtmlUtil.cleanHtmlTag(dto.getContent()))
                .map(it -> it.substring(0, Math.min(100, it.length()))).get());
        if (StringUtil.isEmpty(dto.getBid())) {
            userAgreementDo.setStatus(0);
        }
        userAgreementDomainService.saveOrUpdate(userAgreementDo);
    }

    public UserAgreementDo changeStatus(String bid, Integer status) {
        UserAgreementDo userAgreementDo = userAgreementDomainService.getById(bid);
        userAgreementDo.setStatus(status);
        userAgreementDomainService.updateById(userAgreementDo);
        changeStatusByType(bid, status, userAgreementDo.getType());
        return userAgreementDo;
    }

    public void changeStatusByType(String bid, Integer status, String type) {
        if (status == 0 || status == 2) {
            return;
        }
        userAgreementDomainService.changeStatusByType(bid, type, status + 1);
    }


    public UserAgreementVo detail(String bid) {
        UserAgreementDo userAgreementDo = userAgreementDomainService.getById(bid);
        return Optional.ofNullable(userAgreementDo).map(this::convertAgreementVo).orElseThrow(() -> new ServerException("数据不存在"));
    }

    public UserAgreementVo detailByType(String type) {
        UserAgreementDo agreementDo = userAgreementDomainService.detailByType(type);
        return Optional.ofNullable(agreementDo).map(this::convertAgreementVo).orElse(null);
    }

    private UserAgreementVo convertAgreementVo(UserAgreementDo agreementDo) {
        return ObjectConverter.convert(agreementDo, UserAgreementVo.class, (userAgreementDo, userAgreementVo) -> {
            userAgreementVo.setType(AgreementTypeEnum.of(userAgreementDo.getType()));
            userAgreementVo.setStatus(agreementDo.getStatus().getValue());
            userAgreementVo.setI18Name(FastjsonUtil.toObject(agreementDo.getI18Name(), new TypeReference<Map<String, String>>(){}));
        });
    }

}
