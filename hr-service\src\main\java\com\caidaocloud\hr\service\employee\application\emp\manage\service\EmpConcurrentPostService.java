package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.EmpConcurrentPostDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpConcurrentPostDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpConcurrentPostBatchQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpConcurrentPostQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpNewLeaderOrgQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.PersonnelReportDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.ReportLineDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpOrgSelectVo;
import com.caidaocloud.hr.service.enums.system.PostTypeEnum;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.search.application.service.EmpWorkInfoSearchService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.HR_ERROR_CODE_80011;
import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.HR_ERROR_CODE_80012;

@Service
@Slf4j
public class EmpConcurrentPostService {
    @Resource
    private EmpConcurrentPostDomainService empConcurrentPostDomainService;
    @Resource
    private EmpWorkInfoSearchService empWorkInfoSearchService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private EmpReportLineService empReportLineService;

    public List<EmpConcurrentPostDo> getEmpConcurrentPostList(String empId) {
        return empConcurrentPostDomainService.selectList(empId);
    }

    public List<EmpConcurrentPostDo> getEmpConcurrentPostByLeader(EmpReportLeaderDto empReportLeaderDto) {
        return empConcurrentPostDomainService.getEmpConcurrentPostByLeader(empReportLeaderDto);
    }

    public List<EmpConcurrentPostDo> getListByEmpConcurrentPostLeader(EmpConcurrentPostLeaderDto empConcurrentPostLeaderDto) {
        return empConcurrentPostDomainService.getListByEmpConcurrentPostLeader(empConcurrentPostLeaderDto);
    }

    public PageResult<EmpConcurrentPostDo> getEmpConcurrentPostForPage(EmpConcurrentPostQueryDto empConcurrentPostQueryDto) {
        //caidao - 2020 兼岗分页；
        PageResult<EmpConcurrentPostDo> empConcurrentPostDoPageResult = empConcurrentPostDomainService.selectPage(empConcurrentPostQueryDto);
        return empConcurrentPostDoPageResult;
    }

    public List<EmpConcurrentPostDo> getEmpConcurrentPostListByTime(String empId, Long dateTime) {
        return empConcurrentPostDomainService.selectListByTime(empId, dateTime);
    }

    public List<EmpConcurrentPostDo> selectListWilllostEffective(String tenantId, Long lossEffectiveTime, Long beginTime) {
        return empConcurrentPostDomainService.selectListWilllostEffective(tenantId, lossEffectiveTime, beginTime);
    }

    public void save(EmpConcurrentPostDto concurrentPostDto) {
        EmpConcurrentPostDo data = ObjectConverter.convert(concurrentPostDto, EmpConcurrentPostDo.class);
        convertStatus(concurrentPostDto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), concurrentPostDto.getExt(), data);
        empConcurrentPostDomainService.save(data);
        // DEV-14034
        SpringUtil.getBean(EmpReportLineService.class).updateReportLine(Lists.newArrayList(data.getEmpId()));
        empWorkInfoSearchService.dataChangeSyncEs(data.getEmpId(), getEmpConcurrentPostList(data.getEmpId()));
    }

    /**
     * 离职处理兼岗数据；调用时间为 离职日期的+1日
     *
     * @param empId
     */
    public void dealDepartEmp(String empId, Long terminationDate) {
        log.info("处理人员empId：{}兼岗信息 start", empId);
        Long midnightTimestamp = DateUtil.getMidnightTimestamp();
        List<EmpConcurrentPostDo> concurrentPostListByTime = getEmpConcurrentPostListByTime(empId, midnightTimestamp);
        for (EmpConcurrentPostDo empConcurrentPostDo : concurrentPostListByTime) {
            EnumSimple status = empConcurrentPostDo.getStatus();
            if (ObjectUtil.isNotEmpty(status)) {
                String value = status.getValue();
                if ("0".equals(value)) {
                    // 兼岗结束时间修改为 离职日期；
                    //根据执行时间设定 兼岗结束时间
                    LocalDateTime executionDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(terminationDate), ZoneId.systemDefault());
                    LocalDate executionDate = executionDateTime.toLocalDate();
                    LocalDate endDay = executionDate.plusDays(-1);
                    LocalDateTime time = LocalDateTime.of(endDay, LocalTime.MAX);
                    long endDateTime = time.toEpochSecond(OffsetDateTime.now().getOffset()) * 1000L;
                    EnumSimple newStatus = new EnumSimple();
                    newStatus.setValue(PostTypeEnum.VOIDED.getCode());
                    newStatus.setText(PostTypeEnum.VOIDED.getName());
                    empConcurrentPostDo.setEndDate(endDateTime);
                    empConcurrentPostDo.setStatus(newStatus);
                    empConcurrentPostDomainService.updateById(empConcurrentPostDo);
                } else if ("2".equals(value)) {
                    //未生效 -> 已取消
                    EnumSimple newStatus = new EnumSimple();
                    newStatus.setValue(PostTypeEnum.CANCELED.getCode());
                    newStatus.setText(PostTypeEnum.CANCELED.getName());
                    empConcurrentPostDo.setStatus(newStatus);
                    empConcurrentPostDomainService.updateById(empConcurrentPostDo);
                }
            }
        }

        log.info("处理人员empId：{}兼岗信息 end", empId);
    }

    public void update(EmpConcurrentPostDto dto) {
        postUpdate(preUpdate(dto));
    }

    public void updateById(EmpConcurrentPostDo data) {
        empConcurrentPostDomainService.updateById(data);
    }

    public EmpConcurrentPostDo preUpdate(EmpConcurrentPostDto dto) {
        EmpConcurrentPostDo data = ObjectConverter.convert(dto, EmpConcurrentPostDo.class);
        convertStatus(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return data;
    }

    public void postUpdate(EmpConcurrentPostDo data) {
        empConcurrentPostDomainService.update(data);
        empWorkInfoSearchService.dataChangeSyncEs(data.getEmpId(), getEmpConcurrentPostList(data.getEmpId()));
    }

    private void convertStatus(EmpConcurrentPostDto concurrentPostDto, EmpConcurrentPostDo data) {
        EnumSimple simple = new EnumSimple();
        simple.setValue(null == concurrentPostDto.getStatus() ? null : concurrentPostDto.getStatus().toString());
        data.setStatus(simple);

        PreCheck.preCheckArgument(null == concurrentPostDto.getDataStartTime(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30048));
        data.setDataStartTime(concurrentPostDto.getDataStartTime());
    }

    public EmpConcurrentPostDo getEmpConcurrentPost(String bid, Long dateTime) {
        return empConcurrentPostDomainService.getEmpConcurrentPost(bid, dateTime);
    }

    public List<EmpOrgSelectVo> getOrgByEmp(String empId, Long dataTime) {
        EmpWorkInfoDo emp = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (null == emp || StringUtil.isEmpty(emp.getEmpId())) {
            return Lists.newArrayList();
        }

        OrgDo orgData = orgDomainService.selectById(emp.getOrganize(), dataTime);
        if (null == orgData || null == orgData.getBid()) {
            return Lists.newArrayList();
        }

        List<OrgDo> list = getConcurrentPostOrgByEmp(empId, dataTime);
        EmpOrgSelectVo keyValue = new EmpOrgSelectVo();
        keyValue.setName(orgData.getName());
        keyValue.setMajorPosition(true);
        keyValue.setCode(orgData.getCode());
        keyValue.setValue(orgData.getBid());

        List<EmpOrgSelectVo> collect = list.stream().filter(org -> !org.getBid().contains(keyValue.getValue())).map(org -> {
            EmpOrgSelectVo kv = new EmpOrgSelectVo();
            kv.setName(org.getName());
            kv.setMajorPosition(false);
            kv.setCode(org.getCode());
            kv.setValue(org.getBid());
            return kv;
        }).collect(Collectors.toList());
        collect.add(keyValue);
        return collect;
    }

    public List<OrgDo> getConcurrentPostOrgByEmp(String empId, Long dataTime) {
        List<EmpConcurrentPostDo> list = empConcurrentPostDomainService.selectList(empId);
        List<String> orgList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, EmpConcurrentPostDo> collect = list.stream().filter(ecpd -> dataTime >= ecpd.getStartDate()
                            && dataTime <= ecpd.getEndDate())
                    .collect(Collectors.toMap(EmpConcurrentPostDo::getOrganize,
                            entity -> entity, (newVal, oldVal) -> newVal));
            orgList.addAll(new HashSet(collect.keySet()));
        }

        return orgDomainService.selectAllByIds(orgList, dataTime);
    }

    public List<EmpOrgSelectVo> getPostByEmp(String empId, String orgId, Long dataTime) {
        List<EmpConcurrentPostDo> list = empConcurrentPostDomainService.selectList(empId);
        List<String> postList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, EmpConcurrentPostDo> collect = list.stream().filter(ecpd -> dataTime >= ecpd.getStartDate()
                            && dataTime <= ecpd.getEndDate() && StringUtil.isNotEmpty(ecpd.getOrganize())
                            && Arrays.asList(ecpd.getOrganize().split(",")).contains(orgId))
                    .collect(Collectors.toMap(EmpConcurrentPostDo::getPost,
                            entity -> entity, (newVal, oldVal) -> newVal));
            postList.addAll(collect.keySet());
        }

        List<PostDo> dataList = postDomainService.selectByIds(postList, dataTime);
        List<EmpOrgSelectVo> orgList = dataList.stream().map(post -> {
            EmpOrgSelectVo kv = new EmpOrgSelectVo();
            kv.setName(post.getName());
            kv.setMajorPosition(false);
            kv.setCode(post.getCode());
            kv.setValue(post.getBid());
            return kv;
        }).collect(Collectors.toList());
        return orgList;
    }

    public List<EmpOrgSelectVo> getNewOrgByEmp(EmpNewLeaderOrgQueryDto dto) {
        Long dataTime = dto.getDataTime();
        String empId = dto.getEmpId();
        EmpWorkInfoDo emp = empWorkInfoDomainService.getEmpWorkInfo(dto.getEmpId(), dataTime);
        if (StringUtil.isEmpty(emp.getEmpId())) {
            return Lists.newArrayList();
        }

        // 替换更新主岗组织
        emp.setOrganize(replaceNewOrg(emp.getOrganize(), dto));

        OrgDo orgData = orgDomainService.selectById(emp.getOrganize(), dataTime);
        if (null == orgData || null == orgData.getBid()) {
            return Lists.newArrayList();
        }

        EmpOrgSelectVo keyValue = new EmpOrgSelectVo();
        keyValue.setName(orgData.getName());
        keyValue.setMajorPosition(true);
        keyValue.setCode(orgData.getCode());
        keyValue.setValue(orgData.getBid());

        List<OrgDo> list = getNewConcurrentOrgByEmp(empId, dataTime, dto);

        List<EmpOrgSelectVo> collect = list.stream().filter(org -> !org.getBid().contains(keyValue.getValue())).map(org -> {
            EmpOrgSelectVo kv = new EmpOrgSelectVo();
            kv.setName(org.getName());
            kv.setMajorPosition(false);
            kv.setCode(org.getCode());
            kv.setValue(org.getBid());
            return kv;
        }).collect(Collectors.toList());
        collect.add(keyValue);
        return collect;
    }

    public List<OrgDo> getNewConcurrentOrgByEmp(String empId, Long dataTime, EmpNewLeaderOrgQueryDto dto) {
        List<EmpConcurrentPostDo> list = empConcurrentPostDomainService.selectList(empId);
        // 更新兼岗结束时间
        replaceNewConcurrentPostOrg(list, dto);

        List<String> orgList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, EmpConcurrentPostDo> collect = list.stream().filter(ecpd -> dataTime >= ecpd.getStartDate()
                            && dataTime <= ecpd.getEndDate())
                    .collect(Collectors.toMap(EmpConcurrentPostDo::getOrganize,
                            entity -> entity, (newVal, oldVal) -> newVal));
            orgList.addAll(new HashSet(collect.keySet()));
        }

        return orgDomainService.selectAllByIds(orgList, dataTime);
    }

    public String replaceNewOrg(String organize, EmpNewLeaderOrgQueryDto dto) {
        if (Objects.isNull(dto.getMajorPost()) || Objects.isNull(dto.getEmpId()) || Objects.isNull(dto.getCurrentOrgId()) || Objects.isNull(dto.getUpdEmpId())) {
            return organize;
        }
        // 主岗，同时更新前组织=当前主岗组织
        if (!dto.getMajorPost() || !dto.getUpdEmpId().equals(dto.getEmpId()) || !dto.getCurrentOrgId().equals(organize)) {
            return organize;
        }
        return dto.getUpdOrgId();
    }

    public void replaceNewConcurrentPostOrg(List<EmpConcurrentPostDo> concurrentPostList, EmpNewLeaderOrgQueryDto dto) {
        if (Objects.isNull(dto.getMajorPost()) || Objects.isNull(dto.getEmpId()) || Objects.isNull(dto.getUpdEmpId()) || Objects.isNull(dto.getConcurrentPostBid())) {
            return;
        }
        if (dto.getMajorPost() || !dto.getUpdEmpId().equals(dto.getEmpId())) {
            return;
        }
        concurrentPostList.forEach(pos -> {
            if (pos.getBid().equals(dto.getConcurrentPostBid())) {
                pos.setEndDate(dto.getUpdEndTime());
            }
        });
    }

    public List<EmpOrgSelectVo> getNewPostByEmp(EmpNewLeaderOrgQueryDto dto) {
        String empId = dto.getEmpId();
        String orgId = dto.getOrgId();
        Long dataTime = dto.getDataTime();
        List<EmpConcurrentPostDo> list = empConcurrentPostDomainService.selectList(empId);
        // 替换更新岗位时间,兼岗岗位不允许编辑
        replaceNewConcurrentPostOrg(list, dto);

        List<String> postList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, EmpConcurrentPostDo> collect = list.stream().filter(ecpd -> dataTime >= ecpd.getStartDate()
                            && dataTime <= ecpd.getEndDate() && StringUtil.isNotEmpty(ecpd.getOrganize())
                            && Arrays.asList(ecpd.getOrganize().split(",")).contains(orgId))
                    .collect(Collectors.toMap(EmpConcurrentPostDo::getPost,
                            entity -> entity, (newVal, oldVal) -> newVal));
            postList.addAll(collect.keySet());
        }

        List<PostDo> dataList = postDomainService.selectByIds(postList, dataTime);

        return dataList.stream().map(post -> {
            EmpOrgSelectVo kv = new EmpOrgSelectVo();
            kv.setName(post.getName());
            kv.setMajorPosition(false);
            kv.setCode(post.getCode());
            kv.setValue(post.getBid());
            return kv;
        }).collect(Collectors.toList());
    }

    public void updateConcurrentPost(ReportLineDto dto) {
        List<PersonnelReportDto> personList = dto.getPersonList();
        Set<String> empList = new HashSet<>();
        if (!CollectionUtils.isEmpty(personList)) {
            empList = personList.stream().map(PersonnelReportDto::getEmpId).collect(Collectors.toSet());
        }
        empList.add(dto.getConcurrentPost().getEmpId());

        // 处理主岗信息
        List<EmpWorkInfoDo> empWorkInfoDos = empReportLineService.disposeEmpWorkInfo(personList, dto.getMajorPost(), dto.getDataTime());

        // 处理兼岗数据
        List<EmpConcurrentPostDo> concurrentPostDos = empReportLineService.disposeConcurrentPost(personList, dto.getMajorPost(), dto.getDataTime());

        // 处理组织信息
        List<OrgDo> orgDoList = empReportLineService.disposeOrgInfo(dto.getOrganizeList(), dto.getMajorPost(), dto.getDataTime());

        EmpConcurrentPostDo ecpData = preUpdate(dto.getConcurrentPost());
        SpringUtil.getBean(EmpConcurrentPostService.class).updatePostAndReportLine(empWorkInfoDos, concurrentPostDos, orgDoList, ecpData, new ArrayList<>(empList));
    }

    @PaasTransactional
    public void updatePostAndReportLine(List<EmpWorkInfoDo> empWorkInfoDos, List<EmpConcurrentPostDo> concurrentPostDos,
                                        List<OrgDo> orgDoList, EmpConcurrentPostDo ecpData, List<String> empList) {
        // 兼岗信息更新
        SpringUtil.getBean(EmpConcurrentPostService.class).postUpdate(ecpData);

        // 更新汇报人员及组织数据
        empReportLineService.updateEmpAndOrg(empWorkInfoDos, concurrentPostDos, orgDoList);

        // 更新汇报线
        empReportLineService.updateReportLine(empList);
    }

    public void delete(String bid) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(bid), "bid不能为空");
        EmpConcurrentPostDo empConcurrentPost = empConcurrentPostDomainService.getEmpConcurrentPost(bid, System.currentTimeMillis());
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empConcurrentPost.getEmpId(), System.currentTimeMillis());
        EmpConcurrentPostLeaderDto empConcurrentPostDto = new EmpConcurrentPostLeaderDto();
        empConcurrentPostDto.setEmpId(empConcurrentPost.getEmpId());
        empConcurrentPostDto.setOrganizeId(empConcurrentPost.getOrganize());
        empConcurrentPostDto.setPostId(empConcurrentPost.getPost());
        empConcurrentPostDto.setStartTime(empConcurrentPost.getStartDate());
        empConcurrentPostDto.setEndTime(empConcurrentPost.getEndDate());

        // 查询是否存在汇报给该员工兼岗组织和岗位的员工数据,组织中的组织负责人为该员工兼岗数据
        List<EmpWorkInfoDo> empWorkList = empWorkInfoDomainService.getEmpWorkInfoByEmpConcurrentPostLeaderDto(empConcurrentPostDto, -1L);
        PreCheck.preCheckArgument(null != empWorkList && !empWorkList.isEmpty(), LangUtil.getFormatMsg(HR_ERROR_CODE_80011, empWorkInfo.getName()));

        List<EmpConcurrentPostDo> concurrentPostDos = getListByEmpConcurrentPostLeader(empConcurrentPostDto);
        PreCheck.preCheckArgument(null != concurrentPostDos && !concurrentPostDos.isEmpty(), LangUtil.getFormatMsg(HR_ERROR_CODE_80011, empWorkInfo.getName()));

        // 查询是否存在组织中的组织负责人为该员工兼岗数据
        List<OrgDo> orgDoList = orgDomainService.getOrgListByEmpConcurrentPostLeader(empConcurrentPostDto, -1L);
        PreCheck.preCheckArgument(null != orgDoList && !orgDoList.isEmpty(), LangUtil.getFormatMsg(HR_ERROR_CODE_80012, empWorkInfo.getName()));

        empConcurrentPostDomainService.delete(bid);
    }

    public List<EmpConcurrentPostDo> getBatchEmpConcurrentPostForPage(EmpConcurrentPostBatchQueryDto empConcurrentPostQueryDto) {
        List<EmpConcurrentPostDo> empConcurrentPostDoPageResult = empConcurrentPostDomainService.selectBatchPage(empConcurrentPostQueryDto);
        return empConcurrentPostDoPageResult;
    }

//    public static void main(String[] args) {
//        Long date = 1701273600000L;
//        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date), ZoneId.systemDefault());
//        LocalDate localDate = localDateTime.toLocalDate();
//        LocalDate endDay = localDate.plusDays(-1);
//        LocalDateTime time = LocalDateTime.of(endDay, LocalTime.MAX);
//        long endDateTime = time.toEpochSecond(OffsetDateTime.now().getOffset()) * 1000L;
//        System.out.println(endDateTime);
//    }
}
