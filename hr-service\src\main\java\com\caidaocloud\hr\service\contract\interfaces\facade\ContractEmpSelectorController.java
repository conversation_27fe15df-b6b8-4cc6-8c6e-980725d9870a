package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.application.service.ContractEmpSelectorService;
import com.caidaocloud.hr.service.contract.interfaces.dto.OrgEmpTreeQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractEmpQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector.EmpNodeVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector.EmpTreeVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2023/8/7
 */

@Slf4j
@RestController
@RequestMapping("/api/hr/contract/v1/emp")
@Api(value = "/api/hr/contract/v1/emp", description = "合同管理-员工选择器", tags = "v1.7")
public class ContractEmpSelectorController {

	@Autowired
	private ContractEmpSelectorService contractEmpSelectorService;

	@PostMapping("/searchPage")
	@ApiOperation("员工选择器-员工搜索")
	public Result<PageResult<EmpNodeVo>> searchPage(@RequestBody ContractEmpQueryDto empSearchDto) {
		return Result.ok(contractEmpSelectorService.searchEmpPage(empSearchDto.getKeyword(),  empSearchDto.getPageNo(), empSearchDto.getPageSize(),false));
	}


	@PostMapping("/searchPage/newly")
	@ApiOperation("员工选择器-员工搜索")
	public Result<PageResult<EmpNodeVo>> searchNewlyPage(@RequestBody ContractEmpQueryDto empSearchDto) {
		return Result.ok(contractEmpSelectorService.searchEmpPage(empSearchDto.getKeyword(), empSearchDto.getPageNo(), empSearchDto.getPageSize(),true));
	}


	@PostMapping("empSelector")
	@ApiOperation("员工选择器")
	public Result<EmpTreeVo> loadOrgTree(@RequestBody OrgEmpTreeQueryDto query) {
		return Result.ok(contractEmpSelectorService.loadSubOrgAndEmp( query.getPageNo(), query.getPageSize(),false));
	}


	@PostMapping("empSelector/newly")
	@ApiOperation("员工选择器")
	public Result<EmpTreeVo> loadNewlyOrgTree(@RequestBody OrgEmpTreeQueryDto query) {
		return Result.ok(contractEmpSelectorService.loadSubOrgAndEmp(query.getPageNo(), query.getPageSize(),true));
	}

}
