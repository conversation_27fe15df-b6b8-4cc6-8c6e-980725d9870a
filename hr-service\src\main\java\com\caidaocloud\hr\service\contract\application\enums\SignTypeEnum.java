package com.caidaocloud.hr.service.contract.application.enums;

/**
 * 配合前端开关控件，转换合同设置中的合同期限
 */
public enum SignTypeEnum {
    RENEW("0", "续签"),
    CHANGE("1", "改签"),
    NEW("2", "新签"),
    ;

    private final String code;
    private final String name;

    SignTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (SignTypeEnum e : SignTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.name;
            }
        }
        return null;
    }

    public static SignTypeEnum getByCode(String code) {
        for (SignTypeEnum e : SignTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}
