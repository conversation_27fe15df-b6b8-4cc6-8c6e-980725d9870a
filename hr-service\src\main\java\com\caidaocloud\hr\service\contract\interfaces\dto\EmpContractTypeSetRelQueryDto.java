package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工与合同设置关系查询DTO")
public class EmpContractTypeSetRelQueryDto extends BasePage {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("合同类型ID")
    private String contractTypeSet;

    @ApiModelProperty("合同类型名称")
    private String contractTypeSetTxt;
}
