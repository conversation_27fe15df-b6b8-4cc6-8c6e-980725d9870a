package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工成本中心DTO")
public class EmpCostCenterDto {
    @ApiModelProperty("成本中心ID")
    private String cost;
    @ApiModelProperty("成本中心名称")
    private String costTxt;
    @ApiModelProperty("分摊比例")
    private Float proportionScale;
}
