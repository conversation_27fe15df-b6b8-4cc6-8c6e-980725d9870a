package com.caidaocloud.hr.service.contract.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工信息")
public class ContractEmpTreeNodeVo {
    @ApiModelProperty("人员图像")
    private String avatar;
    @ApiModelProperty("所属组织ID")
    private String deptId;
    /**
     * 所属组织
     */
    @ApiModelProperty("所属组织")
    private String deptDesc;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("员工英文名")
    private String enName;
    @ApiModelProperty("员工ID")
    private String empId;
}
