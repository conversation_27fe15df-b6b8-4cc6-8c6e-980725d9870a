package com.caidaocloud.hr.service.confirmation.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationService;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationApplyApprovalDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationTodoEmpDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationTodoQueryDto;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationImportService;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationApplyDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationImportDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRevokeDto;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationTemplateDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationApplyVo;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationTemplateDataVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/confirmation")
@Api(value = "/api/hr/v1/confirmation", description = "转正申请")
public class ConfirmationController {

    @Autowired
    private ConfirmationService confirmationService;
    @Autowired
    private ConfirmationImportService confirmationImportService;

    @PostMapping("/todo")
    public Result<PageResult<ConfirmationTodoEmpDto>> page(@RequestBody ConfirmationTodoQueryDto query){
        return Result.ok(confirmationService.page(query));
    }

    @PostMapping("/todo/export")
    public void exportTodo(@RequestBody ConfirmationTodoQueryDto query, HttpServletResponse response){
        confirmationService.exportTodo(query, response);
    }

    @PostMapping("/apply")
    @ApiOperation("转正申请")
    public Result<?> apply(@RequestBody ConfirmationApplyDto applyDto) {
        confirmationService.saveApply(applyDto);
        return Result.ok();
    }

    @PostMapping("/reapply")
    @ApiOperation("转正重新发起")
    public Result<?> reapply(@RequestBody ConfirmationApplyDto applyDto) {
        confirmationService.reapply(applyDto);
        return Result.ok(true);
    }

    @PostMapping("/import/apply")
    @ApiOperation("转正申请")
    public Result<Boolean> importApply(@RequestBody ConfirmationImportDto applyDto) {
        confirmationImportService.importApply(applyDto);
        return Result.ok(true);
    }


    @ApiOperation("审批同意")
    @PostMapping("/approve")
    public Result<Boolean> approve(@RequestBody ConfirmationApplyApprovalDto dto) {
        confirmationService.approve(dto);
        return Result.ok(true);
    }

    @ApiOperation("审批拒绝")
    @PostMapping("/refuse")
    public Result<Boolean> refuse(@RequestBody ConfirmationApplyApprovalDto dto) {
        confirmationService.refuse(dto);
        return Result.ok(true);
    }

    @PostMapping("/data")
    @ApiOperation("获取转正前数据")
    public Result<ConfirmationTemplateDataVo> getTemplateData(@RequestBody ConfirmationTemplateDto templateDto) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(templateDto.getEmpId()), "获取数据失败，缺失必要参数");
        return Result.ok(confirmationService.getTemplateData(templateDto));
    }

    @GetMapping("/detail")
    @ApiOperation("转正详情")
    public Result<ConfirmationApplyVo> detail(@RequestParam String applyId) {
        return Result.ok(confirmationService.detailVo(applyId));
    }

    @PostMapping("/revoke")
    @ApiOperation("撤销转正申请")
    public Result<?> revoke(@RequestBody ConfirmationRevokeDto dto) {
        confirmationService.revokeApply(dto);
        return Result.ok();
    }

    @GetMapping("/one")
    @ApiOperation("转正数据详情")
    public Result<ConfirmationTemplateDataVo> one(@RequestParam String businessKey) {
        return Result.ok(confirmationService.one(businessKey));
    }
}
