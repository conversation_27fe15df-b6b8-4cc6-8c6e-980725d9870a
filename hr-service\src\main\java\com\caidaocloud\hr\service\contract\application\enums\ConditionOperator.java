package com.caidaocloud.hr.service.contract.application.enums;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.INVALID_PARAMETER;

/**
 * <AUTHOR>
 */
public enum ConditionOperator {
    EQ("EQ", "等于"){
        @Override
       public Object extractValue(Object value) {
            return value;
        }
    },
    NE("NE", "不等于"){
        @Override
        public  Object extractValue(Object value) {
            return value;
        }
    },
    GT("GT", "大于"){
        @Override
        public  Object extractValue(Object value) {
            return value;
        }
    },
    GE("GE", "大于等于"){
        @Override
        public   Object extractValue(Object value) {
            return value;
        }
    },
    LT("LT", "小于") {
        @Override
        public   Object extractValue(Object value) {
            return value;
        }
    },
    LE("LE", "小于等于") {
        @Override
        public  Object extractValue(Object value) {
            return value;
        }
    },
    // CONTAIN("CONTAIN", "包含", false),
    // NOT_CONTAIN("NOT_CONTAIN", "不包含"),
    IN("IN", "包含于") {
        @Override
        public  Object extractValue(Object value) {
            if (value instanceof List) {
                return StringUtils.join((List) value, ",");
            }
            throw new ServerException(LangUtil.getMsg(INVALID_PARAMETER));
        }
    },
    NOT_IN("NOT_IN", "不包含于") {
        @Override
        public  Object extractValue(Object value) {
            if (value instanceof List) {
                return StringUtils.join((List) value, ",");
            }
            throw new ServerException(LangUtil.getMsg(INVALID_PARAMETER));
        }
    },
    CONTAIN_CHILD("CONTAIN_CHILD", "包含子级") {
        @Override
        public  Object extractValue(Object value) {
            if (value instanceof List) {
                return StringUtils.join((List) value, ",");
            }
            throw new ServerException(LangUtil.getMsg(INVALID_PARAMETER));
        }
    };

    private final String name;
    private final String code;


    ConditionOperator(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public abstract Object extractValue(Object value);

    public static List<ConditionOperator> enableOpts(){
        return Arrays.stream(ConditionOperator.values()).collect(Collectors.toList());
    }

    public static List<ConditionOperator> noContainChild(){
        return Arrays.stream(ConditionOperator.values()).filter(co -> !CONTAIN_CHILD.code.equals(co.getCode())).collect(Collectors.toList());
    }

}
