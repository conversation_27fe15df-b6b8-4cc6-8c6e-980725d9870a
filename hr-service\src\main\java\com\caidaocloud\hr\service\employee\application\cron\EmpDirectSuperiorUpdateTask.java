package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工直接上级更新定时任务
 * 根据员工所属组织的部门负责人，更新员工的直接上级
 * 如员工所属组织的部门负责人为员工本人，则需再找到上一级部门的部门负责人，更新员工的直接上级
 */
@Slf4j
@Service
public class EmpDirectSuperiorUpdateTask {

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    @Resource
    private EmpWorkInfoService empWorkInfoService;

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Resource
    private OrgService orgService;

    /**
     * 员工直接上级更新定时任务
     */
    @XxlJob("empDirectSuperiorUpdateJobHandler")
    public ReturnT<String> empDirectSuperiorUpdateJobHandler() {
        XxlJobHelper.log("XxlJob empDirectSuperiorUpdateJobHandler start");
        log.info("cronTask[Emp Direct Superior Update]------------------------start execution,time {}",
                System.currentTimeMillis());

        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);

                updateEmpDirectSuperior();

            } catch (Exception e) {
                log.error("Employee Direct Superior Update Task error, tenantId={}", tenantId, e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });

        log.info("cronTask[Emp Direct Superior Update]------------------------end execution,time {}",
                System.currentTimeMillis());
        XxlJobHelper.log("XxlJob empDirectSuperiorUpdateJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 更新员工直接上级
     */
    public void updateEmpDirectSuperior() {
        long currentTime = System.currentTimeMillis();

        // 获取所有员工列表
        List<EmpWorkInfoDo> allEmpList = empWorkInfoService.getAllEmpList();
        if (CollectionUtils.isEmpty(allEmpList)) {
            log.info("No employees found for direct superior update");
            return;
        }

        log.info("Found {} employees for direct superior update", allEmpList.size());

        int updateCount = 0;
        int errorCount = 0;

        for (EmpWorkInfoDo empWorkInfo : allEmpList) {
            try {
                // 跳过已离职员工
                if (empWorkInfo.ifTermination()) {
                    continue;
                }

                // 查找员工的直接上级
                EmpSimple newDirectSuperior = findDirectSuperior(empWorkInfo, currentTime);

                // 检查是否需要更新
                if (shouldUpdateDirectSuperior(empWorkInfo, newDirectSuperior)) {
                    updateEmployeeDirectSuperior(empWorkInfo, newDirectSuperior, currentTime);
                    updateCount++;
                    log.debug("Updated direct superior for employee: {}, new superior: {}",
                            empWorkInfo.getEmpId(),
                            newDirectSuperior != null ? newDirectSuperior.getEmpId() : "null");
                }

            } catch (Exception e) {
                errorCount++;
                log.error("Failed to update direct superior for employee: {}, error: {}",
                        empWorkInfo.getEmpId(), e.getMessage(), e);
            }
        }

        log.info("Direct superior update completed. Updated: {}, Errors: {}, Total: {}",
                updateCount, errorCount, allEmpList.size());
    }

    /**
     * 查找员工的直接上级
     */
    private EmpSimple findDirectSuperior(EmpWorkInfoDo empWorkInfo, long currentTime) {
        String orgId = empWorkInfo.getOrganize();
        if (StringUtils.isEmpty(orgId)) {
            log.debug("Employee {} has no organization", empWorkInfo.getEmpId());
            return null;
        }

        return findOrgLeaderRecursively(orgId, empWorkInfo.getEmpId(), currentTime);
    }

    /**
     * 递归查找组织负责人
     * 如果组织负责人是员工本人，则查找上级组织的负责人
     */
    private EmpSimple findOrgLeaderRecursively(String orgId, String empId, long currentTime) {
        if (StringUtils.isEmpty(orgId)) {
            return null;
        }

        try {
            // 查询组织信息
            OrgDo orgDo = orgService.getOrgById(orgId, currentTime);
            if (orgDo == null) {
                log.debug("Organization not found: {}", orgId);
                return null;
            }

            // 获取组织负责人
            EmpSimple orgLeader = orgDo.getLeaderEmp();
            if (orgLeader != null && StringUtils.isNotEmpty(orgLeader.getEmpId())) {
                // 如果组织负责人不是员工本人，则返回该负责人
                if (!empId.equals(orgLeader.getEmpId())) {
                    return orgLeader;
                }
            }

            // 如果组织负责人是员工本人或没有负责人，则查找上级组织
            if (orgDo.getPid() != null && StringUtils.isNotEmpty(orgDo.getPid().getPid())) {
                return findOrgLeaderRecursively(orgDo.getPid().getPid(), empId, currentTime);
            }

        } catch (Exception e) {
            log.error("Error finding org leader for orgId: {}, empId: {}", orgId, empId, e);
        }

        return null;
    }

    /**
     * 判断是否需要更新直接上级
     */
    private boolean shouldUpdateDirectSuperior(EmpWorkInfoDo empWorkInfo, EmpSimple newDirectSuperior) {
        EmpSimple currentSuperior = empWorkInfo.getLeadEmpId();

        // 如果当前没有上级，新上级不为空，需要更新
        if (currentSuperior == null || StringUtils.isEmpty(currentSuperior.getEmpId())) {
            return newDirectSuperior != null && StringUtils.isNotEmpty(newDirectSuperior.getEmpId());
        }

        // 如果新上级为空，当前有上级，需要更新
        if (newDirectSuperior == null || StringUtils.isEmpty(newDirectSuperior.getEmpId())) {
            return true;
        }

        // 如果上级发生变化，需要更新
        return !currentSuperior.getEmpId().equals(newDirectSuperior.getEmpId());
    }

    /**
     * 更新员工的直接上级
     */
    private void updateEmployeeDirectSuperior(EmpWorkInfoDo empWorkInfo, EmpSimple newDirectSuperior,
            long currentTime) {
        try {
            // 获取最新的员工工作信息
            EmpWorkInfoDo latestEmpWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empWorkInfo.getEmpId(),
                    currentTime);
            if (latestEmpWorkInfo == null) {
                log.warn("Employee work info not found for empId: {}", empWorkInfo.getEmpId());
                return;
            }

            // 设置新的直接上级
            latestEmpWorkInfo.setLeadEmpId(newDirectSuperior);
            latestEmpWorkInfo.setDataStartTime(currentTime);

            // 更新员工工作信息
            empWorkInfoDomainService.updateEmpWorkInfo(latestEmpWorkInfo, false);

        } catch (Exception e) {
            log.error("Failed to update employee direct superior, empId: {}, newSuperior: {}",
                    empWorkInfo.getEmpId(),
                    newDirectSuperior != null ? newDirectSuperior.getEmpId() : "null", e);
            throw e;
        }
    }
}
