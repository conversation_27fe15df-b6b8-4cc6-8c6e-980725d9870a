package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工奖惩信息DTO")
public class EmpRewardDto {
    @ApiModelProperty("员工奖惩信息id")
    private String bid;

    /**
     * 员工ID
     */
    @ApiModelProperty("员工ID")
    private String empId;

    /**
     * 奖惩名称
     */
    @ApiModelProperty("奖惩名称")
    private String name;

    /**
     * 奖惩类型
     */
    @ApiModelProperty("奖惩类型")
    private String type;

    /**
     * 奖惩时间
     */
    @ApiModelProperty("奖惩时间")
    private Long date;

    /**
     * 奖惩原因
     */
    @ApiModelProperty("奖惩原因")
    private String reason;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();

}
