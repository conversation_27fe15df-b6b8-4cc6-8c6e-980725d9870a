package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractTemplateDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractTemplateRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2023/3/17
 **/
@Repository
public class ContinueContractTemplateRepositoryImpl
        extends BaseRepositoryImpl<ContinueContractTemplateDo>
        implements IContinueContractTemplateRepository {

}