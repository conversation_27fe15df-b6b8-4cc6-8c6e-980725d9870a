package com.caidaocloud.hr.service.agreement.infrastructure.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.agreement.domain.entity.UserAgreementDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * 用户隐私协议
 * created by: FoAng
 * create time: 14/10/2024 1:34 下午
 */
public interface UserAgreementRepository extends BaseRepository<UserAgreementDo> {

    PageResult<UserAgreementDo> getSummaryList(String identifier, BasePage page);

    void changeStatusByType(String identifier, String bid, String type, Integer status);

    UserAgreementDo detailByType(String identifier, String type);
}
