package com.caidaocloud.hr.service.employee.application.emp.fieldset.handler;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.hr.service.handler.ExcelStyleHandler;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

/**
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
public class WraptextHandler extends ExcelStyleHandler {
	public WraptextHandler(Workbook workbook) {
		super(workbook);
	}
	@Override
	public CellStyle getStyles(Cell cell, int i, ExcelExportEntity excelExportEntity, Object obj, Object value) {
		CellStyle cellStyle = super.getStyles(cell, i, excelExportEntity, obj, value);
		cellStyle.setWrapText(excelExportEntity.isWrap());
		return cellStyle;
	}

	@Override
	public CellStyle getStyles(boolean isTemplate, ExcelExportEntity excelExportEntity) {
		CellStyle styles = super.getStyles(isTemplate, excelExportEntity);
		if (excelExportEntity!=null)
		styles.setWrapText(excelExportEntity.isWrap());
		return styles;
	}
}
