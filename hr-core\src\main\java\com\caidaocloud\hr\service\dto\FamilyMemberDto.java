package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class FamilyMemberDto {
    @ApiModelProperty("bid")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("亲属关系")
    private String relation;

    @ApiModelProperty("出生日期")
    private Long birthDay;

    @ApiModelProperty("身份证")
    private String idCard;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
