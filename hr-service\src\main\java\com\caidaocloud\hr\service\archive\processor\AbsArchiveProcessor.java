package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.archive.IArchiveProcessor;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.temination.application.FormService;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import com.caidaocloud.hr.service.temination.application.dto.FormDefMetadataDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 13/6/2024 4:57 下午
 */
@Slf4j
public abstract class AbsArchiveProcessor implements IArchiveProcessor {

    @Resource
    private FormService formService;

    @Resource
    private CacheService cacheService;

    @Resource
    private ArchiveEventProducer archiveEventProducer;

    @Override
    public void fetchAllArchiveData() {
        log.info("[archive] archive all data by msg start....");
        BasePage basePage = new BasePage();
        basePage.setPageNo(1);
        basePage.setPageSize(50);
        List<ArchiveData> archiveDataList = null;
        boolean retry = false;
        int retryCount = 0;
        do {
            log.info("[archive] archive all data by msg at page:{}", basePage.getPageNo());
            try {
                archiveDataList = fetchPageArchiveData(basePage);
                if (CollectionUtils.isNotEmpty(archiveDataList)) {
                    archiveEventProducer.publishArchiveEvent(archiveDataList);
                }
                retryCount = 0;
                basePage.setPageNo(basePage.getPageNo() + 1);
            } catch (Exception e) {
                log.error("[archive] fetch all data error and retry, msg:{}", e.getMessage(), e);
                retryCount += 1;
                retry = true;
            }
        } while (CollectionUtils.isNotEmpty(archiveDataList) || (retry && retryCount <= 3));
        log.info("[archive] archive all data by msg end....");
    }

    protected List<ArchiveData> filterArchiveList(List<ArchiveData> dataList) {
        return dataList.stream().filter(it -> {
            boolean pass = CollectionUtils.isNotEmpty(it.getArchiveFiles());
            if (!pass) {
                log.warn("[archive] filter archive data by file is empty, [{}]", FastjsonUtil.toJson(it));
            }
            return pass;
        }).collect(Collectors.toList());
    }

    protected ArchiveData filterArchiveData(ArchiveData archiveData) {
        return Optional.ofNullable(archiveData).filter(it -> {
            boolean pass = CollectionUtils.isNotEmpty(it.getArchiveFiles());
            if (!pass) {
                log.warn("[archive] filter archive data by file is empty, [{}]", FastjsonUtil.toJson(it));
            }
            return pass;
        }).orElse(null);
    }

    protected ArchiveFile fetchFormFile(String formId, String formDataId) {
        FormDefDto defDto = loadFormDef(formId);
        if (defDto != null && CollectionUtils.isNotEmpty(defDto.getAttachmentPropertyDef())) {
            Map formDataMap = formService.getFormDataMap(formId, formDataId);
            for (FormDefMetadataDto itemFileDef : defDto.getAttachmentPropertyDef()) {
                final String fileName = itemFileDef.getName();
                final Object fileValue = formDataMap.get(itemFileDef.getProperty());
                if (fileValue != null) {
                    try {
                        ArchiveFile file = FastjsonUtil.toObject(FastjsonUtil.toJson(fileValue), ArchiveFile.class);
                        if (CollectionUtils.isNotEmpty(file.getUrls())) {
                            file.setFileName(fileName);
                            return file;
                        }
                    } catch (Exception e) {
                        log.error("[archive] load form data file error:{}", e.getMessage(), e);
                    }
                }
            }
        }
        return null;
    }


    private FormDefDto loadFormDef(String formId) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        final String formDefCacheKey = String.format("archive.form.def.%s.%s", userInfo.getTenantId(), formId);
        return Optional.ofNullable(cacheService.getValue(formDefCacheKey)).map(it -> FastjsonUtil.toObject(it, FormDefDto.class)).orElseGet(() -> {
            FormDefDto defDto = formService.getFormDefById(formId);
            if (defDto != null) {
                cacheService.cacheValue(formDefCacheKey, FastjsonUtil.toJson(defDto));
            }
            return defDto;
        });
    }
}
