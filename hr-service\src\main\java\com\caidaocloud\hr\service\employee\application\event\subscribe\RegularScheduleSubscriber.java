package com.caidaocloud.hr.service.employee.application.event.subscribe;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.hr.service.employee.application.cron.RegularizationNoticeTask;
import com.caidaocloud.hr.service.temination.application.event.dto.ScheduleTaskMsg;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 8/6/2023 4:04 下午
 */
@Component
@Slf4j
@AllArgsConstructor
public class RegularScheduleSubscriber {

    private MsgNoticeService msgNoticeService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.hr.regularization.notice", durable = "true"),
                    exchange = @Exchange(value = "schedule.task.exchange." + RegularizationNoticeTask.TOPIC),
                    key = {"routingKey.schedule.task." + RegularizationNoticeTask.TOPIC}
            )
    )
    public void process(String msg) {
        log.info("Subscribe regular notice,msg={}", msg);
        ScheduleTaskMsg scheduleTaskMsg = FastjsonUtil.toObject(msg, ScheduleTaskMsg.class);
        try {
            String[] taskId = scheduleTaskMsg.getTaskId().split("_");
            String msgId = taskId[0], empId = taskId[1], ext = scheduleTaskMsg.getTaskDetail();
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(scheduleTaskMsg.getUserInfo().getTenantId());
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            msgNoticeService.sendMsgNoticeEvent(msgId, Lists.list(empId), FastjsonUtil.toObject(ext, new TypeReference<Map<String, String>>() {}), "hr", 0);
        }catch (Exception e){
            log.info("handle regular notice error,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
