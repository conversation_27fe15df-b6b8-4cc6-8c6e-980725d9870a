package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(
        value = "caidaocloud-hr-paas-service",
        fallback = HrPaasFeignClientFallback.class,
        configuration = {FeignConfiguration.class},
        contextId = "hrpassFeign"
)
public interface HrPaasFeignClient {
    @GetMapping("/api/hrpaas/page/v1/detail")
    Result<Map<String, Object>> pageDetail(@RequestParam("pageId") String pageId);

    @GetMapping("/api/hrpaas/v1/form/data/page/sub/{formId}")
    Result<PageResult<Map<String, Object>>> formPage(
            @PathVariable String formId,
            @RequestParam("pageNo") int pageNo,
            @RequestParam("pageSize") int pageSize,
            @RequestParam("empId") String empId,
            @RequestParam("isPlain") boolean isPlain
    );

    @PostMapping("/api/hrpaas/v1/dynamicTable/set/load")
    Result<List<DynamicColumnConfigDto>> dynamicTablesSet(@RequestBody List<String> codes);
}