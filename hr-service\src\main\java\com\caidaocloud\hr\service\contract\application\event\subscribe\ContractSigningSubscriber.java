package com.caidaocloud.hr.service.contract.application.event.subscribe;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationEsService;
import com.caidaocloud.hr.service.contract.application.enums.SignProcessStatusEnum;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractSigningMessageDto;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.temination.application.TerminationService;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApply;
import com.caidaocloud.hr.service.transfer.application.service.TransferService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 文件签署发起后，同步更新关联业务的文件签署状态
 */
@Slf4j
@Component
public class ContractSigningSubscriber {
    @Resource
    private ContractService contractService;
    @Resource
    private TransferService transferService;
    @Resource
    private TerminationService terminationService;
    @Resource
    private ConfirmationEsService confirmationEsService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.hr.contract.signing.queue", durable = "true"),
                    exchange = @Exchange(value = "esign.contract.fac.direct.exchange"),
                    key = {"routingKey.esign.contract.signing"}
            )
    )
    public void process(String message) {
        log.info("Subscribe ContractSigningMessage={}", message);
        try {
            ContractSigningMessageDto msg = FastjsonUtil.toObject(message, ContractSigningMessageDto.class);
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(msg.getTenantId());
            userInfo.setEmpId(0L);
            userInfo.setUserId(Long.valueOf(msg.getUserId()));
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            UserInfo user = new UserInfo();
            user.setTenantId(msg.getTenantId());
            user.setStaffId(0L);
            user.doSetUserId(userInfo.getUserId());
            user.setEmpid(0);
            UserContext.setCurrentUser(user);
            if ("CONTRACT".equals(msg.getFromType())) {// 合同
                contractService.updateProcessStatus(msg.getContractId(), msg.getProcessStatus());
            } else if ("TRANSFER".equals(msg.getFromType())) {// 异动
                transferService.updateEsignStatus(msg.getTransferId(), msg.getProcessStatus());
            } else if ("SEPARATION".equals(msg.getFromType()) && StringUtils.isNotBlank(msg.getTerminationApplyId())) {// 离职
                TerminationApply terminationApply = TerminationApply.load(msg.getTerminationApplyId());
                EnumSimple signProcessStatusEnum = new EnumSimple();
                signProcessStatusEnum.setValue(msg.getProcessStatus());
                terminationApply.setSignProcessStatus(signProcessStatusEnum);
                terminationApply.update();
            } else if ("REGULARIZATION".equals(msg.getFromType()) && StringUtils.isNotBlank(msg.getRegularizationId())) {// 转正
                // 更新转正申请记录的文件签署状态
                DataSimple dbApplyDo = confirmationEsService.getConfirmationApply(msg.getRegularizationId());
                if (dbApplyDo != null) {
                    String esign = "0";// 文件签署发起状态：1:已发起  0:未发起
                    SignProcessStatusEnum signProcessStatus = SignProcessStatusEnum.getByValue(msg.getProcessStatus());
                    if (null != signProcessStatus) {
                        esign = SignProcessStatusEnum.initiate != signProcessStatus ? "1" : esign;
                    }
                    dbApplyDo.getProperties().add("esign", esign);
                    confirmationEsService.updateConfirmationApply(dbApplyDo);
                } else {
                    log.warn("ContractSigningSubscriber getConfirmationApply empty id={}", msg.getRegularizationId());
                }
            }
        } catch (Exception ex) {
            log.error("process ContractMessage err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

}
