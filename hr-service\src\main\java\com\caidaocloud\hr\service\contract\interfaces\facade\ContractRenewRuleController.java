package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.service.ContractRenewRuleDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractRenewRuleDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractRenewRuleVo;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * created by: FoAng
 * create time: 29/11/2024 3:21 下午
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/contract/renewRule/v1")
@Api(value = "/api/hr/contract/renewRule/v1", description = "合同自动续签规则", tags = "v24")
@AllArgsConstructor
public class ContractRenewRuleController {

    private ContractRenewRuleDomainService contractRenewRuleDomainService;

    @GetMapping("/notice/list")
    @ApiOperation("获取消息配置列表")
    public Result<List<MsgConfigDto>> listNoticeType() {
        return Result.ok(contractRenewRuleDomainService.getNoticeType());
    }


    @ApiOperation("续签规则模板匹配条件")
    @GetMapping("/condition")
    public Result<List<ConditionDataVo>> conditionTree() {
        List<ConditionDataVo> list = contractRenewRuleDomainService.getConditionTree();
        return Result.ok(list);
    }

    @PostMapping("/refresh/rel")
    @ApiOperation("同步刷新匹配关系")
    public Result<?> refreshEmpRel(@RequestParam String ruleId) {
        contractRenewRuleDomainService.refreshRuleEmpRel(ruleId);
        return Result.ok(true);
    }

    @PostMapping("/page")
    @ApiOperation("获取合同自动续签规则列表")
    public Result<PageResult<ContractRenewRuleVo>> page(@RequestBody BasePage page) {
        return Result.ok(contractRenewRuleDomainService.pageRule(page));
    }

    @PostMapping("/saveOrUpdate")
    @ApiModelProperty("保存更新续签规则")
    public Result<?> saveOrUpdate(@RequestBody ContractRenewRuleDto dto) {
        contractRenewRuleDomainService.saveOrUpdate(dto);
        return Result.ok(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除续签规则")
    public Result<?> delete(@RequestParam String bids) {
        contractRenewRuleDomainService.deleteContractRules(Arrays.asList(bids.split(",")));
        return Result.ok(true);
    }
}
