package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.condition.tree.ConditionTree;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel("续签意向模版详情vo")
public class ContinueContractTemplateVo extends DataSimple {
    @ApiModelProperty("名称国际化")
    private Map<String, String> i18nName = Maps.newHashMap();
    @ApiModelProperty("模版说明国际化")
    private Map<String, String> i18nDesc = Maps.newHashMap();
    @ApiModelProperty("条件")
    private ConditionTree condition;
    @ApiModelProperty("页面提示说明")
    private String tips;
    @ApiModelProperty("附件")
    private Attachment attachment;
    @ApiModelProperty("合同信息字段")
    private List<MetadataPropertyVo> contractFields;
    @ApiModelProperty("员工续签意向")
    private List<String> feedbacks;
}
