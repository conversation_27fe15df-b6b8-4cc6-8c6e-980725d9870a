package com.caidaocloud.hr.service.archive.interfaces.dto;

import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 9/7/2024 10:02 上午
 */
@Data
public class ArchiveDto implements Serializable {

    @ApiModelProperty("业务类型")
    private String businessLine;

    @ApiModelProperty("业务主键ID")
    private String businessId;

    @ApiModelProperty("归档规则")
    private ArchivePolicy archivePolicy;

    @ApiModelProperty("归档module")
    private ArchiveModule archiveLine;

}
