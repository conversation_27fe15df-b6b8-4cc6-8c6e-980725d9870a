package com.caidaocloud.hr.service.archive.anonation;

import com.caidaocloud.hr.service.config.ArchiveAutoConfiguration;
import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * created by: FoAng
 * create time: 5/6/2024 11:19 上午
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(ArchiveAutoConfiguration.class)
public @interface EnableArchiveFile {

    ArchiveModule module() default ArchiveModule.HR;
}
