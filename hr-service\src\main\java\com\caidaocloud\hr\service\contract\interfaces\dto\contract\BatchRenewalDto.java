package com.caidaocloud.hr.service.contract.interfaces.dto.contract;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("批量续签dto")
public class BatchRenewalDto {
    @ApiModelProperty("续签人员列表")
    private List<EmpSimple> empList;

    @ApiModelProperty("合同公司")
    private String company;

    @ApiModelProperty("合同类型")
    private String contractSetId;

    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("是否开启工作流")
    private Boolean openWorkflow;

    @ApiModelProperty("备注")
    private String remark;
}
