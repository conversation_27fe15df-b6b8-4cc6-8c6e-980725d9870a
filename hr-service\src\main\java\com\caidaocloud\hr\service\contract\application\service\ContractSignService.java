package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.condition.tree.ConditionCallType;
import com.caidaocloud.condition.tree.ConditionItem;
import com.caidaocloud.condition.tree.ConditionOperator;
import com.caidaocloud.condition.tree.ValueComponent;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.ScheduleFeignClient;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractMessageDto;
import com.caidaocloud.hr.service.contract.application.event.factory.ContractEventFactory;
import com.caidaocloud.hr.service.contract.application.event.publish.ContractPublish;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractApprovalSignDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractChangeDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractSignQueryDto;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.RuleSetAutoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.vo.MatchConditionVo;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.domain.workplace.service.WorkplaceDomainService;
import com.caidaocloud.hr.service.tag.application.tag.service.EmpTagInfoService;
import com.caidaocloud.hr.service.tag.interfaces.vo.EmpTagInfoVo;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskApproveDTO;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskRevokeDTO;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.util.PackMatchUtil;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfProcessRuDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractSignService {
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private CompanyDomainService companyDomainService;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private WfOperateFeignClient wfOperateFeignClient;
    @Resource
    private ContractService contractService;
    @Resource
    private ContractPublish contractPublish;
    @Resource
    private RuleSetAutoDomainService ruleSetAutoDomainService;
    @Resource
    private OrgService orgService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private WorkplaceDomainService workplaceDomainService;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private IConditionFeign conditionFeign;
    @Resource
    private ScheduleFeignClient scheduleFeignClient;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private DictService dictService;
    @Resource
    private ArchiveEventProducer archiveEventProducer;
    @Resource
    private EmpTagInfoService empTagInfoService;

    private static final int DAY = 24 * 60 * 60 * 1000;


    public List doChangeContract(ContractChangeDto dto, ContractDo data, ApprovalStatusEnum statusEnum){
        changeContract(dto, data, statusEnum);
        contractDomainService.update(data);

        List<String> result = Lists.newArrayList();
        if(!ApprovalStatusEnum.PASSED.equals(statusEnum)){
            return result;
        }

        // 对生效中的合同进行终止或解除后，如员工存在未生效的合同，则将未生效合同状态更新为“作废”
        PageResult<ContractDo> contractPage = getInEffectiveContract();
        if(null == contractPage || null == contractPage.getItems()){
            return result;
        }

        contractPage.getItems().forEach(contractData -> {
            // 审批通过的作废
            if(ApprovalStatusEnum.PASSED.getIndex().toString().equals(contractData.getApprovalStatus().getValue())){
                EnumSimple contractStatus = contractData.getContractStatus();
                contractStatus.setValue(ContractStatusEnum.CANCEL.getIndex());
                // 作废
                contractData.setContractStatus(contractStatus);
                // 删除合同
                contractDomainService.update(contractData);
                return;
            }
            result.add(contractData.getBid());
        });
        return result;
    }

    private void changeContract(ContractChangeDto dto, ContractDo data, ApprovalStatusEnum statusEnum){
        data.setRemark(dto.getDesc());
        if(dto.isDissolve()){
            // 合同解除
            data.setDissolveDate(dto.getEffectiveDate());
            data.setDissolveReason(dto.getReason());
        } else {
            // 合同终止
            data.setTerminationDate(dto.getEffectiveDate());
            data.setTerminationReason(dto.getReason());
        }
        EnumSimple simple = new EnumSimple();
        simple.setValue(statusEnum.getIndex().toString());
        data.setApprovalStatus(simple);
    }

    private PageResult<ContractDo> getInEffectiveContract(){
        ContractStatusQueryDto queryDto = new ContractStatusQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(20);
        queryDto.setContractStatus(ContractStatusEnum.IN_EFFECTIVE.getIndex());
        PageResult<ContractDo> contractPage = contractDomainService.getContractPage(queryDto);
        log.info("Contract data to be deleted. PageResult={}", FastjsonUtil.toJson(contractPage));
        return contractPage;
    }

    public void updateContractApproval(ContractApprovalSignDto dto, WfTaskActionEnum choice) {
        if(StringUtil.isEmpty(dto.getBusinessKey())){
            return;
        }

        String [] keys = dto.getBusinessKey().split("_");
        if(null == keys || keys.length < 2){
            return;
        }
        dto.setBid(keys[0]);
        //签订类型
        String funcCode = keys[1];
//        dto.setDissolve(false);
        // 修改数据
        updateContract(dto, choice);
        // 驱动流程
        updateWorkflow(dto, choice);
    }

    private void updateContract(ContractApprovalSignDto dto, WfTaskActionEnum choice){
        var data = contractDomainService.getById(dto.getBid());
        if(null == data || StringUtil.isEmpty(data.getBid())){
            return;
        }

        try {
            ContractDto updateData = FastjsonUtil.convertObject(dto, ContractDto.class);
            com.caidaocloud.hr.service.util.BeanUtil.copyProperties(updateData, data, dto.getWritableFields(), true,
                    Lists.newArrayList("contractTypeSet", "contractTypeSetTxt"));
        } catch (Exception e){
            log.warn("Contract Field WritableFields err,{}", e.getMessage(), e);
        }
        // 默认审批中
        ApprovalStatusEnum statusEnum = ApprovalStatusEnum.IN_APPROVAL;
        switch (choice) {
            case APPROVE:{
                val endDate = data.getEndDate();
                val empId = data.getOwner().getEmpId();
                val check = contractTypeSetService.getDetail(data.getContractTypeSet()).getBaseExpectGraduateDate();
                if(null != check && check){
                    val expectedGraduateDate = empWorkInfoService
                            .getEmpWorkInfo(empId, System.currentTimeMillis()).getExpectGraduateDate();
                    if(null != expectedGraduateDate && null != endDate && expectedGraduateDate < endDate){
                        throw new ServerException(ServerException.globalException(ErrorMessage
                                .fromCode("caidao.contract.end_time_after_expected_graduate")).getMessage()
                                + "("+new SimpleDateFormat("yyyy-MM-dd")
                                .format(new Date(expectedGraduateDate))+")");
                    }
                }
                break;
            }
            case REFUSE:
                statusEnum = ApprovalStatusEnum.REJECTED;
                break;
            case REVOKE:
                statusEnum = ApprovalStatusEnum.REVOKE;
                break;
            default:
                throw new ServerException("Unsupported approval callback type");
        }
//        contractDomainService.update(data);
        saveContract(data, false, null, false, statusEnum);
    }

    public void updateWorkflow(ContractApprovalSignDto dto, WfTaskActionEnum choice) {
        WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
        wfApproveTaskDto.setChoice(choice);
        wfApproveTaskDto.setComment(dto.getComment());
        wfApproveTaskDto.setTaskId(dto.getTaskId());
        try {
            // 获取单据生效时间
            if (null != dto.getEffectiveDate()) {
                // 调用 feign 更新业务单据事件时间
                WfProcessRuDto wfProcessRuDto = new WfProcessRuDto();
                wfProcessRuDto.setBusinessKey(dto.getBusinessKey());
                wfProcessRuDto.setEventTime(dto.getEffectiveDate());
                try {
                    iWfRegisterFeign.updateEventTime(wfProcessRuDto);
                } catch (Exception e) {
                    log.error("Transfer updateEventTime err,{}", e.getMessage(), e);
                }
            }
            Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
        } catch (Exception e) {
            log.error("approveWorkflow err,{}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public void revoke(ContractApprovalSignDto dto) {
        var data = contractDomainService.getById(dto.getBid());
        if(null == data || StringUtil.isEmpty(data.getBid())){
            throw new ServerException("contract not existence! ");
        }
        //修改状态
        EnumSimple approvalStatus = new EnumSimple();
        approvalStatus.setValue(ApprovalStatusEnum.REVOKE.getIndex().toString());
        approvalStatus.setText(ApprovalStatusEnum.REVOKE.getName());
        data.setApprovalStatus(approvalStatus);
        //撤销工作流
        revokeWorkflow(dto.getBusinessKey());
        contractDomainService.update(data);
    }

    private void revokeWorkflow(String businessKey) {
        WfTaskRevokeDTO wfRevokeDto = new WfTaskRevokeDTO();
        wfRevokeDto.setBusinessKey(businessKey);
        try {
            Result<?> result = wfOperateFeignClient.revokeProcessOfTask(wfRevokeDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw new ServerException("Failed to revoke confirmation apply");
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public void callback(String tenantId, String businessKey, WfCallbackTriggerOperationEnum callbackType) {
        // 设置回调用户信息
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        // 回调默认用户id为 0
        userInfo.setUserId(0L);
        try {
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            String dataId = StringUtils.substringBefore(businessKey, "_");
            var data = contractDomainService.getById(dataId);
            if(null == data || StringUtil.isEmpty(data.getBid())){
                return;
            }

            ContractChangeDto dto = new ContractChangeDto();
            dto.setDesc(data.getRemark());
            dto.setDissolve(null != data.getDissolveDate());
            dto.setEffectiveDate(dto.isDissolve() ? data.getDissolveDate() : data.getTerminationDate());
            dto.setReason(dto.isDissolve() ? data.getDissolveReason() : data.getTerminationReason());

            ApprovalStatusEnum statusEnum = ApprovalStatusEnum.PASSED;
            switch (callbackType) {
                case APPROVED:
                    break;
                case REFUSED:
                    statusEnum = ApprovalStatusEnum.REJECTED;
                    break;
                case REVOKE:
                    statusEnum = ApprovalStatusEnum.REVOKE;
                    break;
                default:
                    throw new ServerException("Unsupported approval callback type");
            }

//            doChangeContract(dto, data, statusEnum);
            saveContract(data, false, null, false, statusEnum);
            if(SignTypeEnum.RENEW.getCode().equals(data.getSignType().getValue())){
                val list = DataQuery.identifier("entity.hr.ContractRenewEndTimeChangeSignal")
                        .limit(-1,1).filter(DataFilter.eq("contractBid", data.getBid()),
                                DataSimple.class).getItems();
                list.forEach(it->{
                    DataDelete.identifier("entity.hr.ContractRenewEndTimeChangeSignal").delete(it.getBid());
                });
                if(!list.isEmpty() && WfCallbackTriggerOperationEnum.APPROVED.equals(callbackType)){
                    contractService.sendRenewEndDateChangedMsg(data.getOwner().getEmpId());
                }
            }
        } catch (Exception e){
            log.error("contract workflow callback err,{}", e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    public void saveContract(ContractDo contractDo, Boolean openWorkflow, EmpWorkInfoDo empWorkInfo, Boolean closeEsign, ApprovalStatusEnum statusEnum) {
        // 查询旧合同
        ContractDo lastContractDo = contractDomainService.getById(contractDo.getLastContract());
        if (contractDo.getSignType().getValue().equals(SignTypeEnum.CHANGE.getCode())) {
            PreCheck.preCheckArgument(Objects.isNull(lastContractDo), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80006));
            PreCheck.preCheckArgument(contractDo.getStartDate() <= lastContractDo.getStartDate(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80005));
        } else if (!ApprovalStatusEnum.REJECTED.equals(statusEnum)) {
            // 存在未生效合同，则无法续签、新签
            contractService.checkInoperativeContract(contractDo);
            // 员工存在审批中的合同，则无法续签、新签
            contractService.checkUnderApprovalContract(contractDo, true);
        }

        String organize = contractDo.getOrganize() == null ?
                empWorkInfo != null ? empWorkInfo.getOrganize() : null
                : contractDo.getOrganize();
        // 组织编码值补填
        if(organize != null){
            OrgDo orgById = orgService.getOrgById(contractDo.getOrganize(), System.currentTimeMillis());
            contractDo.setOrganizeCode(Optional.ofNullable(orgById).orElse(new OrgDo()).getCode());
        }

        //审批状态
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(statusEnum.getIndex().toString());
        contractDo.setApprovalStatus(enumSimple);

        //合同状态
        setContractStatus(contractDo, statusEnum);

        // 如果当前合同是改签并且未开启工作流，并且当前合同是生效中，则旧合同状态改为终止且合同终止日期变更为改签后合同生效日期前一天
        if (SignTypeEnum.CHANGE.getCode().equals(contractDo.getSignType().getValue())
                && null != openWorkflow && !openWorkflow
                && ContractStatusEnum.EFFECTIVE.getIndex().equals(contractDo.getContractStatus().getValue())) {
            EnumSimple simple = new EnumSimple();
            simple.setValue(String.valueOf(ContractStatusEnum.TERMINATED.getIndex()));
            // 合同终止日期改完当前合同前一天
            lastContractDo.setTerminationDate(contractDo.getStartDate() - DAY);
            lastContractDo.setContractStatus(simple);
            contractDomainService.update(lastContractDo);
        }

        contractDo.setLaunchDate(Optional.ofNullable(contractDo.getLaunchDate()).orElse(DateUtil.getCurrentTimestamp()));
        //合同编号生成规则，没有则按照自动规则生成
        contractDo.setContractNo(ruleSetAutoDomainService.nextContractNo(contractDo.getContractNo(), contractDo.getContractNo()));
        contractDomainService.save(contractDo);

        log.info("empId-->{},contractDomainService.save success",contractDo.getOwner().getEmpId());

        String userId = UserContext.getUserId();
        Long nowDate = System.currentTimeMillis();
        contractDo.setCreateBy(userId);
        contractDo.setUpdateBy(userId);
        contractDo.setUpdateTime(nowDate);
        contractDo.setCreateTime(nowDate);
        contractDo.setDeleted(false);

        ContractMessageDto messageDto = ObjectConverter.convert(contractDo, ContractMessageDto.class);
        messageDto.setTenantId(UserContext.getTenantId());
        messageDto.setEmp(contractDo.getOwner());
        messageDto.setUserId(Long.valueOf(userId));
        messageDto.setContract(contractDo.getBid());
        messageDto.setSignType(contractDo.getSignType());

        // 触发合同新签以及续签通知模板
        contractPublish.startContractPublishMsg(messageDto);
        // 文件归档
        if (statusEnum == ApprovalStatusEnum.PASSED) {
            archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT, contractDo.getBid());
        } else {
            archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT, contractDo.getBid(), ArchivePolicy.DELETE);
        }

        if (null != openWorkflow && !openWorkflow && ApprovalStatusEnum.PASSED.equals(statusEnum)) {
            SpringUtil.getBean(ContractService.class).contractEffective(messageDto, contractDo, closeEsign, empWorkInfo, lastContractDo, nowDate);
        }
    }

    private void setContractStatus(ContractDo contractDo, ApprovalStatusEnum statusEnum) {
        log.info("setContractStatus contractDo={}, statusEnum ={}", FastjsonUtil.toJson(contractDo), statusEnum.getIndex());

        if (SignTypeEnum.NEW.getCode().equals(contractDo.getSignType().getValue())
                || SignTypeEnum.RENEW.getCode().equals(contractDo.getSignType().getValue())
                || SignTypeEnum.CHANGE.getCode().equals(contractDo.getSignType().getValue())) {

            EnumSimple contractStatus = new EnumSimple();
            if (ApprovalStatusEnum.PASSED.getIndex().equals(statusEnum.getIndex())) {
                if (contractDo.getStartDate() > System.currentTimeMillis()) {
                    contractStatus.setValue(ContractStatusEnum.IN_EFFECTIVE.getIndex());
                } else {
                    contractStatus.setValue(ContractStatusEnum.EFFECTIVE.getIndex());
                }
            }
            if (ApprovalStatusEnum.REJECTED.getIndex().equals(statusEnum.getIndex())) {
                contractStatus.setValue(ContractStatusEnum.CANCEL.getIndex());
            }
            contractDo.setContractStatus(contractStatus);
        }
    }

    public ContractSignQueryDto detailVo(String businessKey) {
        String dataId = StringUtils.substringBefore(businessKey, "_");
        ContractDo lastContractDo = null, contractDo = null;
        contractDo = contractDomainService.getById(dataId);
        ContractSignQueryDto contractSignQueryDto = new ContractSignQueryDto();
        if (contractDo == null || contractDo.getOwner() == null) {
            return contractSignQueryDto;
        }
        EmpWorkInfoDo workInfoDo = empWorkInfoService.getEmpWorkInfo(contractDo.getOwner().getEmpId(), DateUtil.getCurrentTimestamp());
        if (workInfoDo == null) {
            return contractSignQueryDto;
        }

        EmpWorkInfoVo workInfoVo = ObjectConverter.convert(workInfoDo, EmpWorkInfoVo.class);
        if (StringUtil.isNotEmpty(workInfoDo.getCostCenters())) {
            workInfoVo.setCostCenters(FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
        }

        // DEV-5215
        if (null != workInfoVo.getLeadEmpId() && StringUtil.isEmpty(workInfoVo.getLeadEmpId().getEmpId())) {
            workInfoVo.setLeadEmpId(null);
        }

        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(workInfoDo.getDoIdentifier(), workInfoDo);
        workInfoVo.setExt(ext);
        //合同类型
        workInfoVo.setContractType(contractDo.getContractType());
        //标签
        List<EmpTagInfoVo> allEmpTagInfo = empTagInfoService.getEmpTagInfo(workInfoVo.getEmpId(), System.currentTimeMillis(), true);
        workInfoVo.setTagIds(allEmpTagInfo.stream().map(o -> o.getTagBid()).collect(Collectors.joining(",")));
        //上一份合同
        String contractSignType = StringUtils.substringAfter(businessKey, "_");
        if (!ContractSignTypeConstant.CONTRACT_NEW_SIGN.equals(contractSignType)) {
            lastContractDo = contractDomainService.getById(contractDo.getLastContract());
        }
        EnumSimple signType = contractDo.getSignType();
        // 英文版本对照翻译
        if (!LangUtil.chineseLocale() && (signType != null && signType.getValue().equals(SignTypeEnum.RENEW.getCode()))) {
            // 组织
            List<String> companyIds = Sequences.sequence(workInfoDo.getCompany(), contractDo.getCompany(),
                            lastContractDo != null ? lastContractDo.getCompany() : null)
                    .stream().filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 组织
            List<String> orgIds = Sequences.sequence(workInfoDo.getOrganize(), workInfoDo.getLeaderOrganize(), contractDo.getOrganize(),
                            lastContractDo != null ? lastContractDo.getOrganize() : null)
                    .stream().filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 岗位
            List<String> postIds = Sequences.sequence(workInfoDo.getPost(), workInfoDo.getLeaderPost())
                    .stream().filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 职务
            List<String> jobIds = Sequences.sequence(workInfoDo.getJob(), contractDo.getJob(), lastContractDo != null ?
                            lastContractDo.getJob() : null)
                    .stream().filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 工作地
            List<String> workplaceIds = Sequences.sequence(workInfoDo.getWorkplace(), contractDo.getWorkplace(), lastContractDo != null ?
                            lastContractDo.getWorkplace() : null)
                    .stream().filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            // 合同类型
            List<String> contractTypeIds = Sequences.sequence(lastContractDo, contractDo)
                    .filter(Objects::nonNull)
                    .map(ContractDo::getContractTypeSet)
                    .filter(Objects::nonNull)
                    .stream().distinct()
                    .collect(Collectors.toList());

            final Map<String, CompanyDo> companyDoMap = Maps.newHashMap();
            final Map<String, OrgDo> orgDoMap = Maps.newHashMap();
            final Map<String, PostDo> postDoMap = Maps.newHashMap();
            final Map<String, JobDo> jobDoMap = Maps.newHashMap();
            final Map<String, WorkplaceDo> workplaceDoMap = Maps.newHashMap();
            final Map<String, ContractTypeSetDo> contractTypeMap = Maps.newHashMap();

            if (CollectionUtils.isNotEmpty(companyIds)) {
                companyDoMap.putAll(Optional.ofNullable(companyDomainService.getBatchI18nCompany(companyIds))
                        .map(it -> it.stream().collect(Collectors.toMap(CompanyDo::getBid, Function.identity()))).orElse(Maps.newHashMap()));
            }

            if (CollectionUtils.isNotEmpty(orgIds)) {
                orgDoMap.putAll(Optional.ofNullable(orgDomainService.getBatchI18nOrg(orgIds))
                        .map(it -> it.stream().collect(Collectors.toMap(OrgDo::getBid, Function.identity()))).orElse(Maps.newHashMap()));
            }

            if (CollectionUtils.isNotEmpty(postIds)) {
                postDoMap.putAll(Optional.ofNullable(postDomainService.getBatchI18nPost(postIds))
                        .map(it -> it.stream().collect(Collectors.toMap(PostDo::getBid, Function.identity()))).orElse(Maps.newHashMap()));
            }

            if (CollectionUtils.isNotEmpty(jobIds)) {
                jobDoMap.putAll(Optional.ofNullable(jobDomainService.getBatchI18nJob(jobIds))
                        .map(it -> it.stream().collect(Collectors.toMap(JobDo::getBid, Function.identity()))).orElse(Maps.newHashMap()));
            }

            if (CollectionUtils.isNotEmpty(workplaceIds)) {
                workplaceDoMap.putAll(Optional.ofNullable(workplaceDomainService.getBatchI18nPlace(workplaceIds))
                        .map(it -> it.stream().collect(Collectors.toMap(WorkplaceDo::getBid, Function.identity()))).orElse(Maps.newHashMap()));
            }

            if (CollectionUtils.isNotEmpty(contractTypeIds)) {
                contractTypeMap.putAll(contractTypeSetService.selectByIds(contractTypeIds).stream()
                        .collect(Collectors.toMap(ContractTypeSetDo::getBid, Function.identity())));
            }
            // workInfo  companyTxt jobTxt leaderOrganizeTxt  leaderPostTxt organizeTxt  postTxt workPlace
            // contractDo  companyTxt jobTxt  organizeTxt workplaceTxt
            workInfoVo.setCompanyTxt(resolveI18nValue(companyDoMap, workInfoVo.getCompany(), workInfoVo.getCompanyTxt()));
            workInfoVo.setJobTxt(resolveI18nValue(jobDoMap, workInfoVo.getJob(), workInfoVo.getJobTxt()));
            workInfoVo.setLeaderOrganizeTxt(resolveI18nValue(orgDoMap, workInfoVo.getLeaderOrganize(), workInfoVo.getLeaderOrganizeTxt()));
            workInfoVo.setLeaderPostTxt(resolveI18nValue(postDoMap, workInfoVo.getLeaderPost(), workInfoVo.getLeaderPostTxt()));
            workInfoVo.setOrganizeTxt(resolveI18nValue(orgDoMap, workInfoVo.getOrganize(), workInfoVo.getOrganizeTxt()));
            workInfoVo.setPostTxt(resolveI18nValue(postDoMap, workInfoVo.getPost(), workInfoVo.getPostTxt()));
            workInfoVo.setWorkplaceTxt(resolveI18nValue(workplaceDoMap, workInfoVo.getWorkplace(), workInfoVo.getWorkplaceTxt()));
            Optional.ofNullable(workInfoVo.getEmpStatus())
                    .filter(it -> StringUtil.isNotEmpty(it.getValue()))
                    .ifPresent(it -> {
                        it.setText(EmpStatusEnum.getLangeName(Integer.valueOf(it.getValue()), false));
                    });
            Stream.of(contractDo, lastContractDo).filter(Objects::nonNull)
                    .forEach(o1 -> {
                        o1.setContractTypeSetTxt(resolveI18nValue(contractTypeMap, o1.getContractTypeSet(), o1.getContractTypeSetTxt()));
                        o1.setCompanyTxt(resolveI18nValue(companyDoMap, o1.getCompany(), o1.getCompanyTxt()));
                        o1.setJobTxt(resolveI18nValue(jobDoMap, o1.getJob(), o1.getJobTxt()));
                        o1.setOrganizeTxt(resolveI18nValue(orgDoMap, o1.getOrganize(), o1.getOrganizeTxt()));
                        o1.setWorkplaceTxt(resolveI18nValue(workplaceDoMap, o1.getWorkplace(), o1.getWorkplaceTxt()));
                    });
        }
        contractSignQueryDto.setCurrentContractDo(contractDo);
        contractSignQueryDto.setLastContractDo(lastContractDo);
        contractSignQueryDto.setEmpWorkInfoVo(workInfoVo);
        return contractSignQueryDto;
    }

    private <T> String resolveI18nValue(Map<String, T> i18nMap, String bid, String defaultValue) {
        if (StringUtils.isNotBlank(bid) && i18nMap.containsKey(bid)) {
            Object objectDo = i18nMap.get(bid);
            if (objectDo == null) {
                return defaultValue;
            } else if (objectDo instanceof CompanyDo) {
                return ((CompanyDo) objectDo).getCompanyName();
            } else if (objectDo instanceof OrgDo) {
                return ((OrgDo) objectDo).getName();
            } else if (objectDo instanceof PostDo) {
                PostDo postDo = (PostDo) objectDo;
                return StringUtils.isEmpty(postDo.getCode()) ? postDo.getName() : String.format("%s(%s)", postDo.getName(), postDo.getCode());
            } else if (objectDo instanceof JobDo) {
                return ((JobDo) objectDo).getName();
            } else if (objectDo instanceof WorkplaceDo) {
                return ((WorkplaceDo) objectDo).getName();
            } else if (objectDo instanceof ContractTypeSetDo) {
                return Optional.ofNullable(((ContractTypeSetDo) objectDo).getContractType())
                        .filter(it -> StringUtil.isNotEmpty(it.getValue()))
                        .map(it -> dictService.getLangText(it.getValue(), it.getText(), false))
                        .orElse("");
            }
        }
        return defaultValue;
    }

    public List<ConditionItem> getConditionList() {
        List<ConditionItem> result = new ArrayList();
        ConditionItem condition = new ConditionItem();
        condition.setName("用工类型");
        condition.setCode("empType.dict.value");
        condition.setOperators(com.googlecode.totallylazy.Lists.list(ConditionOperator.values()));
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.DICT_SELECTOR);
        Map<String, String> empTypeMap = new HashMap<>(2);
        empTypeMap.put("belongModule", "Employee");
        empTypeMap.put("typeCode", "EmployType");
        condition.setDataSourceParams(empTypeMap);
        condition.setComponentValueEnum(com.googlecode.totallylazy.Lists.list());
        result.add(condition);


        condition = new ConditionItem();
        condition.setName("员工工号");
        condition.setCode("workno");
        condition.setOperators(com.googlecode.totallylazy.Lists.list(ConditionOperator.values()));
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.EMP_SELECTOR);
        condition.setComponentValueEnum(com.googlecode.totallylazy.Lists.list());
        result.add(condition);


        condition = new ConditionItem();
        condition.setName("岗位");
        condition.setCode("post");
        condition.setOperators(com.googlecode.totallylazy.Lists.list(ConditionOperator.values()));
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.POS_SELECTOR);
        condition.setComponentValueEnum(com.googlecode.totallylazy.Lists.list());
        result.add(condition);


        condition = new ConditionItem();
        condition.setName("所属组织");
        condition.setCode("organize");
        condition.setOperators(com.googlecode.totallylazy.Lists.list(ConditionOperator.values()));
        condition.setType(ConditionCallType.OUTSIDE_SERVICE);
        condition.setComponent(ValueComponent.ORG);
        condition.setComponentValueEnum(com.googlecode.totallylazy.Lists.list());
        result.add(condition);



        return result;
    }

    public List<MatchConditionVo> listMatchCondition() {
        List<ConditionDataVo> conditionDataList = conditionFeign.getConditionDataByCode("approve_management", false).getData();
        return conditionDataList.stream().map(data -> {
            MatchConditionVo vo = JsonEnhanceUtil.toObject(data, MatchConditionVo.class);
            vo.setCode(PackMatchUtil.CODE_MAPPING.getOrDefault(data.getCode(), data.getCode()));
            vo.getOperators().add(ConditionOperatorEnum.IS_NULL);
            vo.getOperators().add(ConditionOperatorEnum.IS_NOT_NULL);
            switch (vo.getComponent()){
                case ADDRESS:
                    vo.getOperators().removeIf(op -> ConditionOperatorEnum.IN.equals(op));
                    return vo;
            }

            vo.getOperators().add(ConditionOperatorEnum.NOT_CONTAIN);
            vo.getOperators().addAll(PackMatchUtil.findOperators(data.getCode()));
            return vo;
        })
        .collect(Collectors.toList());
    }


    public List<MatchConditionVo> listRoleCondition() {
        List<ConditionDataVo> conditionDataList = conditionFeign.getConditionDataByCode("role_group", false).getData();
        return conditionDataList.stream().map(data -> {
            MatchConditionVo vo = JsonEnhanceUtil.toObject(data, MatchConditionVo.class);
//            vo.setCode(PackMatchUtil.CODE_MAPPING.getOrDefault(data.getCode(), data.getCode()));
            vo.getOperators().add(ConditionOperatorEnum.IS_NULL);
            vo.getOperators().add(ConditionOperatorEnum.IS_NOT_NULL);
            switch (vo.getComponent()){
                case ADDRESS:
                    vo.getOperators().removeIf(op -> ConditionOperatorEnum.IN.equals(op));
                    return vo;
            }

            vo.getOperators().add(ConditionOperatorEnum.NOT_CONTAIN);
            vo.getOperators().addAll(PackMatchUtil.findOperators(data.getCode()));
            return vo;
        })
        .collect(Collectors.toList());
    }

    private void startEsign(String tenantId, String businessKey, WfCallbackTriggerOperationEnum callbackType) {
        if (callbackType!= WfCallbackTriggerOperationEnum.APPROVED) {
            log.info("contract reject, no need to start esign,businessKey={}", businessKey);
            return;
        }
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            String contractId = StringUtils.substringBefore(businessKey, "_");
            ContractDo data = contractDomainService.getById(contractId);
            ContractDo lastData = contractDomainService.getById(data.getLastContract());
            long days = TimeUnit.MILLISECONDS.toDays(lastData.getEndDate() - DateUtil.getCurrentTimestamp());
            log.info("自动发起签署，businessKey={}", businessKey);
            if (days <= 30) {
                log.info("合同到期不足30天，自动发起签署，businessKey={}", businessKey);
                contractPublish.contractInitiated(ContractEventFactory.createContractStartMsg(data));
            }
            else {
                long datetime = lastData.getEndDate() - TimeUnit.DAYS.toMillis(30) + TimeUnit.MINUTES.toMillis(30);
                log.info("合同到期超过30天，定时任务发起签署，businessKey={},datetime={}", businessKey, datetime);
                ScheduleTaskDto task = new ScheduleTaskDto(tenantId, "CONTRACT_START", SnowUtil.nextId(), contractId, datetime);
                scheduleFeignClient.addSchedule(task);
            }
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }

    }

    public void callbackAndStart(String tenantId, String businessKey, WfCallbackTriggerOperationEnum callbackType) {
        callback(tenantId, businessKey, callbackType);
        startEsign(tenantId, businessKey, callbackType);
    }

    public void startMsg(String contractId) {
        ContractDo data = contractDomainService.getById(contractId);
        contractPublish.contractInitiated(ContractEventFactory.createContractStartMsg(data));
    }
}
