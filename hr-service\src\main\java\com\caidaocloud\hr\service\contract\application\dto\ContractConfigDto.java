package com.caidaocloud.hr.service.contract.application.dto;

import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ContractConfigDto {
    private String bid;

    private String name;

    private String description;
    
    private List<ContractPropertyConfigDto> enabledContractProperties;
    
    private ContractApplyType contractApplyType;
    
    private String formDefId;

    private List<MetadataPropertyVo> displayWorkInfos = Lists.list();

    private List<TagInfoKVVo> tagProperties;
}
