package com.caidaocloud.hr.service.contract.application.exp.service;

import com.caidaocloud.hr.service.contract.interfaces.dto.ConditionExpDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.googlecode.totallylazy.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.caidaocloud.util.DateUtil.getMonthDiff;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
@Component
public class EndDateExp extends AbsContractExp{
    @Override
    public List<Long> filterEmpIds(List<Long> empIds, ConditionExpDto exp) {
        // val empHireDateMap = ConditionTreeDto.queryAll("entity.hr.EmpWorkInfo",
        //                 DataFilter.in("empId", Sequences.sequence(empIds).map(it -> String.valueOf(it)).toList()))
        //         .stream().collect(Collectors.toMap(it->fetchSimpleDataProperty(it, "empId"),
        //                 it->Long.parseLong(fetchSimpleDataProperty(it,"hireDate")),
        //                 (A,B)->A));
        List<Map<String, String>> workInfo = Lists.list();
        getInfoAll("entity.hr.EmpWorkInfo", DataFilter.eq("deleted", Boolean.FALSE.toString()), workInfo, "empId", "hireDate");
        Map<String, Long> empHireDateMap = workInfo.stream()
                .filter(map -> map.get("hireDate") != null)
                .collect(Collectors.toMap(map -> map.get("empId"), map -> Long.valueOf(map.get("hireDate")), (A, B) -> A));
        return findLastContract(empIds, exp).filter(map -> {
                            // String empId = ((EmpSimple) fetchDataProperty(map, "owner")).getEmpId();
                            //         String endDate = fetchSimpleDataProperty(map, "endDate");
                            String empId = map.get("owner.empId"), endDate = map.get("endDate");
                            Long hireDate = empHireDateMap.get(empId);
                            if (hireDate == null || endDate == null) {
                                return false;
                            }
                            //   合同结束日期大于9999-12-31时，当作9999-12-31处理
                            Long endTimestamp = Math.min(Long.parseLong(endDate), DateUtil.MAX_TIMESTAMP);

                            return optFilter(getMonthDiff(hireDate, endTimestamp), exp);
                        }
                )
                .map(map -> Long.valueOf(map.get("owner.empId"))).toList();
    }

    @Override
    public boolean optFilter(Object value, ConditionExpDto exp) {
        if (value == null) {
            return false;
        }
        long month = ((Long) value);
        BigDecimal year = new BigDecimal(month).divide(new BigDecimal(12), 1, RoundingMode.HALF_UP);
        switch (exp.getSymbol()) {
            case EQ:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) == 0;
            case NE:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) != 0;
            case LT:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) < 0;
            case LE:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) <= 0;
            case GT:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) > 0;
            case GE:
                return year.compareTo(setScale(String.valueOf(exp.getValue()))) >= 0;
            case IN:
                return ((List) exp.getValue()).stream().anyMatch(val -> year.compareTo(setScale(String.valueOf(val))) == 0);
            default:
                return false;
        }
    }

    BigDecimal setScale(String value) {
        return setScale(new BigDecimal(value.trim()));
    }

    BigDecimal setScale(BigDecimal value) {
        return value.setScale(1, RoundingMode.HALF_UP);
    }

}
