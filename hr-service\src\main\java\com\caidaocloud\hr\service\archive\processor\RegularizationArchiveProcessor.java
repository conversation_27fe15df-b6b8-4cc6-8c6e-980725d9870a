package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationEsService;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationApply;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 转正文件归档
 * created by: FoAng
 * create time: 13/6/2024 4:53 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class RegularizationArchiveProcessor extends AbsArchiveProcessor {

    private ConfirmationEsService confirmationEsService;

    @Override
    public String businessLine() {
        return ArchiveStandardLine.REGULARIZATION.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        DataSimple dataSimple = confirmationEsService.getConfirmationApply(businessId);
        if (dataSimple == null) {
            log.error("[archive] fetch regular archive data error, businessId:{}", businessId);
            return Lists.newArrayList();
        }
        return filterArchiveList(Lists.newArrayList(buildArchiveData(dataSimple)));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        boolean indexExist = confirmationEsService.checkIndexExist();
        if (indexExist) {
            List<DataSimple> confirmLists = confirmationEsService.getArchiveData(page);
            if (CollectionUtils.isNotEmpty(confirmLists)) {
                List<ArchiveData> archiveDataList = Lists.newArrayList();
                for (DataSimple dataSimple : confirmLists) {
                    archiveDataList.add(buildArchiveData(dataSimple));
                }
                return filterArchiveList(archiveDataList);
            }
        }
        return Lists.newArrayList();
    }

    public ArchiveData buildArchiveData(DataSimple dataSimple) {
        ConfirmationApply confirmationApply = BeanUtil.convert(dataSimple, ConfirmationApply.class);
        ArchiveData archiveData = new ArchiveData();
        archiveData.setBusinessLine(ArchiveStandardLine.REGULARIZATION.getDesc());
        archiveData.setBusinessType(ArchiveStandardLine.CONTRACT_LETTER.getDesc());
        archiveData.setBusinessId(confirmationApply.getBid());
        archiveData.setEmpId(confirmationApply.getEmp().getEmpId());
        archiveData.setArchiveFiles(fetchArchiveFiles(confirmationApply));
        archiveData.setEventTime(confirmationApply.getCreateTime());
        return archiveData;
    }

    public List<ArchiveFile> fetchArchiveFiles(ConfirmationApply apply) {
        List<ArchiveFile> archiveFiles = Lists.newArrayList();
        if (StringUtils.isNotEmpty(apply.getFormValueId())) {
            ArchiveFile formFile = fetchFormFile(apply.getFormId(), apply.getFormValueId());
            archiveFiles.add(formFile);
        }
        NestPropertyValue nestPropertyValue = apply.getProperties();
        if (nestPropertyValue.containsKey("attachment")) {
            ArchiveFile file = BeanUtil.convert(nestPropertyValue.get("attachment"), ArchiveFile.class);
            archiveFiles.add(file);
        }
        return archiveFiles.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
