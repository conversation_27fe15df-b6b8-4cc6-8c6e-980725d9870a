package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.EmpContractTypeSetRelDo;
import com.caidaocloud.hr.service.contract.domain.repository.IEmpContractTypeSetRelRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EmpContractTypeSetRelRepositoryImpl extends BaseRepositoryImpl<EmpContractTypeSetRelDo> implements IEmpContractTypeSetRelRepository {

    @Override
    public List<EmpContractTypeSetRelDo> getEmpContractTypeList(String empId, String identifier) {
        DataFilter dataFilter = DataFilter.eq("empId", empId);
        PageResult<EmpContractTypeSetRelDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpContractTypeSetRelDo.class);
        return getPageList(pageResult);

    }

    @Override
    public void batchInsert(String identifier,List<EmpContractTypeSetRelDo> data) {
        DataInsert.identifier(identifier).batchInsert(data);
    }

    @Override
    public void deleteByTypeSetBid(String identifier, String bid) {
        DataDelete.identifier(identifier).batchDelete(DataFilter.eq("contractTypeSet", bid));
    }

    @Override
    public List<EmpContractTypeSetRelDo> selectByEmpId(String identifier, List<String> empIds) {
        DataFilter dataFilter = getBaseFilter().andIn("empId", empIds);
        PageResult<EmpContractTypeSetRelDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpContractTypeSetRelDo.class);
        return getPageList(pageResult);
    }
}
