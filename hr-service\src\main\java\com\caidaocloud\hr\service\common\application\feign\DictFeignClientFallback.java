package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.hr.service.organization.application.org.dto.DictKeyValue;
import com.caidaocloud.hr.service.temination.application.dto.bcc.SysParamDictDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DictFeignClientFallback implements DictFeignClient{

    @Override
    public Result<SysParamDictDto> getDictInfo(String dictType, String dictCode) {
        return Result.fail();
    }

    @Override
    public Result<SysParamDictDto> getDictDetail(Long dictId) {
        return Result.fail();
    }

    @Override
    public Result<List<DictKeyValue>> getEnableDictList(String typeCode, String belongModule) {
        return Result.fail();
    }
}
