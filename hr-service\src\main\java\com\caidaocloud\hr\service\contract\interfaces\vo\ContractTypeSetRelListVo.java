package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.DictDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class ContractTypeSetRelListVo {
    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("合同类型名称")
    private String contractTypeSetName;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("合同公司bid")
    private List<String> company;

    @ApiModelProperty("合同公司名称")
    private List<String> companyTxt;

    @ApiModelProperty("员工类型")
    private List<String> empType;

    @ApiModelProperty("签订类型")
    private List<String> signType;

    @ApiModelProperty("状态")
    private EnumSimple status;

    @ApiModelProperty("是否有合同期限")
    private EnumSimple periodType;

    @ApiModelProperty("合同期限（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("合同类型Simple")
    private DictSimple contractTypeSimple;

    @ApiModelProperty("合同类型名称TXT")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同结束日期类型")
    private String endDateType;

    @ApiModelProperty("是否根据预计毕业日期判断")
    private Boolean baseExpectGraduateDate;

    @ApiModelProperty("是否根据退休日期判断")
    private Boolean baseRetireDate;

    @ApiModelProperty("试用期期限")
    private EnumSimple probation;

    public void convertData(ContractTypeSetDo source) {

        if (source == null) {
            return;
        }

        if (source.getSignTypeTxt() != null) {
            this.setSignType(source.getSignTypeTxt().stream().map(ComponentValue::getDisplay).collect(Collectors.toList()));
        }
        if (source.getEmpTypeTxt() != null) {
            this.setEmpType(source.getEmpTypeTxt().stream().map(DictDto::getLabel).collect(Collectors.toList()));
        }
        if (source.getCompanyTxt() != null) {
            this.setCompanyTxt(source.getCompanyTxt().stream().map(ComponentValue::getDisplay).collect(Collectors.toList()));
        }

        /*
           2022/5/18
           DO 中 contractType 属性名改成 contractClass ， contractTypeSetName 属性改为字典 contractType
           list列表中响应数据结构暂不改动
         */
        this.setContractType(source.getContractClass().getText());
        this.setContractTypeSetName(source.getContractType().getText());
    }

}
