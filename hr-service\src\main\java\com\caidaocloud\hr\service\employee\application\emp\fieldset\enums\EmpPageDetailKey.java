package com.caidaocloud.hr.service.employee.application.emp.fieldset.enums;

import java.util.Set;

import com.googlecode.totallylazy.Sequences;

public enum EmpPageDetailKey {
    ENTITY_HR_CONTRACT("entity.hr.Contract"),
    ENTITY_HR_EMP_WORK_OVERVIEW("entity.hr.EmpWorkOverview"),
    ENTITY_HR_EMP_EDU_INFO("entity.hr.EmpEduInfo"),
    ENTITY_HR_FAMILY_INFO("entity.hr.FamilyInfo"),
    ENTITY_HR_EMP_REWARD("entity.hr.EmpReward"),
    ENTITY_HR_EMP_FILE_ATTACHMENT("entity.hr.EmpFileAttachment"),
    ENTITY_HR_EMP_SALARY_CHANGE("entity.hr.EmpSalaryChange"),
    ENTITY_HR_EMP_OTHER_CONTRACT("entity.hr.EmpOtherContract"),
    ENTITY_HR_EMP_WORK_INFO("entity.hr.EmpWorkInfo"),
    ENTITY_HR_EMP_PRIVATE_INFO("entity.hr.EmpPrivateInfo"),
    ENTITY_HR_EMP_OTHER_ORG("entity.hr.EmpOtherOrg"),
    // ENTITY_HR_EMP_EMERGENCY_CONTACT("entity.hr.EmpEmergencyContact"),
    ;

    private final String value;

    EmpPageDetailKey(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static Set<String> getValues() {
        return Sequences.sequence(values()).map(e -> e.value).toSet();
    }
}
