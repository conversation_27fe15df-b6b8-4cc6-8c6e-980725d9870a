package com.caidaocloud.hr.core.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * created by: FoAng
 * create time: 26/6/2024 5:20 下午
 */
@FeignClient(value = "caidaocloud-hr-paas-service",
        fallback = ArchivePaasFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "archivePaasClient")
public interface ArchivePaasFeign {

    @GetMapping(value = "/api/hrpaas/v1/kv/detail")
    Result<String> getKv(@RequestParam String key);
}
