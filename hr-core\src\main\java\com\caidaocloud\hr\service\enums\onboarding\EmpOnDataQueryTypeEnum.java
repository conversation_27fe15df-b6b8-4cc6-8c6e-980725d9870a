package com.caidaocloud.hr.service.enums.onboarding;

public enum EmpOnDataQueryTypeEnum {
    HRBP("HRBP", "HRBP"),
    RECRUITERS("RECRUITERS", "RECRUITERS"),
    EMP_LEADER_EMP("EMP_LEADER_EMP", "直接上级"),
    GUARDIAN("GUARDIAN","监护人");

    private String code;
    private String name;

    EmpOnDataQueryTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
