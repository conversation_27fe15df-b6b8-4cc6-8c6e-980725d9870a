package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/15
 **/
@Data
@ApiModel("续签员工合同VO")
public class ContinueSignContractVo extends ContractVo {
    @ApiModelProperty("续签意向反馈")
    private DictSimple feedback;
    @ApiModelProperty("续签意向状态")
    private ContinueStatus continueStatus;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("工号")
    private String workno;
    @ApiModelProperty("续签审批流程状态")
    private EnumSimple continueApprovalStatus;
    @ApiModelProperty("流程key")
    private String businessKey;

    public void convertContinueApprovalStatus(boolean isZh){
        if(isZh || null == continueApprovalStatus || StringUtil.isEmpty(continueApprovalStatus.getValue())){
            return;
        }

        String key = "caidao.contract.approval_status_" + continueApprovalStatus.getValue();
        String langContinueApprovalStatus = LangUtil.getLangMsg(key);
        continueApprovalStatus.setText(null == langContinueApprovalStatus ?
                continueApprovalStatus.getText() : langContinueApprovalStatus);
    }

    public void convertFeedback(boolean isZh, Map<String, String> dictMap){
        if(isZh || null == feedback || StringUtil.isEmpty(feedback.getValue())){
            return;
        }

        if(dictMap.containsKey(feedback.getValue())){
            feedback.setText(dictMap.get(feedback.getValue()));
            return;
        }

        String langText = SpringUtil.getBean(DictService.class).getLangText(feedback.getValue(), feedback.getText(), isZh);
        if(StringUtil.isEmpty(langText)){
            return;
        }
        feedback.setText(langText);
        dictMap.put(feedback.getValue(), langText);
    }
}