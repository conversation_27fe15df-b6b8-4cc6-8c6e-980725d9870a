package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.contract.domain.enums.ContractConfigStatus;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationConfigStatus;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Data
public class ContractConfig extends DataSimple {

    private static String IDENTIFIER = "entity.hr.ContractConfig";

    private ContractApplyType signType;

    private ContractConfigStatus status;

    private String name;

    private String description;

    @DisplayAsArray
    private List<MetadataPropertyVo> displayWorkInfos = Lists.list();

    @DisplayAsArray
    private List<TagInfoKVVo> tagProperties = Lists.list();

    @DisplayAsArray
    private List<ContractPropertyConfig> enabledContractProperties = Lists.list();

    private String formDefId;

    public static List<ContractConfig> listAll() {
        return DataQuery.identifier(IDENTIFIER).limit(500, 1).filter(DataFilter.eq("tenantId",
                SecurityUserUtil.getSecurityUserInfo().getTenantId()), ContractConfig.class).getItems();
    }

    public static ContractConfig getOne(String bid) {
        val config = listAll().stream().filter(it -> it.getBid().equals(bid)).findFirst()
                .orElseThrow(() -> new ServerException("配置不存在"));
        return config;
    }

    public static void enabled(String bid) {
        List<ContractConfig> configs = listAll();
        val config = configs.stream().filter(it -> it.getBid().equals(bid)).findFirst()
                .orElseThrow(() -> new ServerException("配置不存在"));
        if (config.status.equals(ContractConfigStatus.ENABLED)) {
            throw new ServerException("配置已开启");
        }
        config.status = ContractConfigStatus.ENABLED;
        DataUpdate.identifier(IDENTIFIER).update(config);
        val type = config.signType;
        configs.stream().filter(it ->
                it.getStatus().equals(ContractConfigStatus.ENABLED) &&
                        it.getSignType().equals(type) &&
                        !it.getBid().equals(bid)).forEach(it -> {
            it.status = ContractConfigStatus.DISABLED;
            DataUpdate.identifier(IDENTIFIER).update(it);
        });
    }

    public void update() {
        val user = SecurityUserUtil.getSecurityUserInfo();
        val one = DataQuery.identifier(IDENTIFIER).one(getBid(), ContractConfig.class);
        if (one.status.equals(TerminationConfigStatus.ENABLED)) {
            if (null != one.getEnabledContractProperties() && !one.getEnabledContractProperties().isEmpty()) {
                if (null == this.getEnabledContractProperties()) {
                    throw new ServerException("标准字段不允许删除");
                }
                for (ContractPropertyConfig propertyConfig : one.getEnabledContractProperties()) {
                    this.getEnabledContractProperties().stream().filter(newPropertyConfig ->
                            newPropertyConfig.getProperty() == propertyConfig.getProperty())
                            .findFirst().orElseThrow(() -> new ServerException("标准字段不允许删除"));
                }
            }
            if (!StringUtils.equals(one.formDefId, formDefId)) {
                throw new ServerException("自定义设置不允许更新");
            }
        } else if (one.status.equals(ContractConfigStatus.DISABLED)) {
            throw new ServerException("配置当前状态不允许更新");
        } else if (one.status.equals(ContractConfigStatus.NOT_ENABLED)) {
            //do nothing
        }

        if (!one.signType.equals(signType)) {
            throw new ServerException("配置类型不允许更新");
        }
        BeanUtils.copyProperties(one, this,
                "name", "tagProperties", "description", "enabledContractProperties", "displayWorkInfos", "formDefId");
        setUpdateBy(String.valueOf(user.getUserId()));
        setUpdateTime(System.currentTimeMillis());
        DataUpdate.identifier(IDENTIFIER).update(this);
    }

    public String create() {
        val user = SecurityUserUtil.getSecurityUserInfo();
        setCreateBy(String.valueOf(user.getUserId()));
        setCreateTime(System.currentTimeMillis());
        setUpdateBy(getCreateBy());
        setUpdateTime(getCreateTime());
        status = ContractConfigStatus.NOT_ENABLED;
        return DataInsert.identifier(IDENTIFIER).insert(this);
    }
}
