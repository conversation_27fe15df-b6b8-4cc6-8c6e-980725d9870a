package com.caidaocloud.hr.service.archive.provider.fallback;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.provider.feign.ESignProviderFeign;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by: FoAng
 * create time: 7/6/2024 4:12 下午
 */
@Component
public class ESignProviderFallback implements ESignProviderFeign {

    @Override
    public Result<List<ArchiveData>> fetchArchive(String businessLine, String businessId) {
        return Result.fail("获取归档失败");
    }

    @Override
    public Result<List<ArchiveData>> fetchPageArchive(BasePage page) {
        return Result.fail("获取归档分页失败");
    }

    @Override
    public Result<Boolean> fetchAllArchive() {
        return Result.fail("操作失败");
    }
}
