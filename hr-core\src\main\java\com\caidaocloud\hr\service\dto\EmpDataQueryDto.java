package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hr.service.enums.EmpDataQueryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmpDataQueryDto {
    /**
     * 角色code 如 HRBP
     */
    private String roleCode;

    private EmpDataQueryTypeEnum typeEnum;

    private List<String> organize;

    // 员工ids
    private List<String> empIds;

    private Long dateTime;

    // 是否查询员工个人信息
    private boolean queryEmpPrivateInfo = true;
}
