package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContinueLetterDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.*;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Zhou
 * @date 2023/3/19
 */
@Slf4j
@Service
public class ContinueLetterService {
    @Autowired
    private ContractDomainService contractDomainService;
    @Autowired
    private ContinueLetterDomainService continueLetterDomainService;
    @Autowired
    private ContractTypeSetService contractTypeSetService;
    @Autowired
    private MsgNoticeService msgNoticeService;
    @Autowired
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private IWfRegisterFeign wfRegisterFeign;
    @Resource
    private ArchiveEventProducer archiveEventProducer;

    private void checkIsUseWorkflow() {
        var continueContractConfig = contractTypeSetService.getContinueContractConfig(SecurityUserUtil.getThreadLocalSecurityUserInfo().getTenantId());
        if (!continueContractConfig.getUseWorkflow()) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80019"));
        }
    }

    /**
     * 创建续签意向书
     *
     * @param continueLetterCreateDto
     */
    public void createContinueLetter(ContinueLetterCreateDto continueLetterCreateDto) {
        checkIsUseWorkflow();
        ContractDo contract = contractDomainService.getById(continueLetterCreateDto.getContractId());
        Optional<ContinueContractTemplateDto> optional = contractTypeSetService.getContinueContractByBId(continueLetterCreateDto.getTemplateId());
        if (!optional.isPresent()) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
        }
        ContinueContractTemplateDto template = optional.get();
        contractDomainService.createContinueLetter(contract, template, continueLetterCreateDto.getAttachment());

        continueLetterNotify(contract);
    }

    public void continueLetterNotify(ContractDo contract) {
        log.info("合同续签消息提醒，contractId={},empId={}", contract.getBid(), contract.getOwner().getEmpId());
        long currentTimeMillis = System.currentTimeMillis();
        EmpWorkInfoDo workInfo = empWorkInfoDomainService.getEmpWorkInfo(contract.getOwner()
                .getEmpId(), currentTimeMillis);
        msgNoticeService.sendMsgNoticeEvent(NoticeType.CONTRACT_CONTINUE_CONFIRM, Lists.list(contract.getOwner()
                .getEmpId()), contractDomainService.genContractNoticeParam(contract, workInfo, currentTimeMillis), "contract", 0);
    }

    /**
     * 续签意向书确认
     *
     * @param dto
     */
    @PaasTransactional
    public void confirm(ContinueLetterConfirmDto dto) {
        checkIsUseWorkflow();
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        ContinueLetterDo continueLetter = continueLetterDomainService.confirm(dto.getBid(), dto.getFeedback());
        ContractDo contract = contractDomainService.getById(continueLetter.getContractId());
        contract.getContinueStatus().setValue(ContinueStatus.CONFIRMED.name());
        DictSimple feedback = new DictSimple();
        feedback.setValue(dto.getFeedback());
        contract.setFeedback(feedback);
        var lastContractBid = contractDomainService.update(contract, true);
        var beginWorkflowDto = new WfBeginWorkflowDto();
        beginWorkflowDto.setFuncCode("CONTINUECONTRACT");
        beginWorkflowDto.setBusinessId(contract.getBid());
        beginWorkflowDto.setApplicantId(contract.getOwner().getEmpId());
        beginWorkflowDto.setApplicantName(contract.getOwner().getName());
        beginWorkflowDto.setEventTime(contract.getCreateTime());
        Result result = wfRegisterFeign.begin(beginWorkflowDto);
        if (!result.isSuccess() || result.getData() == null || StringUtils.isBlank(result.getData().toString())) {
            log.error("occur error when start workflow, isSuccess={} data={}", result.isSuccess(), result.getData());
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_80020"));
        }
        contractDomainService.updateContinueContract(contract.getBid(), ApprovalStatusEnum.IN_APPROVAL);
        // 合同确认文件归档
        archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT_LETTER, dto.getBid());
    }


    public ContinueTemplatePropertyDefDto getPropertyDef(String bid) {
        ContinueLetterDo continueLetter = continueLetterDomainService.getById(bid);
        ContinueTemplatePropertyDefDto def = contractTypeSetService.getPropertyDef(continueLetter.getTemplateId());
        def.setAttachment(continueLetter.getAttachment());
        return def;
    }

    public ContinueLetterDo getById(String bid) {
        ContinueLetterDo continueLetterDo = continueLetterDomainService.getById(bid);
        if (continueLetterDo == null) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
        }
        return continueLetterDo;
    }

    public ContinueLetterDo getByContractId(String contractId) {
        ContinueLetterDo letterDo = new ContinueLetterDo();
        letterDo.setContractId(contractId);
        return continueLetterDomainService.getOne(letterDo);
    }

    public List<ContinueLetterDto> getList(Long empId, ContinueStatus status) {
        List<ContinueLetterDo> list = continueLetterDomainService.selectList(empId, status);
        return ObjectConverter.convertList(list, ContinueLetterDto.class);
    }
}