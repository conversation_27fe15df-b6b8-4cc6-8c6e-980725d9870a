package com.caidaocloud.hr.service.config;

import com.caidaocloud.hr.core.feign.IDictFeignClient;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.impl.GrowthRecordImportService;
import com.caidaocloud.hr.service.util.IdCardUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@Configuration
@EnableFeignClients(basePackages = "com.caidaocloud.hr.core.feign")
public class HrConfig {
    @Resource
    private IDictFeignClient dictFeignClient;

    @Resource
    @Lazy
    private DictService dictService;

    @Bean
    @ConditionalOnMissingBean
    public DictService dictService(){
        DictService dictService = new DictService();
        dictService.setDictFeignClient(dictFeignClient);
        return dictService;
    }

    @Bean
    @ConditionalOnMissingBean
    public IdCardUtil idCardUtil(){
        IdCardUtil idCardUtil = new IdCardUtil(dictService);
        return idCardUtil;
    }

    @Bean
    @ConditionalOnMissingBean
    public GrowthRecordImportService growthRecordImportService(){
        GrowthRecordImportService growthRecordImportService = new GrowthRecordImportService();
        return growthRecordImportService;
    }
}
