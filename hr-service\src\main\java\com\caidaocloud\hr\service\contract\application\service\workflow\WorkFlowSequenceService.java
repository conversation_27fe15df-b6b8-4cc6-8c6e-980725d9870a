package com.caidaocloud.hr.service.contract.application.service.workflow;

import com.caidaocloud.hr.service.contract.application.enums.workflow.WorkFlowSequenceCode;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpOtherContractService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpOtherContractDo;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpOtherContractVo;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 流程条件序列流
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkFlowSequenceService {
    @Resource
    private ContractService contractService;
    @Resource
    private EmpOtherContractService empOtherContractService;

    public String getSeqValue(String businessId, String initiatorId, String applicantId, String code) {
        if (WorkFlowSequenceCode.CONTRACT_TYPE_SET.getValue().equals(code)) {
            // 合同类型
            ContractDo data = contractService.getById(businessId);
            if (null == data || StringUtils.isEmpty(data.getBid())) {
                log.info("getSeqValue data empty");
                return "";
            }
            return data.getContractSettingType().getValue();
        }
        return "";
    }
}
