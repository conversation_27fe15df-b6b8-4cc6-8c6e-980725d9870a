package com.caidaocloud.hr.service.contract.interfaces.facade;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.application.service.ContractRelationService;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.OrgEmpTreeQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchAutoRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractEmpQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.BatchAutoRenewalVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractEmpTreeNodeVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractExportVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractRecordsVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.OrgEmpTreeVo;
import com.caidaocloud.hr.service.contract.interfaces.vo.SearchUserVo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/hr/contract/relation/v1")
@Api(value = "/api/hr/contract/relation/v1", description = "任职信息合同关联", tags = "v1.7")
public class ContractRelationController {

	@Resource
	private ContractRelationService relationService;

	@ApiOperation("初始化员工的合同关系")
	@PostMapping("/initEmpRelation")
	public Result initEmpRelation() {
		relationService.initEmpRelation();
		return Result.ok(true);
	}

}

