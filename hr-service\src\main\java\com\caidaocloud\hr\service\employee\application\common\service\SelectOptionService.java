package com.caidaocloud.hr.service.employee.application.common.service;

import com.caidaocloud.hr.service.employee.interfaces.dto.base.SelectOptionDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class SelectOptionService {
    @Resource
    private MetadataService metadataService;

    public List getOptionEnumDef(SelectOptionDto dto){
        return Optional.ofNullable(metadataService.getPropertyDef(dto.getModel(), dto.getProp()).getEnumDef())
                .orElseGet(() -> Lists.newArrayList());
    }

    public MetadataVo modelDefinition(String modelDef){
        return metadataService.getMetadata(modelDef);
    }
}
