package com.caidaocloud.hr.service.archive.provider.feign;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 5:38 下午
 */
@FeignClient(
        name = "caidaocloud-esign-service",
        configuration = FeignConfiguration.class,
        contextId = "archive-provider-esign"
)
public interface ESignProviderFeign {


    /**
     * 根据业务ID获取归档文件
     */
    @PostMapping("/api/esign/archive/v1/data")
    Result<List<ArchiveData>> fetchArchive(@RequestParam String businessLine, @RequestParam String businessId);

    /**
     * 获取分页归档文件
     */
    @PostMapping("/api/esign/archive/v1/page")
    Result<List<ArchiveData>> fetchPageArchive(@RequestBody BasePage page);

    /**
     * 消息队列模式初始化加载历史文件
     * @return
     */
    @PostMapping("/api/esign/archive/v1/all")
    Result<Boolean> fetchAllArchive();
}
