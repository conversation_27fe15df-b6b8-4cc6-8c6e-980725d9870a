package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;

import java.util.List;

@Data
public class ContractTypeSetPo extends BaseDomainDoImpl{
    /**
     * 合同类型
     */
    DictSimple contractType;
    /**
     * 备注
     */
    String remark;
    /**
     * 合同类别
     */
    DictSimple contractClass;
    /**
     * 合同公司
     */
    List<String> company;
    /**
     * 合同公司名称
     */
    String companyTxt;
    /**
     * 签订类型
     */
    List<String> signType;
    /**
     * 签订类型名称
     */
    String signTypeTxt;
    /**
     * 员工类型bid
     */
    List<String> empType;
    /**
     * 员工类型json
     */
    String empTypeTxt;
    /**
     * 状态
     */
    EnumSimple status;
}
