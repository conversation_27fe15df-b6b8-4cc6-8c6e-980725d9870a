package com.caidaocloud.hr.service.common.infrastructure.utils;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import lombok.SneakyThrows;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class DataConvertUtil {
    public static Object convert(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof DictSimple) {
            DictSimple dictSimple = (DictSimple) value;
            value = dictSimple.getText();
        } else if (value instanceof EnumSimple) {
            EnumSimple enumSimple = (EnumSimple) value;
            value = enumSimple.getText();
        } else if (value instanceof ProvinceCity) {
            ProvinceCity provinceCity = (ProvinceCity) value;
            if (!Objects.isNull(provinceCity.getProvinceName()) && !Objects.isNull(provinceCity.getCityName())) {
                value = String.format("%s/%s", provinceCity.getProvinceName(), provinceCity.getCityName());
            } else if (!Objects.isNull(provinceCity.getProvinceName())) {
                value = provinceCity.getProvinceName();
            } else if (!Objects.isNull(provinceCity.getCityName())) {
                value = provinceCity.getCityName();
            }
        } else if (value instanceof Address) {
            Address address = (Address) value;
            value = address.doText();
        } else if (value instanceof JobGradeRange) {
            JobGradeRange jobGradeRange = (JobGradeRange) value;
            var channelName = jobGradeRange.getChannelName();
            if (StringUtils.isBlank(channelName)) {
                value = "";
            }
            if (StringUtils.isNotBlank(jobGradeRange.getStartGradeName()) && StringUtils.isNotBlank(jobGradeRange.getEndGradeName())) {
                value = String.format("%s/%s-%s", channelName, jobGradeRange.getStartGradeName(), jobGradeRange.getEndGradeName());
            } else if (StringUtils.isNotBlank(jobGradeRange.getStartGradeName())) {
                value = String.format("%s/%s", channelName, jobGradeRange.getStartGradeName());
            } else {
                value = "";
            }
        } else if (value instanceof EmpSimple) {
            EmpSimple empSimple = (EmpSimple) value;
            if (StringUtils.isNotBlank(empSimple.getName())) {
                value = empSimple.getName().replaceAll("\\t", "").replaceAll("\t", "");
            } else if (StringUtils.isNotBlank(empSimple.getEnName())) {
                value = empSimple.getEnName().replaceAll("\\t", "").replaceAll("\t", "");
            } else {
                value = "";
            }
        } else if (value instanceof PhoneSimple) {
            PhoneSimple phoneSimple = (PhoneSimple) value;
            value = phoneSimple.getValue();
        } else if (value instanceof Boolean) {
            value = Boolean.valueOf(String.valueOf(value)) ? "是" : "否";
        } else if (value instanceof Attachment) {
            Attachment attachment = (Attachment) value;
            value = CollectionUtils.isEmpty(attachment.getNames()) ? "" : FastjsonUtil.toJson(attachment.getNames());
        } else if (value instanceof SimplePropertyValue){
            SimplePropertyValue timeProperty = (SimplePropertyValue) value;
            value = ((SimplePropertyValue) value).getValue();
        }
        return value;
    }

    @SneakyThrows
    public static Map<String,Object> convert2map(Object data,Class<?> clazz){
        Map<String, Object> map = new HashMap<>();
        if (data == null) {
            return map;
        }

        // 获取 Data 类的所有字段
        Field[] fields = data.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 确保可以访问私有字段
            Object value = field.get(data);
            // 如果字段的值实现了接口 A，执行特殊处理
            if (value instanceof PropertyValue) {
                map.put(field.getName(), ((PropertyValue) value).toText());
            } else {
                map.put(field.getName(), value);
            }
        }
        return map;
    }
}
