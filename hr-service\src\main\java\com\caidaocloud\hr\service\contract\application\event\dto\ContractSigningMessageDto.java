package com.caidaocloud.hr.service.contract.application.event.dto;

import lombok.Data;

@Data
public class ContractSigningMessageDto {
    private String tenantId;
    private String userId;
    private String contractId;
    /**
     * 签署流程状态
     */
    private String processStatus;
    /**
     * 来源类型：合同：CONTRACT、异动：TRANSFER、离职：SEPARATION、 转正：REGULARIZATION
     */
    private String fromType = "CONTRACT";
    /**
     * 异动ID
     */
    private String transferId;
    /**
     * 离职ID
     */
    private String terminationApplyId;
    /**
     * 转正申请ID
     */
    private String regularizationId;
}
