package com.caidaocloud.hr.service.contract.interfaces.vo.emp.selector;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/5/11
 */
@Data
@ApiModel("员工选择器vo")
public class EmpTreeVo {
	private int pageNo = 1;
	private int pageSize = 10;
	private int total = 0;
	@ApiModelProperty("组织id")
	private String orgId;
	@ApiModelProperty("组织名称")
	private String name;
	@ApiModelProperty("组织信息")
	private List<OrgTreeVo> orgList;
	@ApiModelProperty("员工信息")
	private List<EmpNodeVo> empList;

	public EmpTreeVo(int pageNo, int pageSize) {
		this.pageNo = pageNo;
		this.pageSize = pageSize;
	}

	public EmpTreeVo(int pageNo, int pageSize, String orgId, String name) {
		this.pageNo = pageNo;
		this.pageSize = pageSize;
		this.orgId = orgId;
		this.name = name;
	}
}