package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.domain.repository.ILastContractRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Data
@Service
@Accessors(chain = true)
public class LastContractDo extends BaseDomainDoImpl<LastContractDo> {
    /**
     * 合同签署人
     */
    private String contractId;
    /**
     * 合同签署人
     */
    private EmpSimple owner;
    /**
     * 入职日期
     */
    private Long hireDate;
    /**
     * 员工状态
     */
    private EnumSimple empStatus;
    /**
     * 所属组织Id
     */
    private String organize;
    /**
     * 所属组织名称
     */
    private String organizeTxt;
    /**
     * 关联的职务ID
     */
    private String job;
    /**
     * 关联的职务名称
     */
    private String jobTxt;
    /**
     * 岗位ID
     */
    private String post;
    /**
     * 岗位名称
     */
    private String postTxt;
    /**
     * 员工类型
     */
    private DictSimple empType;
    /**
     * 签订类型
     */
    private EnumSimple signType;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 合同公司Id
     */
    private String company;
    /**
     * 所属公司名称
     */
    private String companyTxt;
    /**
     * 合同设置Bid
     */
    private String contractTypeSet;
    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;
    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;
    /**
     * 合同类别(合同类型)
     */
    private DictSimple contractType;
    /**
     * 合同期限类型
     */
    private EnumSimple periodType;
    /**
     * 合同期（月）
     */
    private Integer contractPeriod;
    /**
     * 试用期（月）
     */
    private Integer probationPeriod;
    /**
     * 试用期截止日期
     */
    private Long probationPeriodEndDate;
    /**
     * 合同签订日期
     */
    private Long signDate;
    /**
     * 合同开始日期
     */
    private Long startDate;
    /**
     * 合同结束日期
     */
    private Long endDate;
    /**
     * 合同签订次数
     */
    private Integer signTime;
    /**
     * 状态
     */
    private EnumSimple contractStatus;
    /**
     * 审批状态
     */
    private EnumSimple approvalStatus;
    /**
     * 合同终止日期
     */
    private Long terminationDate;
    /**
     * 合同终止原因
     */
    private String terminationReason;
    /**
     * 合同附件
     */
    private Attachment attachFile;
    /**
     * 工作地ID
     */
    private String workplace;
    /**
     * 工作地名称
     */
    private String workplaceTxt;
    /**
     * 工时制
     */
    private EnumSimple workHour;
    /**
     * 备注
     */
    private String remark;
    /**
     * 签署流程状态
     */
    private EnumSimple signProcessStatus;
    /**
     * 注册地址
     */
    private String registerAddress;
    /**
     * 试用期期限
     */
    private EnumSimple probation;
    /**
     * 上一份合同bid
     */
    private String lastContract;
    /**
     * 意向反馈建议
     */
    private DictSimple feedback;
    /**
     * 续签意向状态
     */
    private EnumSimple continueStatus;
    /**
     * 续签意向书id
     */
    private String continueLetter;
    /**
     * 发起签署
     */
    private EnumSimple initiateStatus;
    /**
     * 合同期（年）
     */
    private BigDecimal contractYear;
    /**
     * 合同解除日期
     */
    private Long dissolveDate;
    /**
     * 合同解除原因
     */
    private String dissolveReason;
    /**
     * 续签审批流程状态
     */
    private EnumSimple continueApprovalStatus;
    private final static String IDENTIFIER = "entity.hr.LastContract";
    @Resource
    private ILastContractRepository lastContractRepository;

    @Override
    public BaseRepository<LastContractDo> getRepository() {
        return lastContractRepository;
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    public LastContractDo syncFromContract(ContractDo contract) {
        try {
            AuthScopeFilterUtil.put(false);
            UserInfo userInfo = UserContext.preCheckUser();
            String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
            String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
            // 过滤审批中的合同
            if (!checkApproval(contract)) {
                log.info("contract '{}' under approval", contract.getBid());
                return null;
            }
            Optional<LastContractDo> dbOption = getByEmpId(contract.getOwner().getEmpId());
            if (!dbOption.isPresent()) {
                LastContractDo data = new LastContractDo();
                BeanUtil.copyProperties(contract, data, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                        "updateTime", "updateBy", "deleted");
                DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, null);
                data.setContractId(contract.getBid());
                lastContractRepository.insert(data);
                return data;
            }
            LastContractDo dbData = dbOption.get();
            // 判断最新合同的开始时间
            if (!ObjectUtil.nullSafeEquals(dbData.getContractId(), contract.getBid()) && contract.getStartDate() < dbData.getStartDate()) {
                return dbData;
            }
            BeanUtil.copyProperties(contract, dbData, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                    "updateTime", "updateBy", "deleted");
            dbData.setContractId(contract.getBid());
            dbData.setUpdateTime(System.currentTimeMillis());
            dbData.setUpdateBy(userId);
            updateById(dbData);
            return dbData;
        } finally {
            AuthScopeFilterUtil.remove();
        }
    }

    public void updateById(LastContractDo data) {
        lastContractRepository.updateById(data);
    }

    private boolean checkApproval(ContractDo contract) {
        if (contract == null || contract.getApprovalStatus() == null) {
            return false;
        }
        return ApprovalStatusEnum.PASSED.getIndex().toString().equals(contract.getApprovalStatus().getValue());
    }

    public PageResult<LastContractDo> getPage(ContractQueryDto query) {
        return lastContractRepository.selectPage(IDENTIFIER, query);
    }

    public List<LastContractDo> getByEmp(List<String> empIds) {
        return lastContractRepository.selectByEmpIds(IDENTIFIER, empIds);
    }

    public PageResult<LastContractDo> getContractPage(ContractQueryDto queryDto) {
        return lastContractRepository.selectContractPage(IDENTIFIER, queryDto);
    }

    public Optional<LastContractDo> getByEmpId(String empId) {
        return lastContractRepository.selectByEmpId(IDENTIFIER, empId);
    }

    public Optional<LastContractDo> getById(String bid) {
        var lastContractDo = lastContractRepository.selectById(bid, IDENTIFIER);
        if (lastContractDo == null) {
            return Optional.empty();
        }

        return Optional.of(lastContractDo);
    }

    public Optional<LastContractDo> getByContractId(String contractId) {
        if (StringUtils.isBlank(contractId)) {
            return Optional.empty();
        }
        return lastContractRepository.selectByContractId(IDENTIFIER, contractId);
    }
}