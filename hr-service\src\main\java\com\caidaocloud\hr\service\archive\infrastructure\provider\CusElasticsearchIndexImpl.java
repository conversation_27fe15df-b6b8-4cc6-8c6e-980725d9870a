package com.caidaocloud.hr.service.archive.infrastructure.provider;

import com.caidaocloud.hr.service.archive.annotation.DynamicIndex;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.alias.Alias;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.zxp.esclientrhl.index.ElasticsearchIndexImpl;
import org.zxp.esclientrhl.util.IndexTools;
import org.zxp.esclientrhl.util.MappingData;
import org.zxp.esclientrhl.util.MetaData;

import java.io.IOException;

@Slf4j
@Component
@AllArgsConstructor
@Primary
public class CusElasticsearchIndexImpl<T> extends ElasticsearchIndexImpl<T> {

    RestHighLevelClient cusClient;

    IndexTools indexTools;

    @Override
    public void createIndex(Class<T> clazz) throws Exception {
        boolean dynamicIndexMode = clazz.isAnnotationPresent(DynamicIndex.class);
        if (dynamicIndexMode) {
            MetaData metaData = this.indexTools.getMetaData(clazz);
            MappingSetting mappingSource = getMappingSource(clazz, metaData);
            CreateIndexRequest request;
            if (metaData.isRollover()) {
                if (metaData.getRolloverMaxIndexAgeCondition() == 0L && metaData.getRolloverMaxIndexDocsCondition() == 0L && metaData.getRolloverMaxIndexSizeCondition() == 0L) {
                    throw new RuntimeException("rolloverMaxIndexAgeCondition is zero OR rolloverMaxIndexDocsCondition is zero OR rolloverMaxIndexSizeCondition is zero");
                }
                request = new CreateIndexRequest("<" + metaData.getIndexname() + "-{now/d}-000001>");
                Alias alias = new Alias(metaData.getIndexname());
                alias.writeIndex(true);
                request.alias(alias);
            } else {
                request = new CreateIndexRequest(metaData.getIndexname());
            }
            try {
                request.settings(mappingSource.builder);
                request.mapping(metaData.getIndextype(), mappingSource.mappingSource, XContentType.JSON);
                CreateIndexResponse createIndexResponse = cusClient.indices().create(request, RequestOptions.DEFAULT);
                boolean acknowledged = createIndexResponse.isAcknowledged();
                log.info("创建nest内联索引[" + metaData.getIndexname() + "]结果：" + acknowledged);
            } catch (IOException var7) {
                log.error("createIndex error", var7);
            }
        } else {
            super.createIndex(clazz);
        }
    }

    private MappingSetting getMappingSource(Class clazz, MetaData metaData) throws Exception {
        StringBuffer source = new StringBuffer();
        source.append("  {\n    \"" + metaData.getIndextype() + "\": {\n      \"properties\": {\n");
        MappingData[] mappingDataList = this.indexTools.getMappingData(clazz);
        boolean isNgram = false;
        for (int i = 0; i < mappingDataList.length; ++i) {
            MappingData mappingData = mappingDataList[i];
            if (mappingData != null && mappingData.getField_name() != null) {
                source.append(" \"" + mappingData.getField_name() + "\": {\n");
                boolean nestType = mappingData.getNested_class() != null && mappingData.getNested_class() != Object.class;
                if (!mappingData.getDatatype().equals("nested") && !nestType) {
                    source.append(" \"type\": \"" + mappingData.getDatatype() + "\"\n");
                    if (mappingData.isNgram() && (mappingData.getDatatype().equals("text") || mappingData.getDatatype().equals("keyword"))) {
                        isNgram = true;
                    }
                    source.append(this.oneField(mappingData));
                } else {
                    source.append("\"properties\": { ");
                    if (mappingData.getNested_class() == null || mappingData.getNested_class() == Object.class) {
                        throw new Exception("无法识别的Nested_class");
                    }
                    MappingData[] submappingDataList = this.indexTools.getMappingData(mappingData.getNested_class());
                    for (int j = 0; j < submappingDataList.length; ++j) {
                        MappingData submappingData = submappingDataList[j];
                        if (submappingData != null && submappingData.getField_name() != null) {
                            source.append(" \"" + submappingData.getField_name() + "\": {\n");
                            source.append(" \"type\": \"" + submappingData.getDatatype() + "\"\n");
                            if (j == submappingDataList.length - 1) {
                                source.append(" }\n");
                            } else {
                                source.append(" },\n");
                            }
                        }
                    }
                    source.append(" }");
                }

                if (i == mappingDataList.length - 1) {
                    source.append(" }\n");
                } else {
                    source.append(" },\n");
                }
            }
        }

        source.append(" }\n");
        source.append(" }\n");
        source.append(" }\n");
        log.info(source.toString());
        Settings.Builder builder = null;
        if (isNgram) {
            builder = Settings.builder().put("index.number_of_shards", metaData.getNumber_of_shards()).put("index.number_of_replicas", metaData.getNumber_of_replicas()).put("index.max_result_window", metaData.getMaxResultWindow()).put("analysis.filter.autocomplete_filter.type", "edge_ngram").put("analysis.filter.autocomplete_filter.min_gram", 1).put("analysis.filter.autocomplete_filter.max_gram", 20).put("analysis.analyzer.autocomplete.type", "custom").put("analysis.analyzer.autocomplete.tokenizer", "standard").putList("analysis.analyzer.autocomplete.filter", new String[]{"lowercase", "autocomplete_filter"});
        } else {
            builder = Settings.builder().put("index.number_of_shards", metaData.getNumber_of_shards()).put("index.number_of_replicas", metaData.getNumber_of_replicas()).put("index.max_result_window", metaData.getMaxResultWindow());
        }

        MappingSetting mappingSetting = new MappingSetting();
        mappingSetting.mappingSource = source.toString();
        mappingSetting.builder = builder;
        return mappingSetting;
    }

    private String oneField(MappingData mappingData) {
        StringBuilder source = new StringBuilder();
        if (!StringUtils.isEmpty(mappingData.getCopy_to())) {
            source.append(" ,\"copy_to\": \"" + mappingData.getCopy_to() + "\"\n");
        }

        if (!StringUtils.isEmpty(mappingData.getNull_value())) {
            source.append(" ,\"null_value\": \"" + mappingData.getNull_value() + "\"\n");
        }

        if (!mappingData.isAllow_search()) {
            source.append(" ,\"index\": false\n");
        }

        if (!mappingData.isNgram() || !mappingData.getDatatype().equals("text") && !mappingData.getDatatype().equals("keyword")) {
            if (mappingData.getDatatype().equals("text")) {
                source.append(" ,\"analyzer\": \"" + mappingData.getAnalyzer() + "\"\n");
                source.append(" ,\"search_analyzer\": \"" + mappingData.getSearch_analyzer() + "\"\n");
            }
        } else {
            source.append(" ,\"analyzer\": \"autocomplete\"\n");
            source.append(" ,\"search_analyzer\": \"standard\"\n");
        }

        if (mappingData.isKeyword() && !mappingData.getDatatype().equals("keyword") && mappingData.isSuggest()) {
            source.append(" \n");
            source.append(" ,\"fields\": {\n");
            source.append(" \"keyword\": {\n");
            source.append(" \"type\": \"keyword\",\n");
            source.append(" \"ignore_above\": " + mappingData.getIgnore_above());
            source.append(" },\n");
            source.append(" \"suggest\": {\n");
            source.append(" \"type\": \"completion\",\n");
            source.append(" \"analyzer\": \"" + mappingData.getAnalyzer() + "\"\n");
            source.append(" }\n");
            source.append(" }\n");
        } else if (mappingData.isKeyword() && !mappingData.getDatatype().equals("keyword") && !mappingData.isSuggest()) {
            source.append(" \n");
            source.append(" ,\"fields\": {\n");
            source.append(" \"keyword\": {\n");
            source.append(" \"type\": \"keyword\",\n");
            source.append(" \"ignore_above\": " + mappingData.getIgnore_above());
            source.append(" }\n");
            source.append(" }\n");
        } else if (!mappingData.isKeyword() && mappingData.isSuggest()) {
            source.append(" \n");
            source.append(" ,\"fields\": {\n");
            source.append(" \"suggest\": {\n");
            source.append(" \"type\": \"completion\",\n");
            source.append(" \"analyzer\": \"" + mappingData.getAnalyzer() + "\"\n");
            source.append(" }\n");
            source.append(" }\n");
        }

        return source.toString();
    }

    @Data
    @NoArgsConstructor
    private static class MappingSetting {

        protected Settings.Builder builder;

        protected String mappingSource;

    }
}
