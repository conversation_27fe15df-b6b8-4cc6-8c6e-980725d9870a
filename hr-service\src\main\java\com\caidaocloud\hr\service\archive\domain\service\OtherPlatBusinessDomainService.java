package com.caidaocloud.hr.service.archive.domain.service;

import com.caidaocloud.hr.service.archive.domain.entity.OtherPlatBusinessDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OtherPlatBusinessDomainService {
    @Resource
    private OtherPlatBusinessDo otherPlatBusinessDo;

    public void insert(OtherPlatBusinessDo data) {
        otherPlatBusinessDo.insert(data);
    }

    public void update(OtherPlatBusinessDo data) {
        otherPlatBusinessDo.update(data);
    }

    public List<OtherPlatBusinessDo> selectList() {
        return otherPlatBusinessDo.selectList();
    }

    public void delete(OtherPlatBusinessDo data) {
        otherPlatBusinessDo.delete(data);
    }

}
