
package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
public class ContinueLetterBatchResultExportPo extends ContinueLetterBatchExportPo{

	@Excel(name = "匹配状态", orderNum = "17", width = 30)
	private String matchStatus;

	@Excel(name = "发起状态", orderNum = "18", width = 30)
	private String startStatus;
}
