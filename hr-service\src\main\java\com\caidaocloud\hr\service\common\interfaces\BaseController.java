package com.caidaocloud.hr.service.common.interfaces;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@ControllerAdvice
public class BaseController {
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<String> handlerException(Exception exception) {
        log.error("occur error", exception);
        if (exception instanceof ServerException) {
            return Result.fail(exception.getMessage());
        }
        return Result.fail(MessageHandler.getMessage("caidao.exception.service.error", WebUtil.getRequest()));
    }
}