package com.caidaocloud.hr.service.archive.infrastructure.repository;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.archive.infrastructure.po.ArchiveEsData;
import com.caidaocloud.hr.service.archive.interfaces.dto.ArchiveQueryDto;

import java.util.List;

/**
 *
 * created by: FoAng
 * create time: 6/6/2024 10:47 上午
 */
public interface ArchiveRepository {

    /**
     * 检查是否存在
     * todo ? 布隆算法是否可以实现，一个businessId可以对应多个
     */
    ArchiveEsData checkExist(ArchiveEsData data);

    /**
     * 获取es数据详情
     * @param archiveId
     * @return
     */
    ArchiveEsData detail(Long archiveId);


    /**
     * 新增归档文件
     * @param archiveData
     */
    void insertArchiveData(ArchiveEsData archiveData);

    /**
     * 新增修改归档文件
     * @param archiveData
     */
    void saveUpdateArchiveData(ArchiveEsData archiveData);

    /**
     * 批量新增归档文件
     * @param archiveDataList
     */
    void batchInsertArchiveData(List<ArchiveEsData> archiveDataList);

    /**
     * 批量保存更新: 仅支持相同业务线
     * @param archiveDataList
     */
    void batchSaveUpdateArchiveData(List<ArchiveEsData> archiveDataList);

    /**
     * 删除单个归档文件
     * @param esId
     */
    void deleteArchiveData(Long esId);

    /**
     * 删除多个归档文件
     * @param esIds
     */
    void deleteArchiveData(List<Long> esIds);

    /**
     * 删除归档文件：业务 + 业务类型 + 业务ID
     * @param business
     * @param businessType
     * @param businessId
     */
    void deleteArchiveData(String businessLine, String businessId);

    /**
     * 刷新es缓存数据
     */
    void refreshEsData();

    /**
     * 分页获取文件归档
     * @param queryDto
     * @return
     */
    PageResult<ArchiveEsData> pageArchiveData(ArchiveQueryDto queryDto);

    List<ArchiveEsData> getByIds(List<String> ids);
}
