package com.caidaocloud.hr.service.handler;

import com.caidaocloud.hr.core.feign.IEmpWorkInfoFeign;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 26/6/2024 5:39 下午
 */
@Component
@AllArgsConstructor
public class TransferResolveHandler implements ITypeResolveHandler{

    private IEmpWorkInfoFeign empWorkInfoFeign;

    @Override
    public List<String> getTypes(String simpleValue) {
        Result<Object> transferResult = empWorkInfoFeign.getTransferTypeList();
        if (transferResult != null && transferResult.isSuccess() && transferResult.getData() != null) {
            List<Map> mapsList = FastjsonUtil.toList(FastjsonUtil.toJson(transferResult.getData()), Map.class);
            return mapsList.stream().map(it -> it.getOrDefault("name", ""))
                    .filter(StringUtil::isNotEmpty).map(Object::toString).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
