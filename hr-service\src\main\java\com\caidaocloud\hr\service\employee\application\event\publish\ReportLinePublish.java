package com.caidaocloud.hr.service.employee.application.event.publish;

import com.caidaocloud.hr.service.employee.application.common.constant.Constant;
import com.caidaocloud.hr.service.employee.application.event.dto.ReportLineMsgDto;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ReportLinePublish {
    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;

    public void publish(ReportLineMsgDto reportLineMsgDto) {
        RabbitBaseMessage message = new RabbitBaseMessage();
        message.setExchange(Constant.HR_DIRECT_EXCHANGE);
        message.setRoutingKey(Constant.REPORT_LINE_REFRESH_ROUTING);
        message.setBody(FastjsonUtil.toJson(reportLineMsgDto));
        producer.publish(message);
    }
}