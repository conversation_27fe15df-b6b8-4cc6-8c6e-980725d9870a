package com.caidaocloud.hr.service.vo.organization.company;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024.12.6
 */
@Data
public class PayUnitVo {
    @ApiModelProperty("缴纳单位ID")
    private String bid;
    @ApiModelProperty("缴纳单位名称")
    private String unitName;
    @ApiModelProperty("缴纳单位编码")
    private String unitCode;
    @ApiModelProperty("缴纳单位名称多语言")
    private Map<String, Object> i18nUnitName;
    @ApiModelProperty("社会保险登记编号")
    private String socialSecurityNo;
    @ApiModelProperty("统一社会信用代码")
    private String socialCreditCode;
    @ApiModelProperty("经办人姓名")
    private String operatorName;
    @ApiModelProperty("经办人电话")
    private String operatorPhone;
    @ApiModelProperty("开户银行")
    private String bank;
    @ApiModelProperty("开户账号")
    private String account;
    @ApiModelProperty("服务费")
    private String serviceFee;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();

}
