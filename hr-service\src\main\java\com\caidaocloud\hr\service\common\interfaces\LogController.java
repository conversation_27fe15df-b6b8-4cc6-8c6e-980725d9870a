package com.caidaocloud.hr.service.common.interfaces;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/hr/log/v1")
@Api(value = "/api/hr/log/v1", description = "动态日志", tags = "v8.5")
public class LogController {
    @GetMapping("/level")
    public Result getLoglevel(@RequestParam(value="path") String path) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        return Result.ok(loggerContext.getLogger(path).getLevel());
    }

    @PutMapping("/level")
    public Result getLoglevel(@RequestParam(value="logLevel") String logLevel,
        @RequestParam(value="path") String path) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        loggerContext.getLogger(path) .setLevel(Level.valueOf(logLevel));
        return Result.ok();
    }
}
