package com.caidaocloud.hr.service.util;

import cn.hutool.core.util.IdcardUtil;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.enums.system.GenderEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.StringUtil;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 根据身份证计算其他信息
 *
 * <AUTHOR>
 */
public class IdCardUtil {

    private static DictService dictService;

    public IdCardUtil(DictService dictService) {
        IdCardUtil.dictService = dictService;
    }

    public static int getAgeByIdCard(String idCard) {
        return IdcardUtil.getAgeByIdCard(idCard);
    }

    public static Long getBirthByIdCard(String idCard) {
        Long birthDay = null;
        try {
            birthDay = DateUtil.parse(IdcardUtil.getBirthByIdCard(idCard), "yyyyMMdd").getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return birthDay;
    }

    public static DictSimple getGenderByIdCard(String idCard) {
        return doConverterGender(GenderEnum.getName(IdcardUtil.getGenderByIdCard(idCard)));
    }

    private static DictSimple doConverterGender(String sexTex) {
        DictSimple sex = new DictSimple();
        if (StringUtil.isEmpty(sexTex)) {
            return sex;
        }
        Map<String, KeyValue> sexMap = getGenderDictMap();
        if (sexMap.size() > 0 && null != sexMap.get(sexTex)) {
            KeyValue kv = sexMap.get(sexTex);
            sex.setText(sexTex);
            sex.setValue(String.valueOf(kv.getValue()));
        } else {
            PreCheck.preCheckArgument(sexMap.size() > 0, "性别不存在");
        }
        return sex;
    }

    private static Map<String, KeyValue> getGenderDictMap() {
        return getEnableDictMap("Gender", "Employee");
    }

    private static Map<String, KeyValue> getEnableDictMap(String typeCode, String belongModule) {
        Map<String, KeyValue> map = new HashMap<>();
        List<KeyValue> list = dictService.getEnableDictList(typeCode, belongModule);
        for (KeyValue kv : list) {
            map.put(kv.getText(), kv);
        }
        return map;
    }

}
