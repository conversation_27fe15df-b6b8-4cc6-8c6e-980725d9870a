package com.caidaocloud.hr.core.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        value = "caidaocloud-workflow-service-v2",
        fallback = WorkFlowFeignFallBack.class,
        configuration = {FeignConfiguration.class},
        contextId = "wfRegisterFeignV2"
)
public interface WorkFlowFeignClient {
    @GetMapping("/api/workflow/v2/config/def/checkEnabled")
    Result<Boolean> checkDefEnabled(@RequestParam("funCode") String funCode);
    @GetMapping("/api/workflow/v2/view/getBusinessKey")
    Result<String> getBusinessKeyBy(@RequestParam("businessKey") String businessKey);
}
