package com.caidaocloud.hr.service.contract.application.event.subscribe;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.common.constant.ContractMqConstant;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 候选人流转消息监听
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MovingContractSubscriber {
    @Resource
    private ContractService contractService;
    @Resource
    private MqMessageProducer mqMessageProducer;
    @Resource
    private Locker locker;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = ContractMqConstant.CONTRACT_QUEUE, durable = "true"),
                    exchange = @Exchange(value = ContractMqConstant.CONTRACT_EXCHANGE),
                    key = {ContractMqConstant.CONTRACT_ROUTING_KEY}
            )
    )
    public void process(String message) {
        log.info("Subscribe MovingContract Message={}", message);
        Map<String, Object> data = null;
        Lock lock = null;
        try {
            data = FastjsonUtil.convertObject(message, Map.class);
            String tenantId = (String) data.get("tenantId");
            ContractDto msg = FastjsonUtil.toObject(message, ContractDto.class);
            lock = locker.getLock(String.format("move_contract_%s_%s", tenantId, msg.getContractNo()));
            if (lock.tryLock(10, TimeUnit.SECONDS)) {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setEmpId(0L);
                userInfo.setUserId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);

                UserInfo user = new UserInfo();
                user.setTenantId(tenantId);
                user.setStaffId(0L);
                user.setUserid(0);
                user.setStaffId(0L);
                UserContext.setCurrentUser(user);
                log.info("Subscribe MovingContract tenantId={}", tenantId);

                // 入职过来的数据因入职流程程中做过了电子签署，因此合同数据流转过来时，需关闭电子文件签署
                msg.setCloseEsign(true);
                contractService.movingContract(msg);
            } else {
                log.error("process moving contract failed as has already processing");
            }
        } catch (Exception ex) {
            if (data != null) {
                int retry = (int) data.getOrDefault("retry", 1);
                if (retry <= 3) {
                    data.put("retry", ++retry);
                    var rabbitBaseMessage = new RabbitBaseMessage();
                    rabbitBaseMessage.setExchange(ContractMqConstant.CONTRACT_EXCHANGE);
                    rabbitBaseMessage.setRoutingKey(ContractMqConstant.CONTRACT_ROUTING_KEY);
                    rabbitBaseMessage.setBody(FastjsonUtil.toJson(data));
                    // 增加重试延迟，每次重试延迟递增（如每次增加10秒）
                    int delaySeconds = 10 * retry;
                    mqMessageProducer.convertAndSend(rabbitBaseMessage, delaySeconds);
                }
            }
            log.error("process MovingContractSubscriber err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
            if (lock != null) {
                lock.unlock();
            }
        }
    }
}
