package com.caidaocloud.hr.service.archive;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 1:30 下午
 */
public interface IArchiveProcessor {

    String businessLine();

    List<ArchiveData> fetchArchiveData(String businessId);

    List<ArchiveData> fetchPageArchiveData(BasePage page);

    /**
     * 消息中间件实现
     */
    default void fetchAllArchiveData() {}

    /**
     * 删除归档文件
     * @param subBusinessLine
     * @param businessId
     * @param archiveFile
     */
    default void dispatchArchiveDelEvent(String subBusinessLine, String businessId, ArchiveFile archiveFile) {}

    @PostConstruct
    default void register() {
        ArchiveProvider.archiveProcessorMap.putIfAbsent(businessLine(), this);
    }
}
