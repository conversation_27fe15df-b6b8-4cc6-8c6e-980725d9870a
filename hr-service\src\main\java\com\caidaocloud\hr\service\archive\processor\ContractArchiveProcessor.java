package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 *
 * created by: FoAng
 * create time: 13/6/2024 3:34 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractArchiveProcessor extends AbsArchiveProcessor {

    private ContractDomainService contractDomainService;

    @Override
    public String businessLine() {
        return ArchiveStandardLine.CONTRACT.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        ContractDo contractDo = contractDomainService.getById(businessId);
        if (contractDo == null) {
            log.error("[archive] fetch contract error, contractId:{}", businessId);
            return Lists.newArrayList();
        }
        return filterArchiveList(Lists.newArrayList(buildArchiveData(contractDo)));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        List<ContractDo> contractDos = contractDomainService.getArchiveData(page);
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contractDos)) {
            for (ContractDo contractDo: contractDos) {
                archiveDataList.add(buildArchiveData(contractDo));
            }
        }
        return filterArchiveList(archiveDataList);
    }

    public ArchiveData buildArchiveData(ContractDo contractDo) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setBusinessLine(ArchiveStandardLine.CONTRACT.getDesc());
        archiveData.setBusinessType(fetchBusinessType(contractDo));
        archiveData.setSubBusinessLine(fetchBusinessType(contractDo));
        archiveData.setBusinessId(contractDo.getBid());
        archiveData.setEmpId(contractDo.getOwner().getEmpId());
        archiveData.setEventTime(contractDo.getCreateTime());
        archiveData.setArchiveFiles(Lists.newArrayList(BeanUtil.convert(contractDo.getAttachFile(), ArchiveFile.class)));
        return archiveData;
    }


    private String fetchBusinessType(ContractDo contractDo) {
        EnumSimple signType = contractDo.getSignType();
        return Optional.ofNullable(signType).map(it -> Objects.requireNonNull(SignTypeEnum.getByCode(it.getValue())).getName())
                .map(it -> String.format("合同%s", it))
                .orElse("");
    }
}
