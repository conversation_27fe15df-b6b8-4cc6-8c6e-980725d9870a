package com.caidaocloud.hr.service.enums.growthrecord;

import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.util.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 业务事件类型
 *
 * <AUTHOR>
 */

public enum BusinessEventTypeEnum {
    /**
     * 入职
     */
    ONBOARDING("入职") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            PropertyValue hireDate = ((NestPropertyValue) properties).getOrDefault("hireDate", null);
            String hireDateStr = null;
            if (Objects.isNull(hireDate) || StringUtils.isBlank(hireDateStr = ((SimplePropertyValue) hireDate).getValue())) {
                return DateUtil.getCurrentTimestamp();
            }
            return Long.valueOf(hireDateStr);
        }
    },

    /**
     * 转正
     */
    CONFIRMATION("转正") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            PropertyValue confirmationDate = ((NestPropertyValue) properties).get("confirmationDate");
            String confirmationDateStr = null;
            if (Objects.isNull(confirmationDate) || StringUtils.isBlank(confirmationDateStr = ((SimplePropertyValue) confirmationDate).getValue())) {
                return DateUtil.getCurrentTimestamp();
            }
            return Long.valueOf(confirmationDateStr);
        }
    },


    /**
     * 离职
     */
    TERMINATION("离职") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            PropertyValue leaveDate = ((NestPropertyValue) properties).getOrDefault("leaveDate", null);
            String leaveDateStr = null;
            if (Objects.isNull(leaveDate) || StringUtils.isBlank(leaveDateStr = ((SimplePropertyValue) leaveDate).getValue())) {
                return DateUtil.getCurrentTimestamp();
            }
            return Long.valueOf(leaveDateStr);
        }
    },

    /**
     * 人事异动
     */
    TRANSFER("人事异动") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            Object effectiveDate = properties.getOrDefault("effectiveDate", null);
            if (Objects.isNull(effectiveDate)) {
                return DateUtil.getCurrentTimestamp();
            }
            return Long.valueOf(effectiveDate.toString());
        }
    },

    /**
     * 信息编辑
     */
    EDIT("信息编辑") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            if (properties instanceof NestPropertyValue) {
                PropertyValue dataStartTime = ((NestPropertyValue) properties).getOrDefault("dataStartTime", null);
                if (Objects.isNull(dataStartTime)) {
                    return DateUtil.getCurrentTimestamp();
                }
                String value = ((SimplePropertyValue) dataStartTime).getValue();
                return StringUtils.isNotBlank(value) ? Long.valueOf(value) : DateUtil.getCurrentTimestamp();
            }
            return DateUtil.getCurrentTimestamp();
        }
    },

    /**
     * 导入
     */
    IMPORT("导入") {
        @Override
        public Long getEffectiveDate(Map properties) {
            if (Objects.isNull(properties)) {
                return DateUtil.getCurrentTimestamp();
            }
            Object dataStartTime = properties.getOrDefault("dataStartTime", null);
            if (Objects.isNull(dataStartTime)) {
                return DateUtil.getCurrentTimestamp();
            }
            return Long.valueOf(dataStartTime.toString());
        }
    };

    private final String value;

    BusinessEventTypeEnum(String value) {
        this.value = value;
    }

    public String toValue() {
        return value;
    }

    public static BusinessEventTypeEnum fromValue(String value) {
        for (BusinessEventTypeEnum businessEventTypeEnum : BusinessEventTypeEnum.values()) {
            if (businessEventTypeEnum.toValue().equals(value)) {
                return businessEventTypeEnum;
            }
        }
        return null;
    }

    public abstract Long getEffectiveDate(Map properties);
}
