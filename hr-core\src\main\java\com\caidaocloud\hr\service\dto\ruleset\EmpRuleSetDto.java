package com.caidaocloud.hr.service.dto.ruleset;

import com.caidaocloud.hr.service.enums.onboarding.WorknoNode;
import com.caidaocloud.hr.service.enums.ruleset.EmpListOrderEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/17
 */
@Data
public class EmpRuleSetDto {
    @ApiModelProperty("工号自动生成")
    private Boolean worknoAutoCreate;
    @ApiModelProperty("工号前缀")
    private String worknoPrefix;
    @ApiModelProperty("工号起始编码")
    private Long worknoStartValue;
    @ApiModelProperty("编码长度")
    private Integer worknoLength;
    @ApiModelProperty("入职启用")
    private Boolean onBoarding;
    @ApiModelProperty("工号生成节点")
    private WorknoNode worknoNode;
    @ApiModelProperty("节点配置")
    private List<NodeConfig> nodeConfig;
    private List<String> enableEntryManage;
    private Integer createNode;
    private List<NodeSelect> select;
    private EmpListOrderEnum empListOrderEnum;

    private boolean displayEmpStatistics = true;
    private boolean displayEmpStatisticsPost = true;
    private boolean displayEmpStatisticsConcurrent = true;
    private boolean displayEmpStatisticsEmpType = true;
    private boolean displayEmpStatisticsOnJob = true;
    private boolean displayEmpStatisticsLeft = true;

    @Data
    public static class NodeConfig {
        // 方案ID
        private String planId;
        // 方案名称
        private String planName;
        // 步骤ID
        private String stepId;
        // 步骤名称
        private String stepName;
    }

    @Data
    public static class NodeSelect {
        private String scheme;
        private String step;
    }
}
