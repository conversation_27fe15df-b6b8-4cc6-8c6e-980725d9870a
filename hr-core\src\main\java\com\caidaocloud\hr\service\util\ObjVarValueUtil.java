package com.caidaocloud.hr.service.util;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class ObjVarValueUtil {
    public static String getValueFromObj(String prop, Object obj) {
        String propValue = StringUtil.EMPTY;
        if (null == obj) {
            return propValue;
        }
        String [] propLinks = prop.split("\\.");
        Object temp = obj;
        if (obj instanceof HashMap) {
            return null == ((Map) temp).get(prop) ? propValue : ((Map) temp).get(prop).toString();
        }

        int i = propLinks.length > 1 ? 1 : 0;
        for (; i < propLinks.length; ++i) {
            temp = reflectObjectFile(propLinks[i], temp);
        }
        propValue = null == temp ? propValue : temp.toString();
        return propValue;
    }

    public static String getValueFromObjV1(String prop, Object obj) {
        return getValueTxtFromObj(prop, obj, false);
    }

    public static String getValueFromObjV2(String prop, Object obj) {
        return getValueTxtFromObj(prop, obj, true);
    }

    public static String getValueTxtFromObj(String prop, Object obj, boolean isValue) {
        String propValue = StringUtil.EMPTY;
        if (null == obj) {
            return propValue;
        }

        String [] propLinks = prop.split("\\.");
        Object temp = obj;
        if (obj instanceof HashMap) {
            return null == ((Map) temp).get(prop) ? propValue : ((Map) temp).get(prop).toString();
        }

        int i = 0;
        for (; i < propLinks.length; ++i) {
            temp = reflectObjectFile(propLinks[i], temp);
            if(temp instanceof DictSimple){
                propValue = isValue ? ((DictSimple) temp).getValue() : ((DictSimple) temp).getText();
            } else if(temp instanceof EnumSimple){
                propValue = isValue ? ((EnumSimple) temp).getValue() : ((EnumSimple) temp).getText();
            } else if(temp instanceof EmpSimple){
                EmpSimple leaderEmp = (EmpSimple) temp;
                propValue = String.format("%s(%s)", leaderEmp.getName(), leaderEmp.getWorkno());
            } else if(null != temp){
                propValue = temp.toString();
            }
        }
        propValue = null == temp ? StringUtil.EMPTY : propValue;
        return propValue;
    }

    public static Object reflectObjectFile(String prop, Object obj) {
        try {
            if (null == obj) {
                return obj;
            }

            if (obj instanceof Map) {
                Map map = (Map) obj;
                return map.get(prop);
            }

            Class clazz = obj.getClass();
            Field field = clazz.getDeclaredField(prop);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("reflect failed, field={}, from={}, model={}", prop,
                    obj == null ? null : obj.getClass().getName(),
                    FastjsonUtil.toJson(obj));
        }
        return null;
    }

    public static long getMonthDiff(long startDate, long endDate) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date firstWorkTime = new Date(startDate);
        Date todayTimestamp = new Date(endDate);
        String format1 = sf.format(firstWorkTime);
        String format2 =sf.format(todayTimestamp);
        return ChronoUnit.MONTHS.between(LocalDate.parse(format1), LocalDate.parse(format2));
    }

    public static BigDecimal calcWorkAge(Long firstWorkDate, Float workAgeAdjust){
        long monthDiff = getMonthDiff(firstWorkDate, DateUtil.getCurrentTimestamp());
        BigDecimal divide = new BigDecimal(monthDiff).divide(new BigDecimal("12"), 9, BigDecimal.ROUND_HALF_UP);
        divide = divide.add(BigDecimal.valueOf(workAgeAdjust)).setScale(1, BigDecimal.ROUND_HALF_UP);
        return divide;
    }
}
