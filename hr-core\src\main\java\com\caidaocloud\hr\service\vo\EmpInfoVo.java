package com.caidaocloud.hr.service.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工信息VO")
public class EmpInfoVo {
    @ApiModelProperty("员工基本信息")
    private EmpBasicInfoVo empBasicInfo;

    /**
     * 员工任职信息
     */
    @ApiModelProperty("员工任职信息")
    private EmpWorkInfoVo empWorkInfo;

    @ApiModelProperty("员工个人信息")
    private EmpPrivateInfoVo empPrivateInfo;

    @ApiModelProperty("员工工作概况")
    private WorkOverviewVo workOverview;
}
