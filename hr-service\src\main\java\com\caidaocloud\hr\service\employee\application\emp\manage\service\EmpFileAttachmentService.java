package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.dto.EmpFileAttachmentDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpFileAttachmentDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpFileAttachmentDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ZipUtils;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.TmpImportPostBenchmarkFileDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.UploadPostJobDescDto;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.organization.application.post.service.BenchmarkPositionService;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.BenchmarkPositionDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.BenchmarkPositionQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

@Slf4j
@Service
public class EmpFileAttachmentService {
    @Resource
    private EmpFileAttachmentDomainService empFileAttachmentDomainService;

    @Resource
    private BenchmarkPositionService benchmarkPositionService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    @Resource
    private ArchiveEventProducer archiveEventProducer;

    public void save(EmpFileAttachmentDto dto) {
        EmpFileAttachmentDo data = ObjectConverter.convert(dto, EmpFileAttachmentDo.class);
        // empExtFieldService.doCusExtProps(EmpFileAttachmentDo.IDENTIFIER, dto.getExt(), data);
        empFileAttachmentDomainService.save(data, dto.getExt());
    }

    public void update(EmpFileAttachmentDto dto) {
        EmpFileAttachmentDo data = ObjectConverter.convert(dto, EmpFileAttachmentDo.class);

        Attachment nullFile = new Attachment();
        nullFile.setNames(Lists.newArrayList());
        nullFile.setUrls(Lists.newArrayList());

        if(data.getIdCardFace() != null && CollectionUtils.isEmpty(data.getIdCardFace().getUrls()) ){
            data.setIdCardFace(nullFile);
        }
        if(data.getIdCardBackFace() != null && CollectionUtils.isEmpty(data.getIdCardBackFace().getUrls())){
            data.setIdCardBackFace(nullFile);
        }
        if(data.getAcademicCertificate() != null && CollectionUtils.isEmpty(data.getAcademicCertificate().getUrls())){
            data.setAcademicCertificate(nullFile);
        }
        if(data.getDiploma() != null && CollectionUtils.isEmpty(data.getDiploma().getUrls())){
            data.setDiploma(nullFile);
        }
        if(data.getBankCard() != null && data.getBankCard().getUrls() == null){
            data.setBankCard(nullFile);
        }
        empFileAttachmentDomainService.update(data, dto.getExt());
        // 归档员工附件
        archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.EMPLOYEE, dto.getEmpId(),
                ArchivePolicy.ADD_UPDATE);
    }

    public EmpFileAttachmentDo getEmpFileAttachment(String empId, Long dateTime) {
        EmpFileAttachmentDo data = empFileAttachmentDomainService.getEmpFileAttachment(empId, dateTime);
        if (data == null) {
            return null;
        }
        if(data.getIdCardFace() != null && CollectionUtils.isEmpty(data.getIdCardFace().getUrls()) ){
            data.setIdCardFace(null);
        }
        if(data.getIdCardBackFace() != null && CollectionUtils.isEmpty(data.getIdCardBackFace().getUrls())){
            data.setIdCardBackFace(null);
        }
        if(data.getAcademicCertificate() != null && CollectionUtils.isEmpty(data.getAcademicCertificate().getUrls())){
            data.setAcademicCertificate(null);
        }
        if(data.getDiploma() != null && CollectionUtils.isEmpty(data.getDiploma().getUrls())){
            data.setDiploma(null);
        }
        if(data.getBankCard() != null && data.getBankCard().getUrls() == null){
            data.setBankCard(null);
        }
        return data;
    }

    public void uploadJobDesc(UploadPostJobDescDto jobDescDto) {
        MultipartFile zipFile = jobDescDto.getFile();
        Long path = System.currentTimeMillis();
        String outFilePath = "/tmp/" + path + "_" + zipFile.getOriginalFilename();
        log.info("uploadJobDesc outFilePath={}", outFilePath);
        InputStream inputStream = null;
        FileOutputStream os = null;
        try {
            File file = new File(outFilePath);
            file.createNewFile();
            inputStream = zipFile.getInputStream();
            os = new FileOutputStream(file);
            byte temp[] = new byte[1024];
            int size = -1;
            while ((size = inputStream.read(temp)) != -1) {
                // 每次读取1KB，直至读完
                os.write(temp, 0, size);
                os.flush();
            }
        } catch (Exception e) {
            log.error("uploadJobDesc err,{}", e.getMessage(), e);
        } finally {
            if(null != os){
                try {
                    os.close();
                } catch (Exception e){}
            }
            if(null != inputStream){
                try {
                    inputStream.close();
                } catch (Exception e){}
            }
        }
        log.info("upload Job Desc success....");

        try {
            ZipUtils.decompress(outFilePath, "/tmp/", jobDescDto.getCharsetName());
        } catch (Exception e){
            log.error("uploadJobDesc ZipUtils.decompress err,{}", e.getMessage(), e);
        }
        log.info("Job Desc decompress success....");
    }

    public void tmpImportPostBenchmark(TmpImportPostBenchmarkFileDto imported){
        //val file = imported.getFile();
        log.info(FastjsonUtil.toJson(imported));
        val query = new BenchmarkPositionQueryDto();
        query.setNameOrCode(imported.getCode());
        query.setPositionSequenceBid(imported.getPositionSequenceBid());
        if(!benchmarkPositionService.selectList(query).isEmpty()){
            return;
        }
        val dto = new BenchmarkPositionDto();
        dto.setPositionSequenceBid(imported.getPositionSequenceBid());
        dto.setCode(imported.getCode());
        dto.setName(imported.getName());
        Attachment attachment = new Attachment();
        attachment.setNames(Lists.newArrayList(imported.getFileName()));
        attachment.setUrls(Lists.newArrayList(imported.getFileUrl()));
        dto.setJobDescFiles(attachment);
        log.info(FastjsonUtil.toJson(dto));
        benchmarkPositionService.save(ObjectConverter.convert(dto, BenchmarkPositionDo.class));
    }
}
