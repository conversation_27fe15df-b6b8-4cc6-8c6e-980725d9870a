package com.caidaocloud.hr.service.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("员工合同DTO")
public class EmpInputContractDto {
    @ApiModelProperty("合同ID")
    private String contractBid;

	@ApiModelProperty("员工ID")
	private String empId;

	@ApiModelProperty("员工姓名")
	private String name;

    @ApiModelProperty("员工工号")
    private String workNo;

	@ApiModelProperty("员工英文名")
	private String enName;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private String empStatus;

    @ApiModelProperty("所属组织Id")
    private String organize;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("关联的职务ID")
    private String job;

    @ApiModelProperty("关联的职务名称")
    private String jobTxt;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("签订类型")
    private String signType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同公司Id")
    private String company;

    @ApiModelProperty("所属公司名称")
    private String companyTxt;

    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;

    @ApiModelProperty("合同设置名称（合同类型名称/合同名称）")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同类型值")
    private String contractTypeDictText;

	@ApiModelProperty("合同类型值")
	private String contractTypeDictValue;

	@ApiModelProperty("合同类型值")
	private String contractTypeDictCode;

	@ApiModelProperty("合同期限类型")
    private String periodType;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;

    @ApiModelProperty("合同签订日期")
    private Long signDate;

    @ApiModelProperty("合同开始日期")
    private Long startDate;

    @ApiModelProperty("合同结束日期")
    private Long endDate;

    @ApiModelProperty("合同签订次数")
    private Integer signTime;

    @ApiModelProperty("合同终止日期")
    private Long terminationDate;

    @ApiModelProperty("合同终止原因")
    private String terminationReason;

    @ApiModelProperty("工作地ID")
    private String workplace;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否开启工作流")
    private Boolean openWorkflow;

    @ApiModelProperty("合同发起日期")
    private Long launchDate;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("合同状态")
    private String contractStatus;

    @ApiModelProperty("是否在审批记录中隐藏")
    private boolean hideInApproval;
}
