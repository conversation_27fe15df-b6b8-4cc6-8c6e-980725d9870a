package com.caidaocloud.hr.service.contract.application.constant;

public class MsgCodeConstant {
    public final static int RENEWAL_CONTRACT_SAVE_FAIL = 30000;

    /**
     * 未生效
     */
    public final static int INACTIVE = 30001;

    /**
     * 生效中
     */
    public final static int ACTIVE = 30002;

    /**
     * 已失效
     */
    public final static int CLOSED = 30003;

    /**
     * 已解除
     */
    public final static int CANCELLED = 30004;

    /**
     * 已终止
     */
    public final static int TERMINATED = 30005;

    /**
     * 数据不存在
     */
    public final static int NO_DATA_EXIST = 30006;

    /**
     * 无效的bid
     */
    public final static int INVALID_BID = 30011;

    /**
     * 存在相同使用条件的合同
     */
    public final static int DUPLICATE_CONTRACT = 30012;

    /**
     * 缺少必要参数
     */
    public final static int MISSING_REQUIRED_PARAMETER = 30013;

    /**
     * 无效的所属合同公司bid
     */
    public final static int INVALID_COMPANY_BID = 30014;

    /**
     * 无效的参数
     */
    public final static int INVALID_PARAMETER = 30017;

    /**
     * 缺少预计毕业日期
     */
    public final static int MISSING_EXCEPT_GRADUATE_DATE = 30018;

    /**
     * 存在未生效合同
     */
    public final static int INACTIVE_CONTRACT = 30021;

    /**
     * 存在审批中合同
     */
    public final static int  IN_APPROVAL_CONTRACT = 30030;

    // 批量续签不能超过100人
    public final static int BATCH_RENEWAL_MAX_THRESHOLD = 30031;

    // 批量新签署不能超过100人
    public final static int BATCH_NEWLYSIGNED_MAX_THRESHOLD = 32013;

    /**
     * 存在未生效合同
     */
    public final static int BATCH_INACTIVE_CONTRACT = 30114;

    /**
     * 存在审批中合同
     */
    public final static int  BATCH_IN_APPROVAL_CONTRACT = 30115;

    /**
     * 已存在并行任务，请稍后重试
     * Parallel task already exists, please try again later
     */
    public final static int  PARALLEL_TASK_ALREADY_EXISTS = 30116;

    // 批量自动续签数据变化
    public final static int SELECTED_DATA_MAY_HAVE_CHANGED = 30117;

    // 合同期限范围存在冲突
    public final static int TIME_RANGE_CONFLICT = 30118;

    /**
     * 初始化员工合同关系失败
     */
    public final static int  FAILED_INIT_CONTRACT_RELATION = 30119;

    /**
     * 合同改签开始日期必须大于上一份合同开始日期
     */
    public final static int HR_ERROR_CODE_80005 = 80005;
    /**
     * 未查到上份合同数据
     */
    public final static int HR_ERROR_CODE_80006= 80006;

    /**
     * 合同编号已存在
     */
    public final static int HR_ERROR_CODE_80008= 80008;

    /**
     * 存在员工的上级为xxx的【兼任组织】+【兼任岗位】，不可删除
     */
    public final static int HR_ERROR_CODE_80011= 80011;

    /**
     * 存在组织中的组织负责人为xxx的【兼任组织】+【兼任岗位】，不可删除
     */
    public final static int HR_ERROR_CODE_80012= 80012;

    public final static int UNDER_APPROVAL_CONTRACT_RECORD = 30120;
    // 当前合同的结束日期不能超过下一份合同的开始日期!
    public final static int CONTRACT_END_DATE_INVALID = 80017;
    // 当前合同的开始日期不能早于上一份合同的结束日期!
    public final static int CONTRACT_START_DATE_INVALID = 80018;

    //工作流配置未开启
    public final static int WORK_FLOW_CONFIG_NOT_ENABLE = 80022;
    /**
     * 未生效 Inactive
     * 生效中 Active
     * 已失效 Closed
     * 已解除 Cancelled
     * 已终止 Terminated
     * 失效或作废 Invalid
     */
    public final static int INVALID = 30121;
}
