package com.caidaocloud.hr.service.contract.application.feign;

import com.caidaocloud.hr.service.contract.application.event.dto.QueryMsgConfigDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * created by: FoAng
 * create time: 1/9/2022 10:50 上午
 */
@FeignClient(
        value = "caidaocloud-message-service",
        configuration = FeignConfiguration.class,
        fallback = MessageFeignFallBack.class,
        contextId = "contractFeignMessage"
)
public interface MessageFeign {

    @PostMapping("/api/msg/config/v1/queryList")
    Result<List<MsgConfigDto>> getEnableMsgConfigList(@RequestBody QueryMsgConfigDto msgConfigDto);
}
