package com.caidaocloud.hr.service.enums;

import com.caidaocloud.util.StringUtil;

/**
 * 数据来源
 *
 * @Authot CI29616
 * @Date 2024/6/27 15:51
 * @Version 1.0
 **/
public enum DataSourceEnum {
    EMP("EMP", "员工信息"),
    ONBOARDING("ONBOARDING", "入职管理"),
    TRANSFER("TRANSFER", "人员异动"),
    CONFIRMATION("CONFIRMATION", "转正管理");

    private String code;
    private String name;


    DataSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        if (!StringUtil.isBlank(code)) {
            for (DataSourceEnum value : DataSourceEnum.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
        }
        return null;
    }
}
