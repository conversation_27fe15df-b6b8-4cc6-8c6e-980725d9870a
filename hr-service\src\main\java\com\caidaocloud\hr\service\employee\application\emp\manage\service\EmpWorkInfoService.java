package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.AuthDataScopeFeignClient;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractChangeService;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractChangeDto;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.dto.EmpWorkInfoDto;
import com.caidaocloud.hr.service.dto.adapter.EmpOutDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.common.tool.LogChangeDataUtil;
import com.caidaocloud.hr.service.employee.application.emp.constant.EmpConstants;
import com.caidaocloud.hr.service.employee.application.emp.dto.EmpStatisticsDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.StatisticsBaseDto;
import com.caidaocloud.hr.service.employee.application.event.publish.MsgTypeRefreshPublish;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPostRecordDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.*;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.enums.system.WorkStatusEnum;
import com.caidaocloud.hr.service.growthrecord.domain.entity.GrowthRecordDO;
import com.caidaocloud.hr.service.growthrecord.domain.repository.GrowthRecordRepository;
import com.caidaocloud.hr.service.growthrecord.domain.service.GrowthRecordDomainService;
import com.caidaocloud.hr.service.growthrecord.domain.service.GrowthRecordService;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.organization.domain.common.event.CommonChangeEvent;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.domain.cost.service.CostCenterDomainService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.service.JobGradeDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.CustomOrgRoleDomainService;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.domain.workplace.service.WorkplaceDomainService;
import com.caidaocloud.hr.service.search.application.service.EmpWorkInfoSearchService;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.temination.application.TerminationChangeService;
import com.caidaocloud.hr.service.util.PaasUpdUtil;
import com.caidaocloud.hr.service.vo.EmpPageVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.vo.adapter.SyncResultVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.AuthScopeUtil;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpWorkInfoService {
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private EmpWorkInfoSearchService empWorkInfoSearchService;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private WorkplaceDomainService workplaceDomainService;
    @Resource
    private CustomOrgRoleDomainService customOrgRoleDomainService;
    @Resource
    private CompanyDomainService companyDomainService;
    @Resource
    private DictService dictService;
    @Resource
    private CostCenterDomainService costCenterDomainService;
    @Resource
    private EmpSearchInfoRepository empSearchInfoRepository;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private JobGradeDomainService jobGradeDomainService;
    @Resource
    private EmpReportLineService empReportLineService;
    @Resource
    private PostDo postDo;
    @Resource
    private ContractService contractService;
    @Resource
    private EmpPostRecordService empPostRecordService;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private ITenantFeignClient tenantFeignClient;
    @Resource
    private AuthDataScopeFeignClient authDataScopeFeignClient;

    public void doOrgChange(CommonChangeEvent event) {
        log.info("do OrgChangeEvent event.... data={}", FastjsonUtil.toJson(event));
        try {
            BasePage basePage = new BasePage();
            basePage.setPageSize(5000);
            basePage.setPageNo(1);

            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(event.getTenantId());
            user.setStaffId(0L);
            user.setUserid(0);
            UserContext.setCurrentUser(user);

            // 员工信息
            doEmpWorkInfoChangeData(event, basePage);

            // 岗位信息
            basePage.setPageNo(1);
            doPostChangeData(event, basePage);

            // 主岗记录
            basePage.setPageNo(1);
            doEmpPostRecordChangeData(event, basePage);

            // 员工上级组织
            basePage.setPageNo(1);
            doEmpWorkInfoLeaderChangeData(event, basePage);

            // 兼岗信息
            basePage.setPageNo(1);
            doEmpConcurrentPostChangeData(event, basePage);

            // 兼岗上级信息
            basePage.setPageNo(1);
            doEmpConcurrentPostLeaderChangeData(event, basePage);
        } catch (Exception e) {
            log.error("do OrgChangeEvent update emp info.... err, {}", e.getMessage());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private void doEmpConcurrentPostChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("organize", event.getBid())
                .andGe("endDate", String.valueOf(event.getDataStartTime()));
        PageResult<EmpConcurrentPostDo> pr = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpConcurrentPostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }
        pr.getItems().forEach(data -> {
            data.setOrganizeTxt(event.getName());
            data.getProperties().add("organizeTxt", event.getName());
            try {
                empConcurrentPostService.updateById(data);
            } catch (Exception e) {
                log.error("Failed to update the name of the part-time organization. dataId = {}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpConcurrentPostChangeData(event, basePage);
    }

    private void doEmpConcurrentPostLeaderChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("leaderOrganize", event.getBid())
                .andGe("endDate", String.valueOf(event.getDataStartTime()));
        PageResult<EmpConcurrentPostDo> pr = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpConcurrentPostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }
        pr.getItems().forEach(data -> {
            data.setLeaderOrganizeTxt(event.getName());
            data.getProperties().add("leaderOrganizeTxt", event.getName());
            try {
                empConcurrentPostService.updateById(data);
            } catch (Exception e) {
                log.error("Failed to update the organization name of the part-time supervisor. dataId = {}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpConcurrentPostLeaderChangeData(event, basePage);
    }

    private void doEmpPostRecordChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("organize", event.getBid())
                .and(DataFilter.eq("endTime", null).orGe("endTime", String.valueOf(event.getDataStartTime())));
        PageResult<EmpPostRecordDo> pr = DataQuery.identifier("entity.hr.EmpPostRecord")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpPostRecordDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            data.setOrganizeTxt(event.getName());
            data.getProperties().add("organizeTxt", event.getName());
            try {
                empPostRecordService.updateById(data);
            } catch (Exception e) {
                log.error("Failed to update the organization name of the main job record. dataId = {}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpPostRecordChangeData(event, basePage);
    }

    private void doEmpWorkInfoChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("organize", event.getBid())
                .andGe("dataEndTime", String.valueOf(event.getDataStartTime()))
                .andEq("deleted", Boolean.FALSE.toString());
        PageResult<EmpWorkInfoDo> pr = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpWorkInfoDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        GrowthRecordDomainService grdService = SpringUtil.getBean(GrowthRecordDomainService.class);
        pr.getItems().forEach(data -> {
            if (null != data.getEmpStatus() && null != data.getEmpStatus().getValue()
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(data.getEmpStatus().getValue())) {
                // 如果员工已离职，则不处理
                return;
            }

            try {
                EmpWorkInfoDo before = new EmpWorkInfoDo();
                BeanUtils.copyProperties(data, before);
                NestPropertyValue properties = new NestPropertyValue();
                BeanUtils.copyProperties(data.getProperties(), properties);
                before.setProperties(properties);
                data.setOrganizeTxt(event.getName());
                data.getProperties().add("organizeTxt", event.getName());
                if (data.getDataStartTime() < event.getDataStartTime()) {
                    data.setDataStartTime(event.getDataStartTime());
                    empWorkInfoDomainService.updateJoinTxt(data);
                    //empWorkInfoDomainService.doProcessGrowthRecord(before, data);
                    return;
                }

                empWorkInfoDomainService.updateJoinTxt(data);
                grdService.updateGrowthRecord(data.getEmpId(), event, data.getDataStartTime(), "organizeTxt");
            } catch (Exception e) {
                log.error("Failed to update employee's organization name. empId = {}, errMsg={}",
                        data.getEmpId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }

        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpWorkInfoChangeData(event, basePage);
    }

    private void doEmpWorkInfoLeaderChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("leaderOrganize", event.getBid())
                .andGe("dataEndTime", String.valueOf(event.getDataStartTime()))
                .andEq("deleted", Boolean.FALSE.toString());
        PageResult<EmpWorkInfoDo> pr = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpWorkInfoDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        GrowthRecordDomainService grdService = SpringUtil.getBean(GrowthRecordDomainService.class);
        pr.getItems().forEach(data -> {
            if (null != data.getEmpStatus() && null != data.getEmpStatus().getValue()
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(data.getEmpStatus().getValue())) {
                // 如果员工已离职，则不处理
                return;
            }

            try {
                EmpWorkInfoDo before = new EmpWorkInfoDo();
                BeanUtils.copyProperties(data, before);
                NestPropertyValue properties = new NestPropertyValue();
                BeanUtils.copyProperties(data.getProperties(), properties);
                before.setProperties(properties);
                data.setLeaderOrganizeTxt(event.getName());
                data.getProperties().add("leaderOrganizeTxt", event.getName());
                if (data.getDataStartTime() < event.getDataStartTime()) {
                    data.setDataStartTime(event.getDataStartTime());
                    empWorkInfoDomainService.updateJoinTxt(data);
                    //empWorkInfoDomainService.doProcessGrowthRecord(before, data);
                    return;
                }

                empWorkInfoDomainService.updateJoinTxt(data);
                grdService.updateGrowthRecord(data.getEmpId(), event, data.getDataStartTime(), "leaderOrganizeTxt");
            } catch (Exception e) {
                log.error("Failed to update employee's organization name. empId = {}, errMsg={}",
                        data.getEmpId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpWorkInfoLeaderChangeData(event, basePage);
    }

    private void doPostChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
                .andEq("orgId", event.getBid())
                .andGe("dataEndTime", String.valueOf(event.getDataStartTime()));
        PageResult<PostDo> pr = DataQuery.identifier("entity.hr.Post")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, PostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            if (null != data.getStatus() && StatusEnum.DEACTIVATED.realValue().equals(data.getStatus().getValue())) {
                return;
            }

            data.setOrgName(event.getName());
            data.getProperties().add("orgName", event.getName());
            if (data.getDataStartTime() < event.getDataStartTime()) {
                data.setDataStartTime(event.getDataStartTime());
            }

            try {
                postDo.update(data);
            } catch (Exception e) {
                log.error("Failed to update the organization name of the position. dataId = {}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }

        basePage.setPageNo(basePage.getPageNo() + 1);
        doPostChangeData(event, basePage);
    }

    public void doConvertPersistObject(EmpWorkInfoDto source, DataSimple dataSimple) {
        boolean isEntity = dataSimple instanceof EmpWorkInfoDo;
        // 成本中心
        if (CollectionUtils.isNotEmpty(source.getCostCenters())) {
            List<EmpCostCenterDto> costCenters = null == source.getCostCenters() ? Lists.newArrayList() : source.getCostCenters();
            BigDecimal proportionScale = new BigDecimal("0");
            Map<String, EmpCostCenterDto> costMap = new HashMap<>();
            for (EmpCostCenterDto eccd : costCenters) {
                PreCheck.preCheckArgument(StringUtil.isEmpty(eccd.getCost()), "成本中心不能为空");
                CostCenterDo costCenterData = costCenterDomainService.selectById(eccd.getCost(), source.getDataStartTime());

                PreCheck.preCheckArgument(null == costCenterData || StringUtil.isEmpty(costCenterData.getBid()), "成本中心不存在");
                eccd.setCostTxt(costCenterData.getCostCenterName());
                proportionScale = proportionScale.add(BigDecimal.valueOf(eccd.getProportionScale()));
                // 成本中心不能重复
                PreCheck.preCheckArgument(costMap.containsKey(eccd.getCost()), LangUtil.getMsg(MsgCodeConstant.COST_CENTER_DUPLICATION_KEY));
                costMap.put(eccd.getCost(), eccd);
            }

            PreCheck.preCheckArgument(!(proportionScale.compareTo(BigDecimal.valueOf(100)) == 0), "成本中心百分比设置不正确");
            String costCenterValue = FastjsonUtil.toJson(costCenters);
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setCostCenters(costCenterValue);
            } else {
                dataSimple.getProperties().add("costCenters", costCenterValue);
            }
        }

        if (source.getOrganize() != null) {
            OrgDo org = orgDomainService.selectById(source.getOrganize(), source.getDataStartTime());
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30049));
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setOrganizeTxt(org.getName());
            } else {
                dataSimple.getProperties().add("organizeTxt", org.getName());
            }
        }

        if (source.getLeaderOrganize() != null) {
            OrgDo org = orgDomainService.selectById(source.getLeaderOrganize(), source.getDataStartTime());
            PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32045));
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setLeaderOrganizeTxt(org.getName());
            } else {
                dataSimple.getProperties().add("leaderOrganizeTxt", org.getName());
            }
        }

        if (source.getLeaderPost() != null) {
            PostDo post = postDomainService.selectById(source.getLeaderPost(), source.getDataStartTime());
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32046));
            String lpTxt = String.format(HrConstant.NAME_CODE_FORMAT, post.getName(), post.getCode());
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setLeaderPostTxt(lpTxt);
            } else {
                dataSimple.getProperties().add("leaderPostTxt", lpTxt);
            }
        }

        if (source.getCompany() != null) {
            CompanyDo company = companyDomainService.selectById(source.getCompany());
            PreCheck.preCheckArgument(null == company || null == company.getBid(), "所属岗合同公司不存在");
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setCompanyTxt(company.getCompanyName());
            } else {
                dataSimple.getProperties().add("companyTxt", company.getCompanyName());
            }
        }

        if (source.getPost() != null) {
            PostDo post = postDomainService.selectById(source.getPost(), source.getDataStartTime());
            PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30109));
            String postTxt = String.format(HrConstant.NAME_CODE_FORMAT, post.getName(), post.getCode());
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setPostTxt(postTxt);
            } else {
                dataSimple.getProperties().add("postTxt", postTxt);
            }

            // 职务如果为空取值岗位关联职务，如果不为空，则取值传参
            if (StringUtils.isEmpty(source.getJob()) && StringUtils.isNotBlank(post.getJobId())) {
                JobDo job = jobDomainService.selectById(post.getJobId());
                PreCheck.preCheckArgument(null == job || null == job.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30111));
                if (isEntity) {
                    ((EmpWorkInfoDo) dataSimple).setJob(post.getJobId());
                    ((EmpWorkInfoDo) dataSimple).setJobTxt(post.getJobName());
                } else {
                    dataSimple.getProperties().add("job", post.getJobId());
                    dataSimple.getProperties().add("jobTxt", post.getJobName());
                }
            }
        }

        if (source.getJob() != null) {
            JobDo job = jobDomainService.selectById(source.getJob());
            PreCheck.preCheckArgument(null == job || null == job.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30111));
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setJobTxt(job.getName());
            } else {
                dataSimple.getProperties().add("jobTxt", job.getName());
            }
        }

        if (source.getWorkplace() != null) {
            WorkplaceDo workplace = workplaceDomainService.selectById(source.getWorkplace());
            PreCheck.preCheckArgument(null == workplace || null == workplace.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30112));
            if (isEntity) {
                ((EmpWorkInfoDo) dataSimple).setWorkplaceTxt(workplace.getName());
            } else {
                dataSimple.getProperties().add("workplaceTxt", workplace.getName());
            }
        }
    }

    public EmpWorkInfoDo save(EmpWorkInfoDto dto) {
        EmpWorkInfoDo workInfo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        doConverter(dto, workInfo);
        doConvertPersistObject(dto, workInfo);
        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), dto.getExt(), workInfo);
        empWorkInfoDomainService.save(workInfo);
        sendRefreshMessageTemplateKey(dto.getEmpId());
        return workInfo;
    }

    public EmpWorkInfoDo onlySave(EmpWorkInfoDto dto) {
        EmpWorkInfoDo workInfo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        doConverter(dto, workInfo);

        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), dto.getExt(), workInfo);
        empWorkInfoDomainService.save(workInfo);
        sendRefreshMessageTemplateKey(dto.getEmpId());
        return workInfo;
    }


    public EmpWorkInfoDo update(EmpWorkInfoDto dto) {
        EmpWorkInfoDo workInfo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        doConverter(dto, workInfo);
        doConvertPersistObject(dto, workInfo);

        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), dto.getExt(), workInfo);
        empWorkInfoDomainService.update(workInfo);
        sendRefreshMessageTemplateKey(dto.getEmpId());
        return workInfo;
    }

    public EmpWorkInfoDo oblyUpdate(EmpWorkInfoDto dto) {
        EmpWorkInfoDo workInfo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        doConverter(dto, workInfo);

        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), dto.getExt(), workInfo);
        empWorkInfoDomainService.update(workInfo);
        sendRefreshMessageTemplateKey(dto.getEmpId());
        return workInfo;
    }

    public EmpWorkInfoDo preUpdate(EmpWorkInfoDto dto) {
        EmpWorkInfoDo workInfo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        doConverter(dto, workInfo);
        doConvertPersistObject(dto, workInfo);

        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), dto.getExt(), workInfo);
        return workInfo;
    }

    public void workInfoUpdate(EmpWorkInfoDo data) {
        empWorkInfoDomainService.update(data);
        sendRefreshMessageTemplateKey(data.getEmpId());
    }

    public EmpWorkInfoDo update(EmpWorkInfoDo workInfo, Map<String, Object> ext) {
        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), ext, workInfo);
        empWorkInfoDomainService.update(workInfo, false);
        sendRefreshMessageTemplateKey(workInfo.getEmpId());
        return workInfo;
    }

    @Autowired
    private MsgTypeRefreshPublish msgTypeRefreshPublish;

    private void sendRefreshMessageTemplateKey(String empId) {
        try {
            msgTypeRefreshPublish.sendRefreshMsgTemplate(empId);
        } catch (Exception e) {
            log.error("send message template refresh msg failed, {}", e.getMessage());
        }
    }

    public void doConverter(EmpWorkInfoDto dto, DataSimple target) {
        boolean isEntity = target instanceof EmpWorkInfoDo;
        // 员工状态
        if (StringUtil.isNotEmpty(dto.getEmpStatus())) {
            EnumSimple empStatus = new EnumSimple();
            empStatus.setValue(dto.getEmpStatus());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setEmpStatus(empStatus);
            } else {
                target.getProperties().add("empStatus", empStatus);
            }
        }

        // 试用期，probation
        if (StringUtil.isNotEmpty(dto.getProbation())) {
            EnumSimple probation = new EnumSimple();
            probation.setValue(dto.getProbation());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setProbation(probation);
            } else {
                target.getProperties().add("probation", probation);
            }
        }

        // 转正状态
        if (StringUtil.isNotEmpty(dto.getConfirmationStatus())) {
            EnumSimple confirmationStatus = new EnumSimple();
            confirmationStatus.setValue(dto.getConfirmationStatus());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setConfirmationStatus(confirmationStatus);
            } else {
                target.getProperties().add("confirmationStatus", confirmationStatus);
            }
        }

        // 员工类型 empType
        if (StringUtil.isNotEmpty(dto.getEmpType())) {
            DictSimple empType = new DictSimple();
            empType.setValue(dto.getEmpType());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setEmpType(empType);
            } else {
                target.getProperties().add("empType", empType);
            }
        }

        // 工时制 workHour
        if (StringUtil.isNotEmpty(dto.getWorkHour())) {
            EnumSimple workHour = new EnumSimple();
            workHour.setValue(dto.getWorkHour());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setWorkHour(workHour);
            } else {
                target.getProperties().add("workHour", workHour);
            }
        }

        // 入司途径 joinCompanyWay
        if (StringUtil.isNotEmpty(dto.getJoinCompanyWay())) {
            DictSimple joinCompanyWay = new DictSimple();
            joinCompanyWay.setValue(dto.getJoinCompanyWay());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setJoinCompanyWay(joinCompanyWay);
            } else {
                target.getProperties().add("joinCompanyWay", joinCompanyWay);
            }
        }

        // 公司邮箱前后空格
        if (dto.getCompanyEmail() != null) {
            if (isEntity) {
                ((EmpWorkInfoDo) target).setCompanyEmail(dto.getCompanyEmail().trim());
            } else {
                target.getProperties().add("companyEmail", dto.getCompanyEmail().trim());
            }
        }

        // 离职类型
        if (StringUtil.isNotEmpty(dto.getResignType())) {
            DictSimple dict = new DictSimple();
            dict.setValue(dto.getResignType());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setResignType(dict);
            } else {
                target.getProperties().add("resignType", dict);
            }
        }

        // 离职原因
        if (StringUtil.isNotEmpty(dto.getResignReason())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(dto.getResignReason());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setResignReason(dictSimple);
            } else {
                target.getProperties().add("resignReason", dictSimple);
            }
        }

        // 离职原因
        if (StringUtil.isNotEmpty(dto.getEmpResignReason())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(dto.getEmpResignReason());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setEmpResignReason(dictSimple);
            } else {
                target.getProperties().add("empResignReason", dictSimple);
            }
        }

        // 离职状态
        if (StringUtil.isNotEmpty(dto.getResignationStatus())) {
            EnumSimple dictSimple = new EnumSimple();
            dictSimple.setValue(dto.getResignationStatus());
            if (isEntity) {
                ((EmpWorkInfoDo) target).setResignationStatus(dictSimple);
            } else {
                target.getProperties().add("resignationStatus", dictSimple);
            }
        }
    }

    public EmpWorkInfoDo getEmpWorkInfo(String empId, Long dataTime) {
        return empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
    }

    public EmpWorkInfoDo getOrganizeHrbp(String organize, Long dataTime) {
        OrgDo org = orgDomainService.selectById(organize, dataTime);
        CustomOrgRoleDo hrbp = null;
        String empId = null;
        if (null == org || null == (hrbp = org.getHrbp())
                || null == hrbp.getLeaderEmp() || null == (empId = hrbp.getLeaderEmp().getEmpId())) {
            return null;
        }

        return getEmpWorkInfo(empId, dataTime);
    }

    public PageResult<EmpPageVo> getEmpPageListFromEs(EmpPageQueryDto dto) {
        PageResult<EmpSearchInfoPo> pageResult = empWorkInfoSearchService.selectPage(dto);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return new PageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
        }
        List<String> orgIdList = pageResult.getItems().stream().filter(po -> null != po && StringUtil.isNotEmpty(po.getOrganize())).map(EmpSearchInfoPo::getOrganize).collect(Collectors.toList());
        Map<String, String> fullPathMap = getOrgFullPath(orgIdList, dto.getDateTime());
        List<EmpPageVo> items = new ArrayList<>();
        String organizeFullPathFormat = "%s/%s/%s",
                organizePathFormat = "%s/%s",
                organizeFullPathTxt = "", tenantName = orgDomainService.getTenantName();
        for (EmpSearchInfoPo po : pageResult.getItems()) {
            if (null != po) {
                organizeFullPathTxt = formateOrgPath(fullPathMap, organizeFullPathFormat, organizePathFormat, tenantName, po);
                items.add(buildEmpPageVo(po, organizeFullPathTxt));
            }
        }

        return new PageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public String formateOrgPath(Map<String, String> fullPathMap, String organizeFullPathFormat, String organizePathFormat, String tenantName, EmpSearchInfoPo po) {
        String organizeFullPathTxt;
        organizeFullPathTxt = fullPathMap.get(po.getOrganize());
        organizeFullPathTxt = StringUtil.isEmpty(organizeFullPathTxt)
                ? String.format(organizePathFormat, tenantName, po.getOrganizeTxt())
                : String.format(organizeFullPathFormat, tenantName, organizeFullPathTxt, po.getOrganizeTxt());
        return organizeFullPathTxt;
    }

    public PageResult<EmpWorkInfoVo> getWorkInfoPageFromEs(EmpPageQueryDto dto) {
        var pageResult = empWorkInfoDomainService.selectPage(dto);
        var result = new PageResult<EmpWorkInfoVo>();
        BeanUtils.copyProperties(pageResult, result, "items");
        if (pageResult != null && !CollectionUtils.isEmpty(pageResult.getItems())) {
            var items = ObjectConverter.convertList(pageResult.getItems(), EmpWorkInfoVo.class);
            result.setItems(items);
        } else {
            result.setItems(Lists.newArrayList());
        }
        return result;
    }

    public PageResult<EmpWorkInfoVo> getWorkInfoPage(EmpPageQueryDto dto) {
        var pageResult = empWorkInfoDomainService.selectPage(dto);
        var items = ObjectConverter.convertList(pageResult.getItems(), EmpWorkInfoVo.class);
        return new PageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public Map<String, String> getOrgFullPath(List<String> orgIdList, Long dataTime) {
        Map<String, String> fullPathMap = new HashMap<>();
        if (null == orgIdList || orgIdList.isEmpty()) {
            return fullPathMap;
        }

        List<OrgDo> orgDos = orgDomainService.selectAllByIds(orgIdList, dataTime);
        if (null == orgDos || orgDos.isEmpty()) {
            return fullPathMap;
        }

        orgDos.forEach(orgDo -> {
            TreeParent pid = orgDo.getPid();
            if (null != pid && StringUtil.isNotEmpty(pid.getNamePath()) && !fullPathMap.containsKey(orgDo.getBid())) {
                fullPathMap.put(orgDo.getBid(), pid.getNamePath());
            }
        });

        return fullPathMap;
    }

    private EmpPageVo buildEmpPageVo(EmpSearchInfoPo po, String fullPath) {
        EmpPageVo vo = ObjectConverter.convert(po, EmpPageVo.class);
        vo.setOrganizePathTxt(fullPath);
        DictSimple sex = new DictSimple();
        sex.setValue(po.getSex());
        sex.setText(po.getSexText());
        vo.setSex(sex);
        vo.setSexTxt(po.getSexText());
        DictSimple empType = new DictSimple();
        empType.setValue(po.getEmpTypeValue());
        empType.setText(po.getEmpTypeText());
        vo.setEmpType(empType);
        vo.setEmpTypeTxt(po.getEmpTypeText());
        EnumSimple empStatus = new EnumSimple();
        empStatus.setText(po.getEmpStatusText());
        empStatus.setValue(po.getEmpStatusValue());
        EnumSimple confirmationStatus = new EnumSimple();
        confirmationStatus.setValue(po.getConfirmationStatus());
        confirmationStatus.setText(po.getConfirmationStatusText());
        vo.setEmpStatus(empStatus);
        vo.setEmpStatusTxt(po.getEmpStatusText());
        vo.setConfirmationStatus(confirmationStatus);
        vo.setConfirmationStatusTxt(po.getConfirmationStatusText());
        vo.setBenchPostTxt(po.getBenchPostTxt());
        vo.setBenchPostId(po.getBenchPost());
        return vo;
    }

    private void checkOrganizeId(TreeData<LabelData> data, String organize, List<String> orgIds) {
        List<TreeData<LabelData>> child = data.getChildren();
        if (data.getData().getBid().equals(organize)) {
            orgIds.add(organize);
            if (child != null && child.size() > 0) {
                for (TreeData<LabelData> childData : child) {
                    checkOrganizeId(childData, childData.getData().getBid(), orgIds);
                }
            }
        } else {
            if (child != null && child.size() > 0) {
                for (TreeData<LabelData> childData : child) {
                    checkOrganizeId(childData, organize, orgIds);
                }
            }
        }
    }

    public EmpStatisticsDto getEmpStatistics() {
        EmpStatisticsDto dto = new EmpStatisticsDto();
        // 任职状态
        List<StatisticsBaseDto> workStatusList = Lists.newArrayList();
        for (WorkStatusEnum c : WorkStatusEnum.values()) {
            workStatusList.add(new StatisticsBaseDto(c.getIndex().toString(), c.getName(), "0"));
        }

        // 兼岗信息统计
        workStatusList.add(new StatisticsBaseDto(WorkStatusEnum.CONCURRENT_POST.getIndex().toString()
                , WorkStatusEnum.CONCURRENT_POST.getName(), "0"));

        dto.setWorkStatus(workStatusList);
        // 员工状态
        List<StatisticsBaseDto> empStatusList = Lists.newArrayList();
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            empStatusList.add(new StatisticsBaseDto(c.getIndex().toString(), c.getName(), "0"));
        }
        dto.setEmpStatus(empStatusList);
        // 员工类型
        List<StatisticsBaseDto> empTypeList = Lists.newArrayList();
        List<KeyValue> empTypeDictList = dictService.getEnableDictList("EmployType", "Employee");
        if (CollectionUtils.isNotEmpty(empTypeDictList)) {
            empTypeList = empTypeDictList.stream().map(o -> new StatisticsBaseDto(o.getValue().toString(), o.getText(), "0")).collect(Collectors.toList());
        }
        dto.setEmpType(empTypeList);
        return dto;
    }

    public void photoUpload(PhotoUploadDto dto) {
        empWorkInfoDomainService.photoUpload(dto);
    }

    public List<EmpWorkInfoDo> getEmpList(List<String> companyEmailList) {
        return empWorkInfoDomainService.getEmpListByCompanyEmail(companyEmailList);
    }

    public List<EmpWorkInfoDo> getLeaveEmpList(Long leaveDate, Integer empStatus) {
        return empWorkInfoDomainService.getLeaveEmpList(leaveDate, empStatus);
    }

    public List<EmpWorkInfoDo> getEmpListByWorkno(List<String> worknoList) {
        return empWorkInfoDomainService.getEmpListByWorkNos(worknoList);
    }

    public PageResult<Map<String, String>> queryEmpColumnsPage(EmpSearchColumnsDto empSearchColumnsDto) {
        return empWorkInfoDomainService.queryEmpColumnsPage(empSearchColumnsDto);
    }

    /**
     * 根据扩展字段获取用户信息
     *
     * @param extMap
     * @return
     */
    public EmpWorkInfoDo getEmpListByExt(Map<String, String> extMap) {
        return empWorkInfoDomainService.getEmpWorkInfoByExt(extMap);
    }

    public List<EmpWorkInfoDo> getAllOrganizeHrbp(Long dataTime) {
        List<CustomOrgRoleDo> list = customOrgRoleDomainService.selectAllHrbpRole(dataTime);
        List<String> empIds = new ArrayList<>();
        for (CustomOrgRoleDo role : list) {
            if (null != role.getLeaderEmp() && null != role.getLeaderEmp().getEmpId()) {
                empIds.add(role.getLeaderEmp().getEmpId());
            }
        }
        if (empIds.size() == 0) {
            return new ArrayList<>();
        }
        List<EmpWorkInfoDo> empWorkInfos = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
        return empWorkInfos;
    }

    public List<EmpWorkInfoDo> getAllEmpList() {
        int pageNo = 1;
        int pageSize = 500;
        int total = pageSize;
        boolean flag = true;
        List<EmpWorkInfoDo> list = new ArrayList<>();
        while (flag) {
            EmpPageQueryDto dto = new EmpPageQueryDto();
            dto.setPageSize(pageSize);
            dto.setPageNo(pageNo);
            PageResult<EmpWorkInfoDo> temp = empWorkInfoDomainService.selectPage(dto);
            list.addAll(temp.getItems());
            if (temp != null && temp.getTotal() <= total) {
                flag = false;
            }
            pageNo++;
            total += pageSize;
        }
        return list;
    }

    public List<EmpWorkInfoDo> getAllOrganizeLeader(Long dataTime) {
        BasePage page = new BasePage();
        int pageNo = 1;
        int pageSize = 500;
        int total = pageSize;
        List<OrgDo> list = new ArrayList<>();
        boolean flag = true;
        while (flag) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            PageResult<OrgDo> temp = orgDomainService.selectPage(page);
            list.addAll(temp.getItems());
            if (temp.getTotal() <= total) {
                flag = false;
            }
            total += pageSize;
            ++pageNo;
        }
        List<String> empIds = new ArrayList<>();
        for (OrgDo org : list) {
            EmpSimple leader = null;
            String empId = null;
            if (null == org || null == (leader = org.getLeaderEmp()) || null == (empId = leader.getEmpId())) {
                ;
            } else {
                empIds.add(empId);
            }
        }
        if (empIds.size() == 0) {
            return new ArrayList<>();
        }
        List<EmpWorkInfoDo> empWorkInfos = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
        return empWorkInfos;
    }

    public EmpWorkInfoDo getHrbpByEmpId(String empId, Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (null == empWorkInfo || empWorkInfo.getOrganize() == null) {
            return null;
        }
        OrgDo org = orgDomainService.selectById(empWorkInfo.getOrganize(), dataTime);
        CustomOrgRoleDo hrbp = null;
        String hrbpEmpId = null;
        if (null == org || null == (hrbp = org.getHrbp())
                || null == hrbp.getLeaderEmp() || null == (hrbpEmpId = hrbp.getLeaderEmp().getEmpId())) {
            return null;
        }

        return getEmpWorkInfo(hrbpEmpId, dataTime);
    }

    public EmpWorkInfoDo getLeaderByEmpId(String empId, Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (null == empWorkInfo || empWorkInfo.getLeadEmpId() == null || empWorkInfo.getLeadEmpId().getEmpId() == null) {
            return null;
        }
        return empWorkInfoDomainService.getEmpWorkInfo(empWorkInfo.getLeadEmpId().getEmpId(), dataTime);
    }

    public EmpWorkInfoDo getOrgLeaderByEmpId(String empId, Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        if (null == empWorkInfo || empWorkInfo.getOrganize() == null) {
            return null;
        }
        OrgDo org = orgDomainService.selectById(empWorkInfo.getOrganize(), dataTime);
        EmpSimple leader = null;
        String leaderEmpId = null;
        if (null == org || null == (leader = org.getLeaderEmp()) || null == (empId = leader.getEmpId())) {
            return null;
        }

        return getEmpWorkInfo(leaderEmpId, dataTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpIds(String empIds) {
        return empWorkInfoDomainService.getEmpListByEmpIds(Arrays.asList(empIds.split(",")), System.currentTimeMillis());
    }

    public int syncDataFromDb(Long dataTime) {
        EmpPageQueryDto dto = new EmpPageQueryDto();
        dto.setDateTime(dataTime);
        boolean flag = true;
        int pageSize = 5000;
        int pageNo = 0;
        int total = 0;
        while (flag) {
            dto.setPageSize(pageSize);
            pageNo++;
            dto.setPageNo(pageNo);
            PageResult<EmpWorkInfoDo> page = empWorkInfoDomainService.selectPage(dto);
            List<String> workNos = new ArrayList<>();
            for (EmpWorkInfoDo info : page.getItems()) {
                workNos.add(info.getWorkno());
            }

            RangeQueryBuilder startQuery = QueryBuilders.rangeQuery("dataStartTime").lte(dto.getDateTime());
            RangeQueryBuilder endQuery = QueryBuilders.rangeQuery("dataEndTime").gte(dto.getDateTime());
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("workno.keyword", workNos)).must(QueryBuilders.matchQuery("tenantId", UserContext.getTenantId())).must(startQuery).must(endQuery);

            try {
                List<EmpSearchInfoPo> pageEs = searchEsDataList(boolQuery);
                for (EmpSearchInfoPo po : pageEs) {
                    if (workNos.contains(po.getWorkno())) {
                        workNos.remove(po.getWorkno());
                    }
                }
                if (workNos.size() > 0) {
                    List<EmpSearchInfoPo> addList = new ArrayList<>();
                    List<String> empIds = new ArrayList<>();
                    for (EmpWorkInfoDo info : page.getItems()) {
                        if (workNos.contains(info.getWorkno())) {
                            addList.add(convertPo(info));
                            empIds.add(info.getEmpId());
                        }
                    }
                    List<EmpPrivateInfoDo> priDos = empPrivateInfoDomainService.getEmpPrivateInfoListByEmpId(empIds);
                    Map<String, EmpPrivateInfoDo> empPriMap = new HashMap<>();
                    for (EmpPrivateInfoDo privateInfoDo : priDos) {
                        empPriMap.put(privateInfoDo.getEmpId(), privateInfoDo);
                    }

                    for (EmpSearchInfoPo po : addList) {
                        EmpPrivateInfoDo temp = empPriMap.get(po.getEmpId());
                        if (null != temp && temp.getSex() != null) {
                            po.setSexText(temp.getSex().getText());
                            po.setSex(temp.getSex().getValue());
                        } else {
                            log.info("empPriMap size={}, pageNo={}, empId={}",
                                    empPriMap.size(), pageNo, po.getEmpId());
                        }
                    }
                    empSearchInfoRepository.save(addList);
                }
            } catch (Exception e) {
                log.error("ERROR :{}", e);
            }
            total += pageSize;
            if (page.getTotal() <= total) {
                flag = false;
            }
        }

        return 0;
    }

    private List<EmpSearchInfoPo> searchEsDataList(BoolQueryBuilder boolQuery) {
        try {
            List<EmpSearchInfoPo> pageEs = empSearchInfoRepository.search(boolQuery);
            return pageEs;
        } catch (Exception e) {
            log.error("db sync es emp page search err,{}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    private EmpSearchInfoPo convertPo(EmpWorkInfoDo info) {
        EmpSearchInfoPo po = ObjectConverter.convert(info, EmpSearchInfoPo.class);
        if (info.getEmpStatus() != null) {
            po.setEmpStatusValue(info.getEmpStatus().getValue());
            po.setEmpStatusText(info.getEmpStatus().getText());
        }
        if (info.getEmpType() != null) {
            po.setEmpTypeValue(info.getEmpType().getValue());
            po.setEmpTypeText(info.getEmpType().getText());
        }
        if (info.getJoinCompanyWay() != null) {
            po.setJoinCompanyWay(info.getJoinCompanyWay().getValue());
            po.setJoinCompanyWayText(info.getJoinCompanyWay().getText());
        }
        if (info.getConfirmationStatus() != null) {
            po.setConfirmationStatus(info.getConfirmationStatus().getValue());
            po.setConfirmationStatusText(info.getConfirmationStatus().getText());
        }
        if (null != po.getProperties()) {
            po.setProperties(null);
        }
        return po;
    }

    public void syncEmpFromDb(String empId) {
        syncEmpFromDbByEmpId(empId, System.currentTimeMillis());
    }

    public void syncEmpFromDbByEmpId(String empId, Long dataTime) {
        List<EmpWorkInfoDo> page = empWorkInfoDomainService.getEmpListByEmpIds(Lists.newArrayList(empId), dataTime);
        if (null == page || page.isEmpty()) {
            return;
        }

        List<String> workNos = new ArrayList<>();
        for (EmpWorkInfoDo info : page) {
            workNos.add(info.getWorkno());
        }

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("workno.keyword", workNos)).must(QueryBuilders.matchQuery("tenantId", UserContext.getTenantId()));
        List<EmpSearchInfoPo> pageEs = null;
        try {
            pageEs = empSearchInfoRepository.search(boolQuery);
        } catch (Exception e) {
            log.error("search by es err,{}", e.getMessage(), e);
        }

        if (null != pageEs) {
            try {
                for (EmpSearchInfoPo po : pageEs) {
                    empSearchInfoRepository.delete(po);
                }
            } catch (Exception e) {
                log.error("delete es data err,{}", e.getMessage(), e);
            }
        }

        if (workNos.isEmpty()) {
            return;
        }

        List<EmpSearchInfoPo> saveList = new ArrayList<>();
        List<String> empIds = new ArrayList<>();
        for (EmpWorkInfoDo info : page) {
            if (workNos.contains(info.getWorkno())) {
                saveList.add(convertPo(info));
                empIds.add(info.getEmpId());
            }
        }

        List<EmpPrivateInfoDo> empPriList = empPrivateInfoDomainService.getEmpPrivateInfoListByEmpId(empIds);
        Map<String, EmpPrivateInfoDo> empPriMap = new HashMap<>();
        for (EmpPrivateInfoDo privateInfoDo : empPriList) {
            empPriMap.put(privateInfoDo.getEmpId(), privateInfoDo);
        }

        for (EmpSearchInfoPo po : saveList) {
            EmpPrivateInfoDo temp = empPriMap.get(po.getEmpId());
            if (null != temp && temp.getSex() != null) {
                po.setSexText(temp.getSex().getText());
                po.setSex(temp.getSex().getValue());
            }
        }

        try {
            empSearchInfoRepository.save(saveList);
        } catch (Exception e) {
            log.error("save es data err,{}", e.getMessage(), e);
        }
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpIds(List<String> empIds) {
        return getEmpWorkInfoByEmpIds(empIds, System.currentTimeMillis());
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpIds(List<String> empIds, long datetime) {
        return empWorkInfoDomainService.getEmpListByEmpIds(empIds, datetime);
    }

    public List<EmpWorkInfoDo> getNoLeaveEmpWorkInfoByEmpIds(List<String> empIds, long datetime) {
        return empWorkInfoDomainService.getNoLeaveEmpListByEmpIds(empIds, datetime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpReportLeaderDto(EmpReportLeaderDto empReportLeaderDto, long datetime) {
        return empWorkInfoDomainService.getEmpWorkInfoByEmpReportLeaderDto(empReportLeaderDto, datetime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpConcurrentPostLeaderDto(EmpConcurrentPostLeaderDto empConcurrentPostDto, long datetime) {
        return empWorkInfoDomainService.getEmpWorkInfoByEmpConcurrentPostLeaderDto(empConcurrentPostDto, datetime);
    }

    @Resource
    private GrowthRecordRepository growthRecordRepository;

    @Resource
    private GrowthRecordService growthRecordService;

    @Transactional(rollbackFor = Exception.class)
    public void deleteEmpByEmpIds(List<String> empIds) {
        UserInfo userInfo = UserContext.preCheckUser();
        // 删除账号信息，删除个人信息
        if (CollectionUtils.isNotEmpty(empIds)) {
            List<EmpWorkInfoDo> empWorkInfoDos = empWorkInfoDomainService.getEmpListByEmpIds(empIds,
                    System.currentTimeMillis());
            if (CollectionUtils.isNotEmpty(empWorkInfoDos)) {
                StringBuilder builder = new StringBuilder();
                empWorkInfoDos.forEach(it -> {
                    empWorkInfoDomainService.deleteAllWorkInfo(it);
                    // 删除关联合同信息
                    contractDomainService.deleteByEmpIds(Lists.newArrayList(it.getEmpId()));
                    builder.append(it.getName()).append("(").append(it.getWorkno()).append(")、");
                });
                if (builder.length() > 1) {
                    builder.deleteCharAt(builder.length() - 1);
                }
                LogRecordContext.putVariable("content", builder.toString());
            }
            // 删除es关联数据
            deleteEmpFromDb(empIds);
        }

        //同步MDM 成长记录删除信息；
        for (String empId : empIds) {
            List<GrowthRecordDO> recordDOS = growthRecordRepository.listByEmpId(empId, userInfo.getTenantId());
            for (GrowthRecordDO recordDO : recordDOS) {
                growthRecordService.deleteById(recordDO.getId());
                growthRecordService.sendDeleteMessage(recordDO);
            }
        }

        //保留原来删除
//        //删除员工后  停用规则；
//        Result<EmpDepartRuleDto> ruleDetail = empDepartRuleService.getRuleDetail();
//        EmpDepartRuleDto ruleDto = ruleDetail.getData();
//        if (ruleDto.getIsDeleteFlag()) {
//            for (String empId : empIds) {
//                userFeignClient.stopUser(empId);
//            }
//        }
    }

    /**
     * es删除员工信息，时间轴上所有数据均被删除
     *
     * @param empIds
     */
    public void deleteEmpFromDb(List<String> empIds) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        String tenantId = securityUserInfo.getTenantId();
        if (StringUtils.isEmpty(tenantId)) return;
        queryBuilder.must(QueryBuilders.matchQuery("tenantId", tenantId));
        queryBuilder.must(QueryBuilders.termsQuery("empId", empIds.toArray()));
        queryBuilder.must(QueryBuilders.matchQuery("deleted", false));
        try {
            List<EmpSearchInfoPo> empSearchInfoPos = empSearchInfoRepository.search(queryBuilder);
            if (CollectionUtils.isNotEmpty(empSearchInfoPos)) {
                empSearchInfoPos.forEach(it -> {
                    try {
                        empSearchInfoRepository.deleteById(it.getId());
                    } catch (Exception e) {
                        log.error("delete emp from es error, {}", e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            log.error("delete emp search error:{}", e.getMessage());
        }
    }

    /**
     * 迪士尼同步2.0
     *
     * @param insertData
     * @return
     */
    public List<SyncResultVo> syncInsert(List<EmpOutDto> insertData) {
        List<SyncResultVo> resultVos = new LinkedList<>();
        List<String> orgCodes = new LinkedList<>();
        List<String> postCodes = new LinkedList<>();
        List<String> companyCodes = new LinkedList<>();
        List<String> jobGradeCodes = new LinkedList<>();
        for (EmpOutDto data : insertData) {
            if (data.getOrganizeCode() != null) {
                orgCodes.add(data.getOrganizeCode());
            }
            if (data.getPostCode() != null) {
                postCodes.add(data.getPostCode());
            }
            if (data.getCompanyCode() != null) {
                companyCodes.add(data.getCompanyCode());
            }
            if (data.getJobGradeCode() != null) {
                jobGradeCodes.add(data.getJobGradeCode());
            }
        }
        long today = DateUtil.getCurrentTimestamp();
        Map<String, OrgDo> orgDoMap = initOrgMap(orgCodes, today);
        Map<String, PostDo> postDoMap = initPostMap(postCodes, today);
        Map<String, CompanyDo> companyDoMap = initCompanyMap(companyCodes);
        Map<String, JobGradeDo> jobGradeDoMap = initJobGrade(jobGradeCodes);

        for (EmpOutDto dto : insertData) {
            EmpWorkInfoDo workInfoDo = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
            EmpPrivateInfoDo privateInfoDo = ObjectConverter.convert(dto, EmpPrivateInfoDo.class);
            if (dto.getOrganizeCode() != null && orgDoMap.get(dto.getOrganizeCode()) == null) {
                resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "所在组织不存在"));
                continue;
            }
            if (dto.getPostCode() != null && postDoMap.get(dto.getPostCode()) == null) {
                resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "岗位不存在"));
                continue;
            }
            if (dto.getCompanyCode() != null && companyDoMap.get(dto.getCompanyCode()) == null) {
                resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "公司不存在"));
                continue;
            }
            if (dto.getJobGradeCode() != null && jobGradeDoMap.get(dto.getJobGradeCode()) == null) {
                resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "职级不存在"));
                continue;
            }
            //岗位
            if (dto.getPostCode() != null) {
                PostDo postDo = postDoMap.get(dto.getPostCode());
                if (postDo != null) {
                    workInfoDo.setPost(postDo.getBid());
                    workInfoDo.setPostTxt(postDo.getName());
                }
            }

            //公司
            if (dto.getCompanyCode() != null) {
                CompanyDo companyDo = companyDoMap.get(dto.getCompanyCode());
                if (companyDo != null) {
                    workInfoDo.setCompany(companyDo.getBid());
                    workInfoDo.setCompanyTxt(companyDo.getCompanyName());
                }
            }

            //组织
            if (dto.getOrganizeCode() != null) {
                OrgDo orgDo = orgDoMap.get(dto.getOrganizeCode());
                if (orgDo != null) {
                    workInfoDo.setOrganize(orgDo.getBid());
                    workInfoDo.setOrganizeTxt(orgDo.getFullName());
                }
            }

            //职级
            if (dto.getJobGradeCode() != null) {
                JobGradeDo jobGradeDo = jobGradeDoMap.get(dto.getJobGradeCode());
                if (jobGradeDo != null) {
                    JobGradeRange range = new JobGradeRange();
                    range.setChannel(jobGradeDo.getChannelId());
                    range.setStartLevel(jobGradeDo.getJobGradeLevel() != null ? String.valueOf(jobGradeDo.getJobGradeLevel()) : null);
                    workInfoDo.setJobGrade(range);
                }
            }

            empWorkInfoDomainService.save(workInfoDo);
            empPrivateInfoDomainService.save(privateInfoDo);
        }
        return resultVos;
    }

    public List<SyncResultVo> syncUpdate(List<EmpOutDto> updateData) {
        List<SyncResultVo> resultVos = new LinkedList<>();
        List<String> orgCodes = new LinkedList<>();
        List<String> postCodes = new LinkedList<>();
        List<String> companyCodes = new LinkedList<>();
        List<String> jobGradeCodes = new LinkedList<>();
        List<String> workNos = new LinkedList<>();
        Map<String, EmpOutDto> empWorkNoMap = new HashMap<>();
        for (EmpOutDto data : updateData) {
            empWorkNoMap.put(data.getWorkno(), data);
            workNos.add(data.getWorkno());
            if (data.getOrganizeCode() != null) {
                orgCodes.add(data.getOrganizeCode());
            }
            if (data.getPostCode() != null) {
                postCodes.add(data.getPostCode());
            }
            if (data.getCompanyCode() != null) {
                companyCodes.add(data.getCompanyCode());
            }
            if (data.getJobGradeCode() != null) {
                jobGradeCodes.add(data.getJobGradeCode());
            }
        }
        long today = DateUtil.getCurrentTimestamp();
        Map<String, OrgDo> orgDoMap = initOrgMap(orgCodes, today);
        Map<String, PostDo> postDoMap = initPostMap(postCodes, today);
        Map<String, CompanyDo> companyDoMap = initCompanyMap(companyCodes);
        Map<String, JobGradeDo> jobGradeDoMap = initJobGrade(jobGradeCodes);

        if (CollectionUtils.isNotEmpty(workNos)) {
            List<EmpWorkInfoDo> workInfoDos = empWorkInfoDomainService.getEmpListByWorkNos(workNos);
            for (EmpWorkInfoDo workInfoDo : workInfoDos) {
                EmpOutDto dto = empWorkNoMap.get(workInfoDo.getWorkno());
                BeanUtils.copyProperties(dto, workInfoDo);
                if (dto.getOrganizeCode() != null && orgDoMap.get(dto.getOrganizeCode()) == null) {
                    resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "所在组织不存在"));
                    continue;
                }
                if (dto.getPostCode() != null && postDoMap.get(dto.getPostCode()) == null) {
                    resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "岗位不存在"));
                    continue;
                }
                if (dto.getCompanyCode() != null && companyDoMap.get(dto.getCompanyCode()) == null) {
                    resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "公司不存在"));
                    continue;
                }
                if (dto.getJobGradeCode() != null && jobGradeDoMap.get(dto.getJobGradeCode()) == null) {
                    resultVos.add(new SyncResultVo(FastjsonUtil.toJson(dto), "职级不存在"));
                    continue;
                }
                //岗位
                if (dto.getPostCode() != null) {
                    PostDo postDo = postDoMap.get(dto.getPostCode());
                    if (postDo != null) {
                        workInfoDo.setPost(postDo.getBid());
                        workInfoDo.setPostTxt(postDo.getName());
                    }
                }

                //公司
                if (dto.getCompanyCode() != null) {
                    CompanyDo companyDo = companyDoMap.get(dto.getCompanyCode());
                    if (companyDo != null) {
                        workInfoDo.setCompany(companyDo.getBid());
                        workInfoDo.setCompanyTxt(companyDo.getCompanyName());
                    }
                }

                //组织
                if (dto.getOrganizeCode() != null) {
                    OrgDo orgDo = orgDoMap.get(dto.getOrganizeCode());
                    if (orgDo != null) {
                        workInfoDo.setOrganize(orgDo.getBid());
                        workInfoDo.setOrganizeTxt(orgDo.getFullName());
                    }
                }

                //职级
                if (dto.getJobGradeCode() != null) {
                    JobGradeDo jobGradeDo = jobGradeDoMap.get(dto.getJobGradeCode());
                    if (jobGradeDo != null) {
                        JobGradeRange range = new JobGradeRange();
                        range.setChannel(jobGradeDo.getChannelId());
                        range.setStartLevel(jobGradeDo.getJobGradeLevel() != null ? String.valueOf(jobGradeDo.getJobGradeLevel()) : null);
                        workInfoDo.setJobGrade(range);
                    }
                }
                empWorkInfoDomainService.update(workInfoDo);
                EmpPrivateInfoDo privateInfoDo = empPrivateInfoDomainService.getByEmpId(workInfoDo.getEmpId());
                BeanUtils.copyProperties(dto, privateInfoDo);
                empPrivateInfoDomainService.updateByEmpId(privateInfoDo);
            }
        }
        return resultVos;
    }

    private Map<String, OrgDo> initOrgMap(List<String> orgCodes, Long dateTime) {
        Map<String, OrgDo> orgDoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orgCodes)) {
            orgDoMap = orgDomainService.getOrgListByOrgCodes(orgCodes, dateTime).stream().collect(Collectors.toMap(OrgDo::getCode, st -> st));
        }
        return orgDoMap;
    }

    private Map<String, PostDo> initPostMap(List<String> postCodes, Long dateTime) {
        Map<String, PostDo> postDoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(postCodes)) {
            BasePageQueryDto dto = new BasePageQueryDto();
            dto.setStatus(0);
            dto.setDateTime(dateTime);
            dto.setPageNo(0);
            dto.setPageSize(5000);
            postDoMap = postDomainService.selectPostDoByCodes(postCodes, dto).stream().collect(Collectors.toMap(PostDo::getCode, st -> st));
        }
        return postDoMap;
    }

    private Map<String, CompanyDo> initCompanyMap(List<String> companyCodes) {
        Map<String, CompanyDo> companyDoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companyCodes)) {
            companyDoMap = companyDomainService.selectByCodes(companyCodes).stream().collect(Collectors.toMap(CompanyDo::getCompanyCode, st -> st));
        }
        return companyDoMap;
    }

    private Map<String, JobGradeDo> initJobGrade(List<String> jobGradeCodes) {
        Map<String, JobGradeDo> jobGradeDoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(jobGradeCodes)) {
            jobGradeDoMap = jobGradeDomainService.selectByCodes(jobGradeCodes).stream().collect(Collectors.toMap(JobGradeDo::getJobGradeCode, st -> st));
        }
        return jobGradeDoMap;
    }

    @PaasTransactional
    public void updatePostAndReportLine(ReportLineDto dto) {
        EmpWorkInfoDto empWorkInfoDto = dto.getWorkInfo();
        EmpWorkInfoDo beforeEmpWorkInfo = getEmpWorkInfo(empWorkInfoDto.getEmpId(), empWorkInfoDto.getDataStartTime());
        if (null == beforeEmpWorkInfo) {
            // 兼容创建后，立刻更新的情况
            beforeEmpWorkInfo = getEmpWorkInfo(empWorkInfoDto.getEmpId(), empWorkInfoDto.getDataStartTime() - 1000L);
        }
        //字段校验
        checkWorkParam(beforeEmpWorkInfo, dto.getWorkInfo());

        List<PersonnelReportDto> personList = dto.getPersonList();
        Set<String> empList = new HashSet<>();
        if (!CollectionUtils.isEmpty(personList)) {
            empList = personList.stream().map(PersonnelReportDto::getEmpId).collect(Collectors.toSet());
        }
        empList.add(dto.getWorkInfo().getEmpId());

        // 处理兼岗数据
        List<EmpConcurrentPostDo> concurrentPostDos = empReportLineService.disposeConcurrentPost(personList, dto.getMajorPost(), dto.getDataTime());

        // 处理主岗信息
        List<EmpWorkInfoDo> empWorkInfoDos = empReportLineService.disposeEmpWorkInfo(personList, dto.getMajorPost(), dto.getDataTime());

        // 处理组织信息
        List<OrgDo> orgDoList = empReportLineService.disposeOrgInfo(dto.getOrganizeList(), dto.getMajorPost(), dto.getDataTime());

        // 处理当前编辑任职信息
        EmpWorkInfoDo ecpData = preUpdate(dto.getWorkInfo());
        SpringUtil.getBean(EmpWorkInfoService.class).updatePostAndReportLine(empWorkInfoDos, concurrentPostDos, orgDoList, ecpData, new ArrayList<>(empList));

        //离职发消息
        if (EmpStatusEnum.LEAVE_JOB.realValue().equals(empWorkInfoDto.getEmpStatus()) && empWorkInfoDto.getLeaveDate() != null && dto.getDataTime().equals(empWorkInfoDto.getLeaveDate())) {
            SpringUtil.getBean(TerminationChangeService.class).doLastDayEvent(empWorkInfoDto.getLeaveDate(), empWorkInfoDto.getEmpId());
        }

        EmpWorkInfoDo afterEmpWorkInfo = getEmpWorkInfo(empWorkInfoDto.getEmpId(), empWorkInfoDto.getDataStartTime());

        //将【生效中】的合同状态更新为【终止】，终止日期更新为【离职日期】
        doChangeContract(empWorkInfoDto);

        empWorkInfoDomainService.doProcessGrowthRecord(beforeEmpWorkInfo, afterEmpWorkInfo);
        LogRecordContext.putVariable("name", beforeEmpWorkInfo.getName());
        LogRecordContext.putVariable("workno", beforeEmpWorkInfo.getWorkno());
        LogRecordContext.putVariable("change", LogChangeDataUtil.getChangeInfo(beforeEmpWorkInfo, afterEmpWorkInfo));
    }


    public void updateReportLine(ReportLineDto dto) {
        EmpWorkInfoDo ecpData = ObjectConverter.convert(dto.getWorkInfo(), EmpWorkInfoDo.class);
        List<EmpWorkInfoDo> empWorkInfoDos = empReportLineService.disposeEmpWorkInfo(dto.getPersonList(), dto.getMajorPost(), dto.getDataTime());
        List<EmpConcurrentPostDo> concurrentPostDos = empReportLineService.disposeConcurrentPost(dto.getPersonList(), dto.getMajorPost(), dto.getDataTime());
        List<OrgDo> orgDoList = empReportLineService.disposeOrgInfo(dto.getOrganizeList(), dto.getMajorPost(), dto.getDataTime());
        Set<String> empList = new HashSet<>();
        if (!CollectionUtils.isEmpty(dto.getPersonList())) {
            empList = dto.getPersonList().stream().map(PersonnelReportDto::getEmpId).collect(Collectors.toSet());
        }
        updatePostAndReportLine(empWorkInfoDos, concurrentPostDos, orgDoList, ecpData, new ArrayList<>(empList));
    }

    public void doChangeContract(EmpWorkInfoDto empWorkInfoDto) {
        log.info("doChangeContract--empWorkInfoDto: {}", FastjsonUtil.toJson(empWorkInfoDto));
        if (!EmpStatusEnum.LEAVE_JOB.realValue().equals(empWorkInfoDto.getEmpStatus())) {
            return;
        }

        List<ContractDo> empCurrentContractList = contractService.getEmpCurrentContract(empWorkInfoDto.getEmpId());
        log.info("doChangeContract--empCurrentContractList: {}", FastjsonUtil.toJson(empCurrentContractList));
        if (null == empCurrentContractList || empCurrentContractList.isEmpty()) {
            return;
        }

        ContractDo data = empCurrentContractList.get(0);
        ContractChangeDto dto = new ContractChangeDto();
        dto.setDesc("员工离职自动终止合同");
        dto.setDissolve(false);
        dto.setOpenWorkflow(false);
        // 离职日期
        dto.setEffectiveDate(empWorkInfoDto.getLeaveDate());
        // 离职原因
        dto.setReason(empWorkInfoDto.getResignReason());
        SpringUtil.getBean(ContractChangeService.class).doChangeContract(dto, data, ApprovalStatusEnum.PASSED);
    }

    private void checkWorkParam(EmpWorkInfoDo beforeEmpWorkInfo, EmpWorkInfoDto workInfo) {
        //在职→离职，离职日期不能为空
        if (EmpStatusEnum.IN_JOB.realValue().equals(beforeEmpWorkInfo.getEmpStatus().getValue()) && EmpStatusEnum.LEAVE_JOB.realValue().equals(workInfo.getEmpStatus()) && workInfo.getLeaveDate() == null) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_90001"));
        }
        //离职→在职,需清空【离职日期、离职类型、离职原因】字段值
        if (EmpStatusEnum.LEAVE_JOB.realValue().equals(beforeEmpWorkInfo.getEmpStatus().getValue()) && EmpStatusEnum.IN_JOB.realValue().equals(workInfo.getEmpStatus())) {
            if (workInfo.getLeaveDate() != null) {
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_90002"));
            }
            if (StringUtils.isNotEmpty(workInfo.getResignType())) {
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_90003"));
            }
            if (StringUtils.isNotEmpty(workInfo.getResignReason())) {
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_90004"));
            }
        }
    }

    @PaasTransactional
    public void updatePostAndReportLine(List<EmpWorkInfoDo> empWorkInfoDos, List<EmpConcurrentPostDo> concurrentPostDos,
                                        List<OrgDo> orgDoList, EmpWorkInfoDo ecpData, List<String> empList) {
        // 任职信息更新
        SpringUtil.getBean(EmpWorkInfoService.class).workInfoUpdate(ecpData);

        // 更新汇报人员及组织数据
        empReportLineService.updateEmpAndOrg(empWorkInfoDos, concurrentPostDos, orgDoList);

        // 更新汇报线
        empReportLineService.updateReportLine(empList);
    }

    public EmpStatisticsDto getEmpStatisticsFromEs(EmpPageQueryDto dto) {
        return empWorkInfoSearchService.getEmpStatisticsByQueryDto(dto);
    }

    private static final Map<String, String> empPageConfigCodeToKey = Maps.map(
            Sequences.sequence(
                    Pair.pair("empSubset_workInfo", "entity.hr.EmpWorkInfo"),
                    Pair.pair("empSubset_private", "entity.hr.EmpPrivateInfo"),
                    Pair.pair("empSubset_otherOrg", "entity.hr.EmpOtherOrg"),
                    Pair.pair("empSubset_Contract", "entity.hr.Contract"),
                    Pair.pair("empSubset_train", "entity.hr.EmpTrain"),
                    Pair.pair("empSubset_family", "entity.hr.FamilyInfo"),
                    Pair.pair("empSubset_reward", "entity.hr.EmpReward"),
                    Pair.pair("empSubset_workExperience", "entity.hr.EmpWorkOverview"),
                    Pair.pair("empSubset_eduExp", "entity.hr.EmpEduInfo"),
                    Pair.pair("empSubset_file", "entity.hr.EmpFileAttachment"),
                    Pair.pair("empSubset_salaryChange", "entity.hr.EmpSalaryChange"),
                    Pair.pair("empSubset_otherContract", "entity.hr.EmpOtherContract"),
                    Pair.pair("empSubset_certificate", "entity.hr.EmpCertificate"),
                    Pair.pair("empSubset_language", "entity.hr.EmpLanguage"),
                    Pair.pair("empSubCertificate", "entity.certificate.certificateAndEmp"),
                    Pair.pair("empSubset_insurance", "entity.hr.EmpInsuranceInfo")
            )
    );

    private static final Map<String, String> scopeAuthConvertIdentifierMapping = Maps.map(
            Sequences.sequence(
                    Pair.pair("entity.hr.EmpWorkInfo", "entity.hr.EMP_SUB_WORK"),
                    Pair.pair("entity.hr.EmpPrivateInfo", "entity.hr.EMP_SUB_PRIVATE"),
                    Pair.pair("entity.hr.EmpOtherOrg", "entity.hr.EMP_SUB_OTHER_ORG"),
                    Pair.pair("entity.hr.Contract", "entity.hr.EMP_SUB_CONTRACT"),
                    Pair.pair("entity.hr.FamilyInfo", "entity.hr.EMP_SUB_FAMILY"),
                    Pair.pair("entity.hr.EmpReward", "entity.hr.EMP_SUB_REWARD"),
                    Pair.pair("entity.hr.EmpWorkOverview", "entity.hr.EMP_SUB_WORK_OVERVIEW"),
                    Pair.pair("entity.hr.EmpEduInfo", "entity.hr.EMP_SUB_EDU"),
                    Pair.pair("entity.hr.EmpFileAttachment", "entity.hr.EMP_SUB_ATTACH"),
                    Pair.pair("entity.hr.EmpInsuranceInfo", "entity.hr.EMP_SUB_INSURANCE"),
                    Pair.pair("entity.hr.EmpSalaryChange", "entity.hr.EMP_SUB_SALARY")
            )
    );

    public EmpPageConfigDto getPageConfig(String empId, String pageId) {
        log.info("start get config " + System.currentTimeMillis());
        List<String> userAuthKey = authDataScopeFeignClient.getAuthCodes().getData().stream().map(it ->
                        empPageConfigCodeToKey.getOrDefault(it, it)).filter(it -> null != it)
                .collect(Collectors.toList());
        val pageConfig = tenantFeignClient
                .fetchEmpPageConfig(pageId).getData();
        val configDetail = FastjsonUtil.toObject(pageConfig.getStandardPageConfig(), Map.class);
        List<Map> pages = FastjsonUtil.toList(FastjsonUtil.toJson(configDetail.get("childList")),
                Map.class);
        pages.removeIf(it -> !userAuthKey.contains((String) it.get("key")) && !userAuthKey.contains(String.format("%s%s", EmpConstants.EMP_PAGE_DETAIL_PREFIX, ((String) it.get("key")).replaceAll("\\.", "_"))));
        if (StringUtils.isNotEmpty(empId)) {
            pages.removeIf(it -> {
                val identifier = (String) it.get("key");
                if (!scopeAuthConvertIdentifierMapping.containsKey(identifier)) {
                    return false;
                }
                try {
                    log.info("start get auth " + System.currentTimeMillis());
                    AuthScopeUtil.revert();
                    AuthScopeUtil.setAuthScopeIdentifier(scopeAuthConvertIdentifierMapping.get(identifier));
                    return !DataQuery.identifier("entity.hr.EmpWorkInfo").limit(1, 1).filterProperties(DataFilter.eq("empId", empId),
                            Lists.newArrayList(), System.currentTimeMillis()).getItems().isEmpty();
                } finally {
                    AuthScopeUtil.clearRevert();
                    AuthScopeUtil.clearAuthScopeIdentifier();
                    log.info("end get auth " + System.currentTimeMillis());
                }
            });
        }
        configDetail.put("childList", pages);
        pageConfig.setStandardPageConfig(FastjsonUtil.toJson(configDetail));
        return pageConfig;
    }

//    /**
//     * 员工信息导入触发的成长记录；
//     *
//     * @param empGrowthTypeDto
//     */
//    public void importGrowthRecord(EmpGrowthTypeDto empGrowthTypeDto) {
//        if (empGrowthTypeDto.getIsInsert()) {
//            //新增数据 只做 入职信息；
//            insertSaveGrowthRecord(empGrowthTypeDto.getEmpId(), System.currentTimeMillis());
//        } else {
////            LocalDateTime time = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN);
////            ZonedDateTime zonedDateTime = time.atZone(ZoneId.systemDefault());
////            EmpWorkInfoDo before = getEmpWorkInfo(empGrowthTypeDto.getEmpId(), zonedDateTime.toEpochSecond() * 1000L);
////            EmpWorkInfoDo after = getEmpWorkInfo(empGrowthTypeDto.getEmpId(), System.currentTimeMillis());
//
//            EmpWorkInfoDo before = JSON.parseObject(empGrowthTypeDto.getOldDto(), EmpWorkInfoDo.class);
//            EmpWorkInfoDo after = JSON.parseObject(empGrowthTypeDto.getNewDto(), EmpWorkInfoDo.class);
//
//            //触发 修改；
//            log.info("修改前：{}", before);
//            log.info("修改后：{}", after);
//            updateSaveGrowthRecord(before, after, empGrowthTypeDto.getBusinessEventTypeEnum(), empGrowthTypeDto.getExt());
//        }
//    }
//
//    /**
//     * 导入触发新增 save成长记录；
//     *
//     * @param empId
//     * @param dataStartTime
//     */
//    private void insertSaveGrowthRecord(String empId, Long dataStartTime) {
//        dataStartTime = null == dataStartTime ? System.currentTimeMillis() : dataStartTime;
//        EmpWorkInfoDo empWorkInfo = getEmpWorkInfo(empId, dataStartTime);
//        NestPropertyValue properties = empWorkInfo.getProperties();
//        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
//        growthRecordDto.setTenantId(UserContext.getTenantId());
//        growthRecordDto.setEmpId(empWorkInfo.getEmpId());
//        growthRecordDto.setBusinessEventType(BusinessEventTypeEnum.ONBOARDING.toString());
//        growthRecordDto.setCreateBy(UserContext.getUserId());
//        growthRecordDto.setEffectiveDate(empWorkInfo.getHireDate());
//
//        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();
//
//        //记录数据变化情况
//        properties.entrySet().stream().forEach(entry -> {
//            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
//            String propKey = entry.getKey();
//            dataItem.setProp(propKey);
//
//            if (properties.get(propKey) instanceof SimplePropertyValue) {
//                dataItem.setNewValue(((SimplePropertyValue) properties.get(propKey)).getValue());
//            } else if (properties.get(propKey) instanceof DictSimple) {
//                dataItem.setNewValue(((DictSimple) properties.get(propKey)).getValue());
//            } else if (properties.get(propKey) instanceof EnumSimple) {
//                dataItem.setNewValue(((EnumSimple) properties.get(propKey)).getValue());
//            } else if (properties.get(propKey) instanceof Address) {
//                dataItem.setNewValue(((Address) properties.get(propKey)).doText());
//            } else if (properties.get(propKey) instanceof EmpSimple) {
//                dataItem.setNewValue(((EmpSimple) properties.get(propKey)).getEmpId());
//            } else if (properties.get(propKey) instanceof JobGradeRange) {
//                dataItem.setNewValue(((JobGradeRange) properties.get(propKey)).getStartGrade());
//            } else if (properties.get(propKey) instanceof ComponentPropertyValue) {
//                log.warn("propKey:{},propType:{},value:{}", propKey, properties.get(propKey).getClass(), properties.get(propKey));
//            } else {
//                log.warn("[其他类型]propKey:{},propType:{},value:{}", propKey, properties.get(propKey) == null ? null : properties.get(propKey).getClass(), properties.get(propKey));
//            }
//
//            dataList.add(dataItem);
//        });
//        growthRecordDto.setDataList(dataList);
//        growthRecordPublish.publish(growthRecordDto);
//    }
//
//    /**
//     * 导入触发修改 save成长记录
//     *
//     * @param beforeEmpWorkInfo
//     * @param afterEmpWorkInfo
//     * @param businessEventTypeEnum
//     */
//    public void updateSaveGrowthRecord(EmpWorkInfoDo beforeEmpWorkInfo, EmpWorkInfoDo afterEmpWorkInfo, BusinessEventTypeEnum businessEventTypeEnum, Map<String, String> ext) {
//        log.info("成长记录类型：{}", businessEventTypeEnum);
//        NestPropertyValue properties = afterEmpWorkInfo.getProperties();
//        NestPropertyValue oldProperties = beforeEmpWorkInfo.getProperties();
//        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
//        growthRecordDto.setTenantId(UserContext.getTenantId());
//        growthRecordDto.setEmpId(afterEmpWorkInfo.getEmpId());
//        growthRecordDto.setBusinessEventType(businessEventTypeEnum.toString());
//        growthRecordDto.setDataChangeTitle(businessEventTypeEnum.toValue());
//        growthRecordDto.setCreateBy(afterEmpWorkInfo.getCreateBy());
//        growthRecordDto.setEffectiveDate(afterEmpWorkInfo.getDataStartTime() != 0 ? afterEmpWorkInfo.getDataStartTime() : System.currentTimeMillis());
//        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();
//
//        if (businessEventTypeEnum == BusinessEventTypeEnum.ONBOARDING) {
//            //入职
//            //入职日期
//            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
//            dataItem.setProp("hireDate");
//            dataItem.setText("入职日期");
//            dataItem.setNewValue(String.valueOf(afterEmpWorkInfo.getHireDate()));
//            dataList.add(dataItem);
//        } else if (businessEventTypeEnum == BusinessEventTypeEnum.TERMINATION) {
//            //离职
//            //离职类型 离职日期 离职原因
//            GrowthRecordDto.DataItem dataItem01 = new GrowthRecordDto.DataItem();
//            dataItem01.setProp("resignType");
//            dataItem01.setText("离职类型");
//            dataItem01.setNewValue(afterEmpWorkInfo.getResignType() != null ? afterEmpWorkInfo.getResignType().getText() : null);
//            dataList.add(dataItem01);
//
//            GrowthRecordDto.DataItem dataItem02 = new GrowthRecordDto.DataItem();
//            dataItem02.setProp("leaveDate");
//            dataItem02.setText("离职日期");
//            dataItem02.setNewValue(String.valueOf(afterEmpWorkInfo.getLeaveDate()));
//            dataList.add(dataItem02);
//
//            GrowthRecordDto.DataItem dataItem03 = new GrowthRecordDto.DataItem();
//            dataItem03.setProp("resignReason");
//            dataItem03.setText("离职原因");
//            dataItem03.setNewValue(afterEmpWorkInfo.getResignReason() != null ? afterEmpWorkInfo.getResignReason().getText() : null);
//            dataList.add(dataItem03);
//
//        } else {
//            properties.entrySet().stream().forEach(entry -> {
//                GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
//                String propKey = entry.getKey();
//                dataItem.setProp(propKey);
//                PropertyValue pValue = properties.get(propKey);
//                PropertyValue oValue = oldProperties.get(propKey);
//
//                if (pValue instanceof SimplePropertyValue
//                        || oValue instanceof SimplePropertyValue) {
//                    if (null != oValue) {
//                        dataItem.setValue(((SimplePropertyValue) oValue).getValue());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((SimplePropertyValue) pValue).getValue());
//                    }
//                } else if (pValue instanceof DictSimple
//                        || oValue instanceof DictSimple) {
//                    if (null != oValue) {
//                        dataItem.setValue(((DictSimple) oValue).getValue());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((DictSimple) pValue).getValue());
//                    }
//                } else if (pValue instanceof EnumSimple
//                        || oValue instanceof EnumSimple) {
//                    if (null != oValue) {
//                        dataItem.setValue(((EnumSimple) oValue).getValue());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((EnumSimple) pValue).getValue());
//                    }
//                } else if (pValue instanceof Address
//                        || oValue instanceof Address) {
//                    if (null != oValue) {
//                        dataItem.setValue(((Address) oValue).doText());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((Address) pValue).doText());
//                    }
//                } else if (pValue instanceof EmpSimple
//                        || oValue instanceof EmpSimple) {
//                    if (null != oValue) {
//                        dataItem.setValue(((EmpSimple) oValue).getEmpId());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((EmpSimple) pValue).getEmpId());
//                    }
//                } else if (pValue instanceof JobGradeRange
//                        || oValue instanceof JobGradeRange) {
//                    if (null != oValue) {
//                        dataItem.setValue(((JobGradeRange) oValue).getStartGrade());
//                    }
//                    if (null != pValue) {
//                        dataItem.setNewValue(((JobGradeRange) pValue).getStartGrade());
//                    }
//                } else if (pValue instanceof ComponentPropertyValue
//                        || oValue instanceof ComponentPropertyValue) {
//                    log.warn("propKey:{},propType:{},value:{}", propKey, pValue.getClass(), pValue);
//                } else {
//                    log.warn("[other type component]propKey:{},value:{}", propKey, pValue);
//                }
//                dataList.add(dataItem);
//            });
//        }
//
//        //员工外字段特殊处理；
//        if (businessEventTypeEnum == BusinessEventTypeEnum.CONFIRMATION) {
//            //转正类型 confirmationType
//            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
//            dataItem.setProp("confirmationType");
//            dataItem.setText("转正类型");
//            dataItem.setNewValue(ext.get("confirmationType"));
//            dataList.add(dataItem);
//        } else if (businessEventTypeEnum == BusinessEventTypeEnum.TRANSFER) {
//            //生效日期 effectiveDate
//            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
//            dataItem.setProp("effectiveDate");
//            dataItem.setText("生效日期");
//            dataItem.setNewValue(ext.get("effectiveDate"));
//            dataList.add(dataItem);
//        }
//        growthRecordDto.setDataList(dataList);
//        log.info("成长记录dataList：{}", dataList);
//        growthRecordPublish.publish(growthRecordDto);
//    }

    public EmpWorkInfoDo updateNowAndNext(EmpWorkInfoDo workInfo, Map<String, Object> ext) {
        empExtFieldService.doCusExtProps(workInfo.getDoIdentifier(), ext, workInfo);
        PaasUpdUtil.compareUpdate(workInfo, workInfo.getDataStartTime());
        sendRefreshMessageTemplateKey(workInfo.getEmpId());
        return workInfo;
    }

    public List<TagProperty> installNewlySignedExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "workno", "工号", 1);
        addTagPropertyToList(list, "name", "姓名", 2);
        addTagPropertyToList(list, "companyTxt", "合同公司", 3);
        addTagPropertyToList(list, "organizePathTxt", "组织全路径", 4);
        addTagPropertyToList(list, "organizeTxt", "任职组织", 5);
        addTagPropertyToList(list, "jobTxt", "职务", 6);
        addTagPropertyToList(list, "postTxt", "岗位", 7);
        addTagPropertyToList(list, "workplaceTxt", "工作地", 8);
        addTagPropertyToList(list, "empTypeTxt", "用工类型", 9);
        addTagPropertyToList(list, "hireDate", "入职日期", 10);
        addTagPropertyToList(list, "sexTxt", "员工性别", 11);
        addTagPropertyToList(list, "empStatusTxt", "员工状态", 12);
        addTagPropertyToList(list, "confirmationStatusTxt", "转正状态", 13);
        addTagPropertyToList(list, "companyEmail", "员工公司邮箱", 14);

        return list;
    }

    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public PageResult<EmpPageVo> getEmpPageListFromExport(EmpPageQueryDto dto) {
        PageResult<EmpSearchInfoPo> pageResult = empWorkInfoSearchService.selectPageForExport(dto);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return new PageResult<>(new ArrayList<>(), pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
        }
        List<String> orgIdList = pageResult.getItems().stream().filter(po -> null != po && StringUtil.isNotEmpty(po.getOrganize())).map(EmpSearchInfoPo::getOrganize).collect(Collectors.toList());
        Map<String, String> fullPathMap = getOrgFullPath(orgIdList, dto.getDateTime());
        List<EmpPageVo> items = new ArrayList<>();
        String organizeFullPathFormat = "%s/%s/%s",
                organizePathFormat = "%s/%s",
                organizeFullPathTxt = "", tenantName = orgDomainService.getTenantName();
        for (EmpSearchInfoPo po : pageResult.getItems()) {
            if (null != po) {
                organizeFullPathTxt = fullPathMap.get(po.getOrganize());
                organizeFullPathTxt = StringUtil.isEmpty(organizeFullPathTxt)
                        ? String.format(organizePathFormat, tenantName, po.getOrganizeTxt())
                        : String.format(organizeFullPathFormat, tenantName, organizeFullPathTxt, po.getOrganizeTxt());
                items.add(buildEmpPageVo(po, organizeFullPathTxt));
            }
        }

        return new PageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public List<EmpWorkInfoDo> getTimeLine(String empId) {
        return empWorkInfoDomainService.getTimeLine(empId);
    }
}
