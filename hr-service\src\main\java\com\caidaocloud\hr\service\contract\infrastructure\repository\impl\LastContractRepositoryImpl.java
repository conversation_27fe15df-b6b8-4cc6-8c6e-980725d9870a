package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.domain.entity.LastContractDo;
import com.caidaocloud.hr.service.contract.domain.repository.ILastContractRepository;
import com.caidaocloud.hr.service.contract.infrastructure.constant.DataScopeConstant;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.util.BeanUtil;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sets;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.util.BeanUtil.buildData;

@Repository
public class LastContractRepositoryImpl extends BaseRepositoryImpl<LastContractDo> implements ILastContractRepository {

    @Override
    public Optional<LastContractDo> selectByEmpId(String identifier, String empId) {
        PageResult<LastContractDo> pageResult = DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .filter(DataFilter.eq("owner$empId", empId), LastContractDo.class);
        return pageResult.getItems().stream().findFirst();
    }

    @Override
    public PageResult<LastContractDo> selectPage(String identifier, ContractQueryDto queryDto) {
        DataFilter empFilter = getBaseFilter();
        DataFilter contractFilter = getBaseFilter();
        if (StringUtils.isNotEmpty(queryDto.getOrganize())) {
            empFilter = empFilter.andEq("organize", queryDto.getOrganize());
        }
        if (StringUtils.isNotEmpty(queryDto.getKeyword())) {
            empFilter = empFilter.and(DataFilter.regex("workno", queryDto.getKeyword())
                    .orRegex("name", queryDto.getKeyword()));
        }
        if (queryDto.getContractStatus() != null) {
            contractFilter = contractFilter.andNe("contractStatus", queryDto.getContractStatus().getIndex());
        }
/*        Optional<FilterElement> feedbackOtional = queryDto.getFilters()
                .stream().filter(e -> "feedbacks".equals(e.getProp()) && e.getValue() != null).findFirst();
        if (feedbackOtional.isPresent()) {
            List<String> feedbackList = Splitter.on(",").splitToList(String.valueOf(feedbackOtional.get()));
            contractFilter = contractFilter.andIn("feedback$dict$value", feedbackList);
        }*/
        if (null != queryDto.getContractDays()) {
            Long day = queryDto.getContractDays() > 0 ?
                    Long.valueOf(queryDto.getContractDays() + 1) : Long.valueOf(queryDto.getContractDays());
            // 86400000L 一天的毫秒数
            day = 86400000L * day;
            day = DateUtil.getCurrentTimestamp() + day;
            contractFilter = contractFilter.andLt("endDate", day.toString());
        }

        // 筛选
        FilterFunction[] ff = queryDto.doJoinDataFilter(contractFilter, Sets.set("hireDate", "empType", "company"), empFilter);

        // 数据权限
        DataFilter[] filters = joinDataScopeFilter((DataFilter) ff[0], (DataFilter) ff[1]);
        val join = DataJoin.joinModels(
                DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", BeanUtil.getPropName(EmpWorkInfoDo.class, "workno", "name", "enName", "company", "companyTxt", "hireDate", "empStatus", "organize", "organizeTxt", "job", "jobTxt", "empType", "post", "postTxt", "workHour", "workplace", "workplaceTxt"),
                        filters[1]),
                DataJoin.ModelInfo.model(identifier, BeanUtil.getPropName(LastContractDo.class, "bid", "contractId", "owner", "signType", "contractNo", "contractTypeSet", "contractTypeSetTxt", "contractSettingType", "contractType", "periodType", "contractPeriod", "probationPeriod", "probationPeriodEndDate", "signDate", "startDate", "endDate", "signTime", "contractStatus", "approvalStatus", "terminationDate", "terminationReason", "signProcessStatus", "attachFile", "feedback", "continueStatus", "initiateStatus", "continueApprovalStatus"),
                        filters[0]),
                DataJoin.JoinInfo.joinInfo(
                        Lists.list(
                                DataJoin.JoinPropertyInfo.joinProperty("entity.hr.EmpWorkInfo", identifier, "empId", "owner$empId")
                        )
                )
        );
        PageResult<Triple<DataSimple, DataSimple, DataSimple>> pageResult = join
                .limit(queryDto.getPageSize(), queryDto.getPageNo())
                .join(DataSimple.class, System.currentTimeMillis());
        List<LastContractDo> doList = pageResult.getItems().stream()
                .map(tri -> toDo(tri.getLeft(), tri.getMiddle()))
                .collect(Collectors.toList());
        return new PageResult<>(doList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    @Override
    public List<LastContractDo> selectByEmpIds(String identifier, List<String> empIds) {
        DataFilter filter = getBaseFilter().andIn("owner$empId", empIds);
        return DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .limit(5000, 1)
                .filter(filter, LastContractDo.class).getItems();
    }

    @Override
    public PageResult<LastContractDo> selectContractPage(String identifier, ContractQueryDto query) {
        DataFilter filter = getBaseFilter();
        if (CollectionUtils.isNotEmpty(query.getContractNeStatusList())) {
            List<String> collect = query.getContractNeStatusList().stream().map(ContractStatusEnum::getIndex).collect(Collectors.toList());
            filter = filter.andNotIn("contractStatus", collect);
        }
        if (CollectionUtils.isNotEmpty(query.getEndDates())) {
            filter = filter.andIn("endDate", query.getEndDates().stream().map(String::valueOf)
                    .collect(Collectors.toList()));
        }
        return DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .limit(query.getPageSize(), query.getPageNo())
                .filter(filter, LastContractDo.class);
    }

    @Override
    public Optional<LastContractDo> selectByContractId(String identifier, String contractId) {
        if (StringUtils.isBlank(contractId)){
            return Optional.empty();
        }
        DataFilter filter = getBaseFilter().andEq("contractId", contractId);
        List<LastContractDo> list = DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .limit(1, 1)
                .filter(filter, LastContractDo.class).getItems();
        if (CollectionUtils.isNotEmpty(list)) {
            return Optional.ofNullable(list.get(0));
        }
        return Optional.empty();
    }

    /**
     * join查询的数据权限
     *
     * @param contractFilter
     * @param empFilter
     * @return
     */
    private DataFilter[] joinDataScopeFilter(DataFilter contractFilter, DataFilter empFilter) {
        // 数据范围权限
        Result<Map<String, String>> dataScope = getDataScopeFeign().getDataScope(Long.valueOf(UserContext.getUserId()), DataScopeConstant.CONTRACT_MANAGE_PARENT_CODE);
        Map<String, String> scopeMap = null;
        if (null == dataScope || !dataScope.isSuccess() || null == (scopeMap = dataScope.getData()) || scopeMap.isEmpty()) {
            return new DataFilter[]{contractFilter, empFilter};
        }

        // 指定合同公司
        String dataScopeValue = scopeMap.get(DataScopeConstant.CONTRACT_ASSIGN_CONTRACT_COMPANY);
        if (StringUtil.isNotEmpty(dataScopeValue)) {
            Map<String, Object> companyMap = FastjsonUtil.toObject(dataScopeValue, Map.class);
            String detail = (String) companyMap.get("detail");
            if (StringUtil.isNotEmpty(detail)) {
                List<String> company = Arrays.stream(detail.split(",")).collect(Collectors.toList());
                empFilter = ((Boolean) companyMap.get("complement")) ? empFilter.andNotIn("company", company)
                        : empFilter.andIn("company", company);
            }
        }

        // 指定创建人
        dataScopeValue = scopeMap.get(DataScopeConstant.CONTRACT_ASSIGN_CREATE_BY);
        if (StringUtil.isNotEmpty(dataScopeValue)) {
            Map<String, Object> companyMap = FastjsonUtil.toObject(dataScopeValue, Map.class);
            String userId = UserContext.getUserId();
            if (StringUtil.isNotEmpty(userId)) {
                contractFilter = (Boolean) companyMap.get("complement") ? contractFilter.andNe("createBy", userId)
                        : contractFilter.andEq("createBy", userId);
            }
        }

        return new DataFilter[]{contractFilter, empFilter};
    }

    private LastContractDo toDo(DataSimple empWorkInfo, DataSimple lastContract) {
        LastContractDo lc = buildData(lastContract, LastContractDo.class);
        EmpWorkInfoDo ewi = buildData(empWorkInfo, EmpWorkInfoDo.class);
        com.caidaocloud.util.BeanUtil.copyWithNoValue(ewi, lc);
        lc.getOwner().setName(ewi.getName());
        lc.getOwner().setWorkno(ewi.getWorkno());
        lc.getOwner().setEnName(ewi.getEnName());
        return lc;
    }
}
