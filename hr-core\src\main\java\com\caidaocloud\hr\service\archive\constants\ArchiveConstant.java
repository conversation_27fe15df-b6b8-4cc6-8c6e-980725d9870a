package com.caidaocloud.hr.service.archive.constants;

import lombok.Data;

/**
 * created by: FoAng
 * create time: 5/6/2024 10:56 上午
 */
@Data
public class ArchiveConstant {

    public static final String ROUTING_KEY = "archive.routing.key.%s";

    public static final String EXCHANGE_KEY = "archive.exchange.key.%s";

    public static final String QUEUE_KEY = "archive.queue.key.%s";

    public static String tenantRoutingKey(String tenantId) {
        return String.format(ROUTING_KEY, tenantId);
    }

    public static String tenantExchangeKey(String tenantId) {
        return String.format(EXCHANGE_KEY, tenantId);
    }

    public static String tenantQueueKey(String tenantId) {
        return String.format(QUEUE_KEY, tenantId);
    }

}
