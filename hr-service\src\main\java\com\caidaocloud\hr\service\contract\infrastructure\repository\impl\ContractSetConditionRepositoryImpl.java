package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContractSetConditionRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: Aaron.Chen
 * @Date: 2022/4/26 14:26
 * @Description:
 **/
@Repository
public class ContractSetConditionRepositoryImpl extends BaseRepositoryImpl<ContractSetConditionDo> implements IContractSetConditionRepository {
    @Override
    public List<ContractSetConditionDo> selectList(ContractSetConditionDo data) {
        PageResult<ContractSetConditionDo> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage()
                .queryInvisible().filter(DataFilter.eq("tenantId", data.getTenantId())
                        .andNe("deleted", Boolean.TRUE.toString()).
                                andIn("contractTypeSet", data.getContractTypeSetBids()), getDataClazz());
        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }


    @Override
    public List<ContractSetConditionDo> selectByTypeSetId(String identifier, String typeSetBid) {
        PageResult<ContractSetConditionDo> result = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(DataFilter.eq("contractTypeSet", typeSetBid)
                        .andEq("deleted", Boolean.FALSE.toString()), getDataClazz());
        return result.getItems();
    }

    @Override
    public List<ContractSetConditionDo> selectByTypeSetIds(String identifier, List<String> typeSetIds) {
        DataFilter dataFilter = getBaseFilter().andIn("contractTypeSet", typeSetIds);
        PageResult<ContractSetConditionDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(dataFilter, getDataClazz());
        return pageResult.getItems();
    }

}
