package com.caidaocloud.hr.service.confirmation.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationRecordService;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/confirmation/record")
@Api(value = "/api/hr/v1/confirmation/record", description = "转正记录")
public class ConfirmationRecordController {

    @Resource
    private ConfirmationRecordService confirmationRecordService;

    @PostMapping("/page")
    public Result<PageResult<ConfirmationRecordVo>> page(@RequestBody ConfirmationRecordSearchDto searchDto){
        return Result.ok(confirmationRecordService.page(searchDto));
    }

    @PostMapping("/export")
    public void exportQuery(@RequestBody ConfirmationRecordSearchDto query, HttpServletResponse response){
        confirmationRecordService.exportQuery(query, response);
    }

}