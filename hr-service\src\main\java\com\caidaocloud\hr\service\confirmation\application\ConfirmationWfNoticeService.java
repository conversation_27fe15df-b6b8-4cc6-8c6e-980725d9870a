package com.caidaocloud.hr.service.confirmation.application;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationApply;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationApplyVo;
import com.caidaocloud.hr.service.temination.application.dto.FormDataMapDto;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.caidaocloud.hr.service.confirmation.domain.entity.EnabledConfirmationPropInfo.EnabledConfirmationProp.CONFIRMATION_DATE;
import static com.caidaocloud.hr.service.confirmation.domain.entity.EnabledConfirmationPropInfo.EnabledConfirmationProp.PROBATION_PERIOD_END_DATE;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/6/8
 */
@Service
public class ConfirmationWfNoticeService {

	@Autowired
	private ConfirmationService confirmationService;
	@Autowired
	private FormFeignClient formFeignClient;

	public Map<String, String> getNoticeVar(String businessKey, List<String> variables) {
		ConfirmationApplyVo data = confirmationService.detailVo(StringUtils.substringBefore(businessKey, "_"));
		FormDataMapDto formDataMapDto = formFeignClient.getFormDataMap(data.getFormDefId(), data.getFormValueId()).getData();
		List<ConfirmationChangeField> dataField = new ArrayList<>();
		dataField.addAll(data.getConfirmations());
		dataField.addAll(data.getSalary());
		dataField.addAll(data.getWork());
		return fetchNoticeData(dataField, formDataMapDto, variables);
	}

	private Map<String, String> fetchNoticeData(List<ConfirmationChangeField> confirmationData, FormDataMapDto formData, List<String> variables) {
		Map<String, List<ConfirmationChangeField>> confirmationMap = Sequences.sequence(confirmationData)
				.toMap(field -> field.getType().toUpperCase());
		Map<String, String> result = new HashMap<>();
		ConfirmationChangeField field;
		for (String variable : variables) {
			String[] split = variable.split("\\$", 2);
			String type = split[0].toUpperCase(), code = split[1];
			switch (type) {
			case "FORM":
				result.put(variable, formData == null ? null : (String) formData.getPropertiesMap().get(code));
				break;
			case "OTHER":
				String humpCode = StringUtil.underlineToHump(code);
				field = Sequences.sequence(confirmationMap.get(type)).find(f -> humpCode.equals(f.getProperty())).getOrNull();
				if (field == null) {
					result.put(variable, null);
				}
				else {
					if (CONFIRMATION_DATE.name().equals(code) || PROBATION_PERIOD_END_DATE.name().equals(code)) {
						result.put(variable, field.getAfter() == null ? null : field.getAfter().toString());
					}
					else {
						result.put(variable, field.fetchAfterValue());
					}
				}
				break;
			case "CONTRACT":
			case "WORK":
			case "SALARY":
				split = code.split("#");
				String property = split[0];
				field = Sequences.sequence(confirmationMap.get(type)).find(f -> property.equals(f.getProperty())).getOrNull();
				if ("before".equals(split[1])) {
					result.put(variable, field == null ? null : field.fetchBeforeValue());
				}
				else {
					result.put(variable, field == null ? null : field.fetchAfterValue());
				}
				break;
			default:
				break;
			}
		}
		return result;
	}
}
