package com.caidaocloud.hr.service.archive.consumer;

import com.caidaocloud.hr.service.archive.application.ArchiveService;
import com.caidaocloud.hr.service.archive.beans.ArchiveEvent;
import com.caidaocloud.hr.service.archive.properties.ArchiveProperty;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.AbsMQConsumer;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 通知事件消费处理
 * created by: FoAng
 * create time: 5/6/2024 3:44 下午
 */
@Slf4j
@AllArgsConstructor
public class ArchiveMsgConsumer extends AbsMQConsumer {

    private ArchiveService archiveService;

    private String tenantId;

    @Override
    public void process(String message) {
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            log.info("[archive] receive archive event msg: {}", message);
            ArchiveEvent archiveEvent = FastjsonUtil.toObject(message, ArchiveEvent.class);
            archiveService.dispatchArchiveEvent(archiveEvent);
            log.info("[archive] receive archive event complete at {}", DateUtil.formatTime());
        } catch (Exception e) {
            log.error("[archive] consume msg event error: {}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
