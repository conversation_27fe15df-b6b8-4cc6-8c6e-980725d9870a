package com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 合同续签意向字段权限
 *
 * <AUTHOR>
 * @date 2024/2/21
 **/
public class ContinueContractFormDef extends WfMetaFunFormDef {
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return list();
    }

    public static List<WfMetaFunFormFieldDto> list() {
        return Lists.newArrayList(
                new WfMetaFunFormFieldDto("workno", "工号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("name", "姓名", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("hireDate", "入职日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("continueStatus", "续签意向状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("empStatus", "员工状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("organizeTxt", "任职组织", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("jobTxt", "职务", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("postTxt", "岗位", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("empType", "用工类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractNo", "合同编号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractPeriod", "合同期(月)", WfFieldDataTypeEnum.Number),
                new WfMetaFunFormFieldDto("companyTxt", "合同公司", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractSettingType", "合同类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("startDate", "合同开始日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("endDate", "合同结束日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("signTime", "合同签订次数", WfFieldDataTypeEnum.Number),
                new WfMetaFunFormFieldDto("contractDays", "合同到期天数", WfFieldDataTypeEnum.Number),
                new WfMetaFunFormFieldDto("contractStatus", "合同状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("initiateStatus", "续签意向状态", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("feedback", "续签意向反馈", WfFieldDataTypeEnum.Text)
        );
    }
}