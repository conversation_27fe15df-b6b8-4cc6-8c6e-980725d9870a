package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchAutoRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractSetConditionQueryDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ContractSetConditionDomainService extends BaseDomainServiceImpl<ContractSetConditionDo, ContractSetConditionQueryDto> {
    @Resource
    private ContractSetConditionDo contractSetConditionDo;

    @Override
    public BaseDomainDo getDoService() {
        return contractSetConditionDo;
    }

    public void deleteByTypeSetId(String typeSetBid) {
        contractSetConditionDo.deleteByTypeSetId(typeSetBid);

    }

    public ContractSetConditionDo getByTypeSetBid(String typeSetBid) {
        return contractSetConditionDo.getByTypeSetBid(typeSetBid);
    }

    @Deprecated
    public Long calcEndDate(BatchAutoRenewalDto renewalDto, EmpWorkInfoDo workInfoDo, Long startDate) {
        ContractSetConditionDo conditionDo = ObjectConverter.convert(renewalDto, ContractSetConditionDo.class);
        return contractSetConditionDo.calcEndDate(conditionDo, workInfoDo, startDate);
    }
}
