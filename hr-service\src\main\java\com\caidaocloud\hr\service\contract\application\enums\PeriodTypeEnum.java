package com.caidaocloud.hr.service.contract.application.enums;

import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;

/**
 * 配合前端开关控件，转换合同设置中的合同期限
 */
public enum PeriodTypeEnum {
    FIXED("0", "固定期限",true),
    NO_FIXED("1", "无固定期限", false),
    ;

    private String code;
    private String name;
    private final Boolean flag;

    PeriodTypeEnum(String code, String name, Boolean flag) {
        this.code = code;
        this.name = name;
        this.flag = flag;
    }

    public static String getName(String index) {
        for (PeriodTypeEnum c : PeriodTypeEnum.values()) {
            if (c.getCode().equals(index)) {
                return LangUtil.getMsg(Integer.parseInt(c.getName()));
            }
        }
        return null;
    }

    public static String getCodeByFlag(Boolean flag) {
        for (PeriodTypeEnum c : PeriodTypeEnum.values()) {
            if (c.getFlag().equals(flag)) {
                return c.getCode();
            }
        }
        return NO_FIXED.getCode();
    }

    public static Boolean getFlagByCode(String code) {
        for (PeriodTypeEnum c : PeriodTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getFlag();
            }
        }
        return NO_FIXED.getFlag();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {

        this.name = name;
    }

    public Boolean getFlag() {
        return flag;
    }
}
