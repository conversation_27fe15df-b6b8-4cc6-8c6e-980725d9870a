# 员工直接上级更新定时任务

## 功能描述

`EmpDirectSuperiorUpdateTask` 是一个定时任务，用于根据员工所属组织的部门负责人自动更新员工的直接上级。

## 业务逻辑

1. **获取所有员工**: 查询系统中所有在职员工的工作信息
2. **查找组织负责人**: 根据员工所属组织查找部门负责人
3. **处理特殊情况**: 如果部门负责人是员工本人，则向上查找上级部门的负责人
4. **更新直接上级**: 将找到的负责人设置为员工的直接上级
5. **跳过离职员工**: 自动跳过已离职的员工

## 核心方法

### `empDirectSuperiorUpdateJobHandler()`
- XxlJob定时任务入口方法
- 处理多租户环境
- 设置安全上下文

### `updateEmpDirectSuperior()`
- 主要业务逻辑方法
- 遍历所有员工并更新直接上级

### `findDirectSuperior(EmpWorkInfoDo, long)`
- 查找员工的直接上级
- 调用递归方法查找组织负责人

### `findOrgLeaderRecursively(String, String, long)`
- 递归查找组织负责人
- 处理负责人是员工本人的情况
- 向上查找上级组织

### `shouldUpdateDirectSuperior(EmpWorkInfoDo, EmpSimple)`
- 判断是否需要更新直接上级
- 比较当前上级和新上级

### `updateEmployeeDirectSuperior(EmpWorkInfoDo, EmpSimple, long)`
- 执行实际的更新操作
- 更新员工工作信息

## 配置说明

### XxlJob配置
- **任务名称**: `empDirectSuperiorUpdateJobHandler`
- **执行器**: hr-service应用
- **建议执行频率**: 每日凌晨执行一次

### 租户配置
通过 `${caidaocloud.tenant:}` 配置项指定需要处理的租户列表。

## 使用方法

### 1. 配置定时任务
在XxlJob管理后台配置定时任务：
```
任务名称: empDirectSuperiorUpdateJobHandler
Cron表达式: 0 0 2 * * ? (每日凌晨2点执行)
执行器: hr-service
```

### 2. 手动执行
可以通过XxlJob管理后台手动触发任务执行。

### 3. 测试验证
运行测试类 `EmpDirectSuperiorUpdateTaskTest` 来验证功能：
```java
// 测试完整定时任务执行
@Test
public void testUpdateEmpDirectSuperior()

// 测试查找组织负责人逻辑
@Test
public void testFindOrgLeader()

// 测试单个员工更新
@Test
public void testSingleEmpUpdate()

// 测试组织层级结构
@Test
public void testOrgHierarchy()
```

## 注意事项

1. **数据一致性**: 任务执行过程中会获取最新的员工和组织数据
2. **错误处理**: 单个员工更新失败不会影响其他员工的处理
3. **性能考虑**: 大量员工时建议在业务低峰期执行
4. **日志记录**: 任务执行过程会记录详细的日志信息

## 日志说明

### 正常日志
```
cronTask[Emp Direct Superior Update]------------------------start execution,time {timestamp}
Found {count} employees for direct superior update
Updated direct superior for employee: {empId}, new superior: {superiorId}
Direct superior update completed. Updated: {updateCount}, Errors: {errorCount}, Total: {totalCount}
cronTask[Emp Direct Superior Update]------------------------end execution,time {timestamp}
```

### 错误日志
```
Employee Direct Superior Update Task error, tenantId={tenantId}
Failed to update direct superior for employee: {empId}, error: {errorMessage}
Error finding org leader for orgId: {orgId}, empId: {empId}
```

## 依赖服务

- `EmpWorkInfoService`: 员工工作信息服务
- `EmpWorkInfoDomainService`: 员工工作信息领域服务
- `OrgService`: 组织服务
- `SecurityUserUtil`: 安全用户工具类

## 扩展说明

如需扩展功能，可以考虑：
1. 添加更复杂的上级查找规则
2. 支持多种组织架构类型
3. 添加更详细的更新记录
4. 支持批量更新优化
