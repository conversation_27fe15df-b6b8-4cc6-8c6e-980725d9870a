package com.caidaocloud.hr.service.archive.provider.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.ArchiveProvider;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.archive.provider.IModuleProvider;
import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 5:34 下午
 */
@Slf4j
@Service
public class HrModuleProvider implements IModuleProvider {

    @Resource
    private ArchiveProvider archiveProvider;

    @Override
    public String module() {
        return ArchiveModule.HR.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessLine, String businessId) {
        return archiveProvider.fetchArchive(businessLine, businessId);
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        return archiveProvider.fetchPageArchiveData(page);
    }

    @Override
    public void fetchAllArchiveData() {
        archiveProvider.fetchAllArchiveData();
    }

    @Override
    public void dispatchArchiveDelEvent(String businessLine, String subBusinessLine, String businessId, ArchiveFile archiveFile) {
        archiveProvider.dispatchArchiveDelEvent(businessLine, subBusinessLine, businessId, archiveFile);
    }
}
