package com.caidaocloud.hr.service.common.application.dto;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiModel("映射配置dto")
@Slf4j
@Data
public class MappingConfigDto {


	@ApiModelProperty("配置id")
	private String id;
	@ApiModelProperty("映射源identifier")
	private String source;
	@ApiModelProperty("映射源名称")
	private String sourceName;
	@ApiModelProperty("映射目标identifier")
	private String target;
	@ApiModelProperty("映射目标名称")
	private String targetName;
	@ApiModelProperty("属性映射")
	private List<PropertyMapping> mapping;
	@ApiModelProperty("模型字段映射名称")
	private String name;
	@ApiModelProperty("模型字段映射类别")
	private String category;


	public List<DataSimple> createMappingData(String empId) {
		log.info("createMappingData,empId={},mapping={}", empId, FastjsonUtil.toJson(mapping));
		Map<String, PropertyMapping> propertyMap = mapping.stream().collect(Collectors.toMap(PropertyMapping::getSource, obj->obj));
		PageResult<DataSimple> result = querySourceData(empId);
		log.info("createMappingData,result={}", FastjsonUtil.toJson(result.getItems()));
		List<DataSimple> dataSimpleList = result.getItems().stream().map(source -> {
			DataSimple dataSimple = new DataSimple();
			// 根据配置映射属性
			for (Map.Entry<String, PropertyMapping> entry : propertyMap.entrySet()) {
				PropertyValue value = source.getProperties().get(entry.getKey());
				//字段为空值时覆盖原有字段信息，
				if (entry.getValue().getIsOverwriteWithEmpty()) {
					dataSimple.getProperties().add(entry.getValue().getTarget(), value);
					continue;
				}

				if (value != null && checkNullValue(entry.getValue(), value)) {
					dataSimple.getProperties().add(entry.getValue().getTarget(), value);
				}
			}
			// 设置时间轴
			dataSimple.setDataStartTime(DateUtil.getCurrentTimestamp());
			dataSimple.setIdentifier(target);
			return dataSimple;
		}).collect(Collectors.toList());
		return dataSimpleList;
	}

	public static DataSimple queryEmpData(String identifier, String preId, String key, long dataStartTime) {
		PageResult<DataSimple> result = DataQuery.identifier(identifier).queryInvisible().decrypt()
				.specifyLanguage()
				.filter(DataFilter.eq(key, preId).andNe("deleted", Boolean.TRUE.toString())
						.andEq("tenantId", SecurityUserUtil.getSecurityUserInfo()
								.getTenantId()), DataSimple.class, dataStartTime);
		if (result.getItems().isEmpty()) {
			return null;
		}
		return result.getItems().get(0);
	}


	private PageResult<DataSimple> querySourceData(String empId) {
		PageResult<DataSimple> result = DataQuery.identifier(source).specifyLanguage().queryInvisible().decrypt()
				.filter(DataFilter.eq("empId", empId).andNe("deleted", Boolean.TRUE.toString())
						.andEq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId()), DataSimple.class);
		return result;
	}


	private boolean checkNullValue(PropertyMapping pm, PropertyValue value) {
		if (value == null) {
			return false;
		}
		if (value instanceof SimplePropertyValue) {
			return ((SimplePropertyValue)value).getValue() != null;
		}
		switch (pm.getDataType()) {
			case Enum:
				return ((EnumSimple) value).getValue() != null;
			case Dict:
				return ((DictSimple) value).getValue() != null;
			case Address:
				return ((Address) value).getValue() != null;
			case Phone:
				return ((PhoneSimple) value).getValue() != null;
			case Job_Grade_Range:
				return ((JobGradeRange) value).getStartGrade() != null;
			case Emp:
				return ((EmpSimple) value).getEmpId() != null;
			case Attachment:
				return !((Attachment) value).getUrls().isEmpty();
			default:
				if(value instanceof PhoneSimple){
					return ((PhoneSimple) value).getValue() != null;
				}
				return ((SimplePropertyValue) value).getValue() != null;
		}
	}



}
