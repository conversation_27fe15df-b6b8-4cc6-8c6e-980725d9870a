package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.caidaocloud.hr.service.contract.domain.entity.ContractPropertyConfig;
import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationPropertyConfig;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationApplyType;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationConfigStatus;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import lombok.Data;

import java.util.List;

@Data
public class ContractConfigVo {
    private String bid;

    private String name;

    private String description;

    private TerminationConfigStatus status;

    private List<ContractPropertyConfig> enabledContractProperties;

    private List<TagInfoKVVo> tagProperties;

    private ContractApplyType contractApplyType;

    private String formDefId;

    private List<MetadataPropertyVo> displayWorkInfos;
}
