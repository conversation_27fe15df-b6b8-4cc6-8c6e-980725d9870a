package com.caidaocloud.hr.service.confirmation.application;

import com.caidaocloud.hr.service.common.application.feign.MaintenanceFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.FormDataConvertValueUtil;
import com.caidaocloud.hr.service.confirmation.application.constant.ConfirmationConstant;
import com.caidaocloud.hr.service.confirmation.application.factory.ConfirmationWorkflowFactory;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.*;
import com.caidaocloud.workflow.enums.*;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.caidaocloud.workflow.util.BusinessDetailUtil;
import com.google.common.collect.ImmutableMap;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.confirmation.application.constant.ConfirmationConstant.CONFIRMATION_WORKFLOW_APPROVER_URL;
import static com.caidaocloud.hr.service.confirmation.application.constant.ConfirmationConstant.CONFIRMATION_WORKFLOW_CALLBACK_URL;
import static com.caidaocloud.hr.service.confirmation.application.enums.ConfirmationApproverType.*;

/**
 * <AUTHOR> Zhou
 * @date 2023/6/5
 */
@Slf4j
@Service
public class ConfirmationWfRegisterService {
    @Autowired
    private FormFeignClient formFeignClient;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private ConfirmationService confirmationService;
    @Autowired
    private MaintenanceFeignClient maintenanceFeignClient;
    @Autowired
    private FormDataConvertValueUtil formDataConvertValueUtil;

    public ConfirmationWfRegisterService(SpringUtil springUtil) {

    }

    private final List<String> SEQ_CODE = Lists.list("work$workplace", "work$jobGrade");

    public void register(ConfirmationConfig config) {
        String funCode = ConfirmationConstant.CONFIRMATION_WORKFLOW_CODE + '-' + config.getBid();
        FormDefDto formDefDto = null;
        if (config.getFormDefId() != null) {
            formDefDto = formFeignClient.getFormDefById(config.getFormDefId()).getData();
        }
        registerFunction(funCode, config, formDefDto);
        registerSeqCondition(funCode, config.fetchAllBusinessField(), formDefDto);
        registerApprover(funCode, config.getBid());
        registerNoticeVar(funCode, config, formDefDto);
        registerCallback(funCode, config);
    }

    private void registerApprover(String funCode, String bid) {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        WfMetaApproverDto approverDef = new WfMetaApproverDto(
                "按申请人转正后X级HRBP获取",
                APPROVER_X_HRBP.code + '_' + bid,
                tenantId,
                "caidaocloud-hr-service",
                CONFIRMATION_WORKFLOW_APPROVER_URL,
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NUMBER_INPUT,
                Lists.list(),
                ""
        );
        approverDef.setFunCode(funCode);
        wfRegisterFeign.registerApprover(approverDef);
        approverDef = new WfMetaApproverDto(
                "按申请人转正后HRBP获取",
                APPROVER_HRBP.code + '_' + bid,
                tenantId,
                "caidaocloud-hr-service",
                CONFIRMATION_WORKFLOW_APPROVER_URL,
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NONE,
                Lists.list(),
                ""
        );
        approverDef.setFunCode(funCode);
        wfRegisterFeign.registerApprover(approverDef);
        approverDef = new WfMetaApproverDto(
                "按申请人转正后X级行政部门负责人获取",
                APPROVER_ORG_LEADER.code + '_' + bid,
                tenantId,
                "caidaocloud-hr-service",
                CONFIRMATION_WORKFLOW_APPROVER_URL,
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NUMBER_INPUT,
                Lists.list(),
                ""
        );
        approverDef.setFunCode(funCode);
        wfRegisterFeign.registerApprover(approverDef);
        approverDef = new WfMetaApproverDto(
                "按申请人转正后X级上级领导获取",
                APPROVER_X_LEADER.code + '_' + bid,
                tenantId,
                "caidaocloud-hr-service",
                CONFIRMATION_WORKFLOW_APPROVER_URL,
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NUMBER_INPUT,
                Lists.list(),
                ""
        );
        approverDef.setFunCode(funCode);
        wfRegisterFeign.registerApprover(approverDef);
        approverDef = new WfMetaApproverDto(
                "按申请人转正后上X级行政部门负责人获取",
                APPROVER_X_ORG_LEADER.code + '_' + bid,
                tenantId,
                "caidaocloud-hr-service",
                CONFIRMATION_WORKFLOW_APPROVER_URL,
                WfApproverFetchType.RELATIVE_PATH,
                WfValueComponentEnum.NUMBER_INPUT,
                Lists.list(),
                ""
        );
        approverDef.setFunCode(funCode);
        wfRegisterFeign.registerApprover(approverDef);
    }

    private void registerSeqCondition(String funCode, List<ConfirmationChangeFieldDef> regularizationFields, FormDefDto formDefDto) {
        List<WfMetaSeqConditionDto> seqList = Lists.list();
        if (formDefDto != null) {
            seqList.addAll(createFormSeq(funCode, formDefDto));
        }
        seqList.addAll(createStandardSeq(funCode, regularizationFields));
        wfRegisterFeign.registerSeqCondition(seqList);
    }

    private Collection<WfMetaSeqConditionDto> createStandardSeq(String funCode, List<ConfirmationChangeFieldDef> regularizationFields) {
        String callback = ConfirmationConstant.CONFIRMATION_WORKFLOW_SEQ_URL;
        List<WfMetaSeqConditionDto> seqList = ConfirmationWorkflowFactory.commonSeqCondition(funCode, callback);
        for (ConfirmationChangeFieldDef field : regularizationFields) {
            String key = field.getType() + '$' + field.getProperty();
            if (SEQ_CODE.contains(key)) {
                String address;
                WfValueComponentEnum component;
                if (field.getDataType() == PropertyDataType.Job_Grade_Range) {
                    component = WfValueComponentEnum.TREE;
                    address = "/api/hr/jobgrade/wf/v1/treeList";
                } else {
                    component = WfValueComponentEnum.COMPANY;
                    address = "";
                }
                seqList.add(new WfMetaSeqConditionDto(field.getName() + "(由)",
                        funCode.replaceAll("-", "_") + '_' + "old_" + key,
                        Lists.list(funCode),
                        "caidaocloud-hr-service",
                        callback,
                        WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                        SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                        Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                                WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                        component,
                        Lists.list(),
                        address, false));
                seqList.add(new WfMetaSeqConditionDto(field.getName() + "(至)",
                        funCode.replaceAll("-", "_") + '_' + key,
                        Lists.list(funCode),
                        "caidaocloud-hr-service",
                        callback,
                        WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                        SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                        Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                                WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                        component,
                        Lists.list(),
                        address, false));
            }
        }
        List<WfComponentValueDto> list = new ArrayList<>();
        for (ConfirmationType value : ConfirmationType.values()) {
            list.add(new WfComponentValueDto(value.text, value.name()));
        }
        seqList.add(ConfirmationWorkflowFactory.enumSeqCondition(funCode, callback, list, "other$confirmationType", "转正类型"));
        return seqList;
    }

    private List<WfMetaSeqConditionDto> createFormSeq(String funCode, FormDefDto formDefDto) {
        return formDefDto.getProperties().stream()
                .filter(propDef -> propDef.getDataType() == PropertyDataType.Enum && propDef.getEnumDef() != null ||
                        propDef.getDataType() == PropertyDataType.Boolean)
                .map(propDef -> {
                    List<WfComponentValueDto> componentValueList = Lists.list();
                    if (propDef.getDataType() == PropertyDataType.Enum) {
                        for (PropertyEnumDefDto def : propDef.getEnumDef()) {
                            componentValueList.add(new WfComponentValueDto(def.getDisplay(), def.getValue()));
                        }
                    } else {
                        componentValueList = ConfirmationWorkflowFactory.booleanComponentValue();
                    }
                    return new WfMetaSeqConditionDto(propDef.getName(),
                            funCode.replaceAll("-", "_") + "_form$" + propDef.getProperty(),
                            Lists.list(funCode),
                            "caidaocloud-hr-service",
                            ConfirmationConstant.CONFIRMATION_WORKFLOW_SEQ_URL,
                            WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                            SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                            Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                            WfValueComponentEnum.ENUM,
                            componentValueList,
                            "", false);
                })
                .collect(Collectors.toList());
    }

    /**
     * 注册工作流
     *
     * @param funCode
     * @param def
     * @param formDefDto
     */
    private void registerFunction(String funCode, ConfirmationConfig def, FormDefDto formDefDto) {
        List<ConfirmationChangeFieldDef> fields = def.fetchAllBusinessField();
        List<WfMetaFunFormFieldDto> wfFields = new ArrayList<>();
        wfFields.addAll(Sequences.sequence(fields)
                .map(field -> {
                    WfFieldDataTypeEnum type = PropertyDataType.Timestamp.equals(field.getDataType()) ? WfFieldDataTypeEnum.Timestamp : WfFieldDataTypeEnum.Text;
                    if ("other".equals(field.getType())) {
                        return new WfMetaFunFormFieldDto(field.getType() + '-' + StringUtil.underlineToHump(field.getProperty()), field.getName(), type);
                    }
                    return new WfMetaFunFormFieldDto(field.getType() + '-' + field.getProperty(), field.getName(), type);
                }));
        if (formDefDto != null) {
            wfFields.addAll(Sequences.sequence(formDefDto.getProperties())
                    .map(p -> new WfMetaFunFormFieldDto(p.getProperty(), p.getName())));
        }
        WfMetaFunDto dto = new WfMetaFunDto(def.getName(), funCode,
                WfFunctionPageJumpType.RELATIVE_PATH, SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                "caidaocloud-hr-service",
                "", "/api/hr/confirmation/v1/detail", "", wfFields);
        wfRegisterFeign.registerFunction(dto);
    }

    /**
     * 注册工作流通知变量
     *
     * @param funCode
     * @param def
     * @param formDefDto
     */
    private void registerNoticeVar(String funCode, ConfirmationConfig def, FormDefDto formDefDto) {
        List<WfMetaNoticeVarDto> noticeVarDtos = createNoticeParameter(funCode, def.fetchAllBusinessField(), formDefDto);
        wfRegisterFeign.registerNoticeVar(noticeVarDtos);
    }

    /**
     * @param funCode
     * @param defList 转正字段定义集合
     * @param formDef 表单定义
     * @return
     */
    private List<WfMetaNoticeVarDto> createNoticeParameter(String funCode, List<ConfirmationChangeFieldDef> defList, FormDefDto formDef) {
        List<WfMetaNoticeVarDto> noticeVarList = new ArrayList<>();
        // 生成转正变量
        noticeVarList.addAll(generateNoticeParameter(funCode, defList));
        // 生成表单变量
        noticeVarList.addAll(generateFormNoticeParameter(funCode, formDef));
        // 按照类别进行排序
        return noticeVarList.stream().sorted(Comparator.comparing(WfMetaNoticeVarDto::getCode))
                .collect(Collectors.toList());
    }

    /**
     * 注册工作流回调
     *
     * @param funCode
     * @param def
     */
    private void registerCallback(String funCode, ConfirmationConfig def) {
        String callbackCode = ConfirmationConstant.CONFIRMATION_WORKFLOW_CALLBACK_CODE + '-' + def.getBid();
        WfMetaCallbackDto dto = new WfMetaCallbackDto(def.getName(),
                callbackCode, Lists.list(funCode),
                SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                CONFIRMATION_WORKFLOW_CALLBACK_URL,
                "caidaocloud-hr-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        wfRegisterFeign.registerCallback(dto);
    }

    private Collection<? extends WfMetaNoticeVarDto> generateNoticeParameter(String funCode, List<ConfirmationChangeFieldDef> defList) {
        List<WfMetaNoticeVarDto> varList = new ArrayList<>();
        for (ConfirmationChangeFieldDef fieldDef : defList) {
            PropertyDataType dataType = fieldDef.getDataType();
            if (!filterDataType(dataType, fieldDef.getWidgetType())) {
                continue;
            }
            String code = fieldDef.generateCode();
            if (!"other".equals(fieldDef.getType())) {
                varList.add(singleNoticeParameter(funCode, fieldDef.getName() + "（由）", code + "#before", dataType, null, fieldDef.getEnumDef()));
                varList.add(singleNoticeParameter(funCode, fieldDef.getName() + "（至）", code + "#after", dataType, null, fieldDef.getEnumDef()));
            } else {
                varList.add(singleNoticeParameter(funCode, fieldDef.getName(), code, dataType, null, fieldDef.getEnumDef()));
            }
        }
        return varList;
    }

    private List<WfMetaNoticeVarDto> generateFormNoticeParameter(String funCode, FormDefDto formDef) {
        if (formDef == null || CollectionUtils.isEmpty(formDef.getProperties())) {
            return new ArrayList<>();
        }
        List<WfMetaNoticeVarDto> list = Sequences.sequence(formDef.getProperties())
                .filter(p -> {
                    return filterDataType(p.getDataType(), p.getWidgetType());
                })
                .map(p -> singleNoticeParameter(funCode, p.getName(), "FORM$" + p.getProperty(), p.getDataType(), p.getFormat(), p.getEnumDef()))
                .toList();
        return list;
    }

    private WfMetaNoticeVarDto singleNoticeParameter(String funCode, String name, String code, PropertyDataType dataType, String format, List<PropertyEnumDefDto> enumDef) {
        WfMetaNoticeVarDto.WfMetaNoticeVarDtoBuilder builder = WfMetaNoticeVarDto.builder()
                .name(name)
                .code(code)
                .funCode(funCode)
                .type(dataType.name())
                .url(ConfirmationConstant.CONFIRMATION_WORKFLOW_NOTICE_VAR_URL)
                .serviceName("caidaocloud-hr-service")
                .dateFormat(format);
        if (dataType == PropertyDataType.Enum && enumDef != null) {
            for (PropertyEnumDefDto propertyEnumDefDto : enumDef) {
                builder.enums(propertyEnumDefDto.getDisplay(), propertyEnumDefDto.getValue());
            }
        }
        return builder.build();
    }

    private boolean filterDataType(PropertyDataType dataType, String widgetType) {
        // 过滤表单时间区间
        if (dataType == PropertyDataType.Timestamp && "DateRangePicker".equals(widgetType)) {
            return false;
        }
        if (dataType == PropertyDataType.Boolean) {
            return false;
        }
        return dataType == PropertyDataType.Job_Grade_Range
                || dataType == PropertyDataType.Dict || dataType == PropertyDataType.Enum ||
                (!dataType.isComponent() && !dataType.isArray());
    }
}