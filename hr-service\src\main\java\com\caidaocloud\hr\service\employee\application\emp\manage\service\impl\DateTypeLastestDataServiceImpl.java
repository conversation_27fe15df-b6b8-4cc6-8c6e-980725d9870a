package com.caidaocloud.hr.service.employee.application.emp.manage.service.impl;

import com.caidaocloud.hr.service.employee.application.emp.dto.resume.form.DateTypeLastestDataConfig;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.AbsFormResumeService;
import com.caidaocloud.hr.service.employee.infrastructure.enums.FormResumeTypeEnum;
import com.caidaocloud.util.DateUtil;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 取最新数据的date类型的数据
 *
 * <AUTHOR>
 * @date 2025/7/21
 **/
@Slf4j
@Service
public class DateTypeLastestDataServiceImpl extends AbsFormResumeService<DateTypeLastestDataConfig> {
    @Override
    public void handle(ConfigureBuilder cb, Map<String, Object> dataMap, String identifier, Map<String, Object> formDataMap, DateTypeLastestDataConfig param) {
        if (!preCheck(dataMap, identifier, formDataMap, param)) {
            log.info("preCheck fail");
            return;
        }
        String targetIdentifier = identifier.replace(".", "_");
        Object dataObj = formDataMap.get(targetIdentifier);
        List<Map<String, Object>> dataList = null;
        if (Objects.isNull(dataObj) || CollectionUtils.isEmpty(dataList = (List<Map<String, Object>>) dataObj)) {
            log.info("data is empty");
            return;
        }
        if (CollectionUtils.isNotEmpty(param.getFilterList())) {
            dataList = dataList.stream().filter(e -> {
                boolean flag = true;
                for (DateTypeLastestDataConfig.Field field : param.getFilterList()) {
                    if (!e.get(field.getProp()).equals(field.getVal())) {
                        flag = false;
                    }
                }
                return flag;
            }).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(param.getSortProp())) {
            dataList.sort((v1, v2) -> {
                Object o1 = v1.get(param.getSortProp());
                Object o2 = v2.get(param.getSortProp());
                if (Objects.isNull(o1) || Objects.isNull(o2)) {
                    return -1;
                }
                try {
                    Long date1 = DateUtil.convert(String.valueOf(o1), param.getSourceDatePattern());
                    Long date2 = DateUtil.convert(String.valueOf(o2), param.getSourceDatePattern());
                    return -date1.compareTo(date2);
                } catch (ParseException e) {
                    log.error("convert occur error", e);
                }
                return 0;
            });
        }
        Map<String, Object> data = dataList.get(0);
        List<String> propList = param.getFieldList().stream()
                .sorted((v1, v2) -> v1.getSort().compareTo(v2.getSort()))
                .map(e -> e.getProp())
                .collect(Collectors.toList());
        String[] props = new String[propList.size()];
        for (int i = 0; i < propList.size(); i++) {
            Object val = data.get(propList.get(i));
            props[i] = Objects.isNull(val) ? "" : String.valueOf(val);
        }
        String result = String.format(param.getTargetFormat(), props);
        dataMap.putIfAbsent(param.getAliases(), result);
        cb.bind(param.getAliases(), new LoopRowTableRenderPolicy());
    }

    private boolean preCheck(Map<String, Object> dataMap, String identifier, Map<String, Object> formDataMap, DateTypeLastestDataConfig param) {
        if (Objects.isNull(dataMap) ||
                StringUtils.isBlank(identifier) || MapUtils.isEmpty(formDataMap) ||
                StringUtils.isBlank(param.getAliases()) ||
                CollectionUtils.isEmpty(param.getFieldList()) ||
                StringUtils.isBlank(param.getSortProp()) ||
                StringUtils.isBlank(param.getTargetFormat())) {
            return false;
        }
        return true;
    }

    @Override
    protected FormResumeTypeEnum type() {
        return FormResumeTypeEnum.DATETYPE_OF_LASTEST_DATA;
    }
}