package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@ApiModel("员工非行政组织信息DTO")
public class EmpOtherOrgDto {
    @ApiModelProperty("非行政组织BID")
    private String bid;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("非行政架构类型")
    private String schemaType;
    @ApiModelProperty("任职组织")
    private String organize;
    @ApiModelProperty("任职组织名称")
    private String organizeTxt;
    @ApiModelProperty("职务")
    private String job;
    @ApiModelProperty("职务名称")
    private String jobTxt;
    @ApiModelProperty("岗位")
    private String post;
    @ApiModelProperty("岗位名称")
    private String postTxt;
    @ApiModelProperty("组织负责人")
    private EmpSimple orgLeader;
    @ApiModelProperty("直接上级")
    private EmpSimple directSuperior;
    @ApiModelProperty("生效日期")
    private long dataStartTime;
    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
