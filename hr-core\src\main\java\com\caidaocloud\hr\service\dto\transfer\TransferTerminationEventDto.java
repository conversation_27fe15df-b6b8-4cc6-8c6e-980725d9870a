package com.caidaocloud.hr.service.dto.transfer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferTerminationEventDto {
    private String dataId;
    private String tenantId;
    private Long userId;
    private int count = 0;

    public TransferTerminationEventDto(String dataId, String tenantId, Long userId) {
        this.dataId = dataId;
        this.tenantId = tenantId;
        this.userId = userId;
    }

    public void autoincrement() {
        count++;
    }
}