package com.caidaocloud.hr.service.employee.application.dataimport.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService;
import com.caidaocloud.hr.service.contract.domain.entity.CompletedContractImportDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.domain.service.EmpContractTypeSetRelDomainService;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.CompletedContractImportPo;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.RuleSetAutoDomainService;
import com.caidaocloud.hr.service.growthrecord.domain.utils.JodaTimeUtils;
import com.caidaocloud.hr.service.organization.application.company.service.CompanyService;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 已签合同导入
 * <AUTHOR>
 */
@Service
@Slf4j
public class CompletedContractSignImportService extends DataImportService <CompletedContractImportDo, CompletedContractImportPo>{
    @Resource
    private ContractService contractService;
    @Resource
    private DictService dictService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private CompletedContractImportDo completedContractImportDo;
    @Resource
    private CompanyService companyService;
    @Resource
    private CacheService cacheService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private RuleSetAutoDomainService ruleSetAutoDomainService;
    @Resource
    private EmpContractTypeSetRelDomainService empContractTypeSetRelDomainService;
    @Resource
    private OrgService orgService;
    @Autowired
    private IWfRegisterFeign iWfRegisterFeign;

    private static Map<String, Map<String, PropertyEnumDefDto>> staticPropMap = new HashMap<>();
    private static Map<String, Map<String, ContractTypeSetDo>> contractTypeSetMap = new HashMap<>();
    private static Map<String, Map<String, CompanyDo>> companyMaps = new HashMap<>();

    private static final String IDENTIFIER = "entity.hr.Contract";

    private static String businessCode = "COMPLETED_CONTRACT_SIGN_IMPORT";

    private static final String ContractTypeDicCode = "ContractType";

    private static final String SignTypeProp = "signType", PeriodTypeProp = "periodType";

    private static final String CompanyList = "CompanyList", ContractTypeSetList = "ContractTypeSetList";

    @Override
    public String getExcelCode() {
        return businessCode;
    }

    @Override
    public List<CompletedContractImportDo> getPoDataFromExcel(InputStream inputStream) {
        return completedContractImportDo.getCompletedContractImportDoFromExcel(inputStream);
    }

    @Override
    public boolean checkEmptyProp(CompletedContractImportDo data) {
        if(StringUtils.isEmpty(data.getName())){
            setEmptyTips(data, "姓名不能为空");
        }
        if(StringUtils.isEmpty(data.getWorkno())){
            setEmptyTips(data, "工号不能为空");
        }
        if(StringUtils.isEmpty(data.getSignTypeTxt())){
            setEmptyTips(data, "签订类型不能为空");
        }
        if(StringUtils.isEmpty(data.getCompanyTxt())){
            setEmptyTips(data, "合同公司不能为空");
        }
        /*if(StringUtils.isEmpty(data.getContractNo())){
            setEmptyTips(data, "合同编号不能为空");
        }*/
        if(StringUtils.isEmpty(data.getContractTypeSetTxt())){
            setEmptyTips(data, "合同类型不能为空");
        }
        if(StringUtils.isEmpty(data.getStartDateTxt())){
            setEmptyTips(data, "合同开始时间不能为空");
        }
        if(StringUtils.isEmpty(data.getEndDateTxt())){
            setEmptyTips(data, "合同结束时间不能为空");
        }
        return true;
    }

    @Override
    public boolean checkEmptyMark(CompletedContractImportDo data) {
        return data.isCheckEmpty();
    }

    @Override
    public boolean installProp(CompletedContractImportDo con) {
        String tenantId = getTenantId();
        //
//        Map<String, KeyValue> conTypeMap = staticMap.get(getMapKeyWithTenantId(ContractTypeDicCode, tenantId));
        Map<String, PropertyEnumDefDto> signTypeMap = staticPropMap.get(getMapKeyWithTenantId(SignTypeProp, tenantId));
//        Map<String, PropertyEnumDefDto> periodTypeMap = staticPropMap.get(getMapKeyWithTenantId(PeriodTypeProp, tenantId));
        Map<String, CompanyDo> companyMap = companyMaps.get(getMapKeyWithTenantId(CompanyList, tenantId));
        Map<String, ContractTypeSetDo> contractTypeSetDoMap = contractTypeSetMap.get(getMapKeyWithTenantId(ContractTypeSetList, tenantId));
        try {
            installCompanyProp(con, companyMap);
            installConTypeProp(con, contractTypeSetDoMap);
            installSignTypeProp(con, signTypeMap);
//            installPeriodTypeProp(con, periodTypeMap);
            installDate(con);
        } catch (Exception e) {
            log.error("已签订合同初始化数据 error：{}", e.getMessage());
            setEmptyTips(con, "已签订合同初始化数据异常，请联系管理员");
        }
        autoContractNo(con, tenantId);
        if(con.isCheckEmpty()){
            return false;
        }
        return true;
    }

    private void autoContractNo(CompletedContractImportDo con, String tenantId) {
        if(StringUtil.isNotEmpty(con.getContractNo())){
            return;
        }

        try {
            String contractNo = ruleSetAutoDomainService.nextContractNo(con.getContractNo(), con.getContractNo());
            log.info("CompletedContractSignImportService autoContractNo. contractNo={}.", contractNo);
            con.setContractNo(contractNo);
            ruleSetAutoDomainService.updateContractNoMax(tenantId, contractNo);
        } catch (Exception e){
            setEmptyTips(con, "合同编号未开启自动生成或为空");
            log.error("autoContractNo err, errMsg={}", e.getMessage(), e);
        }
    }

    private void installConTypeProp(CompletedContractImportDo con, Map<String, ContractTypeSetDo> contractTypeSetDoMap) {
        if (null != contractTypeSetDoMap && null != contractTypeSetDoMap.get(con.getContractTypeSetTxt())) {
            ContractTypeSetDo contractTypeSetDo = contractTypeSetDoMap.get(con.getContractTypeSetTxt());

            con.setContractTypeSet(contractTypeSetDo.getBid());
            // 合同类别
            con.setContractType(contractTypeSetDo.getContractClass());
            //合同类型
            con.setContractSettingType(contractTypeSetDo.getContractType());
        } else {
            setEmptyTips(con, "合同类型不存在");
        }
    }

    private void installCompanyProp(CompletedContractImportDo con, Map<String, CompanyDo> companyMap) {
        if(null != companyMap && !companyMap.isEmpty() && null != companyMap.get(con.getCompanyTxt())){
            String companyId = companyMap.get(con.getCompanyTxt()).getBid();
            String code = companyMap.get(con.getCompanyTxt()).getOrganizationCode();
            con.setCompany(companyId);
            con.setCompanyCode(code);
        }else {
            setEmptyTips(con, "公司不存在");
        }
    }

    @Override
    public List<CompletedContractImportDo> batchInsertUpdateData(List<CompletedContractImportDo> conRecordList) {
        if (null == conRecordList || conRecordList.size() == 0) {
            return new ArrayList<>();
        }
        List<CompletedContractImportDo> errorList = new ArrayList<>();
        List<String> contractNos = new ArrayList<>();
        List<String> workNos = new ArrayList<>();
        for (CompletedContractImportDo con : conRecordList) {
            contractNos.add(con.getContractNo());
            workNos.add(con.getWorkno());
        }
        List<EmpBasicInfoDo> basicInfoList = empBasicInfoService.getEmpBasicInfoListByWorkNo(workNos, System.currentTimeMillis());
        List<ContractDo> contractList = contractService.getContractByContractNo(contractNos);
        List<EmpWorkInfoDo> workInfoList = empWorkInfoDomainService.getEmpListByWorkNos(workNos);

        // 查询组织信息
        Map<String, OrgDo> orgInfoMap = orgService.selectOrgMapByIds(workInfoList.stream()
                .filter(empWorkInfoDo -> empWorkInfoDo.getOrganize() != null)
                .map(EmpWorkInfoDo::getOrganize).collect(Collectors.toList()), System.currentTimeMillis());

        Map<String, ContractDo> oldConMap = contractList.stream().collect(Collectors.toMap(ContractDo::getContractNo, obj -> obj, (A, B) -> A));
        Map<String, EmpBasicInfoDo> oldEmpInfoMap = basicInfoList.stream().collect(Collectors.toMap(EmpBasicInfoDo::getWorkno, obj -> obj, (A, B) -> A));
        Map<String, EmpWorkInfoDo> oldWorkInfoMap = workInfoList.stream().collect(Collectors.toMap(EmpWorkInfoDo::getWorkno, obj -> obj, (A, B) -> A));

        if (null == contractList || contractList.size() == 0) {
            return insertEmpConRecordImportList(conRecordList, oldEmpInfoMap, oldWorkInfoMap, orgInfoMap);
        }

        List<CompletedContractImportDo> insertList = new ArrayList<>();
        List<CompletedContractImportDo> updateList = new ArrayList<>();
        for (CompletedContractImportDo importDo : conRecordList) {
            ContractDo contractDo = oldConMap.get(importDo.getContractNo());
            if (contractDo == null) {
                insertList.add(importDo);
            } else {
                importDo.setBid(contractDo.getBid());
                updateList.add(importDo);
            }
        }
        errorList.addAll(insertEmpConRecordImportList(insertList, oldEmpInfoMap, oldWorkInfoMap, orgInfoMap));
        errorList.addAll(updateConRecordImportList(updateList, oldEmpInfoMap, oldWorkInfoMap, oldConMap, orgInfoMap));
        return errorList;
    }

    @Override
    public void initProperty(){
        String tenantId = UserContext.getTenantId();
//        staticMap.put(getMapKeyWithTenantId(ContractTypeDicCode, tenantId), getEmployDictMap(ContractTypeDicCode));
        staticPropMap.put(getMapKeyWithTenantId(SignTypeProp, tenantId), getEmpMetadataPropertyMap(SignTypeProp));
        staticPropMap.put(getMapKeyWithTenantId(PeriodTypeProp, tenantId), getEmpMetadataPropertyMap(PeriodTypeProp));
        companyMaps.put(getMapKeyWithTenantId(CompanyList, tenantId), getCompanyMap());
        contractTypeSetMap.put(getMapKeyWithTenantId(ContractTypeSetList, tenantId), getContractTypeSetMap());
    }

    protected String getMapKeyWithTenantId(String key, String tenantId){
        return String.format("%s_%s", key, tenantId);
    }

    private Map<String, KeyValue> getEmployDictMap(String typeCode){
        Map<String, KeyValue> map = new HashMap<>();
        List<KeyValue> list = dictService.getEnableDictList(typeCode,"Employee");
        for(KeyValue kv:list){
            map.put(kv.getText(), kv);
        }
        return map;
    }

    private Map<String, PropertyEnumDefDto> getEmpMetadataPropertyMap(String prop){
        Map<String, PropertyEnumDefDto> map = new HashMap<>();
        MetadataPropertyVo vo = metadataService.getPropertyDef(IDENTIFIER, prop);
        for(PropertyEnumDefDto dto: vo.getEnumDef()){
            map.put(dto.getDisplay(), dto);
        }
        return map;
    }

    private void setEmptyTips(CompletedContractImportDo con, String tip){
        con.setCheckEmpty(true);
        if(null == con.getCheckEmptyTips()){
            con.setCheckEmptyTips(tip);
        }else {
            con.setCheckEmptyTips(con.getCheckEmptyTips() + "，" + tip);
        }
    }

    /**
     * 查询公司信息
     * @return
     */
    private Map<String, CompanyDo> getCompanyMap(){
        List<CompanyDo> companyList = companyService.selectList();
        Map<String, CompanyDo> map = new HashMap<>();
        for(CompanyDo companyDo : companyList){
            map.put(companyDo.getCompanyName(), companyDo);
        }
        return map;
    }

    /**
     * 查询合同设置信息
     * @return
     */
    private Map<String, ContractTypeSetDo> getContractTypeSetMap(){
        List<ContractTypeSetDo> contractTypeSetDos = contractTypeSetService.selectList();
        Map<String, ContractTypeSetDo> map = new HashMap<>();
        for(ContractTypeSetDo data : contractTypeSetDos){
            map.put(data.getContractType().getText(), data);
        }
        return map;
    }

    private void installSignTypeProp(CompletedContractImportDo con, Map<String, PropertyEnumDefDto> signTypeMap) {
        if(null != signTypeMap && null != signTypeMap.get(con.getSignTypeTxt())){
            PropertyEnumDefDto status = signTypeMap.get(con.getSignTypeTxt());
            EnumSimple signType = new EnumSimple();
            signType.setText(status.getDisplay());
            signType.setValue(status.getValue());
            con.setSignType(signType);
        } else {
            setEmptyTips(con, "签订类型不存在");
        }
    }

    private void installPeriodTypeProp(CompletedContractImportDo con, Map<String, PropertyEnumDefDto> periodTypeMap) {
        if(null != periodTypeMap && null != periodTypeMap.get(con.getPeriodTypeTxt())){
            PropertyEnumDefDto status = periodTypeMap.get(con.getPeriodTypeTxt());
            EnumSimple periodType = new EnumSimple();
            periodType.setText(status.getDisplay());
            periodType.setValue(status.getValue());
            con.setPeriodType(periodType);
        } else {
            setEmptyTips(con, "合同期限类型不存在");
        }
    }

    private void installDate(CompletedContractImportDo con) {
        DateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        try {
            con.setStartDate(format.parse(con.getStartDateTxt()).getTime());
            con.setEndDate(format.parse(con.getEndDateTxt()).getTime());
            if (con.getStartDate() >= con.getEndDate()) {
                throw new ServerException("合同日期范围错误");
            }
            EnumSimple simple = new EnumSimple();
            simple.setValue("0");
            if ("9999/12/31".equals(con.getEndDateTxt())) {
                //是无固定期限
                simple.setValue("1");
            }
            con.setPeriodType(simple);
        } catch (ParseException e) {
            setEmptyTips(con, "日期转换异常");
            log.error("import conRecord Data parse error:{}", e.getMessage());
        } catch (ServerException e) {
            setEmptyTips(con, e.getMessage());
        }
    }

    private String getMapKeyWithDictId(String dictId){
        String dictKey = String.format("DICT_%s",dictId);
        String value = cacheService.getValue(dictKey);
        Map map = FastjsonUtil.toObject(value, Map.class);
        return map.get("dictCode").toString();
    }

    private List<CompletedContractImportDo> insertEmpConRecordImportList(List<CompletedContractImportDo> insertList,Map<String, EmpBasicInfoDo> oldEmpInfoMap,
                                                                         Map<String, EmpWorkInfoDo> oldWorkInfoMap, Map<String, OrgDo> orgDoMap) {
        List<CompletedContractImportDo> errorList = new ArrayList<>();


        String contractBid = null;

        Long dataTime = DateUtil.getCurrentTimestamp();
        for(CompletedContractImportDo importDo : insertList){
            try {
                doConvertEmpInfo(importDo, oldEmpInfoMap, oldWorkInfoMap, null, orgDoMap);
                ContractDo contractDo = ObjectConverter.convert(importDo, ContractDo.class);
                contractDomainService.linkDataConvert(contractDo);
                contractDo.calcStatus();

                EnumSimple approvalStatus = new EnumSimple();
                if ("是".equals(importDo.getNeedApprove())) {
                    approvalStatus.setValue(ApprovalStatusEnum.IN_APPROVAL.getIndex().toString());
                    contractDomainService.setShowStatus(contractDo);
                } else {
                    approvalStatus.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
                    contractDomainService.setHideStatus(contractDo);
                }

                contractDo.setApprovalStatus(approvalStatus);
                log.info("insertEmpConRecordImportList contractDo:{}, needApprove = {}", FastjsonUtil.toJson(contractDo), importDo.getNeedApprove());

                contractBid = contractDomainService.insert(contractDo);
                EmpWorkInfoDo empWorkInfoDo = oldWorkInfoMap.get(importDo.getWorkno());
                empWorkInfoDo.setCompany(contractDo.getCompany());
                empWorkInfoDo.setCompanyTxt(contractDo.getCompanyTxt());
                empWorkInfoDomainService.updateByEmpId(empWorkInfoDo);
                contractDo.setBid(contractBid);

                if ("是".equals(importDo.getNeedApprove())) {
                    completedContractOpenWorkflow(contractDo);
                }
                // 已签合同中导入数据不进入签署流程
                // contractEsignService.contractCirculation(contractDo, oldEmpInfoMap, oldWorkInfoMap, dataTime);
            } catch (Exception e){
                log.error("审批记录导入异常--> error:{},{}", e.getMessage(), e);
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }

        return errorList;
    }

    private List<CompletedContractImportDo> updateConRecordImportList(List<CompletedContractImportDo> updateList,Map<String, EmpBasicInfoDo> oldEmpInfoMap,
                                                                 Map<String, EmpWorkInfoDo> oldWorkInfoMap,Map<String, ContractDo> oldConMap,Map<String, OrgDo> orgDoMap) {
        List<CompletedContractImportDo> errorList = new ArrayList<>();
//        EnumSimple approvalStatus = new EnumSimple();
//        approvalStatus.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
        Long dataTime = DateUtil.getCurrentTimestamp();
        for(CompletedContractImportDo importDo : updateList){
            try {
                doConvertEmpInfo(importDo,oldEmpInfoMap,oldWorkInfoMap,oldConMap,orgDoMap);
                ContractDo contractDo = oldConMap.get(importDo.getContractNo());
                BeanUtil.copyProperties(importDo, contractDo);
                contractDomainService.linkDataConvert(contractDo);
                contractDo.calcStatus();

                EnumSimple approvalStatus = new EnumSimple();
                if ("是".equals(importDo.getNeedApprove())) {
                    approvalStatus.setValue(ApprovalStatusEnum.IN_APPROVAL.getIndex().toString());
                    contractDomainService.setShowStatus(contractDo);
                } else {
                    approvalStatus.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
                    contractDomainService.setHideStatus(contractDo);
                }
                contractDo.setApprovalStatus(approvalStatus);
                log.info("updateConRecordImportList contractDo:{}, needApprove = {}", FastjsonUtil.toJson(contractDo), importDo.getNeedApprove());

                if ("是".equals(importDo.getNeedApprove())) {
                    completedContractOpenWorkflow(contractDo);
                }

                contractDomainService.update(contractDo);
                EmpWorkInfoDo empWorkInfoDo = oldWorkInfoMap.get(importDo.getWorkno());
                empWorkInfoDo.setCompany(contractDo.getCompany());
                empWorkInfoDo.setCompanyTxt(contractDo.getCompanyTxt());
                empWorkInfoDomainService.updateByEmpId(empWorkInfoDo);


                // 已签合同中导入的数据不进入签署流程
                // contractEsignService.contractCirculation(contractDo, oldEmpInfoMap, oldWorkInfoMap, dataTime);
            }catch (Exception e){
                log.error("审批记录导入异常--> error:{},{}", e.getMessage(), e);
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }
        return errorList;
    }

    private void completedContractOpenWorkflow(ContractDo contractDo) {
        String funcCode = "";
        if (SignTypeEnum.NEW.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_NEW_SIGN;
        }
        if (SignTypeEnum.CHANGE.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_AMENDMENT;
        }
        if (SignTypeEnum.RENEW.getCode().equals(contractDo.getSignType().getValue())) {
            funcCode = ContractSignTypeConstant.CONTRACT_RENEW;
        }
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        workflowDto.setFuncCode(funcCode);
        workflowDto.setApplicantId(contractDo.getOwner().getEmpId());
        workflowDto.setBusinessId(contractDo.getBid());
        workflowDto.setApplicantName(contractDo.getOwner().getName());
        // 业务单据事件时间
        workflowDto.setEventTime(System.currentTimeMillis());

        log.info("completedContractOpenWorkflow workflowDto:{} ", FastjsonUtil.toJson(workflowDto));

        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("import completedContractOpenWorkflow beginWorkflow err,{}", e.getMessage(), e);
        }
        log.info("import completedContractOpenWorkflow wfResult:{}", FastjsonUtil.toJson(wfResult));
        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());

    }


    private void doConvertEmpInfo(CompletedContractImportDo importDo, Map<String, EmpBasicInfoDo> oldEmpInfoMap,
                                  Map<String, EmpWorkInfoDo> oldWorkInfoMap, Map<String, ContractDo> oldConMap, Map<String,OrgDo> orgInfoMap){
        EmpBasicInfoDo basicInfo = oldEmpInfoMap.get(importDo.getWorkno());
        EmpWorkInfoDo workInfo = oldWorkInfoMap.get(importDo.getWorkno());

        if(basicInfo == null || StringUtils.isEmpty(basicInfo.getWorkno())){
            throw new RuntimeException("导入数据异常，未找到人员工号");
        }

        if (!basicInfo.getName().equals(importDo.getName())) {
            throw new RuntimeException("工号与姓名不匹配");
        }

        if (oldConMap != null) {
            ContractDo contractDo = oldConMap.get(importDo.getContractNo());
            if (!contractDo.getOwner().getWorkno().equals(importDo.getWorkno())) {
                throw new RuntimeException("与系统该合同编号对应人员工号不匹配");
            }
        }

        List<String> workNos = new ArrayList<>();
        workNos.add(importDo.getWorkno());
        List<ContractDo> contractList = contractService.getContractByWorkNo(workNos);
        List<ContractDo> inApprovalList = contractList.stream().filter(contractDo -> ApprovalStatusEnum.IN_APPROVAL.getIndex().toString().equals(contractDo.getApprovalStatus().getValue()))
                .collect(Collectors.toList());

        //1.工号相同，已发起过合同审批流程并审批状态=审批中, 不允许导入是否需要审批=是的单据，校验提示：该员工有审批中的合同
        if (CollectionUtils.isNotEmpty(inApprovalList) && "是".equals(importDo.getNeedApprove())) {
            throw new RuntimeException("该员工有审批中的合同");
        }

        //2.签订类型=续签，工号相同，合同编号在系统不存在，需校验导入的开始结束时间是否与生效中或未生效的合同有时间重叠，如果有校验提示：合同时间范围冲突
        if (SignTypeEnum.RENEW.getCode().equals(importDo.getSignType().getValue())) {
            List<ContractDo> contractDos = contractList.stream()
                    .filter(contractDo -> !Objects.equals(contractDo.getContractNo(), importDo.getContractNo()))
                    .filter(contractDo -> null == contractDo.getContractStatus()
                            || ContractStatusEnum.IN_EFFECTIVE.getIndex().equals(contractDo.getContractStatus().getValue())
                            || ContractStatusEnum.EFFECTIVE.getIndex().equals(contractDo.getContractStatus().getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(contractDos)) {
                for (ContractDo contractDo : contractDos) {
                    if(null == contractDo.getContractStatus()){
                        contractDo.calcStatus();
                    }
                    LocalDate importStart = null;
                    LocalDate importEnd = null;
                    try {
                        importStart = JodaTimeUtils.stringToLocalDate(importDo.getStartDateTxt(), JodaTimeUtils.LOCAL_DATE_FORMAT);
                        importEnd = JodaTimeUtils.stringToLocalDate(importDo.getEndDateTxt(), JodaTimeUtils.LOCAL_DATE_FORMAT);
                    } catch (Exception e) {
                        throw new RuntimeException("合同开始日期或合同结束日期格式有误 应是yyyy/MM/dd");
                    }
                    LocalDate contractStart = JodaTimeUtils.convertTimeStampToLocalDate(contractDo.getStartDate());
                    LocalDate contractEnd = JodaTimeUtils.convertTimeStampToLocalDate(contractDo.getEndDate());

                    //时间范围冲突校验
                    if ((importStart.compareTo(contractStart) < 0 && contractStart.compareTo(importEnd) < 0)
                            || (contractStart.compareTo(importStart) < 0 && importStart.compareTo(contractEnd) < 0)) {
                        throw new RuntimeException("合同时间范围冲突");
                    }
                }
            }
        }

        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId(basicInfo.getBid());
        empSimple.setWorkno(basicInfo.getWorkno());
        empSimple.setName(basicInfo.getName());
        importDo.setOwner(empSimple);

        String organize = basicInfo.getOrganize();
        importDo.setOrganize(organize);
        OrgDo orgData = Optional.ofNullable(orgInfoMap.get(organize)).orElse(new OrgDo());
        importDo.setOrganizeCode(orgData.getCode());
        importDo.setOrganizeTxt(orgData.getName());

        importDo.setEmpStatus(basicInfo.getEmpStatus());
        importDo.setPost(basicInfo.getPost());
        importDo.setPostTxt(basicInfo.getPostTxt());
        if(workInfo != null){
            importDo.setJob(workInfo.getJob());
            importDo.setJobTxt(workInfo.getJobTxt());
            importDo.setHireDate(workInfo.getHireDate());
            importDo.setEmpType(workInfo.getEmpType());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Temporal str = LocalDate.parse(sdf.format(importDo.getStartDate()));
        // fix DEV-11356
        Temporal end = LocalDate.parse(sdf.format(Math.min(importDo.getEndDate() + 86400000, com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.MAX_TIMESTAMP)));
        // 方法返回为相差月份
        if (!"9999/12/31".equals(importDo.getEndDateTxt())) {
            Long contractPeriod = ChronoUnit.MONTHS.between(str, end);
            importDo.setContractPeriod(contractPeriod.intValue());
        }
    }

    @Override
    public void operateAfterImport() {
        // 处理合同签订次数
        contractService.countSignTime();

        // 处理员工、合同设置关系
        empContractTypeSetRelDomainService.doEmpContractRel(null);
    }
}
