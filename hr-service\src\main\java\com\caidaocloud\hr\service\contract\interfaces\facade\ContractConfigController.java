package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.hr.service.contract.application.dto.ContractConfigDto;
import com.caidaocloud.hr.service.contract.application.dto.ContractConfigEnableDto;
import com.caidaocloud.hr.service.contract.application.service.ContractConfigService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractConfig;
import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractConfigVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/contract/config")
@Api(value = "/api/hr/v1/contract/config", tags = "合同配置")
public class ContractConfigController {

    @Autowired
    private ContractConfigService contractConfigService;

    @PostMapping
    public Result<String> create(@RequestBody ContractConfigDto config) {
        return Result.ok(contractConfigService.create(config));
    }
    
    @GetMapping("/list")
    public Result<List<ContractConfigVo>> list(@RequestParam ContractApplyType applyType, @RequestParam(required = false) boolean showDisabled) {
        return Result.ok(contractConfigService.list(applyType, showDisabled));
    }

    @PostMapping("/name/update")
    public Result updateName(@RequestBody ContractConfigDto config) {
        contractConfigService.updateName(config);
        return Result.ok();
    }

    @GetMapping("/one")
    public Result<ContractConfigVo> one(@RequestParam ContractApplyType applyType) {
        return Result.ok(FastjsonUtil.convertObject(contractConfigService.one(applyType), ContractConfigVo.class));
    }

    @GetMapping("/one/byConfigId")
    public Result<ContractConfigVo> oneById(@RequestParam String configId) {
        return Result.ok(FastjsonUtil.convertObject(contractConfigService.one(configId), ContractConfigVo.class));
    }

    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody ContractConfigDto config) {
        contractConfigService.update(config);
        return Result.ok();
    }

    @PutMapping("/enable")
    public Result<Boolean> enable(@RequestBody ContractConfigEnableDto enable) {
        contractConfigService.enable(enable.getBid());
        return Result.ok();
    }
}
