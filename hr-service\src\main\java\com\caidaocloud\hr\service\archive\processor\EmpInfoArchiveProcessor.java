package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpFileAttachmentDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpFileAttachmentDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.sun.corba.se.spi.ior.ObjectKey;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 13/6/2024 4:54 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmpInfoArchiveProcessor extends AbsArchiveProcessor {

    private MetadataService metadataService;

    private EmpFileAttachmentDomainService empFileAttachmentDomainService;

    private final ConcurrentMap<String, WeakReference<List<String>>> propsWeakRefer = new ConcurrentHashMap<>();

    @Override
    public String businessLine() {
        return ArchiveStandardLine.EMPLOYEE.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        EmpFileAttachmentDo empFileAttachmentDo = empFileAttachmentDomainService.getEmpFileAttachment(businessId, null);
        if (empFileAttachmentDo == null) {
            log.error("[archive] fetch emp archive file error, businessId:{}", businessId);
            return Lists.newArrayList();
        }
        return filterArchiveList(buildModuleArchive(empFileAttachmentDo));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        List<EmpFileAttachmentDo> empFileAttachmentDos = empFileAttachmentDomainService.getArchiveData(page, loadModelProperties().stream()
                .map(it -> StringUtils.substringBefore(it, "$")).collect(Collectors.toList()));
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(empFileAttachmentDos)) {
            for (EmpFileAttachmentDo attachment: empFileAttachmentDos) {
                archiveDataList.addAll(buildModuleArchive(attachment));
            }
        }
        return filterArchiveList(archiveDataList);
    }

    public List<ArchiveData> buildModuleArchive(EmpFileAttachmentDo attachmentDo) {
        return buildArchiveData(attachmentDo, loadModelProperties().toArray(new String[]{}));
    }


    private List<String> loadModelProperties() {
        final String tenantId = UserContext.getTenantId();
        if (!propsWeakRefer.containsKey(tenantId) || propsWeakRefer.get(tenantId).get() == null) {
            List<String> fileProps = Lists.newArrayList("idCardFace$证件照正面$证件信息",
                    "idCardBackFace$证件照背面$证件信息", "academicCertificate$学历证书$学历信息", "diploma$学位证书$学位信息", "bankCard$银行卡$财务信息");
            MetadataVo metadata = metadataService.getMetadata(EmpFileAttachmentDo.IDENTIFIER);
            if (metadata == null || CollectionUtils.isNotEmpty(metadata.getCustomProperties())) {
                return fileProps;
            }
            for (MetadataPropertyVo customProperty : metadata.getCustomProperties()) {
                if (customProperty.getDataType() == PropertyDataType.Attachment) {
                    fileProps.add(String.format("%s$其他信息$%s", customProperty.getProperty(), customProperty.getName()));
                }
            }
            log.info("[archive] load module props: {}", FastjsonUtil.toJson(fileProps));
            propsWeakRefer.put(tenantId, new WeakReference<>(fileProps));
            return fileProps;
        }
        return propsWeakRefer.get(tenantId).get();
    }


    public List<ArchiveData> buildArchiveData(EmpFileAttachmentDo attachmentDo, String... propertyKeys) {
        NestPropertyValue nestPropertyValue = attachmentDo.getProperties();
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (!nestPropertyValue.isEmpty()) {
            for (String propertyKey: propertyKeys) {
                String keyPair = StringUtils.substringBeforeLast(propertyKey, "$");
                String key = StringUtils.substringBefore(keyPair, "$");
                String keyName = StringUtils.substringAfter(keyPair, "$");
                String typeName = StringUtils.substringAfterLast(propertyKey, "$");
                Object itemNestValue = nestPropertyValue.get(key);
                if (itemNestValue instanceof Attachment) {
                    if (CollectionUtils.isEmpty(((Attachment) itemNestValue).getUrls())) {
                        continue;
                    }
                    ArchiveData archiveData = new ArchiveData();
                    archiveData.setBusinessLine(ArchiveStandardLine.EMPLOYEE.getDesc());
                    archiveData.setBusinessType(typeName);
                    archiveData.setSubBusinessLine(key);
                    archiveData.setBusinessId(attachmentDo.getEmpId());
                    archiveData.setEmpId(attachmentDo.getEmpId());
                    ArchiveFile archiveFile = BeanUtil.convert(itemNestValue, ArchiveFile.class);
                    archiveFile.setFileName(keyName);
                    archiveData.setArchiveFiles(Lists.newArrayList(archiveFile));
                    archiveData.setEventTime(attachmentDo.getUpdateTime());
                    archiveDataList.add(archiveData);
                }
            }
        }
        if (CollectionUtils.isEmpty(archiveDataList)) {
            log.info("[archive] un resolve emp attachment file, [{}]", FastjsonUtil.toJson(attachmentDo));
        }
        return archiveDataList;
    }

    /**
     * 删除员工信息回调
     * @param subBusinessLine
     * @param businessId
     * @param archiveFile
     */
    @Override
    public void dispatchArchiveDelEvent(String subBusinessLine, String businessId, ArchiveFile archiveFile) {
        EmpFileAttachmentDo empFileAttachmentDo = empFileAttachmentDomainService.getEmpFileAttachment(businessId, null);
        NestPropertyValue propertyValue = empFileAttachmentDo.getProperties();
        if (!propertyValue.containsKey(subBusinessLine)) {
            throw new ServerException("删除附件类型错误");
        }
        updateFieldValue(empFileAttachmentDo, subBusinessLine, archiveFile);
        empFileAttachmentDomainService.update(empFileAttachmentDo, null);
    }

    @SneakyThrows
    private void updateFieldValue(EmpFileAttachmentDo attachmentDo, String subBusinessLine, ArchiveFile archiveFile) {
        Field field = EmpFileAttachmentDo.class.getDeclaredField(subBusinessLine);
        field.setAccessible(true);
        Object attachmentFile = field.get(attachmentDo);
        if (!(attachmentFile instanceof Attachment) || CollectionUtils.isEmpty(((Attachment) attachmentFile).getUrls())) {
            log.error("[archive] del emp archive file error, file not present or type error");
            return;
        }
        for (String url:
                ((Attachment) attachmentFile).getUrls()) {
            if (url.equals(archiveFile.getUrls().get(0))) {
                int position = archiveFile.getUrls().indexOf(url);
                ((Attachment) attachmentFile).getUrls().remove(url);
                ((Attachment) attachmentFile).getNames().remove(position);
                break;
            }
        }
        field.set(attachmentDo, attachmentFile);
    }
}
