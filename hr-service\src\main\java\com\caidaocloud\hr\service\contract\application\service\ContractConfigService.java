package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.dto.ContractConfigDto;
import com.caidaocloud.hr.service.contract.domain.entity.ContractConfig;
import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.contract.domain.enums.ContractConfigStatus;
import com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field.ContractCommonFormDef;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractConfigVo;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.dto.*;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ContractConfigService {
    @Value("${spring.application.name:}")
    private String appName;
    @Resource
    private WfOperateFeignClient wfOperateFeignClient;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;

    public ContractConfig one(String configId) {
        return ContractConfig.listAll().stream()
                .filter(it -> it.getBid().equals(configId))
                .findFirst().orElseThrow(() -> new ServerException("配置不存在"));
    }

    public ContractConfig one(ContractApplyType applyType) {
        try {
            return ContractConfig.listAll().stream()
                    .filter(it -> applyType.equals(it.getSignType()) && it.getStatus().equals(ContractConfigStatus.ENABLED))
                    .findFirst().orElse(null);
        } catch (Exception e) {
            return null;
        }
    }
    
    public List<ContractConfigVo> list(ContractApplyType applyType, boolean showDisabled) {
        List<ContractConfig> list = ContractConfig.listAll().stream()
                .filter(it -> applyType.equals(it.getSignType()) && (showDisabled || !it.getStatus().equals(ContractConfigStatus.DISABLED)))
                .sorted(Comparator.comparing(c -> c.getStatus().ordinal()))
                .collect(Collectors.toList());
        List<ContractConfigVo> contractConfigVos = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)) return contractConfigVos;
        list.forEach(o-> {
            ContractConfigVo vo = FastjsonUtil.convertObject(o, ContractConfigVo.class);
            vo.setContractApplyType(o.getSignType());
            contractConfigVos.add(vo);
        });
        return contractConfigVos;
    }

    public void updateName(ContractConfigDto config) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        String bid = config.getBid();
        String name = config.getName();
        ContractConfig one = one(bid);
        one.setName(name);
        one.setUpdateBy(String.valueOf(user.getUserId()));
        one.setUpdateTime(System.currentTimeMillis());
        DataUpdate.identifier(one.getIdentifier()).update(one);
        WfMetaFunNameDto dto = new WfMetaFunNameDto();
        dto.setName(name);
        dto.setCode("CONTRACTRENEW-" + bid);
        dto.setTenantId(user.getTenantId());
        wfOperateFeignClient.updateFunctionName(dto);
        wfOperateFeignClient.updateFunName(dto);
    }


    public void update(ContractConfigDto config) {
        val entity = FastjsonUtil.convertObject(config, ContractConfig.class);
        entity.setSignType(config.getContractApplyType());
        entity.update();
//        if (entity.getStatus().equals(ContractConfigStatus.ENABLED)) {
//            registerWorkflow(entity.getBid());
//        }
    }

    public void enable(String bid) {
        ContractConfig detail = ContractConfig.getOne(bid);
        ContractConfig.enabled(bid);
        registerWorkflow(detail);
    }

    private void registerWorkflow(ContractConfig detail) {
        String funCode = "CONTRACTRENEW-" + detail.getBid();
        log.info("funCode : {} ", funCode);
        List<WfMetaFunFormFieldDto> formFields = ContractCommonFormDef.formList();
        List<WfMetaSeqConditionDto> seqList = Lists.list();
        WfMetaFunDto dto = new WfMetaFunDto(detail.getName(), funCode,
                WfFunctionPageJumpType.RELATIVE_PATH, SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                "caidaocloud-hr-service",
                "", "/api/hr/contract/sign/v1/workflow/detail", "", formFields);
        iWfRegisterFeign.registerFunction(dto);
        iWfRegisterFeign.registerSeqCondition(seqList);
        log.info("start contract regist callback");
        registerCallback(funCode, "合同续签", "CONTRACTRENEW-CALLBACK" + detail.getBid(), "/api/hr/contract/sign/v1/callback");
        registerCallback(funCode, "合同续签--电子签", "CONTRACTRENEW-CALLBACK-V2" + detail.getBid(), "/api/hr/contract/sign/v1/callbackAndStart");

        log.info("start contract regist noticeVar");
        String periodTypeJson = "[{\"display\":\"固定期限\",\"i18nDisplay\":{\"default\":\"固定期限\"},\"value\":\"0\"},{\"display\":\"无固定期限\",\"i18nDisplay\":{\"default\":\"无固定期限\"},\"value\":\"1\"}]";
        List<PropertyEnumDefDto> enumList = FastjsonUtil.toList(periodTypeJson, PropertyEnumDefDto.class);
        List<WfMetaNoticeVarDto> renewNoticeVarList = new ArrayList<>();
        String signTypeJson = "[{\"display\":\"新签\",\"i18nDisplay\":{\"default\":\"新签\"},\"value\":\"2\"},{\"display\":\"续签\",\"i18nDisplay\":{\"default\":\"续签\"},\"value\":\"0\"},{\"display\":\"改签\",\"i18nDisplay\":{\"default\":\"改签\"},\"value\":\"1\"}]";
        List<PropertyEnumDefDto> signTypeList = FastjsonUtil.toList(signTypeJson, PropertyEnumDefDto.class);
        renewNoticeVarList.add(singleNoticeParameter(funCode, "签订类型", "signType", PropertyDataType.Enum, null, signTypeList));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同公司", "companyTxt", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同公司-英文", "companyTxtEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "注册地址", "registerAddress", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同类型", "contractSettingType", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同类型-英文", "contractSettingTypeEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同期限", "periodType", PropertyDataType.Enum, null, enumList));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同签订日期", "signDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同开始日期", "startDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同结束日期", "endDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "备注", "remark", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "上份合同到期天数", "lastContractExpireDays", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "上份合同结束日期", "lastContractEndDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同续签审批链接", "approvalLink", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "合同续签审批链接-英文", "approvalLinkEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "上一份合同类型", "lastContractType", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(funCode, "上一份合同类型-英文", "lastContractTypeEn", PropertyDataType.String, null, null));
        renewNoticeVarList.stream().forEach(nv -> nv.setFunCode(funCode));
        iWfRegisterFeign.registerNoticeVar(renewNoticeVarList);
    }

    /**
     * 回调注册

     */
    private void registerCallback(String newSignFunCode, String name, String code, String apiPath) {
        WfMetaCallbackDto wmc = new WfMetaCallbackDto(name, code, Lists.list(newSignFunCode),
                "",
                apiPath,
                "caidaocloud-hr-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        iWfRegisterFeign.registerCallback(wmc);
    }

    public String create(ContractConfigDto contractConfig) {
        ContractConfig config = FastjsonUtil.convertObject(contractConfig, ContractConfig.class);
        config.setSignType(contractConfig.getContractApplyType());
        return config.create();
    }

    private WfMetaNoticeVarDto singleNoticeParameter(String funCode, String name,
                                                     String code, PropertyDataType dataType, String format, List<PropertyEnumDefDto> enumDef) {
        WfMetaNoticeVarDto.WfMetaNoticeVarDtoBuilder builder = WfMetaNoticeVarDto.builder()
                .name(name)
                .code(code)
                .funCode(funCode)
                .type(dataType.name())
                .url("/api/hr/contract/change/v1/notice/var")
                .serviceName(appName)
                .dateFormat(format);
        if (dataType == PropertyDataType.Enum && enumDef != null) {
            for (PropertyEnumDefDto propertyEnumDefDto : enumDef) {
                builder.enums(propertyEnumDefDto.getDisplay(), propertyEnumDefDto.getValue());
            }
        }
        return builder.build();
    }
}
