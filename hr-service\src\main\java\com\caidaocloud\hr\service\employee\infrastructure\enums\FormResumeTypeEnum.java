package com.caidaocloud.hr.service.employee.infrastructure.enums;

import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeFormConfig;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.form.DateTypeLastestDataConfig;
import com.caidaocloud.util.FastjsonUtil;

import java.util.Map;
import java.util.Objects;

public enum FormResumeTypeEnum {
    //取最新数据的date类型的数据
    DATETYPE_OF_LASTEST_DATA {
        public ResumeFormConfig convertParam(Map param) {
            if (Objects.isNull(param)) {
                return new DateTypeLastestDataConfig();
            }
            return FastjsonUtil.convertObject(param, DateTypeLastestDataConfig.class);
        }
    };

    public abstract ResumeFormConfig convertParam(Map param);

    public static FormResumeTypeEnum convert(String str) {
        for (FormResumeTypeEnum value : FormResumeTypeEnum.values()) {
            if (value.name().equalsIgnoreCase(str)) {
                return value;
            }
        }
        return null;
    }
}