package com.caidaocloud.hr.service.contract.application.event.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class EmpChangeMessageDto {
    private String tenantId;
    // 员工
    private String empId;

    public EmpChangeMessageDto(String tenantId) {
        this.tenantId = tenantId;
    }
}
