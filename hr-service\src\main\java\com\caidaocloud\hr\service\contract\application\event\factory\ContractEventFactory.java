package com.caidaocloud.hr.service.contract.application.event.factory;

import com.caidaocloud.hr.service.contract.application.event.dto.ContractMessageDto;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractStartMsg;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.enums.contract.ContractEventType;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;

/**
 *
 * <AUTHOR>
 * @date 2024/3/4
 */
public class ContractEventFactory {
	public static ContractMessageDto createContractStartMsg(ContractDo data){
		ContractMessageDto messageDto = ObjectConverter.convert(data, ContractMessageDto.class);
		messageDto.setTenantId(UserContext.getTenantId());
		messageDto.setEmp(data.getOwner());
		messageDto.setUserId(Long.valueOf(data.getCreateBy()));
		messageDto.setContract(data.getBid());
		messageDto.setSignType(data.getSignType());
		messageDto.setEventType(ContractEventType.START);
		return messageDto;
	}
}
