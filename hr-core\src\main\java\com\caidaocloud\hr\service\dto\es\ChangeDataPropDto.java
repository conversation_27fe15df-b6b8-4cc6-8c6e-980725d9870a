package com.caidaocloud.hr.service.dto.es;

import com.caidaocloud.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ChangeDataPropDto {
    private String prop;
    private String dataType;
    private String text;
    private String before;
    private String after;
    private Object beforeObj;
    private Object afterObj;

    public void mergeBefore(String newBefore){
        if(StringUtil.isNotEmpty(before)){
            return;
        }

        if(StringUtil.isNotEmpty(after)){
            before = after;
            return;
        }

        before = newBefore;
    }
}
