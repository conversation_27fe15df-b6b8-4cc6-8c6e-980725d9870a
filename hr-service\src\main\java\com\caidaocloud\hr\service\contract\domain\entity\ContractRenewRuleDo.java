package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.hr.service.contract.domain.repository.IContractRenewRuleRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * created by: FoAng
 * create time: 29/11/2024 3:03 下午
 */
@Data
@Service
@EqualsAndHashCode(callSuper = true)
public class ContractRenewRuleDo extends BaseDomainDoImpl<ContractRenewRuleDo> {

    @ApiModelProperty("规则名称")
    private String name;

    @ApiModelProperty("提前天数")
    private Integer advance;

    @ApiModelProperty("匹配条件")
    private String condition;

    @ApiModelProperty("匹配条件标签")
    private String conditionLabel;

    @ApiModelProperty("匹配条件表达式")
    private String conditionExp;

    @ApiModelProperty("消息配置")
    private String msgConfig;

    @Resource
    private IContractRenewRuleRepository contractRenewRuleRepository;

    private static final String IDENTIFIER = "entity.hr.ContractRenewRule";

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public BaseRepository<ContractRenewRuleDo> getRepository() {
        return contractRenewRuleRepository;
    }

    public List<Integer> listAdvanceDay() {
        return contractRenewRuleRepository.listAdvanceDay(getDoIdentifier());
    }
}
