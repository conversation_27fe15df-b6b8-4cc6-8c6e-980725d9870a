package com.caidaocloud.hr.service.confirmation.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import lombok.Data;

@Data
public class ConfirmationTodoEmpDto {
    private String empId;
    private String workno;
    private String name;
    private Long hireDate;
    private Long probationPeriodEndDate;
    private Long probationPeriodDay;
    private String organize;
    private String organizeTxt;
    private String post;
    private String postTxt;
    private DictSimple empType;
    private String leaderEmpId;
    private String leaderEmpName;
    private String leaderEmpWorkno;
}
