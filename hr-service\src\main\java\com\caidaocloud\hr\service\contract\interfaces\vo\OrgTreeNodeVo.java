package com.caidaocloud.hr.service.contract.interfaces.vo;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("组织信息")
public class OrgTreeNodeVo {
    @ApiModelProperty("组织ID")
    private String id;
    @ApiModelProperty("组织名称")
    private String text;
    @ApiModelProperty("子组织")
    private List<OrgTreeNodeVo> list = Lists.newArrayList();
}
