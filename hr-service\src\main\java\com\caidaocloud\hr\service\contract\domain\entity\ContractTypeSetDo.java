package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.domain.repository.IContractTypeSetRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.DictDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum.CREATE;
import static com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum.UPDATE;

/**
 * <AUTHOR> Zhou
 * @date 2022/5/6
 */
@Data
@Service
public class ContractTypeSetDo  extends BaseDomainDoImpl<ContractTypeSetDo> {
    /**
     * 合同类型
     */
    DictSimple contractType;
    /**
     * 备注
     */
    String remark;
    /**
     * 合同类别;对应字典上的合同类别
     */
    DictSimple contractClass;
    /**
     * 合同公司
     */
    List<String> company;
    /**
     * 合同公司名称
     */
    List<ComponentValue> companyTxt;
    /**
     * 签订类型
     */
    List<String> signType;
    /**
     * 签订类型名称
     */
    List<ComponentValue> signTypeTxt;
    /**
     * 员工类型bid
     */
    List<String> empType;
    /**
     * 员工类型json
     */
    List<DictDto> empTypeTxt;

    // /**
    //  * 状态
    //  */
    // EnumSimple status;

    private String IDENTIFIER = "entity.hr.ContractTypeSet";

    @Resource
    IContractTypeSetRepository contractTypeSetRepository;

    public List<ContractTypeSetDo> getByCondition(ContractTypeSetDo data) {
        return contractTypeSetRepository.selectByCondition(data);
    }

    @Override
    public BaseRepository<ContractTypeSetDo> getRepository() {
        return contractTypeSetRepository;
    }

    @Override
    public String getDoIdentifier(){
        return IDENTIFIER;
    }

    private void check(ContractTypeSetDo data) {
        PreCheck.preCheckArgument(null == data || null == data.getBid(), String.valueOf(MsgCodeConstant.NO_DATA_EXIST));
    }

    public void updateStatus(ContractTypeSetDo data, BusinessEventTypeEnum eventTypeEnum) {
        ContractTypeSetDo dbData = selectById(data.getBid());
        check(dbData);
        DataEntity.initFieldValue(IDENTIFIER, eventTypeEnum, data, dbData);
        contractTypeSetRepository.updateById(data);
    }

    /**
     * 查看详情
     *
     * @param bid
     * @return
     */
    public ContractTypeSetDo selectById(String bid) {
        return contractTypeSetRepository.selectById(bid, IDENTIFIER);
    }

    public ContractTypeSetDo save(ContractTypeSetDo data) {
        DataEntity.initFieldValue(IDENTIFIER, CREATE, data, null);
        return contractTypeSetRepository.insert(data);
    }

    public void updateById(ContractTypeSetDo data) {
        ContractTypeSetDo source = selectById(data.getBid());
        DataEntity.initFieldValue(IDENTIFIER, UPDATE, data, source);
        contractTypeSetRepository.updateById(data);
    }

    @Override
    public PageResult<ContractTypeSetDo> getPage(BasePage query) {
        ContractTypeSetDo data = new ContractTypeSetDo();
        data.setIdentifier(getDoIdentifier());
        data.setTenantId(UserContext.getTenantId());
        return getRepository().selectPage(query, data);
    }

    public List<ContractTypeSetDo> selectListByRegex(String company, String signType) {
        return contractTypeSetRepository.selectByRegex(IDENTIFIER, company, signType);
    }

    /**
     * 分组跑批使用
     */
    public String getEmpTypeKey(){
        return getEmpType().stream().collect(Collectors.joining(","));
    }

    public List<ContractTypeSetDo> getListCompanyByRegex(String signType) {
        return contractTypeSetRepository.getListCompanyByRegex(IDENTIFIER, signType);
    }
}

