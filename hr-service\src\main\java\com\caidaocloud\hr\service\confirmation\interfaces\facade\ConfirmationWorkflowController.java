package com.caidaocloud.hr.service.confirmation.interfaces.facade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationWfNoticeService;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationWfAppoverService;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationWfCallbackService;
import com.caidaocloud.hr.service.confirmation.application.ConfirmationWfSeqService;
import com.caidaocloud.hr.service.transfer.interfaces.dto.NoticeDetailDto;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URLDecoder;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/confirmation/workflow")
@Api(value = "/api/hr/v1/confirmation/workflow", description = "转正工作流")
public class ConfirmationWorkflowController {

	@Autowired
	private ConfirmationWfSeqService confirmationWfSeqService;
	@Autowired
	private ConfirmationWfAppoverService confirmationWfAppoverService;
	@Autowired
	private ConfirmationWfCallbackService confirmationWfCallbackService;
	@Autowired
	private ConfirmationWfNoticeService confirmationWfNoticeService;

	@ApiOperation("工作流-审批人详情")
	@GetMapping("approver")
	public Result<String> getWfApprover(@RequestParam("applicantId") String applicantId,
			@RequestParam("code") String code, @RequestParam("businessKey") String businessKey, @RequestParam("value") String value) {
		return Result.ok(confirmationWfAppoverService.getWfApprover(applicantId, businessKey, code, value));
	}

	@ApiOperation("工作流-消息通知变量")
	@PostMapping("notice")
	public Result<Map<String, String>> noticeDetail(@RequestBody NoticeDetailDto dto) {
		PreCheck.preCheckNotNull(dto.getBusinessKey(), "Business key is empty");
		return Result.ok(confirmationWfNoticeService.getNoticeVar(dto.getBusinessKey(), dto.getVariables()));
	}

	@PostMapping("callback")
	@ApiOperation("工作流-回调")
	public Result<?> callback(@RequestBody WfCallbackResultDto callback) {
		confirmationWfCallbackService.callback(callback.getTenantId(), callback.getBusinessKey(), callback.getCallbackType());
		return Result.ok(true);
	}

	@SneakyThrows
	@ApiOperation("审批序列流条件获取")
	@GetMapping("/seq")
	public Result<String> getSequenceData(
			@RequestParam(value = "businessId") String businessId,
			@RequestParam(value = "code") String code) {
		return Result.ok(confirmationWfSeqService.fetchSeqValue(businessId,  URLDecoder.decode(code, "UTF-8")));
	}
}
