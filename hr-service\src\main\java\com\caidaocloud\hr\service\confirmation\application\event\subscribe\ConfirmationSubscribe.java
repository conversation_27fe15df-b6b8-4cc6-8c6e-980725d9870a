package com.caidaocloud.hr.service.confirmation.application.event.subscribe;

import com.caidaocloud.hr.service.confirmation.application.service.EmpConfirmationService;
import com.caidaocloud.hr.service.temination.application.event.dto.ScheduleTaskMsg;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ConfirmationSubscribe {
    @Autowired
    private EmpConfirmationService empConfirmationService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.hr.confirmation.writeback", durable = "true"),
                    exchange = @Exchange(value = "schedule.task.exchange." + EmpConfirmationService.MQ_TOPIC),
                    key = {"routingKey.schedule.task." + EmpConfirmationService.MQ_TOPIC}
            )
    )
    public void process(String msg) {
        log.info("Subscribe confirmation task message,msg={}", msg);
        ScheduleTaskMsg scheduleTaskMsg = FastjsonUtil.toObject(msg, ScheduleTaskMsg.class);
        try {
            log.info("转正处理开始");
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(scheduleTaskMsg.getUserInfo().getTenantId());
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            String applyId = scheduleTaskMsg.getTaskId();
            empConfirmationService.doUpdate(applyId);
            log.info("转正处理结束");
        }
        catch (Exception e) {
            log.info("contract task error,{}", e.getMessage(), e);
        }
        finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

}
