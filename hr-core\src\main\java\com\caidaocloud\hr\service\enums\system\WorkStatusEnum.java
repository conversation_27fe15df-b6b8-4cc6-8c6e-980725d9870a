package com.caidaocloud.hr.service.enums.system;

/**
 * 任职状态
 *
 * <AUTHOR>
 * @Date 2021/12/3
 */
public enum WorkStatusEnum implements LanguageEnum{
    MAJOR_POST(0, "主岗"),
    CONCURRENT_POST(1, "兼岗");

    private Integer index;
    private String name;

    WorkStatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (WorkStatusEnum c : WorkStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getEnumTextByIndexAndLang(String index, String lang) {
        return null;
    }
}
