package com.caidaocloud.hr.service.vo;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PhoneSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工基本信息VO")
public class EmpBasicInfoVo {
    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("员工英文名")
    private String enName;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("员工性别")
    private DictSimple sex;

    @ApiModelProperty("员工图像")
    private Attachment photo;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("员工类型")
    private DictSimple empType;

    @ApiModelProperty("员工电话")
    private PhoneSimple phone;

    @ApiModelProperty("员工公司邮箱")
    private String companyEmail;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("所属组织Id")
    private String organize;

    @ApiModelProperty("所属岗位名称")
    private String postTxt;

    @ApiModelProperty("所属岗位Id")
    private String post;

    /**
     * 时间轴数据变更时间
     */
    @ApiModelProperty("时间轴数据变更时间")
    private long dataStartTime;

    @ApiModelProperty("扩展字段")
    private Map ext = new LinkedHashMap();
}
