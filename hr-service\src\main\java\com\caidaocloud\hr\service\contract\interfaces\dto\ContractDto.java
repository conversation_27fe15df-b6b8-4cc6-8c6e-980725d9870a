package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("员工合同DTO")
public class ContractDto {
    @ApiModelProperty("合同ID")
    private String bid;
    @ApiModelProperty("合同签署人")
    private EmpSimple owner;
    @ApiModelProperty("入职日期")
    private Long hireDate;
    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;
    @ApiModelProperty("所属组织Id")
    private String organize;
    @ApiModelProperty("所属组织名称")
    private String organizeTxt;
    @ApiModelProperty("所属组织编码")
    private String organizeCode;
    @ApiModelProperty("关联的职务ID")
    private String job;
    @ApiModelProperty("关联的职务名称")
    private String jobTxt;
    @ApiModelProperty("岗位ID")
    private String post;
    @ApiModelProperty("岗位名称")
    private String postTxt;
    @ApiModelProperty("员工类型")
    private DictSimple empType;
    @ApiModelProperty("签订类型")
    private EnumSimple signType;
    @ApiModelProperty("合同编号")
    private String contractNo;
    @ApiModelProperty("合同公司Id")
    private String company;
    @ApiModelProperty("所属公司名称")
    private String companyTxt;
    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;
    @ApiModelProperty("合同设置名称（合同类型名称/合同名称）")
    private String contractTypeSetTxt;
    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;
    @ApiModelProperty("合同类型")
    private DictSimple contractType;
    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;
    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;
    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;
    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;
    @ApiModelProperty("合同签订日期")
    private Long signDate;
    @ApiModelProperty("合同开始日期")
    private Long startDate;
    private Long calcEndDate;
    @ApiModelProperty("合同结束日期")
    private Long endDate;
    @ApiModelProperty("合同签订次数")
    private Integer signTime;
    @ApiModelProperty("状态")
    private EnumSimple contractStatus;
    @ApiModelProperty("审批状态")
    private EnumSimple approvalStatus;
    @ApiModelProperty("合同终止日期")
    private Long terminationDate;
    @ApiModelProperty("合同终止原因")
    private String terminationReason;
    @ApiModelProperty("合同附件")
    private Attachment attachFile;
    @ApiModelProperty("工作地ID")
    private String workplace;
    @ApiModelProperty("工作地名称")
    private String workplaceTxt;
    @ApiModelProperty("工时制")
    private EnumSimple workHour;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("签署流程状态")
    private EnumSimple signProcessStatus;
    @ApiModelProperty("是否开启工作流")
    private Boolean openWorkflow;
    @ApiModelProperty("合同发起日期")
    private Long launchDate;
    @ApiModelProperty("注册地址")
    private String registerAddress;
    @ApiModelProperty("试用期期限")
    private EnumSimple probation;
    @ApiModelProperty("上一份合同bid")
    private String lastContract;
    @ApiModelProperty("是否在审批记录中隐藏")
    private EnumSimple isHideInApproval;
    @ApiModelProperty("合同期（年）")
    private BigDecimal contractYear;
    @ApiModelProperty("关闭电子签署")
    private Boolean closeEsign = false;
    @ApiModelProperty("发起签署")
    private EnumSimple initiateStatus;
    @ApiModelProperty("续签意向状态")
    private EnumSimple continueStatus;
    @ApiModelProperty("续签意向书id")
    private String continueLetter;
    @ApiModelProperty("合同解除日期")
    private Long dissolveDate;
    @ApiModelProperty("合同解除原因")
    private String dissolveReason;
    @ApiModelProperty("意向反馈建议")
    private DictSimple feedback;
    @ApiModelProperty("时间轴")
    private Long dateTime;
    @ApiModelProperty(value = "续签审批流程状态", hidden = true)
    private EnumSimple continueApprovalStatus;
    private String lastContractBid;
}