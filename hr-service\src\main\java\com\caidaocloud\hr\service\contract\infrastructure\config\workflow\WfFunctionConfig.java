package com.caidaocloud.hr.service.contract.infrastructure.config.workflow;

import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.application.service.ContractSignService;
import com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field.ContinueContractFormDef;
import com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field.ContractAmendmentFormDef;
import com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field.ContractNewSignFormDef;
import com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field.ContractRenewFormDef;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.workflow.annotation.WfFunction;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.WfMetaCallbackDto;
import com.caidaocloud.workflow.dto.WfMetaFunDto;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.dto.WfMetaNoticeVarDto;
import com.caidaocloud.workflow.enums.WfCallbackTimeTypeEnum;
import com.caidaocloud.workflow.enums.WfCallbackTypeEnum;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.caidaocloud.workflow.enums.WfFunctionPageJumpType;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.ImmutableMap;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作流-流程应用功能注册
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WfFunctionConfig {
    @Value("${spring.application.name:}")
    private String appName;
    @Autowired
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private ContractService contractService;
    @Resource
    private ContractSignService contractSignService;
    public static final String dissolveFunCode = "CONTRACTDISSOLVE",
            terminationFunCode = "CONTRACTTERMINATION";
    public static final List<WfMetaFunFormFieldDto> dissolveFormFields = Lists.list(
            new WfMetaFunFormFieldDto("signType", "签订类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("contractNo", "合同编号", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("companyTxt", "合同公司", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("registerAddress", "注册地址", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("contractSettingType", "合同类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("periodType", "合同期限", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("signDate", "合同签订日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("startDate", "合同开始日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("endDate", "合同结束日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("remark", "备注", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("dissolveDate", "解除日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("dissolveReason", "解除原因", WfFieldDataTypeEnum.Text)
    );
    public static final List<WfMetaFunFormFieldDto> terminationFormFields = Lists.list(
            new WfMetaFunFormFieldDto("signType", "签订类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("contractNo", "合同编号", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("companyTxt", "合同公司", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("registerAddress", "注册地址", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("contractSettingType", "合同类型", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("periodType", "合同期限", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("signDate", "合同签订日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("startDate", "合同开始日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("endDate", "合同结束日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("remark", "备注", WfFieldDataTypeEnum.Text),
            new WfMetaFunFormFieldDto("terminationDate", "终止日期", WfFieldDataTypeEnum.Timestamp),
            new WfMetaFunFormFieldDto("terminationReason", "终止原因", WfFieldDataTypeEnum.Text)
    );

    @WfFunction(name = "合同新签", code = "ContractNewSign",
            pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/hr/contract/sign/v1/workflow/detail",
            serviceId = "caidaocloud-hr-service",
            redirectAddress = "",
            formDefClass = ContractNewSignFormDef.class)
    public void registerContractNewSign() {
    }

    @WfFunction(name = "合同续签", code = "ContractRenew",
            pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/hr/contract/sign/v1/workflow/detail",
            serviceId = "caidaocloud-hr-service",
            redirectAddress = "",
            formDefClass = ContractRenewFormDef.class)
    public void registerContractRenew() {
    }

    @WfFunction(name = "合同改签", code = "ContractAmendment",
            pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/hr/contract/sign/v1/workflow/detail",
            serviceId = "caidaocloud-hr-service",
            redirectAddress = "",
            formDefClass = ContractAmendmentFormDef.class)
    public void registerContractAmendment() {
    }

    @WfFunction(name = "合同续签意向确认", code = "CONTINUECONTRACT",
            pageJumpType = WfFunctionPageJumpType.RELATIVE_PATH,
            detailAddress = "/api/hr/contract/v1/last/detail",
            serviceId = "caidaocloud-hr-service",
            redirectAddress = "",
            formDefClass = ContinueContractFormDef.class)
    public void registerContinueContract() {
    }

    @PostConstruct
    public void initWfRegister() {
        try {
            doInitWfRegister();
            WfFunctionConfiguration.getFunFieldMap().putIfAbsent(dissolveFunCode, ImmutableMap.copyOf(dissolveFormFields.stream()
                    .collect(Collectors.toMap(e -> e.getCode(), e -> e, (v1, v2) -> v2))));
            WfFunctionConfiguration.getFunFieldMap().putIfAbsent(terminationFunCode, ImmutableMap.copyOf(terminationFormFields.stream()
                    .collect(Collectors.toMap(e -> e.getCode(), e -> e, (v1, v2) -> v2))));
        } catch (Exception e) {
            log.error("registerFunction Exception,{}", e);
        }
    }

    private void doInitWfRegister() {
        List<WfMetaFunFormFieldDto> formFields = Lists.list();
        formFields.add(new WfMetaFunFormFieldDto("signType", "签订类型"));
        formFields.add(new WfMetaFunFormFieldDto("contractNo", "合同编号"));
        formFields.add(new WfMetaFunFormFieldDto("companyTxt", "合同公司"));
        formFields.add(new WfMetaFunFormFieldDto("registerAddress", "注册地址"));
        formFields.add(new WfMetaFunFormFieldDto("contractSettingType", "合同类型"));
        formFields.add(new WfMetaFunFormFieldDto("periodType", "合同期限"));
        formFields.add(new WfMetaFunFormFieldDto("signDate", "合同签订日期"));
        formFields.add(new WfMetaFunFormFieldDto("startDate", "合同开始日期"));
        formFields.add(new WfMetaFunFormFieldDto("endDate", "合同结束日期"));
        formFields.add(new WfMetaFunFormFieldDto("remark", "备注"));
        formFields.add(new WfMetaFunFormFieldDto("dissolveDate", "解除日期"));
        formFields.add(new WfMetaFunFormFieldDto("dissolveReason", "解除原因"));

        // 合同解除
        WfMetaFunDto dto = new WfMetaFunDto("合同解除", dissolveFunCode,
                WfFunctionPageJumpType.RELATIVE_PATH, "",
                "caidaocloud-hr-service",
                "", "/api/hr/contract/v1/getDetail", "", dissolveFormFields);
        iWfRegisterFeign.registerFunction(dto);

        registerCallback(dissolveFunCode, "合同解除", "CONTRACTDISSOLVE-CALLBACK", "/api/hr/contract/change/v1/callback");
        WfMetaCallbackDto wmc;

        int size = formFields.size();
        formFields.remove(size - 1);
        formFields.remove(size - 2);
        formFields.add(new WfMetaFunFormFieldDto("terminationDate", "终止日期"));
        formFields.add(new WfMetaFunFormFieldDto("terminationReason", "终止原因"));

        // 合同终止
        dto = new WfMetaFunDto("合同终止", terminationFunCode,
                WfFunctionPageJumpType.RELATIVE_PATH, "",
                "caidaocloud-hr-service",
                "", "/api/hr/contract/v1/getDetail", "", terminationFormFields);
        iWfRegisterFeign.registerFunction(dto);
        wmc = new WfMetaCallbackDto("合同终止", "CONTRACTTERMINATION-CALLBACK", Lists.list(terminationFunCode),
                "",
                "/api/hr/contract/change/v1/callback",
                "caidaocloud-hr-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        iWfRegisterFeign.registerCallback(wmc);

        List<WfMetaNoticeVarDto> noticeVarList = new ArrayList<>();
        /**
         * 合同类型
         * 合同期限
         * 合同签订日期
         * 合同开始日期
         * 合同结束日期
         * 终止日期/解除日期
         * 终止原因/解除原因
         * 备注
         */
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "合同类型", "contractSettingType", PropertyDataType.String, null, null));
        String periodTypeJson = "[{\"display\":\"固定期限\",\"i18nDisplay\":{\"default\":\"固定期限\"},\"value\":\"0\"},{\"display\":\"无固定期限\",\"i18nDisplay\":{\"default\":\"无固定期限\"},\"value\":\"1\"}]";
        List<PropertyEnumDefDto> enumList = FastjsonUtil.toList(periodTypeJson, PropertyEnumDefDto.class);
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "合同期限", "periodType", PropertyDataType.Enum, null, enumList));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "合同签订日期", "signDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "合同开始日期", "startDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "合同结束日期", "endDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "备注", "remark", PropertyDataType.String, null, null));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "解除日期", "dissolveDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        noticeVarList.add(singleNoticeParameter(dissolveFunCode, "解除原因", "dissolveReason", PropertyDataType.String, null, null));
        iWfRegisterFeign.registerNoticeVar(noticeVarList);

        size = noticeVarList.size();
        noticeVarList.remove(size - 1);
        noticeVarList.remove(size - 2);
        noticeVarList.stream().forEach(nv -> nv.setFunCode(terminationFunCode));
        noticeVarList.add(singleNoticeParameter(terminationFunCode, "终止日期", "terminationDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        noticeVarList.add(singleNoticeParameter(terminationFunCode, "终止原因", "terminationReason", PropertyDataType.String, null, null));
        iWfRegisterFeign.registerNoticeVar(noticeVarList);
        // List<PropertyEnumDefDto> probationList = FastjsonUtil.toList("[{\"display\":\"无试用期\",\"i18nDisplay\":{\"default\":\"无试用期\"},\"value\":\"0\"},{\"display\":\"1个月\",\"i18nDisplay\":{\"default\":\"1个月\"},\"value\":\"1\"},{\"display\":\"2个月\",\"i18nDisplay\":{\"default\":\"2个月\"},\"value\":\"2\"},{\"display\":\"3个月\",\"i18nDisplay\":{\"default\":\"3个月\"},\"value\":\"3\"},{\"display\":\"6个月\",\"i18nDisplay\":{\"default\":\"6个月\"},\"value\":\"6\"}]", PropertyEnumDefDto.class);
        String signTypeJson = "[{\"display\":\"新签\",\"i18nDisplay\":{\"default\":\"新签\"},\"value\":\"2\"},{\"display\":\"续签\",\"i18nDisplay\":{\"default\":\"续签\"},\"value\":\"0\"},{\"display\":\"改签\",\"i18nDisplay\":{\"default\":\"改签\"},\"value\":\"1\"}]";
        List<PropertyEnumDefDto> signTypeList = FastjsonUtil.toList(signTypeJson, PropertyEnumDefDto.class);
        //合同新签
        contractNewSign(enumList, signTypeList);
        //合同续签
        contractRenewSign(enumList, signTypeList);
        //合同改签
        contractAmendmentSign(enumList, signTypeList);
    }

    private void contractNewSign(List<PropertyEnumDefDto> enumList, List<PropertyEnumDefDto> probationList) {
        String newSignFunCode = ContractSignTypeConstant.CONTRACT_NEW_SIGN;
        registerCallback(newSignFunCode, "合同新签", "CONTRACTNEWSIGN-CALLBACK", "/api/hr/contract/sign/v1/callback");
        registerCallback(newSignFunCode, "合同新签--电子签", "CONTRACTNEWSIGN-CALLBACK-V2", "/api/hr/contract/sign/v1/callbackAndStart");
        List<WfMetaNoticeVarDto> newSignNoticeVarList = new ArrayList<>();
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同公司", "companyTxt", PropertyDataType.String, null, null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同类型", "contractSettingType", PropertyDataType.String, null, null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同期限", "periodType", PropertyDataType.Enum, null, enumList));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同签订日期", "signDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同开始日期", "startDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "合同结束日期", "endDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "试用期期限", "probation", PropertyDataType.Enum, null, probationList));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "试用期截止日期", "probationPeriodEndDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        newSignNoticeVarList.add(singleNoticeParameter(newSignFunCode, "备注", "remark", PropertyDataType.String, null, null));
        newSignNoticeVarList.stream().forEach(nv -> nv.setFunCode(newSignFunCode));
        iWfRegisterFeign.registerNoticeVar(newSignNoticeVarList);
    }

    private void registerCallback(String newSignFunCode, String name, String code, String apiPath) {
        WfMetaCallbackDto wmc = new WfMetaCallbackDto(name, code, Lists.list(newSignFunCode),
                "",
                apiPath,
                "caidaocloud-hr-service",
                "",
                WfCallbackTypeEnum.RELATIVE_PATH,
                WfCallbackTimeTypeEnum.NOW);
        iWfRegisterFeign.registerCallback(wmc);
    }


    private void contractRenewSign(List<PropertyEnumDefDto> enumList, List<PropertyEnumDefDto> signTypeList) {
        String renewFunCode = ContractSignTypeConstant.CONTRACT_RENEW;
        registerCallback(renewFunCode, "合同续签", "CONTRACTRENEW-CALLBACK", "/api/hr/contract/sign/v1/callback");
        registerCallback(renewFunCode, "合同续签--电子签", "CONTRACTRENEW-CALLBACK-V2", "/api/hr/contract/sign/v1/callbackAndStart");
        List<WfMetaNoticeVarDto> renewNoticeVarList = new ArrayList<>();
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "签订类型", "signType", PropertyDataType.Enum, null, signTypeList));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同公司", "companyTxt", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同公司-英文", "companyTxtEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "注册地址", "registerAddress", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同类型", "contractSettingType", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同类型-英文", "contractSettingTypeEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同期限", "periodType", PropertyDataType.Enum, null, enumList));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同签订日期", "signDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同开始日期", "startDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同结束日期", "endDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "备注", "remark", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "上份合同到期天数", "lastContractExpireDays", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "上份合同结束日期", "lastContractEndDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同续签审批链接", "approvalLink", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "合同续签审批链接-英文", "approvalLinkEn", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "上一份合同类型", "lastContractType", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(renewFunCode, "上一份合同类型-英文", "lastContractTypeEn", PropertyDataType.String, null, null));
        renewNoticeVarList.stream().forEach(nv -> nv.setFunCode(renewFunCode));
        iWfRegisterFeign.registerNoticeVar(renewNoticeVarList);
    }

    private void contractAmendmentSign(List<PropertyEnumDefDto> enumList, List<PropertyEnumDefDto> signTypeList) {
        String amendmentFunCode = ContractSignTypeConstant.CONTRACT_AMENDMENT;
        registerCallback(amendmentFunCode, "合同改签", "CONTRACTAMENDMENT-CALLBACK", "/api/hr/contract/sign/v1/callback");
        registerCallback(amendmentFunCode, "合同改签--电子签", "CONTRACTAMENDMENT-CALLBACK-V2", "/api/hr/contract/sign/v1/callbackAndStart");
        List<WfMetaNoticeVarDto> renewNoticeVarList = new ArrayList<>();
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "签订类型", "signType", PropertyDataType.Enum, null, signTypeList));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同公司", "companyTxt", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "注册地址", "registerAddress", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同类型", "contractSettingType", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同期限", "periodType", PropertyDataType.Enum, null, enumList));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同签订日期", "signDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同开始日期", "startDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "合同结束日期", "endDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "备注", "remark", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "上份合同到期天数", "lastContractExpireDays", PropertyDataType.String, null, null));
        renewNoticeVarList.add(singleNoticeParameter(amendmentFunCode, "上份合同结束日期", "lastContractEndDate", PropertyDataType.Timestamp, "yyyy-MM-dd", null));

        renewNoticeVarList.stream().forEach(nv -> nv.setFunCode(amendmentFunCode));
        iWfRegisterFeign.registerNoticeVar(renewNoticeVarList);
    }

    private WfMetaNoticeVarDto singleNoticeParameter(String funCode, String name,
                                                     String code, PropertyDataType dataType, String format, List<PropertyEnumDefDto> enumDef) {
        WfMetaNoticeVarDto.WfMetaNoticeVarDtoBuilder builder = WfMetaNoticeVarDto.builder()
                .name(name)
                .code(code)
                .funCode(funCode)
                .type(dataType.name())
                .url("/api/hr/contract/change/v1/notice/var")
                .serviceName(appName)
                .dateFormat(format);
        if (dataType == PropertyDataType.Enum && enumDef != null) {
            for (PropertyEnumDefDto propertyEnumDefDto : enumDef) {
                builder.enums(propertyEnumDefDto.getDisplay(), propertyEnumDefDto.getValue());
            }
        }
        return builder.build();
    }
}