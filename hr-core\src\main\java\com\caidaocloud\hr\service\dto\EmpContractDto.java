package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * created by: FoAng
 * create time: 5/9/2022 1:38 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工导入保存合同信息")
public class EmpContractDto {

    @ApiModelProperty("合同ID")
    private String bid;

    @ApiModelProperty("合同签署人")
    private EmpSimple owner;

    @ApiModelProperty("入职日期")
    private Long hireDate;

    @ApiModelProperty("员工状态")
    private EnumSimple empStatus;

    @ApiModelProperty("所属组织Id")
    private String organize;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("关联的职务ID")
    private String job;

    @ApiModelProperty("关联的职务名称")
    private String jobTxt;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("员工类型")
    private DictSimple empType;

    @ApiModelProperty("签订类型")
    private EnumSimple signType;

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("合同公司Id")
    private String company;

    @ApiModelProperty("所属公司名称")
    private String companyTxt;

    @ApiModelProperty("合同设置Bid")
    private String contractTypeSet;

    @ApiModelProperty("合同设置名称（合同类型名称/合同名称）")
    private String contractTypeSetTxt;

    @ApiModelProperty("合同类型")
    private DictSimple contractType;

    @ApiModelProperty("合同期限类型")
    private EnumSimple periodType;

    @ApiModelProperty("合同期（月）")
    private Integer contractPeriod;

    @ApiModelProperty("试用期（月）")
    private Integer probationPeriod;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriodEndDate;

    @ApiModelProperty("合同签订日期")
    private Long signDate;

    @ApiModelProperty("合同开始日期")
    private Long startDate;

    @ApiModelProperty("合同结束日期")
    private Long endDate;

    @ApiModelProperty("合同签订次数")
    private Integer signTime;

    @ApiModelProperty("状态")
    private EnumSimple contractStatus;

    @ApiModelProperty("审批状态")
    private EnumSimple approvalStatus;

    @ApiModelProperty("合同终止日期")
    private Long terminationDate;

    @ApiModelProperty("合同终止原因")
    private String terminationReason;

    @ApiModelProperty("合同附件")
    private Attachment attachFile;

    @ApiModelProperty("工作地ID")
    private String workplace;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("工时制")
    private EnumSimple workHour;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("签署流程状态")
    private EnumSimple signProcessStatus;

    @ApiModelProperty("是否开启工作流")
    private Boolean openWorkflow;

    @ApiModelProperty("合同发起日期")
    private Long launchDate;

    @ApiModelProperty("合同类型")
    private DictSimple contractSettingType;
}
