package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.emp.constant.EmpConstants;
import com.caidaocloud.hr.service.employee.domain.emp.fieldset.repository.IEmpRegisterModuleDataRepository;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpRegisterModuleDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.util.ConvertValueUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/4/25
 */
@Service
public class EmpRegisterModuleDataService {
	@Autowired
	private IEmpRegisterModuleDataRepository empRegisterModuleDataRepository;
	@Autowired
	private MetadataService metadataService;
	@Autowired
	private ConvertValueUtil convertValueUtil;
	public void save(String pageKey, EmpRegisterModuleDataDto dto) {
		DataSimple dataSimple = createModuleData(pageKey, dto);
		empRegisterModuleDataRepository.save(dto.getEmpId(), dataSimple);
	}

	private DataSimple createModuleData(String pageKey, EmpRegisterModuleDataDto dto) {
		String identifier = convertPageKey(pageKey);
		MetadataVo metadata = metadataService.getMetadata(identifier);

		DataSimple dataSimple = new DataSimple();
		dataSimple.setIdentifier(identifier);
		dataSimple.setUpdateTime(System.currentTimeMillis());
		dataSimple.setUpdateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		dataSimple.setTenantId(SecurityUserUtil.getSecurityUserInfo().getTenantId());
		dataSimple.setDataStartTime(dto.getDataStartTime());
		EmpSimple empSimple = new EmpSimple();
		empSimple.setEmpId(dto.getEmpId());
		dataSimple.getProperties().add("form_owner", empSimple);
		for (MetadataPropertyVo property : metadata.fetchAllProperties()) {
			if (dto.getData().get(property.getProperty()) == null) {
				continue;
			}
			PropertyValue propertyValue = convertValueUtil.convertData2PropertyValue(dto.getData()
					.get(property.getProperty()), property);
			dataSimple.getProperties().add(property.getProperty(), propertyValue);
		}
		return dataSimple;
	}

	private String convertPageKey(String pageKey) {
		return StringUtils.substringAfter(pageKey, EmpConstants.EMP_PAGE_DETAIL_PREFIX).replaceAll("_", ".");
	}

	public EmpRegisterModuleDataDto detail(String pageKey, String empId) {
		String identifier = convertPageKey(pageKey);
		MetadataVo metadata = metadataService.getMetadata(identifier);
		DataSimple data = empRegisterModuleDataRepository.detail(identifier, empId);
		if (data == null) {
			return new EmpRegisterModuleDataDto(empId);
		}

		return new EmpRegisterModuleDataDto(empId, convertPropertyValue2Map(data.getProperties(),metadata));
	}

	private Map<String,Object> convertPropertyValue2Map(NestPropertyValue properties, MetadataVo metadata) {
		Map<String, Object> map = new HashMap<>();
		for (Map.Entry<String, PropertyValue> entry : properties.entrySet()) {
			if (entry.getValue() instanceof SimplePropertyValue) {
				SimplePropertyValue value = (SimplePropertyValue) entry.getValue();
				MetadataPropertyVo propertyVo = Sequences.sequence(metadata.fetchAllProperties())
						.find(p -> p.getProperty().equals(entry.getKey()))
						.get();
				map.put(entry.getKey(), value.getType()
						.isArray() ? Sequences.sequence( value.getArrayValues()).map(v-> {
					List<PropertyEnumDefDto> def = propertyVo.getEnumDef();
					return Sequences.sequence(def).find(ed -> ed.getValue().equals(v)).map(ed -> {
						KeyValue keyValue = new KeyValue();
						keyValue.setText(ed.getDisplay());
						keyValue.setValue(ed.getValue());
						return keyValue;
					}).getOrNull();
				}).toList() : value.getValue());
			}else{
				map.put(entry.getKey(), entry.getValue());
			}
		}
		return map;
	}

}
