package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationApply;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.temination.application.TerminationService;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApply;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationApplyType;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 离职文件归档
 * created by: FoAng
 * create time: 13/6/2024 4:55 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class TerminateArchiveProcessor extends AbsArchiveProcessor {

    private TerminationService terminationService;

    @Override
    public String businessLine() {
        return ArchiveStandardLine.TERMINATE.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        TerminationApply terminationApply = terminationService.one(businessId);
        if (terminationApply == null) {
            log.error("[archive] fetch terminationApply data error, businessId:{}", businessId);
            return Lists.newArrayList();
        }
        return filterArchiveList(Lists.newArrayList(buildArchiveData(terminationApply)));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        List<TerminationApply> terminationApplyList = terminationService.loadArchiveData(page);
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(terminationApplyList)) {
            for (TerminationApply apply : terminationApplyList) {
                archiveDataList.add(buildArchiveData(apply));
            }
        }
        return filterArchiveList(archiveDataList);
    }

    public ArchiveData buildArchiveData(TerminationApply terminationApply) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setBusinessLine(ArchiveStandardLine.TERMINATE.getDesc());
        archiveData.setBusinessType(fetchBusinessType(terminationApply));
        archiveData.setBusinessId(terminationApply.getBid());
        archiveData.setEmpId(terminationApply.getEmp().getEmpId());
        archiveData.setArchiveFiles(fetchArchiveFiles(terminationApply));
        archiveData.setEventTime(terminationApply.getCreateTime());
        return archiveData;
    }

    public List<ArchiveFile> fetchArchiveFiles(TerminationApply apply) {
        List<ArchiveFile> archiveFiles = Lists.newArrayList();
        if (StringUtils.isNotEmpty(apply.getFormDataId())) {
            ArchiveFile formFile = fetchFormFile(apply.getFormDefId(), apply.getFormDataId());
            archiveFiles.add(formFile);
        }
        archiveFiles.add(BeanUtil.convert(apply.getAttachment(), ArchiveFile.class));
        return archiveFiles.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private String fetchBusinessType(TerminationApply apply) {
        TerminationApplyType applyType = apply.getApplyType();
        return applyType == TerminationApplyType.BY_AGENT ? "代员工申请" : "员工申请";
    }


}
