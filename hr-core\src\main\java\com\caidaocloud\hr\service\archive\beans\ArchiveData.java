package com.caidaocloud.hr.service.archive.beans;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 1:32 下午
 */
@Data
public class ArchiveData implements Serializable {

    /**
     * 员工Id
     */
    private String empId;

    /**
     * 员工类型： 0：默认类型， 1: 预入职
     */
    private int empType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 所属业务
     */
    private String businessLine;

    /**
     * 子业务
     */
    private String subBusinessLine;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 所属业务线字典表
     */
    private String businessLineDict;

    /**
     * 所属业务类型字典表
     */
    private String businessTypeDict;

    /**
     * 归档文件列表
     */
    private List<ArchiveFile> archiveFiles;

    /**
     * 事件时间
     */
    private Long eventTime;

}
