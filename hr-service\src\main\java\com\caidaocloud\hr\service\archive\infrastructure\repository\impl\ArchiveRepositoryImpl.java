package com.caidaocloud.hr.service.archive.infrastructure.repository.impl;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.beans.ArchiveScopeVo;
import com.caidaocloud.hr.service.archive.infrastructure.po.ArchiveEsData;
import com.caidaocloud.hr.service.archive.infrastructure.repository.ArchiveEsRepository;
import com.caidaocloud.hr.service.archive.infrastructure.repository.ArchiveRepository;
import com.caidaocloud.hr.service.archive.interfaces.dto.ArchiveQueryDto;
import com.caidaocloud.hr.service.common.base.DataScopeService;
import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.hr.service.dto.auth.EsOperate;
import com.caidaocloud.hr.service.dto.auth.EsScopeQuery;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.admin.indices.refresh.RefreshResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Repository;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.repository.Sort;
import org.zxp.esclientrhl.util.MetaData;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 6/6/2024 11:08 上午
 */
@Slf4j
@Repository
@AllArgsConstructor
public class ArchiveRepositoryImpl implements ArchiveRepository {

    private ArchiveEsRepository archiveEsRepository;

    private ElasticsearchTemplate<ArchiveEsData, Long> elasticsearchTemplate;

    private ElasticsearchIndex<ArchiveEsData> elasticsearchIndex;

    private DataScopeService dataScopeService;

    private RestHighLevelClient cusClient;

    private final SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1L, 1L);

    @Override
    public ArchiveEsData checkExist(ArchiveEsData data) {
        try {
            if (StringUtil.isNotEmpty(data.getArchiveId())) {
                return archiveEsRepository.getById(data.getArchiveId());
            } else {
                List<ArchiveEsData> esDataList = archiveEsRepository.search(QueryBuilders.boolQuery());
                return CollectionUtils.isNotEmpty(esDataList) ? esDataList.get(0) : null;
            }
        } catch (Exception e) {
            log.error("[archive] check exist error, {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public ArchiveEsData detail(Long archiveId) {
        try {
            return archiveEsRepository.getById(archiveId);
        } catch (Exception e) {
            log.error("[archive] fetch detail error, msg:{}", e.getMessage(), e);
            throw new ServerException("获取详情失败");
        }
    }

    @Override
    public void insertArchiveData(ArchiveEsData archiveData) {
        archiveData.setArchiveId(snowflakeUtil.createId());
        try {
            archiveEsRepository.save(archiveData);
        } catch (Exception e) {
            log.error("[archive] save archive data error, {}", e.getMessage(), e);
        }
    }

    @Override
    public void saveUpdateArchiveData(ArchiveEsData archiveData) {
        ArchiveEsData data;
        if ((data = checkExist(archiveData)) != null) {
            try {
                archiveData.setArchiveId(data.getArchiveId());
                archiveEsRepository.update(archiveData);
            } catch (Exception e) {
                log.error("[archive] saveUpdate archive data error, {}", e.getMessage(), e);
            }
        } else {
            try {
                archiveEsRepository.save(archiveData);
            } catch (Exception e) {
                log.error("[archive] save archive data error, {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public void batchInsertArchiveData(List<ArchiveEsData> archiveDataList) {
        try {
            for (ArchiveEsData archiveData : archiveDataList) {
                archiveData.setArchiveId(snowflakeUtil.createId());
            }
            archiveEsRepository.save(archiveDataList);
        } catch (Exception e) {
            log.error("[archive] batchSave archive data error, {}", e.getMessage(), e);
        }
    }

    @Override
    public void batchSaveUpdateArchiveData(List<ArchiveEsData> archiveDataList) {
        final String businessLine = archiveDataList.get(0).getBusinessLine();
        final String businessId = archiveDataList.get(0).getBusinessId();
        deleteArchiveData(businessLine, businessId);
        batchInsertArchiveData(archiveDataList);
    }

    @Override
    public void deleteArchiveData(Long esId) {
        try {
            archiveEsRepository.deleteById(esId);
        } catch (Exception e) {
            log.error("[archive] delete archive data error, {}", e.getMessage(), e);
        }
    }

    @Override
    public void deleteArchiveData(List<Long> esIds) {
        for (Long esId: esIds) {
            deleteArchiveData(esId);
        }
    }

    @Override
    public void deleteArchiveData(String businessLine, String businessId) {
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        if (StringUtil.isNotEmpty(businessLine)) {
            deleteQuery.must(QueryBuilders.matchQuery("businessLine", businessLine));
        }
        if (StringUtil.isNotEmpty(businessId)) {
            deleteQuery.must(QueryBuilders.matchQuery("businessId", businessId));
        }
        try {
            elasticsearchTemplate.deleteByCondition(deleteQuery, ArchiveEsData.class);
        } catch (Exception e) {
            log.error("[archive] delete archive by query error, businessLine:{}, businessId:{}",
                    businessLine, businessId);
        }
    }

    @Override
    public void refreshEsData() {
        MetaData metaData = this.elasticsearchIndex.getMetaData(ArchiveEsData.class);
        String[] indexNames = metaData.getSearchIndexNames();
        try {
            RefreshRequest refreshRequest = new RefreshRequest(indexNames);
            RefreshResponse refreshResponse = this.cusClient.indices().refresh(refreshRequest, RequestOptions.DEFAULT);
            if (refreshResponse.getStatus() != RestStatus.OK) {
                log.error("[archive] refresh es data error, status: {}", refreshResponse.getStatus());
            }
        } catch (IOException e) {
            log.error("[archive] refresh es data error, msg: {}", e.getMessage(), e);
        }
    }

    @SneakyThrows
    public PageResult<ArchiveEsData> pageArchiveData(ArchiveQueryDto queryDto) {
        if (elasticsearchIndex.exists(ArchiveEsData.class)) {
            QueryBuilder queryBuilder = buildQueryParams(queryDto);
            PageSortHighLight pageSortHighLight = new PageSortHighLight(queryDto.getPageNo(), queryDto.getPageSize());
            Sort.Order order;
            if (queryDto.getPrefix() != null) {
                order = new Sort.Order(queryDto.getPrefix(), "prefix");
            }
            //默认时间倒序
            else {
                order = new Sort.Order(SortOrder.DESC, "eventTime");
            }
            Sort sort = new Sort(order);
            pageSortHighLight.setSort(sort);
            try {
                PageList<ArchiveEsData> pageResult =  archiveEsRepository.search(queryBuilder, pageSortHighLight);
                return new PageResult<>(pageResult.getList(), pageResult.getCurrentPage(), pageResult.getPageSize(),
                        (int)pageResult.getTotalElements());
            } catch (Exception e) {
                log.error("[archive] query archive data error, {}", e.getMessage(), e);
            }
        } else {
            log.error("[archive] archive es index is not init");
        }
        return new PageResult<>(Lists.newArrayList(), queryDto);
    }


    private Map<AuthRoleScopeRestriction, EsScopeQuery> loadEsScopeQueryMap(){
        Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap = Maps.newHashMap();
        // 员工类型
        queryMap.put(AuthRoleScopeRestriction.SELECTED_EMP_TYPE,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empType.text"));
        // 合同公司
        queryMap.put(AuthRoleScopeRestriction.SELECTED_COMPANY,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("company"));
        // 查看本组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS,
                new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath"));
        // 查看指定组织
        queryMap.put(AuthRoleScopeRestriction.SELECTED_ORG,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 查看指定组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS,
                new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath"));
        // 查看所属兼岗组织
        queryMap.put(AuthRoleScopeRestriction.MY_CONCURRENT_ORG,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 查看所属兼岗组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.MY_CONCURRENT_ORG_AND_BELONGINGS,
                new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath"));
        // 查看本组织
        queryMap.put(AuthRoleScopeRestriction.MY_ORG,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 按HRBP 查看（选择所属组织的对应HRBP等于**的员工）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_HRBP,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 按部门负责人查看
        queryMap.put(AuthRoleScopeRestriction.SELECTED_LEADER,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 直接下级
        queryMap.put(AuthRoleScopeRestriction.DIRECT_SUBORDINATE,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        // 按HRBP 查看（含兼岗）（选择所属组织的对应HRBP等于**的员工含兼岗）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_HRBP_WITH_CONCURRENT,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        // 按部门负责人查看（含兼岗）（选择所属组织的对应负责人等于**的员工含兼岗）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_LEADER_WITH_CONCURRENT,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        // 直接下级含兼岗
        queryMap.put(AuthRoleScopeRestriction.DIRECT_SUBORDINATE_WITH_CONCURRENT,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        // 查看指定员工（选择指定员工，支持多选）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_EMP,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        // 指定工作地（参数：下拉多选工作地，按照员工身上的工作地圈人）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_WORKPLACE,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("workplace"));
        queryMap.put(AuthRoleScopeRestriction.NO_AUTH,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        queryMap.put(AuthRoleScopeRestriction.SPECIFIED_ORG_CODE_PREFIX,
                new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        // 文件归档类型以及文件名称
        return queryMap;
    }

    private QueryBuilder buildQueryParams(ArchiveQueryDto queryDto) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (queryDto.getFilters() != null) {
            for (FilterElement filter : queryDto.getFilters()) {
                if (filter.getProp() == null || filter.getValue() == null) {
                    continue;
                }
                // 组织
                if ("organize".equals(filter.getProp())) {
                    TermsQueryBuilder organizeQuery = QueryBuilders.termsQuery("organize", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(organizeQuery);
                }
                // 合同公司
                if ("company".equals(filter.getProp())) {
                    TermsQueryBuilder companyQuery = QueryBuilders.termsQuery("company", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(companyQuery);
                }
                // 工作地
                if ("workplace".equals(filter.getProp())) {
                    TermsQueryBuilder workplaceQuery = QueryBuilders.termsQuery("workplace", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(workplaceQuery);
                }
                // 用工类型
                if ("empType".equals(filter.getProp())) {
                    TermsQueryBuilder empTypeQuery = QueryBuilders.termsQuery("empType.value", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(empTypeQuery);
                }
                // 员工状态
                if ("empStatus".equals(filter.getProp())) {
                    TermsQueryBuilder empStatusQuery = QueryBuilders.termsQuery("empStatus.value", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(empStatusQuery);
                }

                // 岗位
                if ("post".equals(filter.getProp())) {
                    TermsQueryBuilder postQuery = QueryBuilders.termsQuery("post", String.valueOf(filter.getValue()).split(","));
                    queryBuilder.must(postQuery);
                }

                // 入职日期
                if ("hireDate".equals(filter.getProp())) {
                    RangeQueryBuilder entryQuery = QueryBuilders.rangeQuery("hireDate");
                    String[] split = String.valueOf(filter.getValue()).split(",");
                    if (split.length > 1) {
                        entryQuery.gte(split[0]);
                        entryQuery.lte(split[1]);
                        queryBuilder.must(entryQuery);
                    }
                }
                // 离职日期
                if ("leaveDate".equals(filter.getProp())) {
                    RangeQueryBuilder terminateQuery = QueryBuilders.rangeQuery("leaveDate");
                    String[] split = String.valueOf(filter.getValue()).split(",");
                    if (split.length > 1) {
                        terminateQuery.gte(split[0]);
                        terminateQuery.lte(split[1]);
                        queryBuilder.must(terminateQuery);
                    }
                }
                //redate
                if ("redate".equals(filter.getProp())) {
                    RangeQueryBuilder terminateQuery = QueryBuilders.rangeQuery("redate");
                    String[] split = String.valueOf(filter.getValue()).split(",");
                    if (split.length > 1) {
                        terminateQuery.gte(split[0]);
                        terminateQuery.lte(split[1]);
                        queryBuilder.must(terminateQuery);
                    }
                }

            }
        }
        // 文件名称
        if (StringUtil.isNotEmpty(queryDto.getFileName())) {
            WildcardQueryBuilder fileQuery = QueryBuilders.wildcardQuery("fileName",
                    String.format("*%s*", queryDto.getFileName()));
            queryBuilder.must(fileQuery);
        }
        // 工号姓名
        if (StringUtil.isNotEmpty(queryDto.getKeyword())) {
            WildcardQueryBuilder workNoQuery = QueryBuilders.wildcardQuery("workno",
                    String.format("*%s*", queryDto.getKeyword()));
            queryBuilder.should(workNoQuery);

            WildcardQueryBuilder sapQuery = QueryBuilders.wildcardQuery("sapid",
                    String.format("*%s*", queryDto.getKeyword()));
            queryBuilder.should(sapQuery);

            WildcardQueryBuilder nameQuery = QueryBuilders.wildcardQuery("name",
                    String.format("*%s*", queryDto.getKeyword()));
            queryBuilder.should(nameQuery);

            WildcardQueryBuilder enNameQuery = QueryBuilders.wildcardQuery("enName",
                    String.format("*%s*", queryDto.getKeyword()));
            queryBuilder.should(enNameQuery);

            queryBuilder.minimumShouldMatch(1);
        }
        //所属业务
        if (StringUtil.isNotEmpty(queryDto.getBusinessLine())) {
            TermsQueryBuilder lineQuery = QueryBuilders.termsQuery("businessLine", queryDto.getBusinessLine());
            queryBuilder.must(lineQuery);
        }
        //业务类型
        if (StringUtil.isNotEmpty(queryDto.getBusinessType())) {
            TermsQueryBuilder typeQuery = QueryBuilders.termsQuery("businessType", queryDto.getBusinessType());
            queryBuilder.must(typeQuery);
        }
        // 数据范围
        addArchiveScope(queryBuilder);
        return queryBuilder;
    }

    /**
     * 添加自定义归档文件数据范围
     */
    private void addArchiveScope(BoolQueryBuilder boolQueryBuilder) {
        UserInfo userInfo = UserContext.preCheckUser();
        Long userId = userInfo.getUserId();
        List<AuthRoleScopeFilterDetail> dataList = dataScopeService.getScopeList("archive.file", userId, "");
        List<AuthRoleScopeFilterDetail> archiveScopeList = dataList.stream().filter(it -> it.getRestriction() == AuthRoleScopeRestriction.SELECT_ARCHIVE_FILE).collect(Collectors.toList());
        log.info("[archive] add archive scope:{}", FastjsonUtil.toJson(archiveScopeList));
        if (CollectionUtils.isNotEmpty(archiveScopeList)) {
            BoolQueryBuilder scopeQueryBuilder = QueryBuilders.boolQuery();
            for (AuthRoleScopeFilterDetail detail : archiveScopeList) {
                String simpleValues = detail.getSimpleValues();
                ArchiveScopeVo archiveScopeVo = FastjsonUtil.toObject(simpleValues, ArchiveScopeVo.class);
                BoolQueryBuilder archiveQuery = QueryBuilders.boolQuery();
                if (StringUtil.isNotEmpty(archiveScopeVo.getBusinessLine())) {
                    archiveQuery.must(QueryBuilders.termsQuery("businessLine", archiveScopeVo.getBusinessLine()));
                }
                if (StringUtil.isNotEmpty(archiveScopeVo.getBusinessType())) {
                    archiveQuery.must(QueryBuilders.termsQuery("businessType", archiveScopeVo.getBusinessType()));
                }
                if (CollectionUtils.isNotEmpty(archiveScopeVo.getFileNames())) {
                    archiveQuery.must(QueryBuilders.termsQuery("fileName", archiveScopeVo.getFileNames()));
                }
                scopeQueryBuilder.should(archiveQuery);
            }
            scopeQueryBuilder.minimumShouldMatch(1);
            boolQueryBuilder.must(scopeQueryBuilder);
        }
        dataList.removeAll(archiveScopeList);
        if (CollectionUtils.isNotEmpty(dataList)) {
            log.info("[archive] add standard data scope: {}", FastjsonUtil.toJson(dataList));
            Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap = loadEsScopeQueryMap();
            dataScopeService.getScopeQuery(dataList, boolQueryBuilder, queryMap);
        }
    }

    @Override
    public List<ArchiveEsData> getByIds(List<String> ids) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(ids)) {
            TermsQueryBuilder id = QueryBuilders.termsQuery("archiveId", ids);
            queryBuilder.must(id);
        }
        PageSortHighLight pageSortHighLight = new PageSortHighLight(1, ids.size());
        try {
            PageList<ArchiveEsData> pageResult =  archiveEsRepository.search(queryBuilder, pageSortHighLight);
            return pageResult.getList();
        } catch (Exception e) {
            log.error("[archive] query archive data error, {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }
}
