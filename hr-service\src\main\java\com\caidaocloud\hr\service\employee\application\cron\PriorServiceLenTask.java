package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.hr.service.employee.application.event.dto.PriorCalEventDto;
import com.caidaocloud.hr.service.employee.domain.workExperience.service.WorkOverviewDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "caidao", name = "priorCal", havingValue = "priorServiceLen")
public class PriorServiceLenTask {
    @Resource
    private WorkOverviewDomainService workOverviewDomainService;
    private final String EXCHANGE = "caidaocloud.hr.priorCal.exchange";
    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private MqMessageProducer mqMessageProducer;

    @XxlJob("publishDynamicWorkAge")
    public ReturnT publishDynamicWorkAge() {
        if (null == tenantList || tenantList.isEmpty()) {
            log.info("publishDynamicWorkAge: tenantList is empty, execution end");
            return ReturnT.SUCCESS;
        }
        for (String tenantId : tenantList) {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                handle(1);
            } catch (Exception e) {
                log.error("publishDynamicWorkAge occur error", e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
        return ReturnT.SUCCESS;
    }

    private void handle(int pageNo) {
        List<Map<String, String>> items = DataQuery.identifier("entity.hr.EmpPrivateInfo").limit(5000, pageNo).filterProperties(DataFilter.eq("deleted", Boolean.FALSE.toString()), Lists.newArrayList("empId"), -1).getItems();
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<String> empIdList = items.stream().map(e -> e.getOrDefault("empId", "")).filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(empIdList)) {
            handle(++pageNo);
            return;
        }
        val workOverviewList = workOverviewDomainService.listByEmpIds(empIdList);
        if (!CollectionUtils.isEmpty(workOverviewList)) {
            var resultMap = workOverviewList.stream().collect(Collectors.toMap(e -> e.getEmpId(), e -> e.getWorkAge()));
            val event = new PriorCalEventDto(SecurityUserUtil.getSecurityUserInfo().getTenantId(), resultMap);
            RabbitBaseMessage rabbitBaseMessage = new RabbitBaseMessage();
            rabbitBaseMessage.setExchange(EXCHANGE);
            val body = FastjsonUtil.toJson(event);
            rabbitBaseMessage.setBody(body);
            mqMessageProducer.publish(rabbitBaseMessage);
            log.info("publish dynamic work age event, body={}", body);
        }
        handle(++pageNo);
    }
}