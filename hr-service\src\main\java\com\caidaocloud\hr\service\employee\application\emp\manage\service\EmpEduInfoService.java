package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpEduInfoDomainService;
import com.caidaocloud.hr.service.dto.EmpEduInfoDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpEduInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Service
public class EmpEduInfoService {
    @Resource
    private EmpEduInfoDomainService empEduInfoDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public void save(EmpEduInfoDto dto) {
        EmpEduInfoDo data = ObjectConverter.convert(dto, EmpEduInfoDo.class);
        doConvert(dto, data);
        empEduInfoDomainService.save(data);
    }

    private void doConvert(EmpEduInfoDto source, EmpEduInfoDo target) {
        if (StringUtils.isNotEmpty(source.getDegree())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getDegree());
            target.setDegree(dictSimple);
        }

        if (StringUtils.isNotEmpty(source.getBackground())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getBackground());
            target.setBackground(dictSimple);
        }
    }

    public void update(EmpEduInfoDto dto) {
        EmpEduInfoDo data = ObjectConverter.convert(dto, EmpEduInfoDo.class);
        doConvert(dto, data);

        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empEduInfoDomainService.update(data);
    }

    public EmpEduInfoDo getEmpEduInfo(String empId) {
        return empEduInfoDomainService.getEducationInfo(empId);
    }

    public EmpEduInfoVo getDetail(String empId) {
        EmpEduInfoDo data = getEmpEduInfo(empId);
        if(null == data){
            return new EmpEduInfoVo();
        }

        EmpEduInfoVo vo = ObjectConverter.convert(data, EmpEduInfoVo.class);
        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
        vo.setExt(ext);
        return vo;
    }
}
