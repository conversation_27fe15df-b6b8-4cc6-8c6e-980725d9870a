package com.caidaocloud.hr.service.handler;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hr.service.dict.DictService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 26/6/2024 3:15 下午
 */
@Component
@AllArgsConstructor
public class DictResolveHandler implements ITypeResolveHandler {

    private DictService dictService;

    @Override
    public List<String> getTypes(String simpleValue) {
        return Optional.ofNullable(simpleValue).map(it -> {
            List<KeyValue> keyValues = dictService.getEnableDictList(simpleValue, "Employee");
            return keyValues.stream().map(KeyValue::getText).collect(Collectors.toList());
        }).orElse(Lists.newArrayList());

    }
}
