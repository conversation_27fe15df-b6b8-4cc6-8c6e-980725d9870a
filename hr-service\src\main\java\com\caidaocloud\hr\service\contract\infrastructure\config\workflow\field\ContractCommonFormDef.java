package com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field;

import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;
import com.googlecode.totallylazy.Lists;

import java.util.List;

/**
 * 合同签订字段定义
 *
 * <AUTHOR>
 */
public class ContractCommonFormDef {
    /**
     * 新签
     *
     * @return
     */
    public static List<WfMetaFunFormFieldDto> newSigningFormList() {
        return Lists.list(
                // 基本信息
//                new WfMetaFunFormFieldDto("name", "姓名"),
//                new WfMetaFunFormFieldDto("hireDate", "入职日期"),
//                new WfMetaFunFormFieldDto("empStatus", "员工状态"),
//                new WfMetaFunFormFieldDto("organizeTxt", "任职组织"),
//                new WfMetaFunFormFieldDto("postTxt", "岗位"),
//                new WfMetaFunFormFieldDto("jobTxt", "职务"),
//                new WfMetaFunFormFieldDto("workplaceTxt", "工作地点"),
//                new WfMetaFunFormFieldDto("companyTxt", "合同公司"),

                new WfMetaFunFormFieldDto("signType", "签订类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("companyTxt", "合同公司", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("registerAddress", "注册地址", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractType", "合同类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("signDate", "合同签订日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("contractPeriod", "合同期限", WfFieldDataTypeEnum.Number),
                new WfMetaFunFormFieldDto("startDate", "合同开始日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("endDate", "合同结束日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("probation", "试用期期限", WfFieldDataTypeEnum.Number),
                new WfMetaFunFormFieldDto("probationPeriodEndDate", "试用期截止日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("remark", "备注", WfFieldDataTypeEnum.Text)

                // 签订信息
//                new WfMetaFunFormFieldDto("signInfo", "签订信息")

        );
    }

    /**
     * 续签、改签
     *
     * @return
     */
    public static List<WfMetaFunFormFieldDto> formList() {
        return Lists.list(
                // 基本信息
//                new WfMetaFunFormFieldDto("name", "姓名"),
//                new WfMetaFunFormFieldDto("hireDate", "入职日期"),
//                new WfMetaFunFormFieldDto("empStatus", "员工状态"),
//                new WfMetaFunFormFieldDto("organizeTxt", "任职组织"),
//                new WfMetaFunFormFieldDto("postTxt", "岗位"),
//                new WfMetaFunFormFieldDto("empType", "用工类型"),

                // 上份合同信息
                new WfMetaFunFormFieldDto("lastContractNo", "上一份合同编号", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastCompanyTxt", "上一份合同公司", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastRegisterAddress", "上一份合同注册地址", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastContractTypeSetTxt", "上一份合同类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastContractPeriod", "上一份合同期限", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastSignDate", "上一份合同签订日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("lastStartDate", "上一份合同开始日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("lastEndDate", "上一份合同结束日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("lastSignTime", "上一份合同签订次数", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastProbationPeriod", "上一份合同试用期（月）", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("lastProbationPeriodEndDate", "上一份合同试用期截止日", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("lastWorkHour", "上一份合同工时制", WfFieldDataTypeEnum.Text),

                new WfMetaFunFormFieldDto("signType", "签订类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("companyTxt", "合同公司", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("registerAddress", "注册地址", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractType", "合同类型", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("contractPeriod", "合同期限", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("signDate", "合同签订日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("startDate", "合同开始日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("endDate", "合同结束日期", WfFieldDataTypeEnum.Timestamp),
                new WfMetaFunFormFieldDto("workplaceTxt", "工作地点", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("workHour", "工时制", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("remark", "备注", WfFieldDataTypeEnum.Text),
                new WfMetaFunFormFieldDto("attachFile", "附件", WfFieldDataTypeEnum.Text)

                // 签订信息
//                new WfMetaFunFormFieldDto("signInfo", "签订信息")

        );
    }
}
