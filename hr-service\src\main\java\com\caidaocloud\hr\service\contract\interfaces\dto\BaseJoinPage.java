package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.EmployeePicker;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.em.FilterElementDataTypeEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@Data
@Slf4j
public class BaseJoinPage extends BasePage {

    /**
     * @param filter
     * @param subProp   关联表筛选字段
     * @param subFilter 关联表筛选filter
     * @return
     */
    public FilterFunction[] doJoinDataFilter(FilterFunction filter, Set<String> subProp, FilterFunction subFilter) {
        List<FilterElement> filters = this.getFilters();
        if (null == filters || filters.isEmpty()) {
            return new FilterFunction[]{filter, subFilter};
        }

        List<FilterElement> filterElementList = new ArrayList<>();
        for (FilterElement element : filters) {
            Object v = element.getValue();
            if (v instanceof Map) {
                for (Object entry : ((Map) element.getValue()).entrySet()) {
                    Object prop = ((Map.Entry) entry).getKey();
                    Object value = ((Map.Entry) entry).getValue();

                    FilterElement filterValueElement = new FilterElement();
                    filterValueElement.setOp(element.getOp());
                    filterValueElement.setProp(String.valueOf(prop));
                    filterValueElement.setValue(value);
                    filterElementList.add(filterValueElement);
                }
                continue;
            }
            filterElementList.add(element);
        }

        for (FilterElement filterElement : filterElementList) {
            String prop = filterElement.getProp();
            Object value = filterElement.getValue();
            FilterElementDataTypeEnum dataType = filterElement.getDataType();
            // 特殊业务组件处理
            if (FilterElementDataTypeEnum.Emp.equals(dataType)) {
                // 员工筛选
                List<EmployeePicker> empList = FastjsonUtil.toArrayList(FastjsonUtil.toJson(value), EmployeePicker.class);
                if (CollectionUtils.isEmpty(empList)) {
                    continue;
                }
                prop = String.format("%s%s%s", prop, "$", "empId");
                value = empList.stream().map(EmployeePicker::getEmpId).distinct().collect(Collectors.toList());
            } else if (FilterElementDataTypeEnum.Dict.equals(dataType)) {
                // 字典筛选
                prop = String.format("%s%s%s", prop, "$", "dict$value");
            }
            if (subProp.contains(filterElement.getProp())) {
                subFilter = doFilter(subFilter, filterElement, prop, value);
            } else {
                filter = doFilter(filter, filterElement, prop, value);
            }
        }

        return new FilterFunction[]{filter, subFilter};
    }

    private FilterFunction doFilter(FilterFunction filter, FilterElement filterElement, String prop, Object value) {
        switch (filterElement.getOp()) {
            case eq:
                filter = filter.andEq(prop, ObjectUtil.convertValueToString(value));
                break;
            case ne:
                filter = filter.andNe(prop, ObjectUtil.convertValueToString(value));
                break;
            case in:
                List<String> inValues = ObjectUtil.convertValueToList(value);
                if (CollectionUtils.isEmpty(inValues)) {
                    break;
                }
                filter = filter.andIn(prop, inValues);
                break;
            case bt:
                List<String> btValues = ObjectUtil.convertValueToList(value);
                if (CollectionUtils.isEmpty(btValues)) {
                    break;
                }
                filter = filter.andGe(prop, Collections.min(btValues)).andLt(prop, Collections.max(btValues));
                break;
            case ge:
                filter = filter.andGe(prop, ObjectUtil.convertValueToString(value));
                break;
            case gt:
                filter = filter.andGt(prop, ObjectUtil.convertValueToString(value));
                break;
            case le:
                filter = filter.andLe(prop, ObjectUtil.convertValueToString(value));
                break;
            case lt:
                filter = filter.andLt(prop, ObjectUtil.convertValueToString(value));
                break;
            case lk:
                filter = filter.andRegex(prop, ObjectUtil.convertValueToString(value));
                break;
            default:
                log.error("Unsupported query operator,queryFilter={}", FastjsonUtil.toJson(filterElement));
                break;
        }
        return filter;
    }
}
