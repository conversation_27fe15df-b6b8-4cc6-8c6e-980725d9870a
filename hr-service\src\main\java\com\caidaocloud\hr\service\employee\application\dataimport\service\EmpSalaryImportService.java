package com.caidaocloud.hr.service.employee.application.dataimport.service;

import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryChangeDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryImportDo;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.EmpSalaryImportPo;
import com.caidaocloud.hr.service.enums.DataSourceEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyEnumDefDto;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmpSalaryImportService extends DataImportService<EmpSalaryImportDo, EmpSalaryImportPo> {
    @Resource
    private MetadataService metadataService;
    @Resource
    private EmpSalaryImportDo empSalaryImportDo;
    @Resource
    private EmpSalaryChangeDo empSalaryChangeDo;
    @Resource
    private EmpBasicInfoService empBasicInfoService;

    private static Map<String, Map<String, PropertyEnumDefDto>> staticPropMap = new HashMap<>();

    private static String businessCode = "EMP_SALARY_CHANGE_IMPORT";

    private static final String SalaryTypeProp = "salaryType", ChangeTypeProp = "changeType";
//    private static final String ChangeTypeProp = "changeType";

    private static final String IDENTIFIER = "entity.hr.EmpSalaryChange";


    @Override
    public String getExcelCode() {
        return businessCode;
    }

    @Override
    public List<EmpSalaryImportDo> getPoDataFromExcel(InputStream inputStream) {
        return empSalaryImportDo.getEmpSalaryImportDoFromExcel(inputStream);
    }

    @Override
    public List<EmpSalaryImportDo> batchInsertUpdateData(List<EmpSalaryImportDo> conRecordList) {
        if (CollectionUtils.isEmpty(conRecordList)) {
            return new ArrayList<>();
        }
        List<EmpSalaryImportDo> errorList = new ArrayList<>();
        List<String> workNos = conRecordList.stream().map(EmpSalaryImportDo::getWorkno).collect(Collectors.toList());

        List<EmpBasicInfoDo> basicInfoList = empBasicInfoService.getEmpBasicInfoListByWorkNo(workNos, System.currentTimeMillis());

        Map<String, List<EmpBasicInfoDo>> empMap = basicInfoList.stream().collect(Collectors.groupingBy(EmpBasicInfoDo::getWorkno));

        for (EmpSalaryImportDo importDo : conRecordList) {
            List<EmpBasicInfoDo> basicInfoDos = empMap.get(importDo.getWorkno());
            if (CollectionUtils.isEmpty(basicInfoDos)) {
                setEmptyTips(importDo, "工号：【" + importDo.getWorkno() + "】不存在，请确认");
                errorList.add(importDo);
            } else if (basicInfoDos.size() == 1) {
                if (!basicInfoDos.get(0).getName().equals(importDo.getName())) {
                    setEmptyTips(importDo, "工号与姓名不匹配");
                    errorList.add(importDo);
                }
                arrangeList(basicInfoDos.get(0).getBid(), importDo, errorList);
            } else {
                setEmptyTips(importDo, "工号：【" + importDo.getWorkno() + "】存在复数员工，请确认工号唯一");
                errorList.add(importDo);
            }
        }
        return errorList;
    }

    private void arrangeList(String empId, EmpSalaryImportDo importDo, List<EmpSalaryImportDo> errorList) {
        EmpSalaryChangeDo data = new EmpSalaryChangeDo();
        data.setEmpId(empId);
        EmpSalaryChangeDo oldData = empSalaryChangeDo.getMaxEffectiveByEmpId(data);
        importDo.setEmpId(empId);
        importDo.setDataSource(DataSourceEnum.EMP.name());
        if (oldData.getEmpId() != null) {
            updateImportList(importDo, errorList, oldData);
        } else {
            insertImportList(importDo, errorList);
        }
    }

    @Override
    public boolean checkEmptyProp(EmpSalaryImportDo data) {
        if (StringUtils.isEmpty(data.getEffectiveDateTxt())) {
            setEmptyTips(data, "生效日期不能为空");
        }
        if (StringUtils.isEmpty(data.getWorkno())) {
            setEmptyTips(data, "工号不能为空");
        }
//        if (StringUtils.isEmpty(data.getSalaryTypeTxt())) {
//            setEmptyTips(data, "薪资类型不能为空");
//        }
        if (StringUtils.isEmpty(data.getSalary())) {
            setEmptyTips(data, "薪资不能为空");
        }
        if (StringUtils.isEmpty(data.getName())) {
            setEmptyTips(data, "姓名不能为空");
        }
        if (!checkSalaryPattern(data.getSalary())) {
            setEmptyTips(data, "金额格式不正确，请输入最多两位小数的数值");
        }
        return true;
    }

    /**
     * 金额格式校验
     *
     * @param str
     * @return
     */
    public static boolean checkSalaryPattern(String str) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        java.util.regex.Matcher match = pattern.matcher(str);
        return match.matches();
    }

    @Override
    public boolean checkEmptyMark(EmpSalaryImportDo data) {
        return data.isCheckEmpty();
    }

    @Override
    public boolean installProp(EmpSalaryImportDo data) {
//        String tenantId = getTenantId();
//        Map<String, PropertyEnumDefDto> salaryTypeMap = staticPropMap.get(getMapKeyWithTenantId(SalaryTypeProp, tenantId));
//        installSalaryTypeProp(data, salaryTypeMap);
        installDate(data);

        //产品暂时不用异动类型的导入
//        Map<String, PropertyEnumDefDto> changeTypeMap = staticPropMap.get(getMapKeyWithTenantId(ChangeTypeProp, tenantId));
//        installChangeTypeProp(data, changeTypeMap);
        if (data.isCheckEmpty()) {
            return false;
        }
        return true;
    }

    @Override
    public void initProperty() {
        String tenantId = UserContext.getTenantId();
        staticPropMap.put(getMapKeyWithTenantId(SalaryTypeProp, tenantId), getEmpMetadataPropertyMap(SalaryTypeProp));
        //目前产品认为导入不需要异动类型等枚举字段，目前注释 如之后使用放开
//        staticPropMap.put(getMapKeyWithTenantId(ChangeTypeProp, tenantId), getEmpMetadataPropertyMap(ChangeTypeProp));
    }

    protected String getMapKeyWithTenantId(String key, String tenantId) {
        return String.format("%s_%s", key, tenantId);
    }

    private Map<String, PropertyEnumDefDto> getEmpMetadataPropertyMap(String prop) {
        Map<String, PropertyEnumDefDto> map = new HashMap<>();
        MetadataPropertyVo vo = metadataService.getPropertyDef(IDENTIFIER, prop);
        for (PropertyEnumDefDto dto : vo.getEnumDef()) {
            map.put(dto.getDisplay(), dto);
        }
        return map;
    }

    private void setEmptyTips(EmpSalaryImportDo con, String tip) {
        con.setCheckEmpty(true);
        if (null == con.getCheckEmptyTips()) {
            con.setCheckEmptyTips(tip);
        } else {
            con.setCheckEmptyTips(con.getCheckEmptyTips() + "，" + tip);
        }
    }

//    private void installSalaryTypeProp(EmpSalaryImportDo con, Map<String, PropertyEnumDefDto> salaryTypeMap) {
//        if (null != salaryTypeMap && null != salaryTypeMap.get(con.getSalaryTypeTxt())) {
//            PropertyEnumDefDto status = salaryTypeMap.get(con.getSalaryTypeTxt());
//            EnumSimple enumSimple = new EnumSimple();
//            enumSimple.setText(status.getDisplay());
//            enumSimple.setValue(status.getValue());
//            con.setSalaryType(enumSimple);
//        } else {
//            setEmptyTips(con, "薪资类型不存在");
//        }
//    }

    private void installChangeTypeProp(EmpSalaryImportDo con, Map<String, PropertyEnumDefDto> changeTypeMap) {
        if (null != changeTypeMap && null != changeTypeMap.get(con.getChangeTypeTxt())) {
            con.setChangeType(con.getChangeType());
        } else {
            setEmptyTips(con, "异动类型不存在");
        }
    }

    private void insertImportList(EmpSalaryImportDo importDo, List<EmpSalaryImportDo> errorList) {
        try {
            EmpSalaryChangeDo data = ObjectConverter.convert(importDo, EmpSalaryChangeDo.class);
            empSalaryChangeDo.insertImportData(data);
        } catch (Exception e) {
            log.error("员工薪资导入异常--> error:{}", e.getMessage());
            setEmptyTips(importDo, "导入员工薪资数据异常，请联系管理员");
            errorList.add(importDo);
        }
    }

    private void installDate(EmpSalaryImportDo con) {
        DateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        try {
            con.setEffectiveDate(format.parse(con.getEffectiveDateTxt()).getTime());
        } catch (ParseException e) {
            setEmptyTips(con, "日期转换异常");
            log.error("import conRecord Data parse error:{}", e.getMessage());
        }
    }

    private void updateImportList(EmpSalaryImportDo importDo, List<EmpSalaryImportDo> errorList, EmpSalaryChangeDo oldData) {
        try {
            convertData(importDo, oldData);
            empSalaryChangeDo.updateByBid(oldData);
        } catch (Exception e) {
            log.error("薪资变动记录导入异常--> error:{}", e.getMessage());
            setEmptyTips(importDo, "薪资变动记录导入异常，请联系管理员");
            errorList.add(importDo);
        }
    }

    private void convertData(EmpSalaryImportDo importDo, EmpSalaryChangeDo oldData) {
        oldData.setSalary(importDo.getSalary());
        oldData.setEffectiveDate(importDo.getEffectiveDate());
//        oldData.setSalaryType(importDo.getSalaryType());
    }
}
