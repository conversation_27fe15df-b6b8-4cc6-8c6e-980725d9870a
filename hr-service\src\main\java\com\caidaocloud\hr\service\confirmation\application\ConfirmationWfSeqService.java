package com.caidaocloud.hr.service.confirmation.application;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
@Service
public class ConfirmationWfSeqService {
	@Autowired
	private ConfirmationService confirmationService;
	@Resource
	private FormFeignClient formFeignClient;


	public String fetchSeqValue(String businessId, String code) {
		DataSimple dataSimple = confirmationService.getConfirmationDataByBusinessId(businessId);
		String paramCode =StringUtils.substringAfter(StringUtils.substringAfter(code, "_"), "_");
		NestPropertyValue properties = dataSimple.getProperties();
		if (paramCode.startsWith("form")) {
			SimplePropertyValue formId = (SimplePropertyValue) properties.get("formDefId");
			SimplePropertyValue formValueId = (SimplePropertyValue) properties.get("formValueId");
			if (formValueId == null) {
				return "";
			}
			String property = paramCode.split("\\$")[1];
			Object value = formFeignClient.getFormDataMap(formId.getValue(), formValueId.getValue())
					.getData().getPropertiesMap().get(property);
			return value == null ? "" : String.valueOf(value);
		}else {
			return getDataSimpleValue(properties, paramCode);
		}
	}

	private String getDataSimpleValue(NestPropertyValue properties, String property){
		PropertyValue propertyValue = properties.get(property);
		if(propertyValue instanceof JobGradeRange){
			JobGradeRange jobGrade = (JobGradeRange) propertyValue;
			return jobGrade.getStartGrade();
		}
		else if (propertyValue instanceof EnumSimple) {
			return ((EnumSimple) propertyValue).getValue();
		}else if (propertyValue instanceof DictSimple) {
			return ((DictSimple) propertyValue).getValue();
		}
		SimplePropertyValue spv = (SimplePropertyValue) properties.get(property);
		String value = null == spv ? "" : spv.getValue();
		if(property.endsWith("Change") && StringUtil.isEmpty(value)){
			return "false";
		}
		return value;
	}
}
