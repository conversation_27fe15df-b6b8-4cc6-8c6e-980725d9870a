package com.caidaocloud.hr.service.employee.application.dataimport;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.organization.application.job.service.JobService;
import com.caidaocloud.hr.service.organization.application.jobgrade.service.JobGradeService;
import com.caidaocloud.hr.service.organization.application.post.service.BenchmarkPositionService;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.BenchmarkPositionQueryDto;
import com.caidaocloud.hr.service.util.BeanUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class DataImportService<T, P> {

    private static String ERROR_CACHE_KEY = "%s_ERROR";
    private static String PERCENTAGE_CACHE_KEY = "%s_PERCENTAGE";

    @Resource
    private CacheService cacheService;
    @Resource
    private WorkplaceService workplaceService;
    @Resource
    private JobService jobService;
    @Resource
    private BenchmarkPositionService benchmarkPositionService;
    @Resource
    private JobGradeService jobGradeService;

    public abstract String getExcelCode();

//    public abstract List<Map<String, String >> getErrorDataExcelColList();


//    public abstract void checkAndSaveData(InputStream inputStream, String processId, String tenantId);

    //使用获取到的数据初始化一些信息
    public List<T> initDataWithExcel(List<T> list) {
        return new ArrayList<>();
    }

    /**
     * 准备操作导入数据
     *
     * @param inputStream
     * @param processId
     * @param tenantId
     * @param userId
     */
    public void prepareOperateDataFromInputStream(InputStream inputStream, String processId, String tenantId, Long userId) {
        List<T> list = getPoDataFromExcel(inputStream);
        SpringUtil.getBean(this.getClass()).operateDataFromInputStream(list, processId, tenantId, userId);
    }

    @Async("taskExecutor")
    public void operateDataFromInputStream(List<T> list, String processId, String tenantId, Long userId) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setUserId(userId);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        user.setStaffId(userId);
        user.setUserid(userId.intValue());
        user.setStaffId(userId);
        UserContext.setCurrentUser(user);
        int total = 0;
        int completed = 0;
        int successCount = 0;
        int failCount = 0;
        int exceptionCount = 0;
        List<T> errorProList = new ArrayList<>();
        List<T> passDataList = new ArrayList<>();
        try {
            putImportExcelProcessVo(processId, total, completed, successCount, failCount);
            total = list.size();
            List<T> repeatList = initDataWithExcel(list);
            errorProList.addAll(repeatList);
            list.removeAll(repeatList);
            if (null == list || list.size() == 0) {
                putImportExcelProcessVo(processId, total, completed, successCount, failCount);
            }
            int i = 0;
            for (T data : list) {
                i++;
                completed++;
//                检查空字段
//                checkEmptyProp(data);
                if (!checkEmptyProp(data)) {
                    errorProList.add(data);
                    failCount++;
                    continue;
                }
//                补充需要的字段，如id，枚举类型的数据，如果成功了放到通过的list，失败了放到失败的列表
                if (installProp(data)) {
                    passDataList.add(data);
                    successCount++;
                } else {
                    errorProList.add(data);
                    failCount++;
                    continue;
                }
                if (passDataList.size() > 0 && passDataList.size() % 300 == 0) {
//                batchInsertUpdate basicInfo, privateInfo and workInfo
                    errorProList.addAll(batchInsertUpdateData(passDataList));
                    passDataList.clear();
                }
                if (i > 0 && i % 50 == 0) {
//                刷新缓存当前解析数据信息
                    putImportExcelProcessVo(processId, total, completed, completed - errorProList.size(), errorProList.size());
                }
            }
            errorProList.addAll(batchInsertUpdateData(passDataList));
            dealErrorListBeforeWrite(errorProList);
        } catch (Exception e) {
            log.error("导入数据异常，-->error{}", e.getMessage(), e);
            ++exceptionCount;
        } finally {
//          结束后，刷新缓存当前解析数据信息,并把错误信息放到
            cacheErrorInfo(processId, errorProList);
            completed = total;
            failCount = errorProList.size() + exceptionCount;
            putImportExcelProcessVo(processId, total, completed, completed - failCount, failCount);
            operateAfterImport();
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    public void dealErrorListBeforeWrite(List<T> list) {
    }

    /**
     * 从excel文件中读取原始数据
     *
     * @param inputStream
     * @return
     */
    public abstract List<T> getPoDataFromExcel(InputStream inputStream);

    /**
     * 批量插入或更新数据，返回错误数据，正确数据入库
     *
     * @param list
     * @return
     */
    public abstract List<T> batchInsertUpdateData(List<T> list);

    /**
     * 检查不能为空的字段
     *
     * @param data
     * @return
     */
    public abstract boolean checkEmptyProp(T data);

    /**
     * 检查字段不能匹配的处理，返回不能匹配的数据
     *
     * @param data
     * @return
     */
    public abstract boolean checkEmptyMark(T data);

    /**
     * 组装属性字段，如id，枚举类型数据
     *
     * @param data
     * @return
     */
    public abstract boolean installProp(T data);

    /**
     * 可覆盖初始化需要的参数
     */
    public void initProperty() {
    }

    public void operateAfterImport() {
    }

    protected String getTenantId() {
        return UserContext.getTenantId();
    }

    protected long getUserId() {
        return UserContext.preCheckUser().getUserId();
    }

    public boolean matchExcelCode(String excelCode) {
        return this.getExcelCode().equals(excelCode);
    }

    public ImportExcelVo importDataWithExcel(ImportExcelDto dto) {

        String processId = UUID.randomUUID().toString().replaceAll("-", "");
        ImportExcelVo vo = new ImportExcelVo();
        vo.setProcessUUid(processId);
        initProperty();
        return vo;
    }

    protected String getErrorCacheKey(String processId) {
        return String.format(ERROR_CACHE_KEY, processId);
    }

    protected String getPercentageCacheKey(String processId) {
        return String.format(PERCENTAGE_CACHE_KEY, processId);
    }

    protected void putImportExcelProcessVo(String processId, int total, int completed, int successCount, int failCount) {
        int notDone = total - completed;
        if (total == 0) {
            total = 1;
        }
        ImportExcelProcessVo vo = new ImportExcelProcessVo(processId, total, completed, notDone, successCount, failCount);
        if (total == 0) {
            vo.setTotal(0);
        }
        boolean flag = cacheService.cacheValue(getPercentageCacheKey(processId), FastjsonUtil.toJson(vo), 3000);
    }

    protected void cacheErrorInfo(String processId, List<?> errorList) {
        cacheService.cacheValue(getErrorCacheKey(processId), FastjsonUtil.toJson(errorList), 1800);
    }

    public ImportExcelProcessVo getImportDataPercentage(String processId) {
        ImportExcelProcessVo vo = FastjsonUtil.toObject(cacheService.getValue(getPercentageCacheKey(processId)), ImportExcelProcessVo.class);
        return vo;
    }

    protected Class<T> getDataClazz() {
        ParameterizedType parameterizedType = (ParameterizedType) getClass().getGenericSuperclass();
        return (Class<T>) parameterizedType.getActualTypeArguments()[1];
    }

    public void downloadErrorImportData(HttpServletResponse response, String processId) {
        Class<T> clazz = getDataClazz();
        List<T> list = FastjsonUtil.toList(cacheService.getValue(getErrorCacheKey(processId)), clazz);
        ExcelUtils.downloadDataMapExcel(installExportEntity(clazz), convertObjectToMap(list), "导入失败员工信息", response);
    }

    private List<ExcelExportEntity> installExportEntity(Class clazz) {
        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field f : fields) {
            Excel annotation = f.getAnnotation(Excel.class);
            if (null == annotation) {
                continue;
            }
            String name = annotation.name();
            String property = f.getName();
            String order = annotation.orderNum();
            double width = annotation.width();
            if (width == 0) {
                width = 13;
            }
            ExcelExportEntity entity = new ExcelExportEntity(name, property);
            entity.setWidth(width);
            if (null != order) {
                entity.setOrderNum(Integer.valueOf(order));
            }
            colList.add(entity);
        }
        return colList;
    }

    private List<Map<String, Object>> convertObjectToMap(List<?> objs) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            for (Object obj : objs) {
                list.add(BeanUtil.bean2map(obj));
            }
            return list;
        } catch (Exception e) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30036));
        }
    }

    protected String getMapKeyWithTenantId(String key, String tenantId) {
        return String.format("%s_%s", key, tenantId);
    }

    protected Map<String, WorkplaceDo> getWorkplaceMap() {
        BasePageQueryDto dto = new BasePageQueryDto();
        dto.setDateTime(System.currentTimeMillis());
        Map<String, WorkplaceDo> map = new HashMap<>();
        List<WorkplaceDo> list = workplaceService.selectList(StatusEnum.ENABLED);
        for (WorkplaceDo workplaceDo : list) {
            map.put(workplaceDo.getName(), workplaceDo);
        }
        return map;
    }

    protected Map<String, JobDo> getJobMap(){
        List<JobDo> list = jobService.selectList(null, StatusEnum.ENABLED.getIndex());
        return list.stream().collect(Collectors.toMap(JobDo::getCode, obj -> obj, (a, b) -> a));
    }

    protected Map<String, BenchmarkPositionDo> getBenchPostMap(){
        List<BenchmarkPositionDo> list = benchmarkPositionService.selectList(new BenchmarkPositionQueryDto());
        return list.stream().collect(Collectors.toMap(BenchmarkPositionDo::getCode, obj -> obj, (a, b) -> a));
    }

    protected Map<String, JobGradeDo> getJobGradeMap() {
        List<JobGradeDo> list = jobGradeService.getAllJobGrade();
        return list.stream().collect(Collectors.toMap(JobGradeDo::getJobGradeCode, obj -> obj, (a, b) -> a));
    }
}
