package com.caidaocloud.hr.service.archive.provider.feign;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.provider.fallback.OnBoardingProviderFallback;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 5:38 下午
 */
@FeignClient(
        name = "caidaocloud-on-boarding-service",
        configuration = FeignConfiguration.class,
        fallback = OnBoardingProviderFallback.class,
        contextId = "archive-provider-onboarding"
)
public interface OnBoardingProviderFeign {

    /**
     * 查询-候选人任职信息
     * @param empId
     */
    @GetMapping("/api/onboarding/employee/work/v1/info")
    Result<Object> getWorkInfoByEmpId(@RequestParam("empId") String empId);

    /**
     * 根据业务ID获取归档文件
     */
    @PostMapping("/api/onboarding/v1/archive/data")
    Result<List<ArchiveData>> fetchArchive(@RequestParam String businessLine, @RequestParam String businessId);

    /**
     * 获取分页归档文件
     */
    @PostMapping("/api/onboarding/v1/archive/page")
    Result<List<ArchiveData>> fetchPageArchive(@RequestBody BasePage page);

    /**
     * 消息获取归档文件
     */
    @PostMapping("/api/onboarding/v1/archive/all")
    Result<Boolean> fetchAllArchive();
}
