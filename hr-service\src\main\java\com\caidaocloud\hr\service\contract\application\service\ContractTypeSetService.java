package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.enums.*;
import com.caidaocloud.hr.service.contract.application.event.publish.ContractTypeSetPublish;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractConfigDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.service.ContractSetConditionDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractTypeSetDomainService;
import com.caidaocloud.hr.service.contract.domain.service.EmpContractTypeSetRelDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.*;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.*;
import com.caidaocloud.hr.service.contract.interfaces.vo.*;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.ConditionNameMapping;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.*;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.DUPLICATE_CONTRACT;
import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.MISSING_REQUIRED_PARAMETER;
import static com.caidaocloud.hr.service.contract.application.enums.ConditionOperator.enableOpts;
import static com.caidaocloud.hr.service.contract.application.enums.ConditionOperator.noContainChild;

@Service
public class ContractTypeSetService extends BaseServiceImpl<ContractTypeSetDo, ContractTypeSetQueryDto> {
    @Resource
    private ContractTypeSetDomainService contractTypeSetDomainService;
    @Resource
    private ContractSetConditionDomainService contractSetConditionDomainService;
    @Resource
    private EmpContractTypeSetRelDomainService empContractTypeSetRelDomainService;
    @Resource
    private ContractTypeSetPublish contractTypeSetPublish;
    @Resource
    private ContractService contractService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private IConditionFeign conditionFeign;
    @Resource
    private Locker locker;
    private final String CONTINUE_CONTRACT_CODE = "continue_contract";
    private final String CONTRACT_SETTING_CODE = "contract_setting";
    Map<String, ContractSetConditionDo.Condition> conditionMap;
    List<ContractSetConditionDo.Condition> conditionList;
    private final String CONFIG_LCOKER = "CONTINUE_CONTRACT_CONFIG_%s";

    /**
     * 续签意向员工字段
     */
    private static final Set<String> CONTINUE_EMP_PROPERTY = Sets.set("name", "hireDate", "organizeTxt", "postTxt");
    public static final Set<String> CONTRACT_EMP_PROPERTY = Sets.set("companyTxt", "startDate", "endDate");

    @PostConstruct
    public void init() {
        List<ContractSetConditionDo.Condition> list = new ArrayList();
        Map<String, String> nationMap = new HashMap<>(2);
        nationMap.put("belongModule", "Employee");
        nationMap.put("typeCode", "Nationality");
        var noChildList = noContainChild();
        list.add(new ContractSetConditionDo.Condition("国籍",
                "hr#empPrivateInfo#nationality$dictValue",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.DICT_SELECTOR,
                Lists.list(),
                nationMap));
        list.add(new ContractSetConditionDo.Condition("任职组织",
                "hr#empWorkInfo#organize",
                ConditionCallType.OUTSIDE_SERVICE,
                enableOpts(),
                ValueComponent.ORG,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("职务",
                "hr#empWorkInfo#job",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.JOB,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("岗位",
                "hr#empWorkInfo#post",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.POS_SELECTOR,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("司龄",
                "hr#empWorkInfo#divisionAge",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.NUMBER_INPUT,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("司龄（至合同结束日期）",
                "hr#Contract#endDate",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.NUMBER_INPUT,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("签订次数",
                "hr#Contract#signTime",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.NUMBER_INPUT,
                Lists.list()));
        list.add(new ContractSetConditionDo.Condition("合同类型",
                "hr#Contract#contractSettingType",
                ConditionCallType.OUTSIDE_SERVICE,
                noChildList,
                ValueComponent.DICT_SELECTOR,
                Lists.list(),
                Maps.map(Sequences.sequence(
                        Pair.pair("belongModule", "Employee"),
                        Pair.pair("typeCode", "ContractType")))));

        list.add(new ContractSetConditionDo.Condition("职级",
                "hr#EmpWorkInfo#jobGrade$startGrade",
                ConditionCallType.INSIDER_SERVICE,
                noChildList,
                ValueComponent.JOB_GRADE,
                Lists.list(),
                Maps.map(),
                "api/hr/jobgrade/v1/treeList"
        ));
        conditionList = list;
        Map<String, ContractSetConditionDo.Condition> map = list.stream()
                .collect(Collectors.toMap(ContractSetConditionDo.Condition::getCode, condition -> condition, (k1, k2) -> k2));
        for (ConditionOperator value : enableOpts()) {
            ContractSetConditionDo.Condition tc = new ContractSetConditionDo.Condition();
            tc.setCode(value.getCode());
            tc.setName(value.getName());
            map.put(value.getCode(), tc);
        }
        conditionMap = map;
    }

    @Override
    protected BaseDomainService getDomainService() {
        return contractTypeSetDomainService;
    }

    public PageResult<ContractTypeSetListVo> getList(ContractTypeSetQueryDto query) {
        PageResult<ContractTypeSetDo> pageResult = getDomainService().getPage(query, ObjectConverter.convert(query, ContractTypeSetDo.class));
        List<ContractTypeSetListVo> voList = toVoList(pageResult.getItems());
        return new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    private List<ContractTypeSetListVo> toVoList(List<ContractTypeSetDo> list) {
        if (list == null || list.isEmpty()) {
            return Lists.list();
        }
        List<ContractTypeSetListVo> voList = new ArrayList<>();
        List<String> contractTypeSetBids = list.stream().map(ContractTypeSetDo::getBid).collect(Collectors.toList());
        ContractSetConditionDo setConditionDo = new ContractSetConditionDo();
        setConditionDo.setContractTypeSetBids(contractTypeSetBids);
        List<ContractSetConditionDo> conditionDos = contractSetConditionDomainService.selectList(setConditionDo);
        Map<String, List<ContractSetConditionDo>> map = new HashMap();
        if (CollectionUtils.isNotEmpty(conditionDos)) {
            map = conditionDos.stream().collect(Collectors.groupingBy(ContractSetConditionDo::getContractTypeSet));
        }
        for (ContractTypeSetDo data : list) {
            ContractTypeSetListVo vo = ObjectConverter.convert(data, ContractTypeSetListVo.class);
            convertData(data, vo);
            List<ContractSetConditionDo> dos = map.get(data.getBid());
            ContractSetConditionDo conditionDo = dos == null || dos.isEmpty() ? null : dos.get(0);
            if (conditionDo != null) {
                vo.setPeriodType(conditionDo.getPeriodType() != null && "0".equals(conditionDo.getPeriodType()
                        .getValue()) ? "有" : "无");
                vo.setContractPeriod(conditionDo.getContractPeriod());
                vo.setProbationPeriod(conditionDo.getProbationPeriod());
                vo.setEndDateType(conditionDo.getEndDateType().getValue());
                vo.setBaseExpectGraduateDate(conditionDo.getBaseExpectGraduateDate());
                vo.setProbation(conditionDo.getProbationDeadline());
            }
            voList.add(vo);
        }
        return voList;
    }

    public static void convertData(ContractTypeSetDo source, ContractTypeSetListVo target) {
        if (source == null) {
            return;
        }

        if (source.getSignTypeTxt() != null) {
            target.setSignType(source.getSignTypeTxt().stream().map(ComponentValue::getDisplay)
                    .collect(Collectors.toList()));
        }
        if (source.getEmpTypeTxt() != null) {
            target.setEmpType(source.getEmpTypeTxt().stream().map(DictDto::getLabel).collect(Collectors.toList()));
        }
        if (source.getCompanyTxt() != null) {
            target.setCompanyTxt(source.getCompanyTxt().stream().map(ComponentValue::getDisplay)
                    .collect(Collectors.toList()));
        }

        /*
           2022/5/18
           DO 中 contractType 属性名改成 contractClass ， contractTypeSetName 属性改为字典 contractType
           list列表中响应数据结构暂不改动
         */
        target.setContractType(source.getContractClass().getText());
        target.setContractTypeSetName(source.getContractType().getText());
    }


    public void enable(ContractTypeSetDo data) {
        contractTypeSetDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public void disable(ContractTypeSetDo data) {
        contractTypeSetDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE);
    }


    public ContractTypeSetVo getDetail(String bid) {
        ContractTypeSetDo data = getById(bid);

        // 查询自定义条件
        ContractSetConditionDo conditionDo = contractSetConditionDomainService.getByTypeSetBid(bid);
        if (conditionDo == null) {
            ContractTypeSetVo vo = ObjectConverter.convert(data, ContractTypeSetVo.class);
            toDetail(data, vo);
            return vo;
        }

        ContractSetConditionVo conditionVo = ObjectConverter.convert(conditionDo, ContractSetConditionVo.class);
        conditionVo.setConditionTree(FastjsonUtil.toObject(conditionDo.getConditionTree(), ConditionTreeDto.class));

        ContractTypeSetVo vo = ObjectConverter.convert(conditionVo, ContractTypeSetVo.class);
        vo.setEndDateType(conditionVo.getEndDateType().getValue());
        vo.setPeriodType(PeriodTypeEnum.getFlagByCode(conditionVo.getPeriodType().getValue()));
        vo.setProbationDeadline(conditionVo.getProbationDeadline());
        BeanUtils.copyProperties(data, vo);
        toDetail(data, vo);
        return vo;
    }

    void toDetail(ContractTypeSetDo data, ContractTypeSetVo vo) {
        if (null == data) {
            return;
        }
        vo.setContractType(data.getContractType().getValue());
        vo.setContractTypeTxt(data.getContractType().getText());
        vo.setContractClass(data.getContractClass().getValue());
        vo.setEmpType(data.getEmpTypeTxt());
        vo.setCompanyTxt(data.getCompanyTxt().stream().map(ComponentValue::getDisplay).collect(Collectors.toList()));
    }

    @Override
    public ContractTypeSetDo saveOrUpdateObj(Object data) {
        // TODO: 2022/5/18  事务暂不支持，暂时把解析自定义条件提前
        ContractTypeSetDto dto = (ContractTypeSetDto) data;
        ContractSetConditionDto conditionDto = ObjectConverter.convert(dto, ContractSetConditionDto.class);
        conditionDto.setPeriodType(PeriodTypeEnum.getCodeByFlag(dto.getPeriodType()));
        ContractSetConditionDo conditionDo = convertCondition(conditionDto);

        ContractTypeSetDo typeSetDo = super.saveOrUpdateObj(data);
        conditionDo.setContractTypeSet(typeSetDo.getBid());
        saveCondition(conditionDo);

        contractTypeSetPublish.contractTypeSetChanged(typeSetDo.getBid());
        return typeSetDo;
    }

    ContractSetConditionDo convertCondition(ContractSetConditionDto condition) {
        ContractSetConditionDo data = ObjectConverter.convert(condition, ContractSetConditionDo.class);
        doConvertCondition(data, condition);
        parseTagLabel(condition, data);
        return data;
    }

    @Override
    public ContractTypeSetDo dto2do(Object dto) {
        ContractTypeSetDo data = super.dto2do(dto);
        doConvertEnum(data, dto instanceof ContractTypeSetDto ? (ContractTypeSetDto) dto : ObjectConverter.convert(dto, ContractTypeSetDto.class));
        return data;
    }

    @Override
    protected void checkObj(ContractTypeSetDo data) {
        PreCheck.preCheckNotNull(data.getContractType(), LangUtil.getMsg(MISSING_REQUIRED_PARAMETER));
        PreCheck.preCheckNotNull(data.getContractClass(), LangUtil.getMsg(MISSING_REQUIRED_PARAMETER));
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(data.getCompany()), LangUtil.getMsg(MISSING_REQUIRED_PARAMETER));
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(data.getSignType()), LangUtil.getMsg(MISSING_REQUIRED_PARAMETER));
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(data.getEmpType()), LangUtil.getMsg(MISSING_REQUIRED_PARAMETER));
    }

    private void doConvertEnum(ContractTypeSetDo data, ContractTypeSetDto dto) {
        if (StringUtils.isNotEmpty(dto.getContractType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(dto.getContractType());
            data.setContractType(dictSimple);
        }

        if (StringUtils.isNotEmpty(dto.getContractClass())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(dto.getContractClass());
            data.setContractClass(dictSimple);
        }

        if (dto.getSignType() != null) {
            data.setSignType(dto.getSignType().stream()
                    .distinct().sorted(String::compareTo).collect(Collectors.toList()));
            List<ComponentValue> signTypeTxts = Lists.list();
            Iterator<String> iterator = data.getSignType().iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                String txt;
                if ((txt = SignTypeEnum.getName(next)) == null) {
                    iterator.remove();
                    continue;
                }

                ComponentValue cv = new ComponentValue();
                cv.setDisplay(txt);
                cv.setValue(next);
                signTypeTxts.add(cv);
            }
            data.setSignTypeTxt(signTypeTxts);
        }

        if (dto.getEmpType() != null) {
            List<DictDto> dicts = dto.getEmpType().stream()
                    .distinct().sorted(Comparator.comparing(DictDto::getCode)).collect(Collectors.toList());
            data.setEmpTypeTxt(dicts);
            data.setEmpType(dicts.stream().map(DictDto::getValue).collect(Collectors.toList()));
        }
    }

    private void doConvertCondition(ContractSetConditionDo data, ContractSetConditionDto dto) {
        // 合同固定期限枚举
        String periodType = StringUtils.isEmpty(dto.getPeriodType()) ? PeriodTypeEnum.NO_FIXED.getCode() : dto.getPeriodType();
        EnumSimple dictSimple = new EnumSimple();
        dictSimple.setValue(periodType);
        data.setPeriodType(dictSimple);

        // 合同结束日期枚举
        String endDataType = StringUtils.isEmpty(dto.getEndDateType()) ? EndDateTypeEnum.DEFAULT.getCode() : dto.getEndDateType();
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(endDataType);
        data.setEndDateType(enumSimple);

        //    预计毕业日期
        data.setBaseExpectGraduateDate(Optional.ofNullable(dto.getBaseExpectGraduateDate()).orElse(Boolean.FALSE));
        // 退休日期
        data.setBaseRetireDate(Optional.ofNullable(dto.getBaseRetireDate()).orElse(Boolean.FALSE));
    }

    private void saveCondition(ContractSetConditionDto condition) {
        ContractSetConditionDo data = ObjectConverter.convert(condition, ContractSetConditionDo.class);
        doConvertCondition(data, condition);
        parseTagLabel(condition, data);
        // 删除自定义条件后重新插入
        contractSetConditionDomainService.deleteByTypeSetId(data.getContractTypeSet());
        contractSetConditionDomainService.save(data);
    }

    private void saveCondition(ContractSetConditionDo data) {
        contractSetConditionDomainService.deleteByTypeSetId(data.getContractTypeSet());
        contractSetConditionDomainService.save(data);
    }

    private void parseTagLabel(ContractSetConditionDto condition, ContractSetConditionDo data) {
        StringBuilder tagLabel = new StringBuilder();
        StringBuilder tagTsql = new StringBuilder();
        if (null != condition.getConditionTree()) {
            List<ConditionNodeDto> children = condition.getConditionTree().getChildren();
            doTagTree(children, tagLabel, condition.getConditionTree().getRelation());
            doTSqlTagTree(children, tagTsql, condition.getConditionTree().getRelation());
        }
        // 分组条件树拼装
        data.setConditionTree(FastjsonUtil.toJson(condition.getConditionTree()));
        data.setConditionLabel(tagLabel.toString());
        data.setConditionExp(tagTsql.toString());
    }


    public ContractSetConditionDuplicateVo checkDuplicate(ContractTypeSetDto dto) {
        ContractTypeSetDo data = dto2do(dto);
        doConvertEnum(data, dto);
        List<ContractTypeSetDo> dataList = contractTypeSetDomainService.getByCondition(data);
        if (CollectionUtils.isEmpty(dataList)) {
            return new ContractSetConditionDuplicateVo();
        }
        return new ContractSetConditionDuplicateVo(LangUtil.getFormatMsg(DUPLICATE_CONTRACT, dataList.get(0)
                .getContractType().getText()));
    }

    private void doTSqlTagTree(List<ConditionNodeDto> children, StringBuilder tagLabel, ConditionNodeRelation relation) {
        if (null == children || children.isEmpty()) {
            return;
        }

        // int size = children.size();
        ContractSetConditionDo.Condition tagConditionDo = null;
        Iterator<ConditionNodeDto> iterator = children.iterator();
        while (iterator.hasNext()) {
            ConditionNodeDto node = iterator.next();

            ConditionExpDto condition = node.getCondition();
            // 简单条件
            if (node.getType().equals(ConditionNodeType.single)) {
                // 当node不包含表达式，或表达式不完整时
                if (node.isIncomplete()) {
                    iterator.remove();
                    continue;
                }
                tagConditionDo = conditionMap.get(condition.getName());
                tagLabel.append(null != tagConditionDo ? tagConditionDo.getCode() : "");
                tagLabel.append(" ");
                tagConditionDo = conditionMap.get(condition.getSymbol().getCode());
                String compare = convertSqlMak(null != tagConditionDo ? tagConditionDo.getCode() : "");
                tagLabel.append(compare);
                tagLabel.append(" ");
                if (compare.indexOf("LIKE") > -1) {
                    tagLabel.append("'%" + condition.getValueByOpt() + "%'");
                } else if (compare.indexOf("IN") > -1) {
                    tagLabel.append("(" + condition.getValueByOpt() + ")");
                } else {
                    tagLabel.append("'" + condition.getValueByOpt() + "'");
                }

                tagLabel.append(" ");
            } else if (ConditionNodeType.group.equals(node.getType())) {
                // 条件组为空
                if (node.getChildren() == null || node.getChildren().isEmpty()) {
                    iterator.remove();
                    continue;
                }
                // 嵌套分组条件
                tagLabel.append(" ( ");
                doTSqlTagTree(node.getChildren(), tagLabel, node.getRelation());
                tagLabel.append(" ) ");
            }

            // 不为最后一个node
            if (iterator.hasNext()) {
                // if (i < (size - 1)) {
                tagLabel.append(null == relation ? "" : relation.getCode());
                tagLabel.append(" ");
            }
        }
    }

    private String convertSqlMak(String code) {
        String temp = code;
        switch (code) {
            case "EQ":
                return " = ";
            case "LT":
                return " < ";
            case "GT":
                return " > ";
            case "LE":
                return " <= ";
            case "GE":
                return " >= ";
            case "IN":
                return " IN ";
            case "CONTAIN":
                return " LIKE ";
            case "NE":
                return " <> ";
        }
        return temp;
    }

    private void doTagTree(List<ConditionNodeDto> children, StringBuilder tagLabel, ConditionNodeRelation relation) {
        if (null == children || children.isEmpty()) {
            return;
        }

        // int size = children.size();
        ContractSetConditionDo.Condition tagConditionDo = null;
        Iterator<ConditionNodeDto> iterator = children.iterator();
        while (iterator.hasNext()) {
            ConditionNodeDto node = iterator.next();

            ConditionExpDto condition = node.getCondition();
            // 简单条件
            if (node.getType().equals(ConditionNodeType.single)) {
                if (node.isIncomplete()) {
                    iterator.remove();
                    continue;
                }
                tagConditionDo = conditionMap.get(condition.getName());
                tagLabel.append(null != tagConditionDo ? tagConditionDo.getName() : "");
                tagLabel.append(" ");
                tagConditionDo = conditionMap.get(condition.getSymbol().getCode());
                tagLabel.append(null != tagConditionDo ? tagConditionDo.getName() : "");
                tagLabel.append(" ");
                tagLabel.append(condition.getValueByOpt());
                tagLabel.append(" ");
            } else if (ConditionNodeType.group.equals(node.getType())) {
                if (node.getChildren() == null || node.getChildren().isEmpty()) {
                    iterator.remove();
                    continue;
                }
                // 嵌套分组条件
                tagLabel.append(" ( ");
                doTagTree(node.getChildren(), tagLabel, node.getRelation());
                tagLabel.append(" ) ");
            }

            if (iterator.hasNext()) {
                tagLabel.append(null == relation ? "" : relation.getName());
                tagLabel.append(" ");
            }
        }
    }

    public List groupConditionList() {
        val result = conditionList;
        result.addAll(selfDefinedConditions().stream().filter(it->
                conditionList.stream().noneMatch(existed->StringUtils.equals(it.getName(), existed.getName()))).collect(Collectors.toList()));
        return result;
    }

    private List<ContractSetConditionDo.Condition> selfDefinedConditions() {
        val contractSettingCondition = getContractSettingCondition();
        return contractSettingCondition.stream().map(it->{
            ContractSetConditionDo.Condition condition = FastjsonUtil.convertObject(it,
                    ContractSetConditionDo.Condition.class);
            condition.setCode(it.getIdentifier()+"#"+it.getCode());
            return condition;
        }).collect(Collectors.toList());
    }

    public List<ContractTypeSetRelListVo> listCompanyAll(String company, String signType) {
        List<ContractTypeSetDo> list = contractTypeSetDomainService.getListByRegex(company, signType);
        return toRelVoList(list);
    }

    private List<ContractTypeSetRelListVo> toRelVoList(List<ContractTypeSetDo> list) {
        if (list == null || list.isEmpty()) {
            return Lists.list();
        }
        List<ContractTypeSetRelListVo> voList = new ArrayList<>();
        List<String> contractTypeSetBids = list.stream().map(ContractTypeSetDo::getBid).collect(Collectors.toList());
        ContractSetConditionDo setConditionDo = new ContractSetConditionDo();
        setConditionDo.setContractTypeSetBids(contractTypeSetBids);
        List<ContractSetConditionDo> conditionDos = contractSetConditionDomainService.selectList(setConditionDo);
        Map<String, List<ContractSetConditionDo>> map = new HashMap();
        if (CollectionUtils.isNotEmpty(conditionDos)) {
            map = conditionDos.stream().collect(Collectors.groupingBy(ContractSetConditionDo::getContractTypeSet));
        }
        for (ContractTypeSetDo data : list) {
            ContractTypeSetRelListVo vo = ObjectConverter.convert(data, ContractTypeSetRelListVo.class);
            vo.convertData(data);
            List<ContractSetConditionDo> dos = map.get(data.getBid());
            ContractSetConditionDo conditionDo = dos == null || dos.isEmpty() ? null : dos.get(0);
            if (conditionDo != null) {
                vo.setPeriodType(conditionDo.getPeriodType());
                vo.setContractPeriod(conditionDo.getContractPeriod());
                vo.setProbationPeriod(conditionDo.getProbationPeriod());
                vo.setEndDateType(conditionDo.getEndDateType().getValue());
                vo.setBaseExpectGraduateDate(conditionDo.getBaseExpectGraduateDate());
                vo.setProbation(conditionDo.getProbationDeadline());
            }
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 保存或更新续签合同模版
     *
     * @param contractTemplateDto
     */
    public void saveOrUpdateConditionContract(ContinueContractTemplateDto contractTemplateDto) {
        if (contractTemplateDto == null) {
            return;
        }
        contractTypeSetDomainService.saveOrUpdateContinueContract(contractTemplateDto.convertDo());
    }

    /**
     * 移除续签意向模版
     *
     * @param bid
     */
    public void removeConditionContract(String bid) {
        if (StringUtils.isBlank(bid)) {
            return;
        }
        contractTypeSetDomainService.removeConditionContractByBid(bid);
    }

    /**
     * 获取续签意向模版详情
     *
     * @param bid
     * @return
     */
    public Optional<ContinueContractTemplateDto> getContinueContractByBId(String bid) {
        return contractTypeSetDomainService.getConditionContractByBid(bid);
    }

    /**
     * 分页获取续签意向模版
     *
     * @param page
     * @return
     */
    public PageResult<ContinueContractTemplateDto> getPageOfContinueContract(ContinueContractSetQueryDto page) {
        return contractTypeSetDomainService.getPageOfConditionContract(page);
    }

    /**
     * 合同bid
     *
     * @param contractId
     * @return
     */
    public List<ContinueContractTemplateDto> findMatchedContinueTemplate(String contractId) {
        ContractDo contract = contractService.getById(contractId);
        List<ContinueContractTemplateDto> list = contractTypeSetDomainService.getContinueTemplateList();
        return contractTypeSetDomainService.findMatchedContinueTemplate(contract.getOwner().getEmpId(),list,getNameMapping());
    }

    public ContinueTemplatePropertyDefDto getPropertyDef(String templateId) {
        Optional<ContinueContractTemplateDto> templateOption = contractTypeSetDomainService.getConditionContractByBid(templateId);
        if (!templateOption.isPresent()) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_30006"));
        }
        ContinueContractTemplateDto template = templateOption.get();
        ContinueTemplatePropertyDefDto def = ObjectConverter.convert(template, ContinueTemplatePropertyDefDto.class);
        def.setContract(getContractPropertyDef());
        def.setEmpInfo(getEmpPropertyDef());
        return def;
    }

    private List<MetadataPropertyVo> getContractPropertyDef(){
        List<MetadataPropertyVo> list = metadataService.getMetadataProperty("entity.hr.LastContract");
        Locale locale = LangUtil.getLocale();
        return Sequences.sequence(list)
            .filter(p -> CONTRACT_EMP_PROPERTY.contains(p.getProperty()))
            .map(mpv -> {
                mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
                return mpv;
            }).toList();
    }

    private List<MetadataPropertyVo> getEmpPropertyDef() {
        List<MetadataPropertyVo> list = metadataService.getMetadataProperty("entity.hr.EmpWorkInfo");
        Locale locale = LangUtil.getLocale();
        return Sequences.sequence(list).filter(p -> CONTINUE_EMP_PROPERTY.contains(p.getProperty()))
            .map(mpv -> {
                mpv.setName(LangUtil.getCurrentLangVal(mpv.getI18nName(), locale));
                return mpv;
            }).toList();
    }

    public List<ConditionDataVo> getContinueCondition() {
        return conditionFeign.getConditionDataByCode(CONTINUE_CONTRACT_CODE, false).getData();
    }

    public List<ConditionDataVo> getContractSettingCondition() {
        return conditionFeign.getConditionDataByCode(CONTRACT_SETTING_CODE, false).getData();
    }

    public List<ContractTypeSetRelListVo> listCompanyBySignType(String signType) {
        List<ContractTypeSetDo> list = contractTypeSetDomainService.getListCompanyByRegex(signType);
        return toRelVoList(list);
    }

    public List<ConditionNameMapping> getNameMapping() {
        return Sequences.sequence(conditionFeign.getConditionDataByCode(CONTINUE_CONTRACT_CODE, true).getData())
                .map(con -> new ConditionNameMapping(con.getProperty(), con.getIdentifier(), con.getQueryProperty()))
                .toList();
    }

    @PaasTransactional
    @CacheDelete({@CacheDeleteKey(value = "'continue-contract-config-' + #args[1].tenantId")})
    public void saveOrUpdateContinueContractConfig(ContinueContractConfigDto continueContractConfigDto, SecurityUserInfo userInfo) {
        var lock = locker.getLock(String.format(CONFIG_LCOKER, userInfo.getTenantId()));
        val b = lock.tryLock();
        if (b) {
            try {
                var configOptional = empContractTypeSetRelDomainService.getContinueContractConfig();
                if (configOptional.isPresent()) {
                    ContinueContractConfigDo continueContractConfigDo = configOptional.get();
                    continueContractConfigDo.setUseWorkflow(BooleanUtils.toBooleanDefaultIfNull(continueContractConfigDto.getUseWorkflow(), false));
                    continueContractConfigDo.setUpdateTime(System.currentTimeMillis());
                    continueContractConfigDo.update();
                    return;
                }
                var continueContractConfigDo = new ContinueContractConfigDo();
                continueContractConfigDo.setUseWorkflow(BooleanUtils.toBooleanDefaultIfNull(continueContractConfigDto.getUseWorkflow(), false));
                continueContractConfigDo.setBid(SnowUtil.nextId());
                continueContractConfigDo.setTenantId(userInfo.getTenantId());
                continueContractConfigDo.setCreateTime(System.currentTimeMillis());
                continueContractConfigDo.save();
            } finally {
                lock.unlock();
            }
        }
    }

    @Cache(expire = 600, key = "'continue-contract-config-' + #args[0]")
    public ContinueContractConfigVo getContinueContractConfig(String tenantId) {
        var configOptional = empContractTypeSetRelDomainService.getContinueContractConfig();
        if (!configOptional.isPresent()) {
            return new ContinueContractConfigVo(false);
        }
        return new ContinueContractConfigVo(BooleanUtils.toBooleanDefaultIfNull(configOptional.get().getUseWorkflow(), false));
    }
}