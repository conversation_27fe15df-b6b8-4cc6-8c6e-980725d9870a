package com.caidaocloud.hr.service.contract.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.WorkFlowFeignClient;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.contract.application.constant.CacheKeyConstant;
import com.caidaocloud.hr.service.contract.application.constant.ContractSignTypeConstant;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.application.enums.*;
import com.caidaocloud.hr.service.contract.application.event.dto.ContractMessageDto;
import com.caidaocloud.hr.service.contract.application.event.publish.ContractPublish;
import com.caidaocloud.hr.service.contract.application.exception.ContractAutoStartException;
import com.caidaocloud.hr.service.contract.domain.entity.ContractConfig;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.enums.ContractApplyType;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.contract.domain.service.ContractSetConditionDomainService;
import com.caidaocloud.hr.service.contract.domain.service.LastContractDomainService;
import com.caidaocloud.hr.service.contract.interfaces.dto.*;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchAutoRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.BatchRenewalDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.*;
import com.caidaocloud.hr.service.dto.EmpContractDto;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.application.emp.ruleset.service.RuleSetService;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.RuleSetAutoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.enums.system.BaseEmpType;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContractService extends BaseServiceImpl<ContractDo, ContractQueryDto> {
    private static SnowflakeUtil snowFlake = new SnowflakeUtil(1L, 1L);
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private ContractPublish contractPublish;
    @Resource
    private EmpBasicInfoDo empBasicInfoDo;
    @Resource
    private CacheService cacheService;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private ContractSetConditionDomainService contractSetConditionDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private LastContractDomainService lastContractDomainService;
    @Resource
    private CompanyDomainService companyDomainService;
    @Resource
    private RuleSetAutoDomainService ruleSetAutoDomainService;
    @Resource
    private OrgService orgService;
    @Resource
    private WorkplaceService workplaceService;
    @Autowired
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private MsgNoticeService messageService;
    @Resource
    private RuleSetService ruleSetService;
    @Resource
    private WorkFlowFeignClient workFlowFeignClient;
    @Resource
    private ArchiveEventProducer archiveEventProducer;
    @Resource
    private ContractConfigService contractConfigService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    private static final int COUNT_SIZE = 500;
    private static final int DAY = 24 * 60 * 60 * 1000;
    private static final int BATCH_RENEWAL_LIMIT = 100;

    @Override
    protected BaseDomainService getDomainService() {
        return contractDomainService;
    }

    public List<ContractDo> getEmpCurrentContract(String empId) {
        List<ContractDo> empCurrentList = contractDomainService.getEmpCurrentContract(Arrays.asList(empId), ApprovalStatusEnum.PASSED.getIndex().toString());
        //empCurrentList.stream().sorted(Comparator.comparing(ContractDo::getStartDate).reversed()).collect(Collectors.toList());
        return empCurrentList;
    }

    public ContractDo getCurrentContract(String empId) {
        List<ContractDo> reList = contractDomainService.getEmpCurrentContract(Arrays.asList(empId), ApprovalStatusEnum.PASSED.getIndex().toString());
        if (CollectionUtils.isEmpty(reList)) {
            return null;
        }
        long nowDate = System.currentTimeMillis();
        ContractDo ret = reList.stream()
                .filter(cd -> {
                    cd.calcStatus();
                    return null != cd.getStartDate() && cd.getStartDate() <= nowDate
                            && null != cd.getEndDate() && nowDate <= cd.getEndDate()
                            && ContractStatusEnum.EFFECTIVE.getIndex().equals(cd.getContractStatus().getValue());
                })
                .findFirst().orElse(reList.get(0));
        if (null != ret) {
            ret.convertContractStatus();
        }
        return ret;
    }

    public List<ContractDo> getCurrentContractList(List<String> empIds) {
        List<ContractDo> reList = contractDomainService.getEmpCurrentContract(empIds, ApprovalStatusEnum.PASSED.getIndex().toString());
        if (CollectionUtils.isEmpty(reList)) {
            return Lists.list();
        }
        long nowDate = System.currentTimeMillis();
        return Sequences.sequence(reList).groupBy(data -> data.getOwner().getEmpId()).map(data -> {
            String empId = data.key();
            ContractDo ret = data.stream()
                    .filter(cd -> {
                        cd.calcStatus();
                        return null != cd.getStartDate() && cd.getStartDate() <= nowDate
                                && null != cd.getEndDate() && nowDate <= cd.getEndDate()
                                && ContractStatusEnum.EFFECTIVE.getIndex().equals(cd.getContractStatus().getValue());
                    })
                    .findFirst().orElse(reList.get(0));
            if (null != ret) {
                ret.convertContractStatus();
            }
            return ret;
        }).toList();
    }



    public ContractDo getEmpLastContract(String empId) {
        return contractDomainService.getEmpLastContract(empId, ApprovalStatusEnum.PASSED.getIndex().toString());
    }

    public List<ContractDo> getEmpHistoryContract(String empId) {
        return contractDomainService.getEmpHistoryContract(Arrays.asList(empId), ApprovalStatusEnum.PASSED.getIndex().toString());
    }

    public Integer getContractSignCount(String empId) {
        List<ContractDo> list = contractDomainService.getContractSignList(Arrays.asList(empId));
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.size();
    }

    public Map<String, Integer> getEmpContractSignCount(List<String> empList) {
        List<ContractDo> list = contractDomainService.getContractSignList(empList);
        Map<String, Integer> countMap = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return countMap;
        }

        list.forEach(contractDo -> {
            String empId = contractDo.getOwner().getEmpId();
            Integer count = countMap.get(empId);
            count = null == count ? 1 : (count + 1);
            countMap.put(empId, count);
        });

        return countMap;
    }

    public PageResult<ContractRecordsVo> selectRecordPage(ContractQueryDto dto) {
        AuthScopeFilterUtil.put(true);
        PageResult<ContractDo> page = contractDomainService.selectRecordPage(dto);
        List<ContractRecordsVo> vos = toRecordVo(page);
        vos.forEach(vo->{
            if(!"enabled".equals(postTxtShowCode)){
                if(StringUtils.isNotEmpty(vo.getPostTxt()) && vo.getPostTxt().indexOf("(") >= 0){
                    vo.setPostTxt(vo.getPostTxt().substring(0, vo.getPostTxt().lastIndexOf("(")));
                }
            }
        });
        return new PageResult<>(vos, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    public PageResult<ContractRecordsVo> selectApprovalRecordPage(ContractQueryDto dto) {
        PageResult<ContractDo> page = contractDomainService.selectApprovalRecordPage(dto);
        List<ContractRecordsVo> vos = toRecordVo(page);
        vos.forEach(vo->{
            if(!"enabled".equals(postTxtShowCode)){
                if(StringUtils.isNotEmpty(vo.getPostTxt()) && vo.getPostTxt().indexOf("(") >= 0){
                    vo.setPostTxt(vo.getPostTxt().substring(0, vo.getPostTxt().lastIndexOf("(")));
                }
            }
        });
        return new PageResult<>(vos, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    @NotNull
    private List<ContractRecordsVo> toRecordVo(PageResult<ContractDo> page) {
        List<String> empList = new ArrayList<>();
        List<ContractRecordsVo> vos = CollectionUtils.isNotEmpty(page.getItems()) ?
                convertContract(page.getItems(), empList) : new ArrayList<>();

        long nowDateTime = System.currentTimeMillis();
        for (ContractRecordsVo vo : vos) {
            if (SignTypeEnum.NEW.getCode().equals(vo.getSignType().getValue())) {
                vo.setBusinessKey(vo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_NEW_SIGN);
            }
            if (SignTypeEnum.CHANGE.getCode().equals(vo.getSignType().getValue())) {
                vo.setBusinessKey(vo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_AMENDMENT);
            }
            if (SignTypeEnum.RENEW.getCode().equals(vo.getSignType().getValue())) {
                Result<String> result = workFlowFeignClient.getBusinessKeyBy(vo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_RENEW);
                if(result.isSuccess() && result.getData() != null)
                    vo.setBusinessKey(result.getData());
                else 
                    vo.setBusinessKey(vo.getBid() + "_" + ContractSignTypeConstant.CONTRACT_RENEW);
            }
            //无固定期限合同不需要合同到期天数
            String periodType = vo.getPeriodType().getValue();
            if (StringUtil.isNotEmpty(periodType) && "1".equals(periodType)) {
                continue;
            } else if (StringUtil.isNotEmpty(vo.getEndDate())) {
                vo.setContractDays(String.valueOf((vo.getEndDate() - nowDateTime) / 86400000));
            }
        }
        return vos;
    }

    private List<ContractRecordsVo> convertContract(List<ContractDo> list, List<String> empList) {
        List<ContractRecordsVo> voList = new ArrayList<>(list.size());
        list.forEach(contract -> {
            empList.add(contract.getOwner().getEmpId());
            voList.add(ObjectConverter.convert(contract, ContractRecordsVo.class));
        });
        return voList;
    }

    public PageResult<ContractExportVo> selectExportRecordPage(ContractQueryDto dto) {
        dto.setPageNo(1);
        dto.setPageSize(5000);
        PageResult<ContractDo> page = contractDomainService.selectRecordPage(dto);
        loadMore(dto, page, true);
        return toContractExportVo(page);
    }

    /**
     * 用于导出和列表数据同步的接口（已签订合同）
     */
    public  List<ContractExportVo> selectExportDataForSign(ContractQueryDto dto) {
        dto.setStatus("1");
        dto.setPageNo(1);
        dto.setPageSize(5000);
        PageResult page = getContractPage(dto);
        loadMore(dto, page, true);
        PageResult result = toContractExportVo(page);
        return result.getItems();
    }

    public PageResult<ContractExportVo> selectExportApprovalRecordPage(ContractQueryDto dto) {
        dto.setPageNo(1);
        dto.setPageSize(5000);
        PageResult<ContractDo> page = contractDomainService.selectApprovalRecordPage(dto);
        loadMore(dto, page, false);
        return toContractExportVo(page);
    }

    private void loadMore(ContractQueryDto dto, PageResult<ContractDo> page, boolean isRecord) {
        List<ContractDo> items = page.getItems();
        if (items.size() >= 30000) {
            return;
        }

        if (items.size() == dto.getPageNo() * dto.getPageSize()) {
            dto.setPageNo(dto.getPageNo() + 1);
            PageResult<ContractDo> pages = isRecord ?
                    contractDomainService.selectRecordPage(dto) : contractDomainService.selectApprovalRecordPage(dto);
            items.addAll(pages.getItems());
            page.setItems(items);
            loadMore(dto, page, isRecord);
        }
    }

    @NotNull
    private <T> PageResult<ContractExportVo> toContractExportVo(PageResult<T> page) {
//        List<String> empList = page.getItems().stream()
//                .map(contractDo -> contractDo.getOwner().getEmpId())
//                .distinct()
//                .collect(Collectors.toList());
//        List<List<String>> lists = splitList(empList, 5000);
//        Map<String, EmpWorkInfoDo> workMap = new HashMap<>(empList.size());
//        lists.forEach(subList -> {
//            Map<String, EmpWorkInfoDo> subWorkMap = getEmpWorkInfoMap(subList);
//            workMap.putAll(subWorkMap);
//        });
        List<ContractExportVo> vos = new ArrayList<>();
        for (T data : page.getItems()) {
            ContractExportVo vo = ObjectConverter.convert(data, ContractExportVo.class);
            convertData(data, vo);
//            EmpWorkInfoDo workInfo = workMap.get(data.getOwner().getEmpId());
//            if (null == workInfo) {
//                log.warn("EmpWorkInfo is empty,empid = {}", data.getOwner().getEmpId());
//                continue;
//            }
            //导出应与列表中展示保持一致
//            DataSimpleUtil.copyWithNoValue(workInfo, vo);
//            vo.setEmpType(workInfo.getEmpType().getText());
            vos.add(vo);
        }
        return new PageResult<>(vos, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    private List<List<String>> splitList(List<String> dataList, int groupSize) {
        int length = dataList.size();
        // 计算可以分成多少组
        int num = (length + groupSize - 1) / groupSize;
        List<List<String>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = (i + 1) * groupSize < length ? (i + 1) * groupSize : length;
            newList.add(dataList.subList(fromIndex, toIndex));
        }
        return newList;
    }

    public <T> void convertData(T so, ContractExportVo ta) {
        Class clazz = null;
        if (so instanceof ContractVo) {
            clazz = ContractVo.class;
        } else if (so instanceof ContractDo) {
            clazz = ContractDo.class;
        } else {
            return;
        }
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            try {
                Object o = declaredField.get(so);
                if (Objects.isNull(o)) {
                    continue;
                }
                String name = declaredField.getName();
                if ("contractType".equals(name)) {
                    ta.setContractType(((DictSimple) o).getText());
                } else if ("owner".equals(name)) {
                    ta.setName(((EmpSimple) o).getName());
                    ta.setWorkno(((EmpSimple) o).getWorkno());
                } else if ("hireDate".equals(name)) {
                    ta.setHireDate(DateUtil.formatDate((Long) o));
                } else if ("empStatus".equals(name)) {
                    ta.setEmpStatus(((EnumSimple) o).getText());
                } else if ("empType".equals(name)) {
                    ta.setEmpType(((DictSimple) o).getText());
                } else if ("signType".equals(name)) {
                    ta.setSignType(((EnumSimple) o).getText());
                } else if ("startDate".equals(name)) {
                    ta.setStartDate(DateUtil.formatDate((Long) o));
                } else if ("endDate".equals(name)) {
                    ta.setEndDate(DateUtil.formatDate((Long) o));
                    ta.setContractDays(String.valueOf((((Long) o).longValue() - DateUtil.getCurrentTimestamp()) / 24 / 60 / 60 / 1000));
                } else if ("terminationDate".equals(name)) {
                    ta.setTerminationDate(DateUtil.formatDate((Long) o));
                } else if ("contractStatus".equals(name)) {
                    ta.setContractStatus(((EnumSimple) o).getText());
                } else if ("approvalStatus".equals(name)) {
                    ta.setApprovalStatus(((EnumSimple) o).getText());
                } else if ("launchDate".equals(name)) {
                    ta.setLaunchDate(DateUtil.formatDate((Long) o));
                }
            } catch (IllegalAccessException e) {
                log.error("occur error", e);
            }
        }
    }

    private void addDefaultTagPropertyRecord(List<TagProperty> list) {
        addTagPropertyToList(list, "workno", "工号", 1);
        addTagPropertyToList(list, "name", "姓名", 2);
        addTagPropertyToList(list, "hireDate", "入职日期", 3);
        addTagPropertyToList(list, "organizeTxt", "任职组织", 4);
        addTagPropertyToList(list, "jobTxt", "职务", 5);
        addTagPropertyToList(list, "postTxt", "岗位", 6);
        addTagPropertyToList(list, "contractNo", "合同编号", 8);
        addTagPropertyToList(list, "companyTxt", "合同公司", 9);
        //addTagPropertyToList(list, "contractType", "合同类型",10);
        addTagPropertyToList(list, "contractTypeSetTxt", "合同类型", 10);
    }

    public List<TagProperty> installApprovalTagProperty() {
        List<TagProperty> list = new ArrayList<>();
        addDefaultTagPropertyRecord(list);
        addTagPropertyToList(list, "signType", "签订类型", 11);
        addTagPropertyToList(list, "contractPeriod", "合同期限", 12);
        addTagPropertyToList(list, "startDate", "合同开始日期", 13);
        addTagPropertyToList(list, "endDate", "合同结束日期", 14);
        addTagPropertyToList(list, "contractDays", "合同到期天数", 15);
        addTagPropertyToList(list, "signTime", "合同签订次数", 16);
        addTagPropertyToList(list, "contractStatus", "合同状态", 17);
        addTagPropertyToList(list, "approvalStatus", "审批状态", 18);
        addTagPropertyToList(list, "launchDate", "合同发起日期", 18);

        return list;
    }

    public List<TagProperty> initContractExportTagProperty() {
        List<TagProperty> list = new ArrayList<>();
        addDefaultTagProperty(list);
        addTagPropertyToList(list, "empStatus", "员工状态", 3);
        addTagPropertyToList(list, "contractPeriod", "合同期限", 12);
        addTagPropertyToList(list, "startDate", "合同开始日期", 13);
        addTagPropertyToList(list, "endDate", "合同结束日期", 14);
        addTagPropertyToList(list, "signTime", "合同签订次数", 15);
        addTagPropertyToList(list, "contractDays", "合同到期天数", 16);
        addTagPropertyToList(list, "contractStatus", "合同状态", 17);
        return list;
    }

    private void addDefaultTagProperty(List<TagProperty> list) {
        addTagPropertyToList(list, "workno", "工号", 1);
        addTagPropertyToList(list, "name", "姓名", 2);
        addTagPropertyToList(list, "hireDate", "入职日期", 3);
        addTagPropertyToList(list, "organizeTxt", "任职组织", 4);
        addTagPropertyToList(list, "jobTxt", "职务", 5);
        addTagPropertyToList(list, "postTxt", "岗位", 6);
        addTagPropertyToList(list, "empType", "用工类型", 7);
        addTagPropertyToList(list, "contractNo", "合同编号", 8);
        addTagPropertyToList(list, "companyTxt", "合同公司", 9);
        //addTagPropertyToList(list, "contractType", "合同类型",10);
        addTagPropertyToList(list, "contractTypeSetTxt", "合同类型", 10);
    }

    public List<TagProperty> installContractRecordExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "workno", "工号", 1);
        addTagPropertyToList(list, "name", "姓名", 2);
        addTagPropertyToList(list, "hireDate", "入职日期", 3);
//        addTagPropertyToList(list, "empStatus", "员工状态", 4);
        addTagPropertyToList(list, "organizeTxt", "任职组织", 5);
        addTagPropertyToList(list, "organizeCode", "任职组织编码", 6);
        addTagPropertyToList(list, "jobTxt", "职务", 7);
        addTagPropertyToList(list, "postTxt", "岗位", 8);
        addTagPropertyToList(list, "empType", "用工类型", 9);
        addTagPropertyToList(list, "companyTxt", "合同公司", 10);
        //addTagPropertyToList(list, "contractType", "合同类型",10);
        addTagPropertyToList(list, "contractTypeSetTxt", "合同类型", 11);
        addTagPropertyToList(list, "signType", "签订类型", 12);
        addTagPropertyToList(list, "signTime", "合同签订次数", 13);
        addTagPropertyToList(list, "contractNo", "合同编号", 14);
        addTagPropertyToList(list, "startDate", "合同开始日期", 15);
        addTagPropertyToList(list, "endDate", "合同结束日期", 16);
        addTagPropertyToList(list, "terminationDate", "解除/终止日期", 17);
        addTagPropertyToList(list, "contractPeriod", "合同期（月）", 18);
        addTagPropertyToList(list, "contractStatus", "合同状态", 19);
        return list;
    }


    public List<TagProperty> installNewlySignedExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "workno", "工号", 1);
        addTagPropertyToList(list, "name", "姓名", 2);
        addTagPropertyToList(list, "hireDate", "入职日期", 3);
        addTagPropertyToList(list, "empStatusTxt", "员工状态", 4);
        addTagPropertyToList(list, "organizeTxt", "任职组织", 5);
        addTagPropertyToList(list, "jobTxt", "职务", 6);
        addTagPropertyToList(list, "postTxt", "岗位", 7);
        addTagPropertyToList(list, "workplaceTxt", "工作地", 8);
        addTagPropertyToList(list, "companyTxt", "合同公司", 9);
        addTagPropertyToList(list, "empTypeTxt", "用工类型", 10);

        return list;
    }


    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public List<EmpTreeNodeDto> searchUserList(ContractQueryDto dto) {
        dto.setPageNo(1);
        dto.setPageSize(2000);
        return searchContractUser(dto).getItems();
    }

    @NotNull
    public PageResult<EmpTreeNodeDto> searchContractUser(ContractQueryDto dto) {
        dto.setStatus(ApprovalStatusEnum.PASSED.getIndex().toString());
        PageResult<ContractDo> pageResult = contractDomainService.selectContractUser(dto);
        List<ContractDo> dataList = null;
        List<EmpTreeNodeDto> result = new ArrayList<>();
        if (null == pageResult || null == (dataList = pageResult.getItems()) || dataList.isEmpty()) {
            return new PageResult<>();
        }
        Map<String, String> setMap = new HashMap<>();
        dataList.forEach(data -> {
            EmpSimple empInfo = data.getOwner();
            if (null != empInfo && StringUtil.isNotEmpty(empInfo.getEmpId()) && !setMap.containsKey(empInfo.getEmpId())) {
                EmpTreeNodeDto node = new EmpTreeNodeDto();
                node.setEmpId(empInfo.getEmpId());
                node.setWorkno(empInfo.getWorkno());
                node.setName(empInfo.getName());
                node.setEnName(empInfo.getEnName());
                result.add(node);
                setMap.put(empInfo.getEmpId(), empInfo.getEmpId());
            }
        });
        return new PageResult<>(result, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    /**
     * 获取未签署合同的员工
     *
     * @param dto
     * @return
     */
    public List<EmpTreeNodeDto> searchNewlySignedList(ContractQueryDto dto) {
        dto.setPageNo(1);
        dto.setPageSize(2000);
        return searchNewlyContractUser(dto).getItems();
    }

    @NotNull
    public PageResult<EmpTreeNodeDto> searchNewlyContractUser(ContractQueryDto dto) {
        PageResult<EmpWorkInfoNewlySignedVo> pageResult = newlySignedList(dto);
        List<EmpWorkInfoNewlySignedVo> dataList = null;
        List<EmpTreeNodeDto> result = new ArrayList<>();
        if (null == pageResult || null == (dataList = pageResult.getItems()) || dataList.isEmpty()) {
            return new PageResult<>();
        }
        Map<String, String> setMap = new HashMap<>();
        dataList.forEach(empInfo -> {
            if (null != empInfo && StringUtil.isNotEmpty(empInfo.getEmpId()) && !setMap.containsKey(empInfo.getEmpId())) {
                EmpTreeNodeDto node = new EmpTreeNodeDto();
                node.setEmpId(empInfo.getEmpId());
                node.setWorkno(empInfo.getWorkno());
                node.setName(empInfo.getName());
                result.add(node);
                setMap.put(empInfo.getEmpId(), empInfo.getEmpId());
            }
        });
        return new PageResult<>(result, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    public OrgEmpTreeDto fetchOrgEmpTree(OrgEmpTreeQueryDto queryDto) {
        OrgEmpTreeDto treeData = new OrgEmpTreeDto();
        List<OrgTreeNodeDto> orgNodeList = new ArrayList<>();
        OrgTreeNodeDto rootNode = new OrgTreeNodeDto();
        rootNode.setId("-1");
        rootNode.setText(getTenantName());
        orgNodeList.add(rootNode);

        ContractQueryDto query = new ContractQueryDto();
        query.setOrganize(queryDto.getDeptId());
        query.setKeyword(queryDto.getKeyword());
        treeData.setDeptList(orgNodeList)
                .setRootUserList(searchUserList(query));
        return treeData;
    }

    /**
     * 获取未签署合同的员工
     *
     * @param queryDto
     * @return
     */
    public OrgEmpTreeDto fetchNewlySignedEmp(OrgEmpTreeQueryDto queryDto) {
        OrgEmpTreeDto treeData = new OrgEmpTreeDto();
        List<OrgTreeNodeDto> orgNodeList = new ArrayList<>();
        OrgTreeNodeDto rootNode = new OrgTreeNodeDto();
        rootNode.setId("-1");
        rootNode.setText(getTenantName());
        orgNodeList.add(rootNode);

        ContractQueryDto query = new ContractQueryDto();
        query.setOrganize(queryDto.getDeptId());
        query.setKeyword(queryDto.getKeyword());
        treeData.setDeptList(orgNodeList)
                .setRootUserList(searchNewlySignedList(query));
        return treeData;
    }

    /**
     * 合同管理列表
     *
     * @param queryDto
     * @return
     */
    public PageResult<ContractDo> getList(ContractQueryDto queryDto) {
        return contractDomainService.getList(ApprovalStatusEnum.PASSED.getIndex().toString(), queryDto);
    }

    /**
     * 合同管理-新增
     *
     * @param dto
     */
    public void saveContract(ContractDto dto) {
        if (dto == null) {
            return;
        }
        ContractDo contractDo = ObjectConverter.convert(dto, ContractDo.class);
        val endDate = contractDo.getEndDate();
        val empId = contractDo.getOwner().getEmpId();
        val check = contractTypeSetService.getDetail(contractDo.getContractTypeSet()).getBaseExpectGraduateDate();
        if (null != check && check) {
            val expectedGraduateDate = empWorkInfoDomainService
                    .getEmpWorkInfo(empId, System.currentTimeMillis()).getExpectGraduateDate();
            if (null != expectedGraduateDate && null != endDate && expectedGraduateDate < endDate) {
                throw new ServerException(ServerException.globalException(ErrorMessage
                        .fromCode("caidao.contract.end_time_after_expected_graduate")).getMessage()
                        + "(" + new SimpleDateFormat("yyyy-MM-dd")
                        .format(new Date(expectedGraduateDate)) + ")");
            }
        }
        saveContract(contractDo, dto.getOpenWorkflow(), null);
    }

    /**
     * 合同管理-候选人流转合同信息
     *
     * @param dto
     */
    @PaasTransactional
    public void movingContract(ContractDto dto) {
        if (dto == null) {
            return;
        }
        ContractDo contractDo = ObjectConverter.convert(dto, ContractDo.class);

        // 查询历史合同，已有全部（未生效、生效中）的合同数据，终止日期变更为新签合同的开始日期，合同状态变更为“已终止”
        List<String> contractStatus = new ArrayList<>();
        contractStatus.add(ContractStatusEnum.EFFECTIVE.getIndex());
        contractStatus.add(ContractStatusEnum.IN_EFFECTIVE.getIndex());
        List<ContractDo> hisContracts = contractDomainService.getEmpContractByStatus(contractDo.getOwner().getEmpId(),
                ApprovalStatusEnum.PASSED.getIndex().toString(), contractStatus);

        hisContracts.forEach(hisCon -> {
            EnumSimple simple = new EnumSimple();
            simple.setValue(String.valueOf(ContractStatusEnum.TERMINATED.getIndex()));
            hisCon.setTerminationDate(contractDo.getStartDate() - DAY);
            hisCon.setContractStatus(simple);
            contractDomainService.update(hisCon);
        });
//        try {
//            TimeUnit.MILLISECONDS.sleep(2000);
//        } catch (InterruptedException e) {
//            log.error("TimeUnit.MILLISECONDS.sleep err,{}", e.getMessage());
//        }
        EmpWorkInfoDo empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(contractDo.getOwner().getEmpId(), System.currentTimeMillis());
        if (null == empWorkInfo || StringUtils.isEmpty(empWorkInfo.getBid())) {
            log.error("movingContract 未获取到任职数据,empId{}", contractDo.getOwner().getEmpId());
        }
        // 处理公司注册地
        BeanUtil.copyProperties(empWorkInfo, contractDo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                "updateTime", "updateBy", "deleted", "properties", "dataStartTime", "contractType", "probation");
        contractDo.setRegisterAddress(getRegisterAddress(contractDo.getCompany()));
        saveContract(contractDo, dto.getOpenWorkflow(), null, dto.getCloseEsign());
    }

    /**
     * 获取公司注册地
     *
     * @param bid
     * @return
     */
    private String getRegisterAddress(String bid) {
        if (StringUtils.isEmpty(bid)) {
            return null;
        }
        CompanyDo company = companyDomainService.selectById(bid);
        if (null == company || StringUtils.isEmpty(company.getRegisterAddress())) {
            return null;
        }
        return company.getRegisterAddress();
    }

    /**
     * 保存更新合同信息
     *
     * @param dto
     */
    public ContractDo submitContract(ContractDto dto) {
        if (dto == null) return null;
        ContractDo contractDo = ObjectConverter.convert(dto, ContractDo.class);
        if (StringUtil.isNotEmpty(dto.getBid())) {
            ContractDo detail = contractDomainService.getById(dto.getBid());
            BeanUtil.copyWithNoValue(contractDo, detail);
            convertContractEnumSimple(detail, dto.getOpenWorkflow(), dto.getCloseEsign());
            contractDomainService.update(detail);
        } else {
            saveContract(contractDo, dto.getOpenWorkflow(), null);
        }
        return contractDo;
    }

    /**
     * 保存合同
     *
     * @param contractDo
     * @param openWorkflow
     * @param empWorkInfo
     */
    public void saveContract(ContractDo contractDo, Boolean openWorkflow, EmpWorkInfoDo empWorkInfo) {
        saveContract(contractDo, openWorkflow, empWorkInfo, false);
    }

    /**
     * 保存合同
     *
     * @param contractDo
     * @param openWorkflow
     * @param empWorkInfo
     * @param closeEsign   关闭电子签署
     */
    public void saveContract(ContractDo contractDo, Boolean openWorkflow, EmpWorkInfoDo empWorkInfo, Boolean closeEsign) {
        if (null == contractDo.getOwner() || StringUtil.isEmpty(contractDo.getOwner().getEmpId())) {
            return;
        }

        doSaveContract(contractDo, openWorkflow, empWorkInfo, closeEsign, null, null);
    }

    public void doSaveContract(ContractDo contractDo, Boolean openWorkflow, EmpWorkInfoDo empWorkInfo, Boolean closeEsign, ContractDo lastContractDo, Long dataTime) {
        List<ContractDo> contractByInApproval = contractDomainService.getContractByInApproval(Lists.list(contractDo.getOwner().getEmpId()));
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(contractByInApproval), LangUtil.getMsg(MsgCodeConstant.UNDER_APPROVAL_CONTRACT_RECORD));

        // 查询旧合同
        lastContractDo = null == lastContractDo ? contractDomainService.getById(contractDo.getLastContract()) : lastContractDo;
        if (null == lastContractDo) {
            lastContractDo = new ContractDo();
        }
        //用于还原单据
        ContractDo rollBack = lastContractDo;
        String newSignType = contractDo.getSignType().getValue();
        boolean isChangeSign = SignTypeEnum.CHANGE.getCode().equals(newSignType);
        if (isChangeSign) {
            PreCheck.preCheckArgument(Objects.isNull(lastContractDo), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80006));
            PreCheck.preCheckArgument(contractDo.getStartDate() <= lastContractDo.getStartDate(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80005));
        } else if (StringUtil.isNotEmpty(lastContractDo.getBid())) {

            // 该员工存在审批中的合同单据，无法进行合同信息的修改，请稍后操作
            this.checkUnderApprovalContract(contractDo, false);
        }

        // 存在未生效合同，不能再次发起续签
        if (SignTypeEnum.RENEW.getCode().equals(newSignType) && StringUtils.isEmpty(contractDo.getBid())) {
            this.checkInoperativeContract(contractDo);
        }

        String organize = contractDo.getOrganize() == null ?
                empWorkInfo != null ? empWorkInfo.getOrganize() : null
                : contractDo.getOrganize();
        // 组织编码值补填
        if (organize != null) {
            OrgDo orgById = orgService.getOrgById(contractDo.getOrganize(), System.currentTimeMillis());
            contractDo.setOrganizeCode(Optional.ofNullable(orgById).orElse(new OrgDo()).getCode());
        }

        doOrganizeInfo(contractDo, empWorkInfo, System.currentTimeMillis());
        convertContractEnumSimple(contractDo, openWorkflow, closeEsign);

        log.info("saving contract ...");
        // 如果当前合同是改签并且未开启工作流，并且当前合同是生效中，则旧合同状态改为终止且合同终止日期变更为改签后合同生效日期前一天
        if (null != openWorkflow && !openWorkflow && isChangeSign && StringUtil.isNotEmpty(lastContractDo.getBid())) {
            // 合同终止日期改完当前合同前一天
            lastContractDo.doLastContractChangeSign(contractDo.getStartDate() - DAY, newSignType);
            contractDomainService.update(lastContractDo);
        }

        contractDo.setLaunchDate(Optional.ofNullable(contractDo.getLaunchDate()).orElse(DateUtil.getCurrentTimestamp()));
        //合同编号生成规则，没有则按照自动规则生成
        contractDo.setContractNo(ruleSetAutoDomainService.nextContractNo(contractDo.getContractNo(), contractDo.getContractNo()));
        contractDomainService.save(contractDo);

        log.info("empId-->{},contractDomainService.save success", contractDo.getOwner().getEmpId());

        //开启工作流
        try {
            if (null != openWorkflow && openWorkflow) {
                if (SignTypeEnum.NEW.getCode().equals(contractDo.getSignType().getValue())) {
                    contractOpenWorkflow(contractDo, ContractSignTypeConstant.CONTRACT_NEW_SIGN);
                }
                if (SignTypeEnum.CHANGE.getCode().equals(contractDo.getSignType().getValue())) {
                    contractOpenWorkflow(contractDo, ContractSignTypeConstant.CONTRACT_AMENDMENT);
                }
                if (SignTypeEnum.RENEW.getCode().equals(contractDo.getSignType().getValue())) {
                    ContractConfig config = contractConfigService.one(ContractApplyType.RENEW);
                    contractOpenWorkflow(contractDo, config !=null ? "CONTRACTRENEW-"+config.getBid() : ContractSignTypeConstant.CONTRACT_RENEW);
                }
            }
        } catch (Exception e) {
            log.error("doSaveContract workFlow error :{},{}", e.getMessage(), e);
            //回退审批单据
            contractDomainService.delete(contractDo.getBid());
            if (StringUtil.isNotEmpty(rollBack.getBid())) {
                contractDomainService.update(rollBack);
            }
            throw new ServerException(e.getMessage());
        }

        String userId = UserContext.getUserId();
        Long nowDate = System.currentTimeMillis();
        contractDo.setCreateBy(userId);
        contractDo.setUpdateBy(userId);
        contractDo.setUpdateTime(nowDate);
        contractDo.setCreateTime(nowDate);
        contractDo.setDeleted(false);
        ContractMessageDto messageDto = ObjectConverter.convert(contractDo, ContractMessageDto.class);
        messageDto.setTenantId(UserContext.getTenantId());
        messageDto.setEmp(contractDo.getOwner());
        messageDto.setUserId(Long.valueOf(userId));
        messageDto.setContract(contractDo.getBid());
        messageDto.setSignType(contractDo.getSignType());

        // 触发合同新签以及续签通知模板
        contractPublish.startContractPublishMsg(messageDto);

        if (null != openWorkflow && openWorkflow) {
            if (SignTypeEnum.RENEW.getCode().equals(newSignType)) {
                if (!Objects.equals(contractDo.getEndDate(), contractDo.getCalcEndDate())) {
                    val dataSimple = new DataSimple();
                    dataSimple.setIdentifier("entity.hr.ContractRenewEndTimeChangeSignal");
                    dataSimple.getProperties().add("contractBid", contractDo.getBid());
                    DataInsert.identifier("entity.hr.ContractRenewEndTimeChangeSignal").insert(dataSimple);
                }
            }
            return;
        }
        // 合同归档事件触发
        archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT, contractDo.getBid(),
                ArchivePolicy.ADD_UPDATE);
        // 不走工作流，立即审批通过的合同
        contractEffective(messageDto, contractDo, closeEsign, empWorkInfo, lastContractDo, dataTime);
        if (SignTypeEnum.RENEW.getCode().equals(newSignType)) {
            if (!Objects.equals(contractDo.getEndDate(), contractDo.getCalcEndDate())) {
                sendRenewEndDateChangedMsg(contractDo.getOwner().getEmpId());
            }
        }
    }

    public void sendRenewEndDateChangedMsg(String empId) {
        messageService.sendMsgNoticeEvent(NoticeType.CONTRACT_RENEW_END_TIME_CHANGE,
                Lists.list(empId),
                null, "contract", 0);
    }

    /**
     * 无审批立即生效的当前合同和未来合同
     */
    public void contractEffective(ContractMessageDto messageDto,
                                  ContractDo contractDo, Boolean closeEsign, EmpWorkInfoDo empWorkInfo, ContractDo lastContractDo, Long dataTime) {
        // 流程通过的，流转到电子签待发起列表
        String empId = messageDto.getEmp().getEmpId();
        //支持未来员工新增合同及合同续签等
        if (dataTime == null) {
            dataTime = DateUtil.getCurrentTimestamp();
        }
        EmpBasicInfoDo basicInfo = empBasicInfoDo.getEmpBasicInfo(empId, dataTime);
        messageDto.setPhone(basicInfo.getPhone());
        messageDto.setCompanyEmail(basicInfo.getCompanyEmail());
        // 查询个人信息
        EmpPrivateInfoDo empPrivateInfo = empPrivateInfoDomainService.getByEmpId(empId);
        if (empPrivateInfo != null) {
            messageDto.setDisability(empPrivateInfo.getDisability());
            messageDto.setGuardianName(empPrivateInfo.getGuardianName());
            messageDto.setGuardianPhone(empPrivateInfo.getGuardianPhone());
            messageDto.setGuardianEmail(empPrivateInfo.getGuardianEmail());
            // 国籍
            messageDto.setNationality(empPrivateInfo.getNationality());
        }

        if (null == closeEsign || !closeEsign) {
            contractPublish.contractInitiated(messageDto);
        }

        if (null == empWorkInfo) {
            empWorkInfo = empWorkInfoDomainService.getEmpWorkInfo(empId, dataTime);
        }

        if (null == empWorkInfo || StringUtil.isEmpty(empWorkInfo.getEmpId())) {
            return;
        }

        if (null == contractDo.getStartDate() || contractDo.getStartDate() > DateUtil.getCurrentTimestamp()) {
            return;
        }

        // 补全职务信息
        messageDto.setJob(empWorkInfo.getJob());
        messageDto.setJobTxt(empWorkInfo.getJobTxt());
        messageDto.setWorkHour(empWorkInfo.getWorkHour());
        messageDto.setWorkplace(empWorkInfo.getWorkplace());
        messageDto.setWorkplaceTxt(empWorkInfo.getWorkplaceTxt());

        //【合同公司、工时制、工作地】发生变更，且审核通过，则根据合同开始日期更新员工任职信息上的【合同公司、工时制、工作地】数据
        log.info("saveContract ContractDo:{}, lastContractDo:{}", FastjsonUtil.toJson(contractDo), FastjsonUtil.toJson(lastContractDo));
        if (lastContractDo != null && StringUtil.isNotEmpty(lastContractDo.getBid())) {
            if (contractDo.getWorkHour() != null && lastContractDo.getWorkHour() != null && StringUtils.isNotEmpty(contractDo.getWorkHour().getValue())
                    && !contractDo.getWorkHour().getValue().equals(lastContractDo.getWorkHour().getValue())) {
                empWorkInfo.setWorkHour(contractDo.getWorkHour());
            }
            if (contractDo.getWorkplace() != null && !contractDo.getWorkplace().equals(lastContractDo.getWorkplace())) {
                doWorkPlace(contractDo.getWorkplace(), empWorkInfo);
            }
        }

        // 审批通过的修改，且合同公司有编号的，修改合同公司
        // 将员工信息->合同编辑为未生效状态，员工任职信息处的合同公司需要清空
        if (ContractStatusEnum.IN_EFFECTIVE.getIndex().equals(contractDo.getContractStatus().getValue())) {
            contractDo.setCompany("");
            contractDo.setCompanyTxt("");
        }
        empWorkInfoDomainService.updateContractCompany(empWorkInfo, contractDo.getCompany(),
                contractDo.getCompanyTxt(), contractDo.getProbation(), contractDo.getProbationPeriodEndDate(), contractDo.getStartDate());
    }

    private void doOrganizeInfo(ContractDo contractData, EmpWorkInfoDo empWorkInfo, Long dataTime) {
        String organize = contractData.getOrganize() == null ?
                (empWorkInfo != null ? empWorkInfo.getOrganize() : null)
                : contractData.getOrganize();
        if (null == organize) {
            return;
        }

        // 组织编码值补填
        OrgDo orgById = orgService.getOrgById(organize, dataTime);
        if (null == orgById) {
            return;
        }

        contractData.setOrganize(orgById.getBid());
        contractData.setOrganizeCode(orgById.getCode());
        contractData.setOrganizeTxt(orgById.getName());
    }

    private void doWorkPlace(String workplace, EmpWorkInfoDo empWorkInfo) {
        empWorkInfo.setWorkplace(workplace);
        WorkplaceDo workplaceData = workplaceService.getWorkplaceById(workplace);
        empWorkInfo.setWorkplaceTxt((null == workplaceData || StringUtil.isEmpty(workplaceData.getName()))
                ? empWorkInfo.getWorkplaceTxt() : workplaceData.getName());
    }

    private void contractOpenWorkflow(ContractDo data, String code) {
        log.info("contractOpenWorkflow ContractDo:{}", FastjsonUtil.toJson(data));
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        workflowDto.setFuncCode(code);
        workflowDto.setApplicantId(data.getOwner().getEmpId());
        workflowDto.setBusinessId(data.getBid());
        workflowDto.setApplicantName(data.getOwner().getName());
        // 业务单据事件时间
        workflowDto.setEventTime(data.getSignDate() != null ? data.getSignDate() : System.currentTimeMillis());
        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("contractOpenWorkflow beginWorkflow err,{}", e.getMessage(), e);
        }
        log.info("contractOpenWorkflow wfResult:{}", FastjsonUtil.toJson(wfResult));
        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());
    }


    /**
     * 合同管理枚举状态转换
     *
     * @param contractDo
     * @param openWorkflow
     * @param closeEsign   关闭电子签署流程
     */
    public void convertContractEnumSimple(ContractDo contractDo, Boolean openWorkflow, Boolean closeEsign) {
        //审批状态
        EnumSimple simple = new EnumSimple();
        simple.setValue(String.valueOf(openWorkflow ? ApprovalStatusEnum.IN_APPROVAL.getIndex() : ApprovalStatusEnum.PASSED.getIndex()));
        contractDo.setApprovalStatus(simple);
        //合同状态
        contractDo.calcStatus();
        //签署流程状态
        if (!openWorkflow) {
            EnumSimple simpleSignProcess = new EnumSimple();
            simpleSignProcess.setValue(String.valueOf(ContractStatusEnum.IN_EFFECTIVE.getIndex()));
            contractDo.setSignProcessStatus(simpleSignProcess);
        }

        // 如果关闭了电子签署，则设置电子签发起状态为已发起，目的是控制页面不允许发起电子签署
        if (null != closeEsign && closeEsign) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(InitiateStatus.signing.getValue());
            contractDo.setInitiateStatus(enumSimple);
        }
    }

    public void checkInoperativeContract(ContractDo contractDo) {
        boolean flag = true;
        List<ContractDo> list = getInoperativeContract(contractDo);
        if (CollectionUtils.isNotEmpty(list) && list.size() > 0) {
            flag = false;
        }
        PreCheck.preCheckArgument(!flag, LangUtil.getMsg(MsgCodeConstant.INACTIVE_CONTRACT));
    }

    public void checkUnderApprovalContract(ContractDo contractDo, boolean isExcludeSelf) {
        boolean flag = true;
        List<ContractDo> list = getUnderApprovalContract(contractDo, isExcludeSelf);
        if (CollectionUtils.isNotEmpty(list) && list.size() > 0) {
            flag = false;
        }
//        PreCheck.preCheckArgument(!flag, LangUtil.getMsg(MsgCodeConstant.IN_APPROVAL_CONTRACT));
        PreCheck.preCheckArgument(!flag, "该员工存在审批中的合同单据，无法进行合同信息的修改，请稍后操作!");
    }

    public void checkInoperativeContract(List<ContractDo> contractDo) {
        List<ContractDo> list = getInoperativeContract(contractDo);
        // if (CollectionUtils.isEmpty(list)) {
        //     return;
        // }
        // throw new ServerException(LangUtil.getFormatMsg(MsgCodeConstant.BATCH_INACTIVE_CONTRACT, list.get(0).getOwner().getName()));
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(list), LangUtil.getMsg(MsgCodeConstant.INACTIVE_CONTRACT));
    }

    public void checkUnderApprovalContract(List<ContractDo> contractDo, boolean isExcludeSelf) {
        List<ContractDo> list = getUnderApprovalContract(contractDo, isExcludeSelf);
        // if (CollectionUtils.isEmpty(list)) {
        //     return;
        // }
        // throw new ServerException(LangUtil.getFormatMsg(MsgCodeConstant.BATCH_IN_APPROVAL_CONTRACT, list.get(0).getOwner().getName()));
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(list), LangUtil.getMsg(MsgCodeConstant.IN_APPROVAL_CONTRACT));
    }

    /**
     * 获取员工未生效的合同的数据
     *
     * @param contractDo
     * @return
     */
    public List<ContractDo> getInoperativeContract(ContractDo contractDo) {
        // List<ContractDo> list = contractDomainService.getInoperativeContract(Arrays.asList(contractDo.getOwner().getEmpId()),ApprovalStatusEnum.PASSED.getIndex().toString()
        //         ,ContractStatusEnum.IN_EFFECTIVE.getIndex());
        return getInoperativeContract(Arrays.asList(contractDo));
    }

    /**
     * 批量获取员工未生效的合同的数据
     *
     * @param contractDos
     * @return
     */
    public List<ContractDo> getInoperativeContract(List<ContractDo> contractDos) {
        List<ContractDo> list = contractDomainService.getInoperativeContract(contractDos.stream()
                        .map(contract -> contract.getOwner().getEmpId())
                        .collect(Collectors.toList()), ApprovalStatusEnum.PASSED.getIndex().toString()
                , ContractStatusEnum.IN_EFFECTIVE.getIndex());
        return list;
    }

    /**
     * 获取员工审批中的合同的数据
     *
     * @param contractDo
     * @return
     */
    public List<ContractDo> getUnderApprovalContract(ContractDo contractDo, boolean isExcludeSelf) {
        return getUnderApprovalContract(Arrays.asList(contractDo), isExcludeSelf);
    }

    /**
     * 批量获取员工审批中的合同的数据
     *
     * @param contractDos
     * @return
     */
    public List<ContractDo> getUnderApprovalContract(List<ContractDo> contractDos, boolean isExcludeSelf) {
        List<ContractDo> list = contractDomainService.getInoperativeContract(contractDos.stream()
                .map(contract -> contract.getOwner().getEmpId())
                .collect(Collectors.toList()), ApprovalStatusEnum.IN_APPROVAL.getIndex().toString(), "");
        if (null != list && isExcludeSelf) {
            List<String> selfList = contractDos.stream().map(cd -> cd.getBid()).collect(Collectors.toList());
            return list.stream().filter(cd -> !selfList.contains(cd.getBid())).collect(Collectors.toList());
        }
        return list;
    }

    /**
     * 根据合同时间判断状态
     *
     * @param contractDo
     */
    @Deprecated
    public void contractStatus(ContractDo contractDo) {
        // 合同状态
        Long startDate = contractDo.getStartDate();
        Long endDate = contractDo.getEndDate();
        EnumSimple simple = new EnumSimple();
        try {
            Long nowDate = DateUtil.convert(DateUtil.formatDate(System.currentTimeMillis()), "yyyy-MM-dd");
            // 当前时间<合同开始时间 (合同未生效)
            if (nowDate < startDate) {
                simple.setValue(String.valueOf(ContractStatusEnum.IN_EFFECTIVE.getIndex()));
                contractDo.setContractStatus(simple);
            }
            // 合同结束时间>=当前时间>=合同开始时间 (合同生效中)
            if (endDate >= nowDate && nowDate >= startDate) {
                simple.setValue(String.valueOf(ContractStatusEnum.EFFECTIVE.getIndex()));
                contractDo.setContractStatus(simple);
            }
            // 当前时间>合同开始时间 (合同失效)
            if (nowDate > endDate) {
                simple.setValue(String.valueOf(ContractStatusEnum.INVALID.getIndex()));
                contractDo.setContractStatus(simple);
            }
        } catch (Exception e) {
            log.error("Troubleshoot the abnormal contract status.{}", e.getMessage(), e);
        }
    }

    public String getTenantName() {
        String tenantInfoCacheKey = String.format(CacheKeyConstant.TENANT_INFO_KEY, UserContext.getTenantId());
        String tenantInfo = cacheService.getValue(tenantInfoCacheKey);
        if (org.apache.commons.lang.StringUtils.isNotBlank(tenantInfo)) {
            TenantConfigDto tenantConfigDto = FastjsonUtil.toObject(tenantInfo, TenantConfigDto.class);
            return tenantConfigDto.getName();
        }

        return UserContext.getTenantId();
    }

    public List<ContractDo> batchRenewal(BatchRenewalDto renewalDto) {
        List<ContractDo> contractDos = new ArrayList<>();
        List<EmpSimple> empList = renewalDto.getEmpList();
        if (null == empList || empList.isEmpty()) {
            return contractDos;
        }

        List<String> empIds = empList.stream().filter(emp -> StringUtil.isNotEmpty(emp.getEmpId()))
                .map(EmpSimple::getEmpId).distinct().collect(Collectors.toList());
        if (empList.size() != empIds.size()) {
            return contractDos;
        }

        // 批量发起不能超过100人
        PreCheck.preCheckArgument(empList.size() > BATCH_RENEWAL_LIMIT, LangUtil.getMsg(MsgCodeConstant.BATCH_RENEWAL_MAX_THRESHOLD));

        ContractTypeSetDo contractTypeSetDo = contractTypeSetService.getById(renewalDto.getContractSetId());
        if (null == contractTypeSetDo || StringUtil.isEmpty(contractTypeSetDo.getBid())) {
            return contractDos;
        }

        ContractSetConditionDo conditionDo = contractSetConditionDomainService.getByTypeSetBid(renewalDto.getContractSetId());
        if (null == conditionDo || StringUtil.isEmpty(conditionDo.getBid())) {
            return contractDos;
        }

        Map<String, EmpWorkInfoDo> workInfoMap = new HashMap<>();
        if (conditionDo.getBaseExpectGraduateDate() || conditionDo.getBaseRetireDate()) {
            List<EmpWorkInfoDo> workInfoList = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
            for (EmpWorkInfoDo workInfoDo : workInfoList) {
                workInfoMap.put(workInfoDo.getEmpId(), workInfoDo);
            }
        }
        CompanyDo company = companyDomainService.selectById(renewalDto.getCompany());

        List<ContractDo> latestList = lastContractDomainService.getByEmp(empIds);

        // 校验员工是否能续签
        batchCheckContract(latestList);

        // 续签合同的合同开始日期
        Long newStartDate = null;
        Boolean openWorkflow = renewalDto.getOpenWorkflow();
        long signDate = System.currentTimeMillis();
        for (ContractDo contract : latestList) {
            contract.setBid(null);
            contract.setId(null);
            // 下一份合同开始时间为上一份合同开始时间+1
            newStartDate = contract.getEndDate() + DAY;
            contract.setStartDate(newStartDate);
            contract.setContractTypeSet(renewalDto.getContractSetId());
            contract.setContractTypeSetTxt(contractTypeSetDo.getContractType().getText());
            contract.setContractType(contractTypeSetDo.getContractClass());
            contract.setPeriodType(conditionDo.getPeriodType());
            contract.setContractPeriod(renewalDto.getContractPeriod());
            contract.setProbationPeriod(conditionDo.getProbationPeriod());
            contract.setSignDate(signDate);
            contract.setAttachFile(null);
            contract.setRemark(renewalDto.getRemark());
            contract.setContractNo(ruleSetAutoDomainService.nextContractNo());
            contract.setSignTime(null == contract.getSignTime() ? 1 : contract.getSignTime() + 1);
            contract.setCompany(company.getBid());
            contract.setCompanyTxt(company.getCompanyName());
            EnumSimple simpleType = new EnumSimple();
            simpleType.setValue("0");
            contract.setSignType(simpleType);
            // 有固定期限
            EmpWorkInfoDo empWorkInfo = workInfoMap.get(contract.getOwner().getEmpId());
            if (null == renewalDto.getContractPeriod()) {
                // 无固定期限 9999
                contract.setEndDate(253402185600000L);
                saveContract(contract, openWorkflow, empWorkInfo);
                contractDos.add(contract);
                continue;
            }

            contract.setEndDate(contractDomainService.calcEndDate(conditionDo, empWorkInfo, newStartDate, renewalDto.getContractPeriod()));
            saveContract(contract, openWorkflow, empWorkInfo);
            contractDos.add(contract);
        }
        return contractDos;
    }

    private void batchCheckContract(List<ContractDo> latestList) {
        // 存在未生效合同，则无法续签
        checkInoperativeContract(latestList);
        // 员工存在审批中的合同，则无法续签
        checkUnderApprovalContract(latestList, false);
    }

    @Deprecated
    private Long calEndDate(Long newStartDate, Integer contractPeriod) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(newStartDate);
        calendar.add(Calendar.MONTH, contractPeriod);
        return calendar.getTimeInMillis();
    }

    public List<ContractDo> getContractByContractNo(List<String> contractNos) {
        return contractDomainService.getContractByContractNo(contractNos);
    }

    public List<ContractDo> getContractByWorkNo(List<String> workNos) {
        return contractDomainService.getContractByWorkNo(workNos);
    }

    @Deprecated
    public Long calEndDate(ContractSetConditionDo conditionDo, Long newStartDate, Integer contractPeriod, EmpWorkInfoDo empWorkInfo) {
        if (newStartDate == null || contractPeriod == null) {
            return 0L;
        }
        Long endDate = DateUtil.timeCalculate(DateUtil.formatDate(newStartDate), null, null, contractPeriod, -1);
        // endDateType :0：默认  1： 结束月最后一天 2：季度末最后一天
        if (conditionDo != null) {
            if (EndDateTypeEnum.DEFAULT.getCode().equals(conditionDo.getEndDateType().getValue())) {

            }
            if (EndDateTypeEnum.MONTH.getCode().equals(conditionDo.getEndDateType().getValue())) {
                Calendar calendar = new GregorianCalendar();
                calendar.setTime(new Date(endDate));
                // 获得本月最后一天
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = calendar.getTime().getTime();
            }
            if (EndDateTypeEnum.SEASON.getCode().equals(conditionDo.getEndDateType().getValue())) {
                Calendar endCalendar = Calendar.getInstance();
                endCalendar.setTime(new Date(endDate));
                //计算季度数：由于月份从0开始，即1月份的Calendar.MONTH值为0,所以计算季度的第三个月份只需 月份 / 3 * 3 + 2
                endCalendar.add(Calendar.MONTH, (endCalendar.get(Calendar.MONTH) / 3) * 3 + 2 - endCalendar.get(Calendar.MONTH));
                endCalendar.set(Calendar.DAY_OF_MONTH, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                endDate = endCalendar.getTime().getTime();
            }
            //caidao - 1959 合同结束日期 增加 年度最后有一天；
            //年度最后一天操作；
            if (EndDateTypeEnum.YEAR.getCode().equals(conditionDo.getEndDateType().getValue())) {
                //年度最后一天
                Calendar currCal = Calendar.getInstance();
                int currentYear = currCal.get(Calendar.YEAR);
                Calendar endYearcalendar = Calendar.getInstance();
                endYearcalendar.clear();
                endYearcalendar.set(Calendar.YEAR, currentYear);
                endYearcalendar.roll(Calendar.DAY_OF_YEAR, -1);
                long currYearLast = endYearcalendar.getTime().getTime();
                endDate = currYearLast;
            }
            /*
               是否根据预计毕业日期判断
               历史数据中，baseExpectGraduateDate可能为null
             */
            if (Optional.ofNullable(conditionDo.getBaseExpectGraduateDate()).orElse(false)) {
                // 用工类型是实习生
                if (empWorkInfo != null && BaseEmpType.getInterns().stream().map(BaseEmpType::getCode).collect(Collectors.toList())
                        .contains(empWorkInfo.getEmpType().getCode())) {
                    return endDate >= empWorkInfo.getExpectGraduateDate() ? empWorkInfo.getExpectGraduateDate() : endDate;
                }
            }
            if (Optional.ofNullable(conditionDo.getBaseRetireDate()).orElse(false)) {
                // 校验退休日期
                if (empWorkInfo != null && empWorkInfo.getRetireDate() != null) {
                    return Math.min(empWorkInfo.getExpectGraduateDate(), endDate);
                }
            }
        }
        return endDate;
    }

    public void countSignTime() {
        int pageNo = 1;
        ContractQueryDto queryDto = new ContractQueryDto();
        queryDto.setPageSize(COUNT_SIZE);
        PageResult<ContractDto> contractPage;
        do {
            queryDto.setPageNo(pageNo);
            contractPage = lastContractDomainService.getContractPage(queryDto);
            List<String> contractIds = new ArrayList<>(contractPage.getItems().size());
            List<String> empIds = new ArrayList<>(contractPage.getItems().size());
            for (ContractDto contract : contractPage.getItems()) {
                empIds.add(contract.getOwner().getEmpId());
                contractIds.add(contract.getBid());
            }
            Map<String, String> collect = contractDomainService.selectByIds(contractIds).stream().collect(Collectors.toMap(ContractDo::getBid, ContractDo::getBid));
            Map<String, Long> countResults = contractDomainService.countSignTimes(empIds);
            contractPage.getItems().stream().filter(contact -> countResults.containsKey(contact.getOwner().getEmpId()) && collect.containsKey(contact.getBid())).forEach(contract -> {
                int newSignTime = Math.toIntExact(countResults.getOrDefault(contract.getOwner().getEmpId(), 0L));
                // 签订次数没有变化
                if (ObjectUtil.nullSafeEquals(contract.getSignTime(), newSignTime)) {
                    return;
                }
                contract.setSignTime(newSignTime);
                contractDomainService.update(ObjectConverter.convert(contract, ContractDo.class));
            });
        } while (pageNo++ * COUNT_SIZE < contractPage.getTotal());
    }

    public Map<String, EmpWorkInfoDo> getEmpWorkInfoMap(List<String> empList) {
        Map<String, EmpWorkInfoDo> map = new HashMap<>();
        if (null == empList || empList.isEmpty()) {
            return map;
        }

        List<EmpWorkInfoDo> list = empWorkInfoDomainService.getEmpListByEmpIds(empList, System.currentTimeMillis());
        if (null == list || list.isEmpty()) {
            return map;
        }

        map = list.stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, prop -> prop, (k1, k2) -> k1));
        return map;
    }

    public PageResult<ContractDto> getLastList(ContractQueryDto queryDto) {
        AuthScopeFilterUtil.put(true);
        return lastContractDomainService.getEmpContractPage(queryDto);
    }

    public PageResult getSignedContractPage(ContractQueryDto queryDto) {
        var pageResult = getLastList(queryDto);
        long nowDateTime = DateUtil.getCurrentTimestamp();
        final Map<String, String> statusMap = new HashMap<>();
        List<ContractVo> newContractList = pageResult.getItems().stream().map(data -> {
            ContractVo vo = ObjectConverter.convert(data, ContractVo.class);
            if (vo.getEndDate() != null) {
                Long num = vo.getEndDate() - nowDateTime;
                long lastDay = num / 86400000;
                vo.changeStatus(lastDay, statusMap);
                vo.setContractDays(String.valueOf(lastDay));
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageResult(newContractList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }


    public PageResult<ContractVo> getContractPage(ContractQueryDto queryDto) {
        AuthScopeFilterUtil.put(true);
        PageResult<ContractDo> pageResult = contractDomainService.selectRecordPage(queryDto);
        //处理合同数据
        List<ContractVo> newContractList = initDataPageList(pageResult);
        return new PageResult(newContractList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    private List<ContractVo> initDataPageList(PageResult<ContractDo> pageResult) {
        long nowDateTime = DateUtil.getCurrentTimestamp();
        final Map<String, String> statusMap = new HashMap<>();
        boolean isZh = LangUtil.chineseLocale();
        List<String> linkContractIds = pageResult.getItems().stream().map(ContractDo::getBid).collect(Collectors.toList());
        Map<String, String> linkContractMap = Optional.of(linkContractIds)
                .filter(CollectionUtils::isNotEmpty)
                .map(it -> contractDomainService.getLinkContracts(linkContractIds).stream()
                        .collect(Collectors.toMap(ContractDo::getLastContract, ContractDo::getBid, (v1, v2) -> v1)))
                .orElse(Maps.newHashMap());
        List<ContractVo> voList = BeanUtil.convertList(pageResult.getItems(), ContractVo.class);
        voList.forEach(vo -> {
            vo.convertEmpStatus(isZh);
            String linkContract = linkContractMap.getOrDefault(vo.getBid(), null);
            vo.setHandled(StringUtils.isNotEmpty(linkContract) && !linkContract.equals(vo.getBid()));
            if (vo.getEndDate() != null) {
                Long num = vo.getEndDate() - nowDateTime;
                long lastDay = num / 86400000;
                vo.changeStatus(lastDay, statusMap);
                vo.setContractDays(String.valueOf(lastDay));
            }
            String postTxt = vo.getPostTxt();
            if(!"enabled".equals(postTxtShowCode)){
                if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    vo.setPostTxt(postTxt);
                }
            }

        });
        return voList;
    }


    public Optional<ContractDo> getLastContractByBid(String bid) {
        if (StringUtils.isBlank(bid)) {
            return Optional.empty();
        }
        var result = contractDomainService.getById(bid);
        if (result == null) {
            return Optional.empty();
        }
        return Optional.of(result);
    }

    /**
     * 新签合同列表
     *
     * @param queryDto
     * @return
     */
    public PageResult<EmpWorkInfoNewlySignedVo> newlySignedList(ContractQueryDto queryDto) {
        AuthScopeFilterUtil.put(true);
        PageResult<Map<String, String>> dataMapList = lastContractDomainService.newlySignedList(queryDto);
        List<EmpWorkInfoNewlySignedVo> workInfoNewlySignedVoList = dataMapList.getItems().stream().map(data -> {
            EmpWorkInfoNewlySignedVo empWorkInfoNewlySignedVo = FastjsonUtil.convertObject(data, EmpWorkInfoNewlySignedVo.class);
            String empStatus = data.get("empStatus");
            //员工状态
            empWorkInfoNewlySignedVo.setEmpStatusTxt(StringUtil.isNotEmpty(empStatus) ? EmpStatusEnum.getName(Integer.valueOf(data.get("empStatus"))) : "");
            //员工类型
            empWorkInfoNewlySignedVo.setEmpTypeTxt(data.get("empType.dict.text"));
            //入职日期
            empWorkInfoNewlySignedVo.setHireDate(StringUtil.isNotEmpty(data.get("hireDate")) ? DateUtil.formatDate(Long.parseLong(data.get("hireDate"))) : "");

            if(!"enabled".equals(postTxtShowCode)){
                String postTxt = empWorkInfoNewlySignedVo.getPostTxt();
                if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    empWorkInfoNewlySignedVo.setPostTxt(postTxt);
                }
            }

            return empWorkInfoNewlySignedVo;
        }).collect(Collectors.toList());
        return new PageResult<>(workInfoNewlySignedVoList, dataMapList.getPageNo(), dataMapList.getPageSize(), dataMapList.getTotal());
    }

    public void updateProcessStatus(String contractId, String processStatus) {
        ContractDo data = contractDomainService.getById(contractId);
        if (data == null) {
            log.warn("contract '{}' does not exist", contractId);
            return;
        }
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(processStatus);
        data.setSignProcessStatus(enumSimple);

        // 更新发起签署状态
        enumSimple = new EnumSimple();
        enumSimple.setValue(InitiateStatus.signing.getValue());
        data.setInitiateStatus(enumSimple);
        contractDomainService.update(data);
    }


    /**
     * 批量自动续签校验
     *
     * @param empIds
     * @return
     */
    public List<BatchAutoRenewalDto> batchAutoRenewalCheck(List<String> empIds) {
        List<BatchAutoRenewalDto> list = contractDomainService.batchAutoRenewalCheck(empIds);
        log.info("after renewal check >>>>>>>>>>{}", list);
        return list;
    }

    /**
     * 批量自动发起新签
     *
     * @param empIds
     * @return
     */
    public List<BatchAutoRenewalDto> batchAutoNewlySignedCheck(List<String> empIds) {
        List<BatchAutoRenewalDto> list = contractDomainService.batchAutoNewlySignedCheck(empIds);
        log.info("after NewlySigned check >>>>>>>>>>{}", list);
        return list;
    }

    /**
     * 批量自动续签
     *
     * @param empIds
     * @return
     */
    public void batchAutoRenewal(List<String> empIds) {
        // 查询对应的合同设置
        List<BatchAutoRenewalDto> renewalList = batchAutoRenewalCheck(empIds);
        PreCheck.preCheckArgument(empIds.size() > renewalList.size(), LangUtil.getMsg(SELECTED_DATA_MAY_HAVE_CHANGED));
        if (empIds.isEmpty()) {
            log.warn("input param empIds is empty");
            return;
        }

        empIds = renewalList.stream().map(BatchAutoRenewalDto::getEmpId).collect(Collectors.toList());
        // 查询上一份合同
        List<ContractDo> contractList = lastContractDomainService.getByEmp(empIds);
        // 查询任职信息
        Map<String, EmpWorkInfoDo> workInfoMap = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis()).stream()
                .collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, obj -> obj, (A, B) -> A));
        Map<String, BatchAutoRenewalDto> renewalMap = renewalList.stream().collect(Collectors.toMap(BatchAutoRenewalDto::getEmpId, obj -> obj, (A, B) -> A));
        //工作流开关
        boolean flowSwitch = ruleSetService.getWorkFlowSwitch(ContractSignSceneEnum.BATCH_RENEW.name());
        //续签配置校验是否开启
        ContractConfig config = contractConfigService.one(ContractApplyType.RENEW);
        checkContractSignWorkFlow(config != null ? "CONTRACTRENEW-"+config.getBid() : ContractSignTypeConstant.CONTRACT_RENEW, flowSwitch);
        log.info("batch auto renewal start");
        long signDate = System.currentTimeMillis();
        for (ContractDo contract : contractList) {
            String empId = contract.getOwner().getEmpId();
            contract.setBid(null);
            contract.setId(null);
            contract.setStartDate(contract.getEndDate() + DAY);

            BatchAutoRenewalDto renewalDto = renewalMap.get(empId);
            BeanUtil.copyProperties(renewalDto, contract);

            contract.setSignDate(signDate);
            contract.setAttachFile(null);
            contract.setRemark(null);
            contract.setContractNo(ruleSetAutoDomainService.nextContractNo());
            contract.setSignTime(null == contract.getSignTime() ? 1 : contract.getSignTime() + 1);

            //签订类型(默认是续签)
            EnumSimple simpleType = new EnumSimple();
            simpleType.setValue("0");
            contract.setSignType(simpleType);

            contract.setEndDate(contractDomainService.calcEndDate(renewalDto, workInfoMap.get(empId), contract.getStartDate()));
            // 批量自动续签默认开启工作流
            saveContract(contract, flowSwitch, workInfoMap.get(empId));
        }
    }

    private void checkContractSignWorkFlow(String funCode, boolean flowSwitch) {
        if (flowSwitch) {
            Result<Boolean> result = workFlowFeignClient.checkDefEnabled(funCode);
            if (result.isSuccess() && result.getData() != null) {
                PreCheck.preCheckArgument(!result.getData(), LangUtil.getMsg(WORK_FLOW_CONFIG_NOT_ENABLE));
            }
        }
    }

    /**
     * 批量自动新签
     *
     * @param empIds
     * @return
     */
    public void batchAutoNewlySigned(List<String> empIds) {
        // 查询对应的合同设置
        List<BatchAutoRenewalDto> renewalList = batchAutoNewlySignedCheck(empIds);
        PreCheck.preCheckArgument(empIds.size() > renewalList.size(), LangUtil.getMsg(SELECTED_DATA_MAY_HAVE_CHANGED));
        if (empIds.isEmpty()) {
            log.warn("input param empIds is empty");
            return;
        }
        empIds = renewalList.stream().map(BatchAutoRenewalDto::getEmpId).collect(Collectors.toList());

        // 查询任职信息
        List<EmpWorkInfoDo> empListByEmpIds = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
        Map<String, EmpWorkInfoDo> workInfoMap = empListByEmpIds.stream()
                .collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, obj -> obj, (A, B) -> A));
        Map<String, BatchAutoRenewalDto> renewalMap = renewalList.stream().collect(Collectors.toMap(BatchAutoRenewalDto::getEmpId, obj -> obj, (A, B) -> A));

        // 查询组织信息
        Map<String, OrgDo> orgInfoMap = orgService.selectOrgMapByIds(empListByEmpIds.stream().filter(empWorkInfoDo -> empWorkInfoDo.getOrganize() != null)
                .map(EmpWorkInfoDo::getOrganize).collect(Collectors.toList()), System.currentTimeMillis());
        //工作流统一开关
        boolean flowSwitch = ruleSetService.getWorkFlowSwitch(ContractSignSceneEnum.BATCH_NEW.name());
        //合同新签工作流配置是否开启
        checkContractSignWorkFlow(ContractSignTypeConstant.CONTRACT_NEW_SIGN, flowSwitch);
        log.info("batch auto renewal start");
        long signDate = System.currentTimeMillis();
        empIds.forEach(empId -> {
            ContractDo contract = new ContractDo();
            contract.setBid(null);
            contract.setId(null);
            //新签的合同入职日期作为合同开始日期
            contract.setStartDate(workInfoMap.get(empId).getHireDate());

            BatchAutoRenewalDto renewalDto = renewalMap.get(empId);
            BeanUtil.copyProperties(renewalDto, contract);
            //试用期期限
            contract.setProbation(renewalDto.getProbationDeadline());
            contract.setProbationPeriodEndDate(calculateProbationEnd(contract.getStartDate(), contract.getProbation()));
            contract.setSignDate(signDate);
            contract.setAttachFile(null);
            contract.setRemark(null);
            //启用合同编号自动生成规则
            contract.setContractNo(ruleSetAutoDomainService.nextContractNo());
            contract.setSignTime(null == contract.getSignTime() ? 1 : contract.getSignTime() + 1);
            contract.setEndDate(contractDomainService.calcEndDate(renewalDto, workInfoMap.get(empId), contract.getStartDate()));
            contract.setHireDate(workInfoMap.get(empId).getHireDate());

            // 组织信息
            String organize = workInfoMap.get(empId).getOrganize();
            contract.setOrganize(organize);
            contract.setOrganizeTxt(workInfoMap.get(empId).getOrganizeTxt());
            contract.setOrganizeCode(Optional.ofNullable(orgInfoMap.get(organize)).orElse(new OrgDo()).getCode());

            contract.setJob(workInfoMap.get(empId).getJob());
            contract.setJobTxt(workInfoMap.get(empId).getJobTxt());
            contract.setPost(workInfoMap.get(empId).getPost());
            contract.setPostTxt(workInfoMap.get(empId).getPostTxt());
            contract.setCompany(workInfoMap.get(empId).getCompany());
            contract.setCompanyTxt(workInfoMap.get(empId).getCompanyTxt());

            //员工状态
            EnumSimple empStatusSimple = new EnumSimple();
            empStatusSimple.setValue(workInfoMap.get(empId).getEmpStatus().getValue());
            empStatusSimple.setText(workInfoMap.get(empId).getEmpStatus().getText());
            contract.setEmpStatus(empStatusSimple);

            //用工类型
            DictSimple empTypeSimple = new DictSimple();
            empTypeSimple.setValue(workInfoMap.get(empId).getEmpType().getValue());
            empTypeSimple.setText(workInfoMap.get(empId).getEmpType().getText());
            empTypeSimple.setCode(workInfoMap.get(empId).getEmpType().getCode());
            contract.setEmpType(empTypeSimple);

            //签订类型(默认是新签)
            EnumSimple simpleType = new EnumSimple();
            simpleType.setValue("2");
            contract.setSignType(simpleType);

            //员工信息
            EmpSimple empSimple = new EmpSimple();
            empSimple.setName(workInfoMap.get(empId).getName());
            empSimple.setWorkno(workInfoMap.get(empId).getWorkno());
            empSimple.setEmpId(empId);
            empSimple.setEnName(workInfoMap.get(empId).getEnName());
            contract.setOwner(empSimple);

            // 批量自动新签默认不开启工作流
            saveContract(contract, flowSwitch, workInfoMap.get(empId));
        });
    }

    private Long calculateProbationEnd(Long startDate, EnumSimple probation) {
        return Optional.ofNullable(probation).filter(it -> StringUtil.isNotEmpty(it.getValue()))
                .map(it -> {
                    try {
                        Date endDate = new Date(startDate);
                        int probationValue = Integer.parseInt(it.getValue());
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(endDate);
                        calendar.add(Calendar.MONTH, probationValue);
                        return calendar.getTimeInMillis();
                    } catch (NumberFormatException e) {
                        log.error("calculateProbationEnd error, startDate: {}, probationValue: {}", startDate, probation);
                        return null;
                    }
                }).orElse(null);
    }

    public void invalidContractStatus(Long currentDay) {
        int pageNo = 1;
        PageResult<ContractDo> contractPage;
        ContractStatusQueryDto queryDto = new ContractStatusQueryDto();
        queryDto.setEndDate(currentDay);
        queryDto.setPageSize(COUNT_SIZE);
        queryDto.setContractStatus(ContractStatusEnum.EFFECTIVE.getIndex());
        queryDto.setApprovalStatus(ApprovalStatusEnum.PASSED.getIndex().toString());
        EnumSimple simple = new EnumSimple();
        do {
            queryDto.setPageNo(pageNo);
            contractPage = contractDomainService.getContractPage(queryDto);
            contractPage.getItems().forEach(contract -> {
                simple.setValue(ContractStatusEnum.INVALID.getIndex());
                contract.setContractStatus(simple);
                try {
                    contractDomainService.update(contract);
                } catch (Exception e) {
                    log.error("invalidContractStatus err, currentDay={}, contract={}, errMsg={}",
                            currentDay, FastjsonUtil.toJson(contract), e.getMessage(), e);
                }
            });
        } while (pageNo++ * COUNT_SIZE < contractPage.getTotal());
    }

    public void effectiveContractStatus(Long currentDay) {
        int pageNo = 1;
        PageResult<ContractDo> contractPage;
        ContractStatusQueryDto queryDto = new ContractStatusQueryDto();
        // queryDto.setStartDate(currentDay);
        // fix DEV-18967
        queryDto.setCurrentDay(currentDay);
        queryDto.setLastDay(currentDay - 30L * 24 * 60 * 60 * 1000);
        queryDto.setPageSize(COUNT_SIZE);
        queryDto.setContractStatus(ContractStatusEnum.IN_EFFECTIVE.getIndex());
        queryDto.setApprovalStatus(ApprovalStatusEnum.PASSED.getIndex().toString());
        EnumSimple simple = new EnumSimple();
        do {
            queryDto.setPageNo(pageNo);
            contractPage = contractDomainService.getContractPage(queryDto);
            contractPage.getItems().forEach(contract -> {
                simple.setValue(ContractStatusEnum.EFFECTIVE.getIndex());
                contract.setContractStatus(simple);
                try {
                    contractDomainService.update(contract);
                    empWorkInfoDomainService.updateContractCompany(contract.getCompany(), contract.getCompanyTxt(), contract.getOwner().getEmpId(), currentDay);
                } catch (Exception e) {
                    log.error("effectiveContractStatus err, currentDay={}, contract={}, errMsg={}",
                            currentDay, FastjsonUtil.toJson(contract), e.getMessage(), e);
                }
            });
        } while (pageNo++ * COUNT_SIZE < contractPage.getTotal());
    }

    public void updateLastContract(EmpContractDto dto) {
        ContractDo convert = ObjectConverter.convert(dto, ContractDo.class);
        List<ContractDo> byEmp = lastContractDomainService.getByEmp(Lists.list(dto.getOwner().getEmpId()));
        if (CollectionUtils.isNotEmpty(byEmp)) {
            ContractDo lastContract = byEmp.get(0);
            BeanUtil.copyWithNoValue(convert, lastContract);
            contractDomainService.onlyUpdate(lastContract);
        } else {
            //续签
            EnumSimple signTypeSimple = new EnumSimple();
            signTypeSimple.setValue("0");
            convert.setSignType(signTypeSimple);

            //审批通过
            EnumSimple approvalSimple = new EnumSimple();
            approvalSimple.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
            convert.setApprovalStatus(approvalSimple);

            //合同状态
            EnumSimple contractStatusSimple = new EnumSimple();
            contractStatusSimple.setValue("1");
            convert.setContractStatus(contractStatusSimple);
            contractDomainService.onlySave(convert);
        }
    }

    public String getNextContractNo(String contractNo) {
        return ruleSetAutoDomainService.nextContractNo(contractNo, contractNo);
    }

    /**
     * 校验合同开始时间、结束数据
     *
     * @param empId     员工id
     * @param signType  签订类型
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    public void checkContractDate(String empId, String signType, Long startDate, Long endDate) {
        List<ContractDo> contractList = lastContractDomainService.getByEmp(Lists.list(empId));
        if (contractList.isEmpty()) {
            return;
        }
        ContractDo contract = contractList.get(0);
        // 签订类型为null或新签
        SignTypeEnum st;
        if ((st = SignTypeEnum.getByCode(signType)) == null) {
            return;
        }
        // 开始时间、结束时间为null
        if (contract.getStartDate() == null || contract.getEndDate() == null || startDate == null || endDate == null) {
            return;
        }
        contractDomainService.checkContractDate(contract, st, startDate, endDate);
    }

    public void insertOrUpdateContract(ContractDto nowContractDto) {
        //合同校验
        ContractQueryDto queryDto = new ContractQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(100);
        queryDto.setEmpId(nowContractDto.getOwner().getEmpId());
        PageResult<ContractDo> page = contractDomainService.selectRecordPage(queryDto);
        List<ContractDo> contractDoList = page.getItems();
        Long currentTimeMillis = System.currentTimeMillis();

        // 合同编号生成规则，没有则按照自动规则生成
        nowContractDto.setContractNo(ruleSetAutoDomainService.nextContractNo(nowContractDto.getContractNo(), nowContractDto.getContractNo()));
        for (ContractDo contractDo : contractDoList) {
            // 排查同一份合同
            if (contractDo.getBid().equals(nowContractDto.getBid())
                    || null != contractDo.getTerminationDate()
                    || isApproved(contractDo)
                    || null != contractDo.getDissolveDate()) {
                continue;
            }
            //（1）如果已有未来开始日期的合同记录，编辑/新增当前合同结束日期和未来合同的开始日期存在交集时，提示：当前合同的结束日期不能超过下一份合同的开始日期；
            PreCheck.preCheckArgument(contractDo.getStartDate() != null && contractDo.getStartDate() > currentTimeMillis && nowContractDto.getEndDate() != null && nowContractDto.getEndDate() >= contractDo.getStartDate(), LangUtil.getMsg(CONTRACT_END_DATE_INVALID));
            //（2）如存在已失效的历史合同记录，编辑/新增当前合同，开始日期和已失效的历史合同的结束日期存在交集时，提示：当前合同的开始日期不能早于上一份合同的结束日期；
            PreCheck.preCheckArgument(ContractStatusEnum.INVALID.getIndex().equals(contractDo.getContractStatus().getValue()) && nowContractDto.getStartDate() != null
                    && contractDo.getEndDate() != null && nowContractDto.getStartDate() <= contractDo.getEndDate(), LangUtil.getMsg(CONTRACT_START_DATE_INVALID));
        }
        // 新增，编辑审批状态都为已通过
        EnumSimple approvalSimple = new EnumSimple();
        approvalSimple.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
        nowContractDto.setApprovalStatus(approvalSimple);
        // 合同公司名称
        // 查询公司下拉列表
        if (StringUtil.isNotEmpty(nowContractDto.getCompany())) {
            CompanyDo companyData = companyDomainService.selectById(nowContractDto.getCompany());
            nowContractDto.setCompanyTxt(companyData.getCompanyName());
        }
        ContractDo convertContract = ObjectConverter.convert(nowContractDto, ContractDo.class);
        //时间轴时间
        Long dateTime = nowContractDto.getDateTime();
        EmpWorkInfoDo empWorkInfoData = empWorkInfoDomainService.getEmpWorkInfo(nowContractDto.getOwner().getEmpId(), dateTime);
        // doOrganizeInfo(convertContract, empWorkInfoData, currentTimeMillis);
        doPostInfo(convertContract, empWorkInfoData, dateTime);
        doWorkplaceInfo(convertContract, empWorkInfoData);
        doJobInfo(convertContract, empWorkInfoData);
        if (null == convertContract.getHireDate()) {
            convertContract.setHireDate(empWorkInfoData.getHireDate());
        }
        convertContract.setEmpStatus(empWorkInfoData.getEmpStatus());
        convertContract.setEmpType(empWorkInfoData.getEmpType());
        if (null == convertContract.getWorkHour() || StringUtil.isEmpty(convertContract.getWorkHour().getValue())) {
            convertContract.setWorkHour(empWorkInfoData.getWorkHour());
        }
        if (null == convertContract.getApprovalStatus() || StringUtil.isEmpty(convertContract.getApprovalStatus().getValue())) {
            EnumSimple approvalStatus = new EnumSimple();
            approvalStatus.setValue(ApprovalStatusEnum.PASSED.getIndex().toString());
            convertContract.setApprovalStatus(approvalStatus);
        }
        doContractStatus(convertContract, dateTime);
        ContractDo lastContractDo = new ContractDo();
        lastContractDo.setStartDate(0L);
        if (null == convertContract.getSignType() || StringUtil.isEmpty(convertContract.getSignType().getValue())) {
            EnumSimple signType = new EnumSimple();
            signType.setValue(SignTypeEnum.NEW.getCode());
            convertContract.setSignType(signType);
        }
        SpringUtil.getBean(ContractService.class).doSaveContract(convertContract, false, empWorkInfoData, null, lastContractDo, dateTime);
    }

    private boolean isApproved(ContractDo ecd) {
        if (null == ecd || null == ecd.getApprovalStatus() || StringUtil.isEmpty(ecd.getApprovalStatus().getValue())) {
            return false;
        }

        return Objects.equals(ApprovalStatusEnum.REVOKE.getIndex().toString(), ecd.getApprovalStatus().getValue())
                || Objects.equals(ApprovalStatusEnum.REJECTED.getIndex().toString(), ecd.getApprovalStatus().getValue());
    }

    private void doContractStatus(ContractDo contractData, Long dataTime) {
        String contractStatusValue = null;
        if (null != contractData.getContractStatus()) {
            contractStatusValue = contractData.getContractStatus().getValue();
            if (ContractStatusEnum.RELEASED.getIndex().equals(contractStatusValue)
                    || ContractStatusEnum.TERMINATED.getIndex().equals(contractStatusValue)
                    || ContractStatusEnum.CANCEL.getIndex().equals(contractStatusValue)) {
                return;
            }
        }
        EnumSimple contractStatus = new EnumSimple();
        contractStatus.setValue(ContractStatusEnum.IN_EFFECTIVE.getIndex());
        contractData.setContractStatus(contractStatus);
        if (null == contractData.getStartDate() || null == contractData.getEndDate()) {
            return;
        }

        if (contractData.getEndDate() < dataTime) {
            contractStatus.setValue(ContractStatusEnum.INVALID.getIndex());
            contractData.setContractStatus(contractStatus);
        }
    }

    private void doPostInfo(ContractDo contractData, EmpWorkInfoDo empWorkInfoData, Long dataTime) {
        if (empWorkInfoData != null && StringUtil.isEmpty(contractData.getPost()) || contractData.getPost().equals(empWorkInfoData.getPost())) {
            contractData.setPost(empWorkInfoData.getPost());
            contractData.setPostTxt(empWorkInfoData.getPostTxt());
            return;
        }

        PostDo postData = postDomainService.selectById(contractData.getPost(), dataTime);
        if (null == postData) {
            return;
        }

        contractData.setPost(postData.getBid());
        contractData.setPostTxt(postData.getName());
    }

    private void doWorkplaceInfo(ContractDo contractData, EmpWorkInfoDo empWorkInfoData) {
        if (StringUtil.isEmpty(contractData.getWorkplace()) || contractData.getWorkplace().equals(empWorkInfoData.getWorkplace())) {
            contractData.setWorkplace(empWorkInfoData.getWorkplace());
            contractData.setWorkplaceTxt(empWorkInfoData.getWorkplaceTxt());
            return;
        }

        WorkplaceDo workplaceData = workplaceService.getWorkplaceById(contractData.getWorkplace());
        if (null == workplaceData) {
            return;
        }

        contractData.setWorkplace(workplaceData.getBid());
        contractData.setWorkplaceTxt(workplaceData.getName());
    }

    public void updateApprovalStatusOfContineContract(WfCallbackResultDto wfCallbackResult) {
        if (wfCallbackResult == null || StringUtils.isBlank(wfCallbackResult.getBusinessKey()) ||
                wfCallbackResult.getCallbackType() == null ||
                WfCallbackTriggerOperationEnum.ERROR.equals(wfCallbackResult.getCallbackType())) {
            log.info("return updateApprovalStatusOfContineContract method, parameter={}", FastjsonUtil.toJson(wfCallbackResult));
            return;
        }
        String bid = wfCallbackResult.getBusinessKey().split("_")[0];
        ApprovalStatusEnum status = null;
        switch (wfCallbackResult.getCallbackType()) {
            case APPROVED:
                status = ApprovalStatusEnum.PASSED;
                archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT_LETTER, bid);
                break;
            case REFUSED:
                status = ApprovalStatusEnum.REJECTED;
                break;
            case REVOKE:
                status = ApprovalStatusEnum.REVOKE;
                archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.CONTRACT_LETTER, bid, ArchivePolicy.DELETE);
                break;
        }
        contractDomainService.updateContinueContract(bid, status);
    }

    private void doJobInfo(ContractDo contractData, EmpWorkInfoDo empWorkInfoData) {
        if (StringUtil.isEmpty(contractData.getJob()) || contractData.getJob().equals(empWorkInfoData.getJob())) {
            contractData.setJob(empWorkInfoData.getJob());
            contractData.setJobTxt(empWorkInfoData.getJobTxt());
            return;
        }

        JobDo jobData = jobDomainService.selectById(contractData.getJob());
        if (null == jobData) {
            return;
        }

        contractData.setJob(jobData.getBid());
        contractData.setJobTxt(jobData.getName());
    }

    /**
     * 合同自动发起
     *
     * @param emp
     * @param lastContract  上一份合同
     * @param lastCompany   上一份合同的合同公司
     * @param lastWorkplace 上一份合同的工作地
     * @param typeList
     */
    public void autoStart(EmpWorkInfoDo emp, ContractDto lastContract, CompanyDo lastCompany, WorkplaceDo lastWorkplace, List<ContractTypeSetRelListVo> typeList) {
        if (typeList.isEmpty()) {
            log.warn("合同类型为空,empId={},contractId={}", emp, lastContract.getBid());
            throw new ContractAutoStartException("无匹配的合同类型");
        }
        if (lastCompany == null) {
            log.warn("合同公司不存在,empId={},contractId={}", emp, lastContract.getBid());
            throw new ContractAutoStartException("合同公司不存在");
        }
        EmpSimple empSimple = new EmpSimple();
        empSimple.setEmpId(emp.getEmpId());
        BatchRenewalDto dto = new BatchRenewalDto();
        dto.setCompany(lastCompany.getBid());
        dto.setContractPeriod(typeList.get(0).getContractPeriod());
        dto.setContractSetId(typeList.get(0).getBid());
        dto.setPeriodType(typeList.get(0).getPeriodType());
        dto.setEmpList(Lists.list(empSimple));
        dto.setOpenWorkflow(true);
        log.info("自动发起合同,empId={},contractId={},renewalData={}", emp, lastContract.getBid(), dto);
        batchRenewal(dto);
    }

    @Deprecated
    public void callbackAndStart(WfCallbackResultDto callback) {
        this.updateApprovalStatusOfContineContract(callback);
        if (callback.getCallbackType() == WfCallbackTriggerOperationEnum.APPROVED) {
            String bid = callback.getBusinessKey().split("_")[0];
            // contractStartMsg(bid);
        }
    }

}
