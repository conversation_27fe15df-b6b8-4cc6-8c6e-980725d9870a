package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractTypeSetDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractTypeSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractConfigDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueTemplatePropertyDefDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.*;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.StatusOptDto;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.LanguageUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.INVALID_BID;

@Slf4j
@RestController
@RequestMapping("/api/hr/contract/type/set/v1")
@Api(value = "/api/hr/contract/type/set/v1", description = "合同设置", tags = "v1.6")
public class ContractTypeSetController {
    @Resource
    private ContractTypeSetService contractTypeSetService;

    @ApiOperation("新增合同设置")
    @PostMapping("/save")
    @LogRecordAnnotation(menu = "人事-合同管理-合同设置-合同设置模板", category = "新增", success = "新增了{contractType{#dto.contractType}}")
    public Result saveContract(@RequestBody ContractTypeSetDto dto) {
        dto.setBid(null);
        contractTypeSetService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation(value = "获取分组条件列表")
    @GetMapping("condition")
    public Result<List<TagConditionVo>> conditionList() {
        List list = contractTypeSetService.groupConditionList();
        return Result.ok(ObjectConverter.convertList(list, TagConditionVo.class));
    }

    @ApiOperation("查询是否存在相同条件的合同设置")
    @PostMapping("/checkDuplicate")
    public Result<ContractSetConditionDuplicateVo> checkDuplicate(@RequestBody ContractTypeSetDto dto) {
        return Result.ok(contractTypeSetService.checkDuplicate(dto));
    }


    @ApiOperation("更新合同设置")
    @PostMapping("/update")
    @LogRecordAnnotation(menu = "人事-合同管理-合同设置-合同设置模板", category = "编辑", success = "编辑了{contractType{#dto.contractType}}")
    public Result updContract(@RequestBody ContractTypeSetDto dto) {
        PreCheck.preCheckNotNull(dto.getBid(), LangUtil.getMsg(INVALID_BID));
        contractTypeSetService.saveOrUpdateObj(dto);
        return Result.ok(true);
    }

    @ApiOperation("删除合同设置")
    @DeleteMapping("/delContract")
    @LogRecordAnnotation(menu = "人事-合同管理-合同设置-合同设置模板", category = "删除", success = "删除了{contractType{#contractSetDo.contractType.value}}")
    public Result delContract(@RequestParam("bid") String bid) {
        ContractTypeSetDo contractTypeSetDo = contractTypeSetService.getById(bid);
        LogRecordContext.putVariable("contractSetDo", contractTypeSetDo);
        contractTypeSetService.delete(bid);
        return Result.ok(true);
    }

    @ApiOperation("获取合同设置详情")
    @GetMapping("/getDetail")
    public Result<ContractTypeSetVo> getDetail(@RequestParam("bid") String bid) {
        ContractTypeSetVo vo = contractTypeSetService.getDetail(bid);
        return Result.ok(vo);
    }

    @ApiOperation("合同设置列表")
    @PostMapping("/list")
    public Result<PageResult<ContractTypeSetListVo>> getList(@RequestBody ContractTypeSetQueryDto dto) {
        return Result.ok(contractTypeSetService.getList(dto));
    }

    @ApiOperation("获取签订类型、合同公司对应的合同设置")
    @GetMapping("/listCompanyAll")
    public Result<List<ContractTypeSetRelListVo>> listCompanyAll(String company, String signType) {
        return Result.ok(contractTypeSetService.listCompanyAll(company, signType));
    }

    @ApiOperation("合同设置启用或停用")
    @PostMapping("/updateStatus")
    @LogRecordAnnotation(menu = "人事-合同管理-合同设置-合同设置模板", category = "{{#dto.status == 0 ? '启用' : '停用'}}",
            success = "{{#dto.status == 0 ? '启用' : '停用'}}了{contractType{#contractSetDo.contractType.value}}")
    public Result updateStatus(@RequestBody StatusOptDto dto) {
        dto.preCheckArgument();
        ContractTypeSetDo data = ObjectConverter.convert(dto, ContractTypeSetDo.class);
        ContractTypeSetDo contractTypeSetDo = contractTypeSetService.getById(data.getBid());
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            contractTypeSetService.enable(data);
        } else {
            contractTypeSetService.disable(data);
        }
        LogRecordContext.putVariable("contractSetDo", contractTypeSetDo);
        return Result.ok(true);
    }

    @ApiOperation("获取签订类型对应的合同设置")
    @GetMapping("/listCompanyBySignType")
    public Result<List<ContractTypeSetRelListVo>> listCompanyBySignType(String signType) {
        return Result.ok(contractTypeSetService.listCompanyBySignType(signType));
    }


    @ApiOperation("续签意向模版分页")
    @PostMapping("continue/page")
    public Result<PageResult<ContinueContractTemplatePageVo>> continueContractPage(@RequestBody ContinueContractSetQueryDto queryDto) {
        var pageResult = contractTypeSetService.getPageOfContinueContract(queryDto);
        var voPageResult = new PageResult<ContinueContractTemplatePageVo>();
        BeanUtils.copyProperties(pageResult, voPageResult, "items");
        if (!CollectionUtils.isEmpty(pageResult.getItems())) {
            List<ContinueContractTemplatePageVo> items = pageResult.getItems().stream().map(e -> {
                var vo = new ContinueContractTemplatePageVo()
                        .setBid(e.getBid())
                        .setName(LanguageUtil.getCurrentLangVal(e.getI18nName()))
                        .setDesc(LanguageUtil.getCurrentLangVal(e.getI18nDesc()));
                return vo;
            }).collect(Collectors.toList());
            voPageResult.setItems(items);
        }
        return Result.ok(voPageResult);
    }

    @ApiOperation("保存或更新续签意向配置")
    @PostMapping("continue/config")
    public Result continueContractConfig(@RequestBody ContinueContractConfigDto continueContractConfigDto) {
        contractTypeSetService.saveOrUpdateContinueContractConfig(continueContractConfigDto, SecurityUserUtil.getSecurityUserInfo());
        return Result.ok();
    }

    @ApiOperation("获取续签意向配置")
    @GetMapping("continue/config")
    public Result<ContinueContractConfigVo> continueContractConfig() {
        return Result.ok(contractTypeSetService.getContinueContractConfig(SecurityUserUtil.getThreadLocalSecurityUserInfo().getTenantId()));
    }

    @ApiOperation("续签意向模版删除")
    @DeleteMapping("continue")
    public Result removeContinueContract(@RequestParam("bid") @ApiParam(value = "bid") String bid) {
        contractTypeSetService.removeConditionContract(bid);
        return Result.ok();
    }

    @ApiOperation("续签意向模版保存")
    @PostMapping("continue")
    public Result saveContinueContractTemplate(@RequestBody ContinueContractTemplateDto contractTemplateDto) {
        contractTypeSetService.saveOrUpdateConditionContract(contractTemplateDto);
        return Result.ok();
    }

    @ApiOperation("续签意向模版更新")
    @PutMapping("continue")
    public Result updateContinueContractTemplate(@RequestBody ContinueContractTemplateDto contractTemplateDto) {
        contractTypeSetService.saveOrUpdateConditionContract(contractTemplateDto);
        return Result.ok();
    }

    @ApiOperation("续签意向模版获取")
    @GetMapping("continue")
    public Result<ContinueContractTemplateVo> getContinueByBId(@RequestParam("bid") @ApiParam(value = "bid") String bid) {
        var optional = contractTypeSetService.getContinueContractByBId(bid);
        if (optional.isPresent()) {
            return Result.ok(FastjsonUtil.convertObject(optional.get(), ContinueContractTemplateVo.class));
        }
        return Result.ok(new ContinueContractTemplateVo());
    }

    /**
     * @param contractId 合同id
     * @return
     */
    @ApiOperation("续签意向模板匹配")
    @GetMapping("continue/matched")
    public Result<List<ContinueContractTemplatePageVo>> findMatched(@RequestParam("contractId") @ApiParam(value = "contractId") String contractId) {
        List<ContinueContractTemplateDto> matched = contractTypeSetService.findMatchedContinueTemplate(contractId);
        List<ContinueContractTemplatePageVo> voList = matched.stream().map(e -> {
            var vo = new ContinueContractTemplatePageVo()
                    .setBid(e.getBid())
                    .setName(LanguageUtil.getCurrentLangVal(e.getI18nName()))
                    .setDesc(LanguageUtil.getCurrentLangVal(e.getI18nDesc()));
            return vo;
        }).collect(Collectors.toList());
        return Result.ok(voList);
    }

    @ApiOperation("续签意向模板匹配条件")
    @GetMapping("continue/condition")
    public Result<List<ConditionDataVo>> findMatched() {
        List<ConditionDataVo> list = contractTypeSetService.getContinueCondition();
        return Result.ok(list);
    }

    /**
     * @param templateId 续签意向模板id
     * @return
     */
    @ApiOperation("续签意向字段定义")
    @GetMapping("continue/def")
    public Result<ContinueTemplatePropertyDefVo> getTemplatePropertyDef(@RequestParam("templateId") @ApiParam(value = "templateId") String templateId) {
        ContinueTemplatePropertyDefDto dto = contractTypeSetService.getPropertyDef(templateId);
        ContinueTemplatePropertyDefVo vo = ObjectConverter.convert(dto, ContinueTemplatePropertyDefVo.class);
        vo.setName(LanguageUtil.getCurrentLangVal(dto.getI18nName()));
        return Result.ok(vo);
    }
}