package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.condition.util.ConditionExpUtil;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.enums.ConditionOperator;
import com.caidaocloud.hr.service.contract.application.enums.ValueComponent;
import com.caidaocloud.hr.service.contract.application.exp.service.IBaseExp;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class ConditionExpDto {
    @ApiModelProperty("条件名称")
    private String name;
    @ApiModelProperty("条件操作符")
    private ConditionOperator symbol;
    @ApiModelProperty("条件值")
    private Object value;
    @ApiModelProperty("值类型")
    private ValueComponent componentType;

    public boolean isIncomplete() {
        return StringUtils.isBlank(name) || symbol == null || value == null;
    }

    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private Map<String, String> identifierMap = Maps.map(
            Sequences.sequence(
                    Pair.pair("hr#empPrivateInfo#nationality$dictValue", "entity.hr.EmpPrivateInfo"),
                    Pair.pair("hr#empWorkInfo#organize", "entity.hr.EmpWorkInfo"),
                    Pair.pair("hr#empWorkInfo#job", "entity.hr.EmpWorkInfo"),
                    Pair.pair("hr#empWorkInfo#post", "entity.hr.EmpWorkInfo"),
                    Pair.pair("hr#empWorkInfo#divisionAge", "entity.hr.EmpWorkInfo"),
                    Pair.pair("hr#Contract#signTime", "entity.hr.LastContract"),
                    Pair.pair("hr#Contract#contractSettingType", "entity.hr.LastContract"),
                    Pair.pair("hr#Contract#endDate", "entity.hr.LastContract"),
                    Pair.pair("hr#EmpWorkInfo#jobGrade$startGrade", "entity.hr.EmpWorkInfo")
            )
    );

    private String fetchIdentifier(){
        if(identifierMap.containsKey(name)){
            return identifierMap.get(name);
        }else{
            return StringUtils.substringBefore(name, "#");
        }
    }


    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private Map<String, String> filterPropertyMap = Maps.map(
            Sequences.sequence(
                    Pair.pair("hr#empPrivateInfo#nationality$dictValue", "nationality$dictValue"),
                    Pair.pair("hr#empWorkInfo#organize", "organize"),
                    Pair.pair("hr#empWorkInfo#job", "job"),
                    Pair.pair("hr#empWorkInfo#post", "post"),
                    Pair.pair("hr#empWorkInfo#divisionAge", "divisionAge"),
                    Pair.pair("hr#Contract#signTime", "signTime"),
                    Pair.pair("hr#Contract#contractSettingType", "contractSettingType$dictValue"),
                    Pair.pair("hr#Contract#endDate", "endDate"),
                    Pair.pair("hr#EmpWorkInfo#jobGrade$startGrade", "jobGrade$startGrade")
            )
    );

    private String fetchProperty(){
        if(filterPropertyMap.containsKey(name)){
            return filterPropertyMap.get(name);
        }else{
            String property = StringUtils.substringAfter(name, "#");
            if(ValueComponent.DICT_SELECTOR.equals(componentType)){
                return property + "$dictValue";
            }
            return property;
        }
    }

    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private Map<String, String> empIdPropertyMap = Maps.map(
            Sequences.sequence(
                    Pair.pair("hr#empPrivateInfo#nationality$dictValue", "empId"),
                    Pair.pair("hr#empWorkInfo#organize", "empId"),
                    Pair.pair("hr#empWorkInfo#job", "empId"),
                    Pair.pair("hr#empWorkInfo#post", "empId"),
                    Pair.pair("hr#empWorkInfo#divisionAge", "empId"),
                    Pair.pair("hr#Contract#signTime", "owner.empId"),
                    Pair.pair("hr#Contract#contractSettingType", "owner.empId"),
                    Pair.pair("hr#Contract#endDate", "owner.EmpId"),
                    Pair.pair("hr#EmpWorkInfo#jobGrade$startGrade", "empId")
            )
    );

    private String fetchEmpIdProperty(){
        if(empIdPropertyMap.containsKey(name)){
            return empIdPropertyMap.get(name);
        }else{
            return "empId";
        }
    }

    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private Map<String, String> expBeanMap = Maps.map(
            Sequences.sequence(
                    // Pair.pair("hr#Contract#contractSettingType", "contractTypeExp"),
                    Pair.pair("hr#Contract#endDate", "endDateExp")
            )
    );

    @Setter(value = AccessLevel.NONE)
    @Getter(value = AccessLevel.NONE)
    private Map<String, Integer> scaleMap = Maps.map(
            Sequences.sequence(
                    Pair.pair("divisionAge", 1)
            )
    );

    public List<Long> filterEmpIds(List<Long> empIds) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Lists.list();
        }
        if (log.isDebugEnabled()) {
            log.debug("exp ========>{}", FastjsonUtil.toJson(this));
        }
        Object expImpl;
        if ((expImpl = SpringUtil.getBean(expBeanMap.get(name))) != null) {
            return ((IBaseExp) expImpl).filterEmpIds(empIds, this);
        }
        List<Long> result = new ArrayList<>(5000);
        // 组装filter条件
        DataFilter filter = toFilter(fetchProperty(), value, symbol)
                .andEq("deleted", "false");
        ConditionTreeDto.getEmpIdAll(fetchIdentifier(), filter, fetchEmpIdProperty(), result);
        return result;
    }
    public DataFilter toFilter(){
        return toFilter(fetchProperty(), value, symbol);
    }

    private DataFilter toFilter(String property, Object value, ConditionOperator symbol){
        if ("divisionAge".equals(property)) {
            value = new BigDecimal(String.valueOf(value)).setScale(1, RoundingMode.HALF_UP);
        }
        switch (symbol){
            case EQ:
                return DataFilter.eq(property, setScale(property,String.valueOf(value)));
            case NE:
                return DataFilter.ne(property,setScale(property,String.valueOf(value)));
            case GT:
                return DataFilter.gt(property, setScale(property,String.valueOf(value)));
            case GE:
                return DataFilter.ge(property, setScale(property,String.valueOf(value)));
            case LT:
                return DataFilter.lt(property, setScale(property,String.valueOf(value)));
            case LE:
                return DataFilter.le(property, setScale(property,String.valueOf(value)));
            case IN:
                return DataFilter.in(property, getValueList(property, value));
            case NOT_IN:
                return DataFilter.notIn(property, getValueList(property, value));
            case CONTAIN_CHILD:
                return DataFilter.in(property, ConditionExpUtil.containChild(String.valueOf(value)));
           default:
                throw new ServerException("not supported");
        }
    }

    private List<String> getValueList(String property, Object objValue){
        return Sequences.sequence(((List) objValue)).map(v -> setScale(property, String.valueOf(v))).toList();
    }

    String setScale(String prop, String value) {
        Integer scale;
        if ((scale = scaleMap.get(prop)) != null) {
            value = new BigDecimal(value).setScale(scale, RoundingMode.HALF_UP).toString();
        }
        return value;
    }

    public Object getValueByOpt() {
        return symbol.extractValue(value);
    }

    public String getIdentifier() {
        return fetchIdentifier();
    }

}
