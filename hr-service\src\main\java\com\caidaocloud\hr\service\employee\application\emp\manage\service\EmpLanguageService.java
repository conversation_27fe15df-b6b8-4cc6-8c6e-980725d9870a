package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.emp.EmpLanguageDto;
import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpLanguageDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpLanguageDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class EmpLanguageService extends BaseServiceImpl {
    @Resource
    private EmpLanguageDomainService empLanguageDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    @Override
    protected BaseDomainService getDomainService() {
        return empLanguageDomainService;
    }


    public String save(EmpLanguageDto dto) {
        EmpLanguageDo data = ObjectConverter.convert(dto, EmpLanguageDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empLanguageDomainService.saveEmpLanguage(data);
    }

    public void update(EmpLanguageDto dto) {
        EmpLanguageDo data = ObjectConverter.convert(dto, EmpLanguageDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empLanguageDomainService.update(data);
    }

    private void doConvert(EmpLanguageDto source, EmpLanguageDo target) {
        // 语种
        if (StringUtil.isNotEmpty(source.getLanguage())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getLanguage());
            target.setLanguage(dictSimple);
        }
        // 听说能力
        if (StringUtil.isNotEmpty(source.getHearSkill())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getHearSkill());
            target.setHearSkill(dictSimple);
        }
        // 读写能力
        if (StringUtil.isNotEmpty(source.getReadWriteSkill())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getReadWriteSkill());
            target.setReadWriteSkill(dictSimple);
        }
    }

    public EmpLanguageDo selectById(String bid) {
        return empLanguageDomainService.selectById(bid);
    }

    public void deleteEmpLanguage(String bid) {
        empLanguageDomainService.deleteEmpLanguage(bid);
    }

    public List<EmpLanguageDo> selectListByEmpId(String empId) {
        return empLanguageDomainService.selectListByEmpId(empId);
    }
}
