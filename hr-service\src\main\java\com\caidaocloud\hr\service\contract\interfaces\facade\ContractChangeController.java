package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.hr.service.contract.application.service.ContractChangeService;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractApprovalChangeDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContractChangeDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractRevokeVo;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/hr/contract/change/v1")
@Api(value = "/api/hr/contract/change/v1", description = "合同管理", tags = "v1.7")
public class ContractChangeController {

    @Resource
    private ContractChangeService contractChangeService;

    @ApiOperation("终止合同")
    @PostMapping("/termination")
    public Result termination(@RequestBody ContractChangeDto dto) {
        contractChangeService.changeContract(dto);
        return Result.ok();
    }

    @ApiOperation("解除合同")
    @PostMapping("/dissolve")
    public Result dissolve(@RequestBody ContractChangeDto dto) {
        dto.setDissolve(true);
        contractChangeService.changeContract(dto);
        return Result.ok();
    }

    @ApiOperation("审批拒绝")
    @PostMapping("/refuse")
    public Result refuse(@RequestBody ContractApprovalChangeDto dto) {
        contractChangeService.updateContractApproval(dto, WfTaskActionEnum.REFUSE);
        return Result.ok();
    }

    @ApiOperation("审批撤回")
    @PostMapping("/revoke")
    public Result<ContractRevokeVo> revoke(@RequestBody WfRevokeDto dto) {
        return Result.ok(contractChangeService.revoke(dto));
    }

    @ApiOperation("审批同意")
    @PostMapping("/approve")
    public Result approve(@RequestBody ContractApprovalChangeDto dto) {
        contractChangeService.updateContractApproval(dto, WfTaskActionEnum.APPROVE);
        return Result.ok();
    }

    @PostMapping("/callback")
    @ApiOperation("工作流-回调")
    public Result callback(@RequestBody WfCallbackResultDto callback) {
        log.info("contract change callback data={}", FastjsonUtil.toJson(callback));
        contractChangeService.callback(callback.getTenantId(), callback.getBusinessKey(), callback.getCallbackType());
        return Result.ok(true);
    }

    @ApiOperation("合同流程变量值获取")
    @PostMapping("/notice/var")
    public Result<Map<String, String>> getNoticeVar(@RequestBody ContractApprovalChangeDto request) {
        return Result.ok(contractChangeService.getNoticeVar(request.getBusinessKey(), request.getVariables()));
    }
}
