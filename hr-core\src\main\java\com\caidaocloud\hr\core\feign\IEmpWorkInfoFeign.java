package com.caidaocloud.hr.core.feign;

import java.util.List;

import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@FeignClient(value = "caidaocloud-hr-service",
		fallback = EmpWorkInfoFeignFallback.class,
		configuration = FeignConfiguration.class,
		contextId = "empWorkInfoFeign")
public interface IEmpWorkInfoFeign {

	@PostMapping("/api/hr/emp/work/v1/getEmpListByWorkno")
	Result<List<EmpWorkInfoVo>> getEmpListByWorkno(@RequestBody List<String> worknoList);

	@GetMapping("/api/hr/transfer/config/v1/typeList")
	Result<Object> getTransferTypeList();
}
