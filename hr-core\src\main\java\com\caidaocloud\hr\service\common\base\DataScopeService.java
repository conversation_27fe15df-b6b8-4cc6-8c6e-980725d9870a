package com.caidaocloud.hr.service.common.base;

import com.caidaocloud.hr.core.feign.AuthDataScopeFeignClient;
import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.hr.service.dto.auth.EsScopeQuery;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class DataScopeService {
    @Resource
    private AuthDataScopeFeignClient dataScopeFeign;

    public List<AuthRoleScopeFilterDetail> getScopeList(String identifier, Long subjectId, String typeId){
        val target = StringUtils.isNotBlank(typeId) ? String.format("transfer_%s", typeId) : "";
        Result<List<AuthRoleScopeFilterDetail>> result = dataScopeFeign.getScopeBySubject(identifier, subjectId, target);
        List<AuthRoleScopeFilterDetail> dataList = null;
        if(null == result || !result.isSuccess() || null == (dataList = result.getData()) || dataList.isEmpty()){
            return Lists.newArrayList();
        }
        return dataList;
    }

    public BoolQueryBuilder getScopeQuery(List<AuthRoleScopeFilterDetail> dataList, Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap){

        boolean allAuth = dataList.stream().filter(it->it.getRestriction().equals(AuthRoleScopeRestriction.ALL)).findFirst().isPresent();
        if(allAuth){
            return null;
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        BoolQueryBuilder scopeQueryBuilder = QueryBuilders.boolQuery();
        dataList.forEach(scope -> {
            AuthRoleScopeRestriction restriction = scope.getRestriction();
            String simpleValues = scope.getSimpleValues();
            List<String> scopeList = restriction.toValues(simpleValues);
            if(scopeList.isEmpty()){
                scopeList.add("_null");
            }
            log.info("restriction is " + restriction.name());
            log.info("restriction Keys are " + FastjsonUtil.toJson(queryMap.keySet()));
            EsScopeQuery esScopeQuery = queryMap.get(restriction);
            if (Objects.isNull(esScopeQuery)) {
                log.info("not found esScopeQuery, restriction={}", restriction);
                return;
            }
            switch (esScopeQuery.getOp()){
                case TERMS:
                    scopeQueryBuilder.should(QueryBuilders.termsQuery(esScopeQuery.getProperty(), scopeList));
                    break;
                case WILDCARD:
                    //scopeQueryBuilder.should(QueryBuilders.wildcardQuery(esScopeQuery.getProperty(), "*" + scopeList.get(0) + "*"));
                    // wildcard查询也属于模糊查询；通配查询，和MySql中的like是一个套路
                    for(String scopeItem : scopeList){
                        scopeQueryBuilder.should(QueryBuilders.wildcardQuery(esScopeQuery.getProperty(), "*" + scopeItem + "*"));
                    }
                    break;
                case PRE_FIX:
                    for(String scopeItem : scopeList){
                        scopeQueryBuilder.should(QueryBuilders.prefixQuery(esScopeQuery.getProperty(), scopeItem));
                    }
                    break;
            }
        });
        scopeQueryBuilder.minimumShouldMatch(1);
        return scopeQueryBuilder;
    }

    public void getScopeQuery(List<AuthRoleScopeFilterDetail> dataList, BoolQueryBuilder boolQuery, Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap){
        log.info("data scope filter list={}", FastjsonUtil.toJson(dataList));
        if (CollectionUtils.isEmpty(dataList)){
            return;
        }
        BoolQueryBuilder sqb = getScopeQuery(dataList, queryMap);
        if(null == sqb){
            return;
        }
        boolQuery.must(sqb);
    }

    private void filterScopeList(List<String> scopeList, AuthRoleScopeRestriction scope){
        if(null == scopeList || scopeList.isEmpty()){
            return;
        }

        switch (scope){
            case MY_ORG_AND_BELONGINGS:
                String one = scopeList.get(0);
                scopeList.clear();
                scopeList.add(one);
                break;
        }
    }
}
