package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeFormConfig;
import com.caidaocloud.hr.service.employee.infrastructure.enums.FormResumeTypeEnum;
import com.deepoove.poi.config.ConfigureBuilder;
import org.apache.commons.collections.MapUtils;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

public abstract class AbsFormResumeService<T extends ResumeFormConfig> {
    private static Map<FormResumeTypeEnum, AbsFormResumeService> container = new ConcurrentHashMap<>();

    public static void process(ConfigureBuilder cb, Map<String, Object> dataMap, String identifier, Map<String, Object> formDataMap, ResumeFormConfig param) {
        if (MapUtils.isEmpty(formDataMap) || Objects.isNull(param) || Objects.isNull(param.getType())) {
            return;
        }
        AbsFormResumeService service = container.get(param.getType());
        if (Objects.isNull(service)) {
            return;
        }
        service.handle(cb, dataMap, identifier, formDataMap, param);
    }

    public abstract void handle(ConfigureBuilder cb, Map<String, Object> dataMap, String identifier, Map<String, Object> formDataMap, T param);

    protected abstract FormResumeTypeEnum type();

    @PostConstruct
    protected void register() {
        container.putIfAbsent(type(), this);
    }
}