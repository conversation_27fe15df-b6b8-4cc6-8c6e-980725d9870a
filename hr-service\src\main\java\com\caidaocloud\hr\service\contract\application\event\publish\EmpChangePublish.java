package com.caidaocloud.hr.service.contract.application.event.publish;

import com.caidaocloud.hr.service.contract.application.event.dto.EmpChangeMessageDto;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class EmpChangePublish {
    private final static String EXCHANGE = "hr.emp.fac.direct.exchange";
    private final static String ROUTING_KEY = "routingKey.hr.emp.import";

    @Resource
    private MqMessageProducer<ContractMessage> producer;

    public void empImported(EmpChangeMessageDto message){
        ContractMessage contract = new ContractMessage();
        contract.setBody(FastjsonUtil.toJson(message));
        contract.setExchange(EXCHANGE);
        contract.setRoutingKey(ROUTING_KEY);
        log.info("EmpChangeMessage={}", contract.getBody());
        producer.publish(contract);
    }
}
