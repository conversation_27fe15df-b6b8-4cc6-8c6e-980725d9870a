package com.caidaocloud.hr.workflow.dto;

import com.caidaocloud.hr.service.enums.growthrecord.BusinessEventTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Authot CI29616
 * @Date 2024/4/11 14:15
 * @Version 1.0
 **/
@ApiModel("导入触发员工成长记录")
@Data
public class EmpGrowthTypeDto {

    /**
     * 成长记录类型
     */
    @ApiModelProperty("成长记录类型")
    private BusinessEventTypeEnum businessEventTypeEnum;

    /**
     * 导入触发 新增/修改
     * 默认 是 新增数据；
     */
    @ApiModelProperty("导入触发 新增/修改")
    private Boolean isInsert = true;

    @ApiModelProperty("员工id")
    private String empId;

    /**
     * 修改提供-原员工数据
     */
    @ApiModelProperty("旧员工数据")
    private String oldDto;

    /**
     * 新增/修改提供-新员工数据
     */
    @ApiModelProperty("新员工数据")
    private String newDto;

    /**
     * 配置一些非员工数据
     */
    @ApiModelProperty("额外添加数据")
    private Map<String, String> ext = new HashMap<>();
}
