package com.caidaocloud.hr.service.employee.application.emp.dto.resume.form;

import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeFormConfig;
import lombok.Data;

import java.util.List;

@Data
public class DateTypeLastestDataConfig extends ResumeFormConfig {
    //查询的属性
    private List<Field> fieldList;
    //过滤的属性
    private List<Field> filterList;
    //原始日期数据格式
    private String sourceDatePattern;
    //目标数据格式
    private String targetFormat;
    //排序字段
    private String sortProp;

    @Data
    public static class Field {
        //字段
        private String prop;
        private String val;
        //排序
        private Integer sort;
    }
}