package com.caidaocloud.hr.service.dto.ruleset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Authot CI29616
 * @Date 2023/8/15 10:04
 * @Version 1.0
 **/
@Data
@ApiModel("员工离职规则配置参数")
public class EmpDepartRuleDto {

    @ApiModelProperty("是否离职生效")
    private Boolean isDepartFlag = false;

    @ApiModelProperty("离职停用时间（XX天后）")
    private Integer dayTime;

//    @ApiModelProperty("是否删除生效")
//    private Boolean isDeleteFlag = false;

}
