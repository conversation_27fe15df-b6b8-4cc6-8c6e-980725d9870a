package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/4/26 14:25
 * @Description:
 **/
public interface IContractTypeSetRepository extends BaseRepository<ContractTypeSetDo> {

    List<ContractTypeSetDo> selectByCondition(ContractTypeSetDo data);

    List<ContractTypeSetDo> selectByRegex(String IDENTIFIER, String company, String signType);

    List<ContractTypeSetDo> getListCompanyByRegex(String identifier, String signType);
}
