package com.caidaocloud.hr.service.contract.interfaces.dto.contract;


import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("合同管理查询DTO")
public class ContractEmpQueryDto extends BasePage {
    @ApiModelProperty("任职组织")
    private String organize;
    @ApiModelProperty("工号或姓名关键字搜索")
    private String keyword;
    @ApiModelProperty("审批状态 0:审批中 1:已通过 2:已拒绝")
    private String status;
    @ApiModelProperty("合同到期天数")
    private Integer contractDays;
}
