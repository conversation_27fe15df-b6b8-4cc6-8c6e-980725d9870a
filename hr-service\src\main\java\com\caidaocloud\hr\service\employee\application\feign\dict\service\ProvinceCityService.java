package com.caidaocloud.hr.service.employee.application.feign.dict.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInfoImportDo;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hrpaas.metadata.sdk.dto.ProvinceCity;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProvinceCityService {


    @Resource
    private ITenantFeignClient iTenantFeignClient;

    public List<ProvinceCity> getCityProvinceInfo(String tenantId) {
        Result result = iTenantFeignClient.cityProvinceInfo(tenantId);
        log.info("getCityProvinceInfo:{}", FastjsonUtil.toJson(result));
        if (!result.isSuccess()) {
            return Lists.newArrayList();
        } else {
            Object data = result.getData();
            return FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), ProvinceCity.class);
        }
    }

    public void transferEmpNativePlace(List<EmpInfoImportDo> empInfoImportDos, String tenantId) {
        boolean present = empInfoImportDos.stream().anyMatch(it -> StringUtil.isNotEmpty(it.getNativePlaceTxt()));
        if (present) {
            List<ProvinceCity> cityProvinceInfo = getCityProvinceInfo(tenantId);
            if (CollectionUtils.isNotEmpty(cityProvinceInfo)) {
                for (EmpInfoImportDo empInfoImportDo : empInfoImportDos) {
                    if (empInfoImportDo.getNativePlaceTxt() != null && empInfoImportDo.getNativePlaceTxt().split("/").length == 2) {
                        log.info("transferEmpNativePlace empName:{} nativePlaceTxt:{}", empInfoImportDo.getName(), empInfoImportDo.getNativePlaceTxt());
                        for (ProvinceCity provinceCity : cityProvinceInfo) {
                            String[] provinceAndCity = empInfoImportDo.getNativePlaceTxt().split("/");
                            if (provinceCity.getCityName().equals(provinceAndCity[1]) && provinceCity.getProvinceName().equals(provinceAndCity[0])) {
                                empInfoImportDo.setNativePlace(provinceCity);
                            }
                        }
                    }
                }
            }
        }
    }
}
