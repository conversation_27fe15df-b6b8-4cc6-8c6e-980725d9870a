package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.hr.service.contract.domain.repository.ICompletedContractImportRepository;
import com.caidaocloud.hr.service.contract.infrastructure.repository.po.CompletedContractImportPo;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class CompletedContractImportRepositoryImpl implements ICompletedContractImportRepository {
    @Override
    public List<CompletedContractImportPo> getCompletedContractImportDoFromExcel(InputStream inputStream) {
        if (null == inputStream) {
            return new ArrayList<>();
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        List<CompletedContractImportPo> list = null;
        try {
            list = ExcelImportUtil.importExcel(inputStream, CompletedContractImportPo.class, params);
            if (null != list) {
                list = list.stream().filter(ccip ->
                        StringUtil.isNotEmpty(ccip.getWorkno()) &&
                                StringUtil.isNotEmpty(ccip.getName()) &&
                                StringUtil.isNotEmpty(ccip.getSignTypeTxt()) &&
                                StringUtil.isNotEmpty(ccip.getCompanyTxt()) &&
                                StringUtil.isNotEmpty(ccip.getContractTypeSetTxt()) &&
                                StringUtil.isNotEmpty(ccip.getStartDateTxt()) &&
                                StringUtil.isNotEmpty(ccip.getEndDateTxt())
                ).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("read contract excel file err,{}", e.getMessage(), e);
        }
        return list;
    }
}
