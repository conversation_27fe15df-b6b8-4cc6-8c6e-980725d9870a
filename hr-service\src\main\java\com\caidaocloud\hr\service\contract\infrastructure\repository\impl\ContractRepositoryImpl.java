package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.FilterFunction;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.em.SortOrder;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.PeriodTypeEnum;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContractRepository;
import com.caidaocloud.hr.service.contract.infrastructure.constant.DataScopeConstant;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.util.BeanUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.GroupedCountResult;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataJoin;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sets;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.util.BeanUtil.buildData;

@Repository
public class ContractRepositoryImpl extends BaseRepositoryImpl<ContractDo> implements IContractRepository {

    @Override
    public ContractDo insert(ContractDo data) {
        String bid = DataInsert.identifier(data.getIdentifier()).insert(ObjectConverter.convert(data, ContractDo.class));
        data.setBid(bid);
        return data;
    }

    @Override
    public PageResult<ContractDo> selectPage(BasePage page, ContractDo data, String keywords, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        if (data.getOrganize() != null) {
            dataFilter = dataFilter.andEq("organize", data.getOrganize());
        }
        if (StringUtils.isNotEmpty(keywords)) {
            dataFilter = dataFilter.and(DataFilter.regex("owner$workno", keywords).orRegex("owner$name", keywords));
        }
        if (StringUtils.isNotEmpty(data.getApprovalStatus().getValue())) {
            dataFilter = dataFilter.andEq("approvalStatus", data.getApprovalStatus().getValue());
        }
        dataFilter = (DataFilter) page.doDataFilter(page.getFilters(), dataFilter);

        // 数据范围权限
        dataFilter = getDataScopeFilter(dataFilter);

        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, ContractDo.class);
    }

    /**
     * 合同信息子集->员工当前合同
     */
    @Override
    public List<ContractDo> getEmpCurrentContract(String identifier, List<String> empList, String approvalStatus) {
        /*DataFilter filter = getBaseFilter();
        filter = filter.andEq("approvalStatus", approvalStatus).andEq("contractType$dictCode", "Contract").andIn("owner$empId", empList);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);*/
        return getLatestList(identifier, empList);
    }

    @Override
    public ContractDo getEmpLastContract(String identifier, String empId, String approvalStatus) {
        List<ContractDo> list = getEmpHistoryContract(identifier, Arrays.asList(empId), approvalStatus);
        if (CollectionUtils.isEmpty(list) || list.size() <= 1) {
            return null;
        }
        list.sort(Comparator.comparing(ContractDo::getStartDate).reversed());
        return list.get(1);
    }

    @Override
    public List<ContractDo> getEmpContractByStatus(String identifier, String empId, String approvalStatus, List<String> contractStatus) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("approvalStatus", approvalStatus).andIn("owner$empId", Arrays.asList(empId))
                .andIn("contractStatus", contractStatus);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<ContractDo> getEmpHistoryContract(String identifier, List<String> empList, String approvalStatus) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("approvalStatus", approvalStatus).andIn("owner$empId", empList);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<ContractDo> getContractSignList(String identifier, List<String> empList) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("contractType$dictCode", "Contract").andIn("owner$empId", empList);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public void deleteByEmpIds(String identifier, List<String> empIds) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("owner$empId", empIds);
        DataDelete.identifier(identifier).batchDelete(filter);
    }

    /**
     * 查询 empList 中员工的所有最新一份审批通过的合同
     */
    @Override
    public List<ContractDo> getLatestList(String identifier, List<String> empList) {
        //caidao - 2144 只获取当前生效中数据；
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("owner$empId", empList)
                .andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString())
                .andEq("contractStatus", ContractStatusEnum.EFFECTIVE.getIndex().toString());

        // 数据范围权限
        filter = getDataScopeFilter(filter);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(500, 1).filter(filter, ContractDo.class);

        //caidao-2144 合同取值优化；
        List<ContractDo> resultEffective = getPageList(pageResult);

        if (CollectionUtils.isEmpty(resultEffective)) {
            //生效中无数据则选取 非生效中  合同则取值结束时间最大的合同，若结束时间相同则取开始时间最大的；
            DataFilter filterReject = getBaseFilter();
            filterReject = filterReject.andIn("owner$empId", empList)
                    .andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString())
                    .andNe("contractStatus", ContractStatusEnum.EFFECTIVE.getIndex().toString());

//            filterReject = getDataScopeFilter(filterReject);
            PageResult<ContractDo> resultInvalid = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                    .limit(500, 1).filter(filterReject, ContractDo.class);

            List<ContractDo> invalid = getPageList(resultInvalid);

            if (invalid.size() == 1) {
                return invalid;
            } else {
                return dealContractInvalid(invalid);
            }

        }


        return resultEffective;
    }

    /**
     * 处理 失效中数据
     * 生效中无数据则选取失效中  合同则取值结束时间最大的合同，若结束时间相同则取开始时间最大的；
     *
     * @param contractDos
     * @return
     */
    private List<ContractDo> dealContractInvalid(List<ContractDo> contractDos) {
        List<ContractDo> collect = contractDos.stream()
                .sorted(Comparator.comparing(ContractDo::getEndDate, Comparator.reverseOrder()).thenComparing(ContractDo::getStartDate, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        return Arrays.asList(collect.get(0));
    }

    @Override
    public PageResult<ContractDo> selectContractUser(BasePage page, ContractDo data, String keywords, Long dateTime) {
        DataFilter dataFilter = getBaseFilter();
        if (data.getOrganize() != null) {
            dataFilter = dataFilter.andEq("organize", data.getOrganize());
        }
        if (StringUtils.isNotEmpty(keywords)) {
            dataFilter = dataFilter.and(DataFilter.regex("owner$workno", keywords).orRegex("owner$name", keywords));
        }
        if (StringUtils.isNotEmpty(data.getApprovalStatus().getValue())) {
            dataFilter = dataFilter.andEq("approvalStatus", data.getApprovalStatus().getValue());
        }
        dataFilter = (DataFilter) page.doDataFilter(page.getFilters(), dataFilter);
        dataFilter = dataFilter.andEq("periodType", PeriodTypeEnum.FIXED.getCode());

        // 数据范围权限
        dataFilter = getDataScopeFilter(dataFilter);

        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, ContractDo.class);
    }

    @Override
    public PageResult<ContractDo> getLatestPage(String identifier, int pageNo, int pageSize) {
        DataFilter filter = getBaseFilter().andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString());
        // 数据范围权限
        filter = getDataScopeFilter(filter);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(pageSize, pageNo).max(filter, "startDate",
                        System.currentTimeMillis(), ContractDo.class,
                        "ownerEmpId");
        return pageResult;
    }

    @Override
    public long countSignTime(String identifier, String empId) {
        DataFilter filter = getBaseFilter()
                .andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString())
                .andEq("owner$empId", empId);
        long count = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().count(filter, System.currentTimeMillis());
        return count;
    }

    @Override
    public Map<String, Long> countGroupSignTime(String identifier, List<String> empIds) {
        DataFilter filter = getBaseFilter()
                .andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString())
                .andIn("owner$empId", empIds);
        List<GroupedCountResult> results = DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .countByGroup(filter, System.currentTimeMillis(), "owner$empId");
        Map<String, Long> countMap = results.stream()
                .collect(Collectors.toMap(r -> r.getBy().get("owner$empId"), GroupedCountResult::getCount, (a, b) -> a));
        return countMap;
    }

    /**
     * 获取关联合同
     * @param ids
     * @param identifier
     * @return
     */
    @Override
    public List<ContractDo> getLinkContracts(String identifier, List<String> ids) {
        return getLinkContracts(identifier, ids, Lists.list(ApprovalStatusEnum.PASSED.getIndex().toString(),
                ApprovalStatusEnum.IN_APPROVAL.getIndex().toString()));
    }

    @Override
    public List<ContractDo> getLinkContracts(String identifier, List<String> ids, List<String> approvalStatus) {
        DataFilter filter = getBaseFilter()
                .andIn("lastContract", ids);
        if (CollectionUtils.isNotEmpty(approvalStatus)) {
            filter = filter.andIn("approvalStatus", approvalStatus);
        }
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage().filter(filter, ContractDo.class);
        return pageResult != null ? pageResult.getItems() : new ArrayList<>();
    }

    @Override
    public PageResult<ContractDo> selectRecordPage(BasePage page, ContractDo data, String keywords, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId())
                .andEq("deleted", Boolean.FALSE.toString());
        if (data.getOrganize() != null) {
            dataFilter = dataFilter.andEq("organize", data.getOrganize());
        }

        if (StringUtils.isNotEmpty(keywords)) {
            dataFilter = dataFilter.and(DataFilter.regex("owner$workno", keywords).orRegex("owner$name", keywords));
        }

        ContractQueryDto queryDto = (ContractQueryDto) page;
        if (StringUtils.isNotEmpty(queryDto.getEmpId())) {
            dataFilter = dataFilter.andEq("owner$empId", queryDto.getEmpId());
        }

        if (StringUtils.isNotEmpty(data.getApprovalStatus().getValue())) {
            dataFilter = dataFilter.andEq("approvalStatus", data.getApprovalStatus().getValue());
        }

        //合同到期天数
        if (queryDto.getContractDays() != null) {
            Long day = queryDto.getContractDays() > 0 ?
                    Long.valueOf(queryDto.getContractDays() + 1) : Long.valueOf(queryDto.getContractDays());
            // 86400000L 一天的毫秒数
            day = 86400000L * day;
            day = DateUtil.getCurrentTimestamp() + day;
            dataFilter = dataFilter.andLt("endDate", day.toString());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getContractStatusEnumList())) {
            List<String> contractStatusList = queryDto.getContractStatusEnumList().stream().map(ContractStatusEnum::getIndex).collect(Collectors.toList());
            dataFilter = dataFilter.andIn("contractStatus", contractStatusList);
        }

        dataFilter = (DataFilter) page.doDataFilter(page.getFilters(), dataFilter);
        // 合同记录列表不判断数据权限
        // dataFilter = getDataScopeFilter(dataFilter);
        if (StringUtils.isNotEmpty(page.getOrder()) && page.getSortOrder() != null) {
            String sort = page.getSortOrder() != null && page.getSortOrder() == SortOrder.ASC ? "asc" : "";
            return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                    .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, ContractDo.class,
                            String.format("%s %s", page.getOrder(), sort), System.currentTimeMillis());
        } else {
            String defaultOrder = "endDate";
            return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                    .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, ContractDo.class, defaultOrder,
                            System.currentTimeMillis());
        }
    }

    @Override
    public PageResult<ContractDo> selectApprovalRecordPage(ContractQueryDto page, ContractDo data, String keywords, Long dateTime) {
        DataFilter empFilter = getBaseFilter();
        DataFilter contractFilter = getBaseFilter().andNe("isHideInApproval", "1");
        if (data.getOrganize() != null) {
            empFilter = empFilter.andEq("organize", data.getOrganize());
        }

        if (StringUtils.isNotEmpty(keywords)) {
            empFilter = empFilter.and(DataFilter.regex("workno", keywords).orRegex("name", keywords));
        }

        if (StringUtils.isNotEmpty(data.getApprovalStatus().getValue())) {
            contractFilter = contractFilter.andEq("approvalStatus", data.getApprovalStatus().getValue());
        }

        FilterFunction[] ff = page.doJoinDataFilter(contractFilter, Sets.set("hireDate", "empType", "company"), empFilter);
        DataFilter[] filters = joinDataScopeFilter((DataFilter) ff[0], (DataFilter) ff[1]);
        val join = DataJoin.joinModels(
                DataJoin.ModelInfo.model("entity.hr.EmpWorkInfo", BeanUtil.getPropName(EmpWorkInfoDo.class, "workno", "name", "enName", "company", "companyTxt", "hireDate", "empStatus", "organize", "organizeTxt", "job", "jobTxt", "empType", "post", "postTxt", "workHour", "workplace", "workplaceTxt"),
                        filters[1]),
                DataJoin.ModelInfo.model(data.getIdentifier(), BeanUtil.getPropName(ContractDo.class, "bid", "owner", "signType", "contractNo", "contractTypeSet", "contractTypeSetTxt", "contractSettingType", "contractType", "periodType", "contractPeriod", "probationPeriod", "probationPeriodEndDate", "signDate", "startDate", "endDate", "signTime", "contractStatus", "approvalStatus", "terminationDate", "terminationReason", "signProcessStatus", "initiateStatus", "attachFile", "launchDate", "remark"),
                        filters[0]),
                DataJoin.JoinInfo.joinInfo(
                        Lists.list(
                                DataJoin.JoinPropertyInfo.joinProperty("entity.hr.EmpWorkInfo", data.getIdentifier(), "empId", "owner$empId")
                        )
                )
        );
        PageResult<Triple<DataSimple, DataSimple, DataSimple>> pageResult = join
                .limit(page.getPageSize(), page.getPageNo())
                .join(DataSimple.class, System.currentTimeMillis());
        List<ContractDo> doList = pageResult.getItems().stream()
                .map(tri -> toDo(tri.getLeft(), tri.getMiddle()))
                .collect(Collectors.toList());
        return new PageResult<>(doList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    @Override
    public long countCrossDate(String identifier, String empId, String bid, Long startDate, Long endDate, String contractStatus) {
        String s = String.valueOf(startDate);
        String e = String.valueOf(endDate);
        DataFilter dataFilter = getBaseFilter().andEq("owner$empId", empId);
        if (bid != null) {
            dataFilter = dataFilter.andNe("bid", bid);
        }
        /**
         * 判断已有合同的开始时间 startDate，结束时间 endDate
         * 若 s ≤ startDate,endDate ≤ e，当前合同与已有合同时间范围交叉
         */
        if (StringUtils.isNotBlank(contractStatus)) {
            dataFilter = dataFilter.andEq("contractStatus", contractStatus);
        }
        dataFilter = dataFilter.and(DataFilter.ge("startDate", s).andLe("startDate", e)
                .or(DataFilter.ge("endDate", s).andLe("endDate", e))
                .or(DataFilter.ge("startDate", s).andLe("endDate", e))
                .or(DataFilter.le("startDate", s).andGe("endDate", e)));
        return DataQuery.identifier(identifier).decrypt().queryInvisible().specifyLanguage()
                .count(dataFilter, System.currentTimeMillis());
    }

    @Override
    public PageResult<ContractDo> getContractPage(String identifier, ContractStatusQueryDto queryDto) {
        DataFilter dataFilter = getBaseFilter();
        // 小于结束时间
        if (queryDto.getEndDate() != null) {
            dataFilter = dataFilter.and(DataFilter.lt("endDate", String.valueOf(queryDto.getEndDate())));
        }

        if (queryDto.getEmpId() != null) {
            dataFilter = dataFilter.andEq("owner$empId", queryDto.getEmpId());
        }

        // 等于开始时间
        if (queryDto.getStartDate() != null) {
            dataFilter = dataFilter.andEq("startDate", String.valueOf(queryDto.getStartDate()));
        }

        if (queryDto.getLastDay() != null && queryDto.getCurrentDay() != null) {
            dataFilter = dataFilter.andLe("startDate", String.valueOf(queryDto.getCurrentDay()))
                    .andGe("startDate", String.valueOf(queryDto.getLastDay()));
        }

        if (StringUtils.isNotEmpty(queryDto.getContractStatus())) {
            dataFilter = dataFilter.andEq("contractStatus", queryDto.getContractStatus());
        }

        if (StringUtils.isNotEmpty(queryDto.getApprovalStatus())) {
            dataFilter = dataFilter.andEq("approvalStatus", queryDto.getApprovalStatus());
        }

        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(queryDto.getPageSize(), queryDto.getPageNo()).filter(dataFilter, ContractDo.class);
    }

    @Override
    public PageResult<ContractDo> getList(String identifier, String approvalStatus, ContractQueryDto queryDto) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("approvalStatus", approvalStatus);
        if (StringUtils.isNotEmpty(queryDto.getOrganize())) {
            filter = filter.andEq("organize", queryDto.getOrganize());
        }

        if (StringUtils.isNotEmpty(queryDto.getKeyword())) {
            filter = filter.and(DataFilter.regex("owner$workno", queryDto.getKeyword())
                    .orRegex("owner$name", queryDto.getKeyword()));
        }

        if (null != queryDto.getContractDays()) {
            Long day = queryDto.getContractDays() > 0 ?
                    Long.valueOf(queryDto.getContractDays() + 1) : Long.valueOf(queryDto.getContractDays());
            // 86400000L 一天的毫秒数
            day = 86400000L * day;
            day = DateUtil.getCurrentTimestamp() + day;
            filter = filter.andLt("endDate", day.toString());
        }

        // DataFilter.eq("periodType", PeriodTypeEnum.NO_FIXED.getCode());

        DataFilter where = (DataFilter) queryDto.doDataFilter(queryDto.getFilters(),
                DataFilter.eq("tenantId", UserContext.getTenantId()));

        // 数据范围权限
        filter = getDataScopeFilter(filter);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(queryDto.getPageSize(), queryDto.getPageNo()).max(where, filter, "startDate",
                        System.currentTimeMillis(), ContractDo.class,
                        "ownerEmpId");
        return pageResult;
    }

    @Override
    public List<ContractDo> getInoperativeContract(String identifier, List<String> empList,
                                                   String approvalStatus, String contractStatus) {
        DataFilter filter = getBaseFilter();
        if (StringUtils.isNotEmpty(contractStatus)) {
            filter = filter.andEq("approvalStatus", approvalStatus).andEq("contractStatus", contractStatus).andIn("owner$empId", empList);
        } else {
            filter = filter.andEq("approvalStatus", approvalStatus).andIn("owner$empId", empList);
        }
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<ContractDo> getContractByContractNo(String identifier, List<String> contractNos) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("contractNo", contractNos);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<ContractDo> getContractByWorkNo(String identifier, List<String> workNos) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("owner$workno", workNos);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    /**
     * join查询的数据权限
     *
     * @param contractFilter
     * @param empFilter
     * @return
     */
    private DataFilter[] joinDataScopeFilter(DataFilter contractFilter, DataFilter empFilter) {
        // 数据范围权限
        Result<Map<String, String>> dataScope = getDataScopeFeign().getDataScope(Long.valueOf(UserContext.getUserId()), DataScopeConstant.CONTRACT_MANAGE_PARENT_CODE);
        Map<String, String> scopeMap = null;
        if (null == dataScope || !dataScope.isSuccess() || null == (scopeMap = dataScope.getData()) || scopeMap.isEmpty()) {
            return new DataFilter[]{contractFilter, empFilter};
        }

        // 指定合同公司
        String dataScopeValue = scopeMap.get(DataScopeConstant.CONTRACT_ASSIGN_CONTRACT_COMPANY);
        if (StringUtil.isNotEmpty(dataScopeValue)) {
            Map<String, Object> companyMap = FastjsonUtil.toObject(dataScopeValue, Map.class);
            String detail = (String) companyMap.get("detail");
            if (StringUtil.isNotEmpty(detail)) {
                List<String> company = Arrays.stream(detail.split(",")).collect(Collectors.toList());
                empFilter = ((Boolean) companyMap.get("complement")) ? empFilter.andNotIn("company", company)
                        : empFilter.andIn("company", company);
            }
        }

        // 指定创建人
        dataScopeValue = scopeMap.get(DataScopeConstant.CONTRACT_ASSIGN_CREATE_BY);
        if (StringUtil.isNotEmpty(dataScopeValue)) {
            Map<String, Object> companyMap = FastjsonUtil.toObject(dataScopeValue, Map.class);
            String userId = UserContext.getUserId();
            if (StringUtil.isNotEmpty(userId)) {
                contractFilter = (Boolean) companyMap.get("complement") ? contractFilter.andNe("createBy", userId)
                        : contractFilter.andEq("createBy", userId);
            }
        }

        return new DataFilter[]{contractFilter, empFilter};
    }

    private ContractDo toDo(DataSimple empWorkInfo, DataSimple contract) {
        ContractDo c = buildData(contract, ContractDo.class);
        EmpWorkInfoDo ewi = buildData(empWorkInfo, EmpWorkInfoDo.class);
        com.caidaocloud.util.BeanUtil.copyWithNoValue(ewi, c);
        c.getOwner().setName(ewi.getName());
        c.getOwner().setWorkno(ewi.getWorkno());
        c.getOwner().setEnName(ewi.getEnName());
        return c;
    }

    @Override
    public List<ContractDo> getContractByLastDay(String identifier, Long lastDay) {
        DataFilter filter = getBaseFilter();
        String endDate = String.valueOf(lastDay);
        DataFilter tFilter = DataFilter.ne("terminationDate", null).lt("terminationDate", endDate);
        tFilter = tFilter.or(DataFilter.ne("dissolveDate", null).lt("dissolveDate", endDate));
        filter = filter.andEq("approvalStatus", ApprovalStatusEnum.PASSED.getIndex().toString())
                .andEq("contractStatus", ContractStatusEnum.EFFECTIVE.getIndex())
                .and(tFilter);
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(100, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public List<ContractDo> getContractByInApproval(String identifier, List<String> empList) {
        DataFilter filter = getBaseFilter();
        filter = filter.andIn("owner$empId", empList)
                .andEq("approvalStatus", ApprovalStatusEnum.IN_APPROVAL.getIndex().toString());
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(500, 1).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }

    @Override
    public ContractDo selectByLoseEfficacy(String identifier, ContractDo data) {
        DataFilter filter = getBaseFilter();
        filter = filter.andEq("owner$empId", data.getOwner().getEmpId())
                .andEq("contractStatus", ContractStatusEnum.INVALID.getIndex());
        List<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(500, 1).filter(filter, ContractDo.class).getItems();
        if (CollectionUtils.isEmpty(pageResult)){
            return new ContractDo();
        }
        return pageResult.stream().sorted(Comparator.comparing(ContractDo::getEndDate).reversed()).collect(Collectors.toList()).get(0);
    }

    @Override
    public List<ContractDo> getArchiveData(String identifier, BasePage page) {
        DataFilter filter = getBaseFilter();
        filter = filter.andNe("attachFile$names", null)
                .andNe("attachFile$names", "[]");
        PageResult<ContractDo> pageResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(filter, ContractDo.class);
        return getPageList(pageResult);
    }
}






























