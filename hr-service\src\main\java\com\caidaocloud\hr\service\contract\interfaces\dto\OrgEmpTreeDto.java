package com.caidaocloud.hr.service.contract.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("租户组织员工树信息")
@Accessors(chain = true)
public class OrgEmpTreeDto {
    @ApiModelProperty("员工信息")
    private List<EmpTreeNodeDto> rootUserList;
    @ApiModelProperty("组织信息")
    private List<OrgTreeNodeDto> deptList;
}
