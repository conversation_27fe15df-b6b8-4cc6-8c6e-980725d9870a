package com.caidaocloud.hr.service.contract.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新签合同
 * <AUTHOR>
 */
@Data
@ApiModel("员工新签合同VO")
public class EmpWorkInfoNewlySignedVo {

    @ApiModelProperty("主键ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("入职日期")
    private String hireDate;

    @ApiModelProperty("员工状态")
    private String empStatusTxt;

    @ApiModelProperty("所属组织名称")
    private String organizeTxt;

    @ApiModelProperty("关联的职务名称")
    private String jobTxt;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("工作地名称")
    private String workplaceTxt;

    @ApiModelProperty("合同公司名称")
    private String companyTxt;

    @ApiModelProperty("合同公司名称")
    private String empTypeTxt;
}
