package com.caidaocloud.hr.service.dto;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工汇报线DTO")
public class EmpReportLineDto {
    @ApiModelProperty("bid")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("员工工号")
    private String workno;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("员工英文名")
    private String enName;

    @ApiModelProperty("是否主岗")
    private Boolean majorPost;

    @ApiModelProperty("所属组织ID")
    private String organize;

    @ApiModelProperty("所属组织编码")
    private String organizeCode;

    @ApiModelProperty("所属组织编码名称")
    private String organizeTxt;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("岗位编码")
    private String postCode;

    @ApiModelProperty("岗位名称")
    private String postTxt;

    @ApiModelProperty("直接上级")
    private EmpSimple leadEmp;

    @ApiModelProperty("直接上级组织ID")
    private String leaderOrganize;

    @ApiModelProperty("直接上级组织编码")
    private String leaderOrganizeCode;

    @ApiModelProperty("直接上级组织名称")
    private String leaderOrganizeTxt;

    @ApiModelProperty("直接上级岗位ID")
    private String leaderPost;

    @ApiModelProperty("直接上级岗位编码")
    private String leaderPostCode;

    @ApiModelProperty("直接上级岗位名称")
    private String leaderPostTxt;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();

}
