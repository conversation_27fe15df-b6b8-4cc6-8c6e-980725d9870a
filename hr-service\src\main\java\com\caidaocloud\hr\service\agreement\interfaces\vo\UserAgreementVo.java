package com.caidaocloud.hr.service.agreement.interfaces.vo;

import com.caidaocloud.hr.service.enums.AgreementTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 14/10/2024 1:56 下午
 */
@Data
public class UserAgreementVo implements Serializable {

    /**
     * 协议ID
     */
    private String bid;

    /**
     * 协议类型
     */
    private AgreementTypeEnum type;

    /**
     * 协议名称
     */
    private Map<String, String> i18Name;

    /**
     * 协议汇总
     */
    private String summary;

    /**
     * 协议内容
     */
    private String content;

    /**
     * 是否启用
     */
    private String status;

}
