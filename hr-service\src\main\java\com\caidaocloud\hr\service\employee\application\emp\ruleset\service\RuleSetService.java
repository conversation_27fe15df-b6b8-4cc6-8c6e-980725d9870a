package com.caidaocloud.hr.service.employee.application.emp.ruleset.service;

import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RuleSetDo;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.RuleSetDomainService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RuleSetService {
    @Resource
    private RuleSetDomainService ruleSetDomainService;

    public String save(RuleSetDo data) {
        return ruleSetDomainService.save(data);
    }

    public RuleSetDo getRuleSet() {
        return ruleSetDomainService.getRuleSet();
    }

    public boolean getWorkFlowSwitch(String code) {
        boolean result = false;
        RuleSetDo ruleSet = getRuleSet();
        List<String> sceneList = FastjsonUtil.toList(ruleSet.getScene(), String.class);
        if (ruleSet.getOpenWorkFlow() != null && ruleSet.getOpenWorkFlow() && sceneList.contains(code)) {
            result = true;
        }
        return result;
    }
}
