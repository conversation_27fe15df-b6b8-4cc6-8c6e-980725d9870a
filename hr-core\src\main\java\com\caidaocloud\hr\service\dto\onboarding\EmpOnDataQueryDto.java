package com.caidaocloud.hr.service.dto.onboarding;

import com.caidaocloud.hr.service.enums.onboarding.EmpOnDataQueryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmpOnDataQueryDto {

    /**
     * 角色code 如 HRBP
     */
    private String roleCode;

    private EmpOnDataQueryTypeEnum typeEnum;

    /**
     * 组织bid
     */
    private List<String> organize;

    private List<String> empIds;

    private Long dateTime;
}
