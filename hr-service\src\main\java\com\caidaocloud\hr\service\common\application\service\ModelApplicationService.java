package com.caidaocloud.hr.service.common.application.service;

import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.hrpaas.metadata.sdk.feign.IMetadataFeign;
import com.google.common.collect.Lists;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/18
 **/
@Service
public class ModelApplicationService {
    @Resource
    private IMetadataFeign metadataFeign;

    public List<MetadataPropertyVo> getContractModel() {
        var result = metadataFeign.one("entity.hr.LastContract");
        if (!result.isSuccess() || result.getData() == null) {
            return Lists.newArrayList();
        }
        var metadata = JsonEnhanceUtil.toObject(result.getData(), MetadataVo.class);
        return fetchProperties(metadata);
    }

    private List<MetadataPropertyVo> fetchProperties(MetadataVo metadata) {
        List<MetadataPropertyVo> propertyList = Lists.newArrayList();
        if (metadata == null) {
            return propertyList;
        }
        if (!CollectionUtils.isEmpty(metadata.getStandardProperties())) {
            propertyList.addAll(metadata.getStandardProperties());
        }
        if (!CollectionUtils.isEmpty(metadata.getCustomProperties())) {
            propertyList.addAll(metadata.getCustomProperties());
        }
        return propertyList;
    }
}