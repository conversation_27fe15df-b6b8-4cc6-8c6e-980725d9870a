package com.caidaocloud.hr.service.employee.application.emp.dto.resume;

import com.caidaocloud.hr.service.employee.infrastructure.enums.FormResumeTypeEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class EmpResumeConfigDto {
    private Long id;
    private String tenantId;
    // 模板路径
    private String template;
    // 字段配置
    private List<EmpResumePropDto> configs;
    // 合并配置约束
    private Map<String, ResumeEmpMergeConfig> mergeConfigs;
    //表单特殊处理约束配置
    private Map<String, ? extends ResumeFormConfig> formConfigs;
    private String fileName;

    public String getFileName() {
        return null != template && !"".equals(template) ? template.substring(template.lastIndexOf("-") + 1) : "";
    }

    public static EmpResumeConfigDto convert(String str) {
        if (StringUtils.isBlank(str)) {
            return new EmpResumeConfigDto();
        }
        Map object = FastjsonUtil.toObject(str, Map.class);
        EmpResumeConfigDto empResumeConfig = FastjsonUtil.convertObject(object, EmpResumeConfigDto.class);
        Object formConfigObj = object.get("formConfigs");
        if (Objects.nonNull(formConfigObj)) {
            Map<String, ResumeFormConfig> formConfigs = Maps.newHashMap();
            for (Map.Entry<String, Map> entry : ((Map<String, Map>) formConfigObj).entrySet()) {
                FormResumeTypeEnum formResumeTypeEnum = FormResumeTypeEnum.convert(entry.getValue().getOrDefault("type", "").toString());
                if (Objects.isNull(formResumeTypeEnum)) {
                    continue;
                }
                ResumeFormConfig resumeFormConfig = formResumeTypeEnum.convertParam(entry.getValue());
                formConfigs.putIfAbsent(entry.getKey(), resumeFormConfig);
            }
            empResumeConfig.setFormConfigs(formConfigs);
        }
        return empResumeConfig;
    }
}