package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.contract.application.enums.ApprovalStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContinueStatus;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.InitiateStatus;
import com.caidaocloud.hr.service.contract.application.enums.SignTypeEnum;
import com.caidaocloud.hr.service.contract.domain.repository.IContractRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.util.FunUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.TIME_RANGE_CONFLICT;
import static com.caidaocloud.hr.service.contract.application.enums.ContractHideStatus.SHOW;
import static com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum.CANCEL;

@Slf4j
@Data
@Service
public class ContractDo extends BaseDomainDoImpl<ContractDo> {
    /**
     * 合同签署人
     */
    private EmpSimple owner;
    /**
     * 入职日期
     */
    private Long hireDate;
    /**
     * 员工状态
     */
    private EnumSimple empStatus;
    /**
     * 所属组织Id
     */
    private String organize;
    /**
     * 所属组织名称
     */
    private String organizeTxt;
    /**
     * 所属组织编码
     */
    private String organizeCode;
    /**
     * 关联的职务ID
     */
    private String job;
    /**
     * 关联的职务名称
     */
    private String jobTxt;
    /**
     * 岗位ID
     */
    private String post;
    /**
     * 岗位名称
     */
    private String postTxt;
    /**
     * 员工类型
     */
    private DictSimple empType;
    /**
     * 签订类型
     */
    private EnumSimple signType;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 合同公司Id
     */
    private String company;
    /**
     * 所属公司名称
     */
    private String companyTxt;
    /**
     * 合同设置Bid
     */
    private String contractTypeSet;
    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;
    /**
     * 签订合同时，合同设置的合同类型
     */
    private DictSimple contractSettingType;
    /**
     * 合同类别
     */
    private DictSimple contractType;
    /**
     * 合同期限类型
     */
    private EnumSimple periodType;
    /**
     * 合同期（月）
     */
    private Integer contractPeriod;
    /**
     * 试用期（月）
     */
    private Integer probationPeriod;
    /**
     * 试用期截止日期
     */
    private Long probationPeriodEndDate;
    /**
     * 合同签订日期
     */
    private Long signDate;
    /**
     * 合同开始日期
     */
    private Long startDate;
    private Long calcEndDate;
    /**
     * 合同结束日期
     */
    private Long endDate;
    /**
     * 合同签订次数
     */
    private Integer signTime;
    /**
     * 合同状态
     */
    private EnumSimple contractStatus;
    /**
     * 审批状态
     */
    private EnumSimple approvalStatus;
    /**
     * 合同终止日期
     */
    private Long terminationDate;
    /**
     * 合同终止原因
     */
    private String terminationReason;
    /**
     * 合同附件
     */
    private Attachment attachFile;
    /**
     * 工作地ID
     */
    private String workplace;
    /**
     * 工作地名称
     */
    private String workplaceTxt;
    /**
     * 工时制
     */
    private EnumSimple workHour;
    /**
     * 备注
     */
    private String remark;
    /**
     * 签署流程状态
     */
    private EnumSimple signProcessStatus;
    /**
     * 发起签署状态
     */
    private EnumSimple initiateStatus;
    /**
     * 是否在审批记录显示
     */
    private EnumSimple isHideInApproval;
    /**
     * 合同发起日期
     */
    private Long launchDate;
    /**
     * 注册地址
     */
    private String registerAddress;
    /**
     * 试用期期限
     */
    private EnumSimple probation;
    /**
     * 上一份合同bid
     */
    private String lastContract;
    /**
     * 合同期（年）
     */
    private BigDecimal contractYear;
    /**
     * 意向反馈建议
     */
    private DictSimple feedback;
    /**
     * 续签意向状态
     */
    private EnumSimple continueStatus;
    /**
     * 续签意向书id
     */
    private String continueLetter;
    /**
     * 合同解除日期
     */
    private Long dissolveDate;
    /**
     * 合同解除原因
     */
    private String dissolveReason;

    public static String IDENTIFIER = "entity.hr.Contract";

    @Resource
    private IContractRepository contractRepository;

    @Override
    public BaseRepository<ContractDo> getRepository() {
        return contractRepository;
    }

    @Override
    protected DataSimple build() {
        return new ContractDo();
    }

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    public String save(ContractDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setDeleted(Boolean.FALSE);
        data.setIdentifier(IDENTIFIER);

        if (null == data.getInitiateStatus() || StringUtils.isBlank(data.getInitiateStatus().getValue())) {
            // 设置默认发起签署状态 为 未发起
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(InitiateStatus.initiate.getValue());
            data.setInitiateStatus(enumSimple);
        }

        // 默认在审批记录中展示
        if (data.getIsHideInApproval() == null) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(SHOW.getCode());
            data.setIsHideInApproval(enumSimple);
        }

        if (data.getContinueStatus() == null) {
            EnumSimple continueStatus = new EnumSimple();
            continueStatus.setValue(ContinueStatus.TODO.name());
            data.setContinueStatus(continueStatus);
        }

        // 校验合同时间范围
        checkContractDate(data, ContractStatusEnum.EFFECTIVE.getIndex());
        // 校验候选人合同编号是否存在
        checkContractNo(data);
        log.info("saving contract>>>>>>>>>>>>{}", FastjsonUtil.toJson(data));
        if (StringUtil.isNotEmpty(data.getBid())) {
            contractRepository.updateById(data);
            return data.getBid();
        }

        return contractRepository.insert(data).getBid();
    }

    public String onlySave(ContractDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setDeleted(Boolean.FALSE);
        data.setIdentifier(IDENTIFIER);

        // 设置默认发起签署状态 为 未发起
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(InitiateStatus.initiate.getValue());
        data.setInitiateStatus(enumSimple);

        // 默认在审批记录中展示
        if (data.getIsHideInApproval() == null) {
            enumSimple = new EnumSimple();
            enumSimple.setValue(SHOW.getCode());
            data.setIsHideInApproval(enumSimple);
        }

        log.info("saving contract>>>>>>>>>>>>{}", FastjsonUtil.toJson(data));
        return contractRepository.insert(data).getBid();
    }

    public String update(ContractDo data) {
        doUpdateBaseInfo(data);
        // 校验合同时间范围
        checkContractDate(data, ContractStatusEnum.EFFECTIVE.getIndex());
        log.info("updating contract>>>>>>>>>>>>{}", FastjsonUtil.toJson(data));
        contractRepository.updateById(data);
        return data.getBid();
    }

    private void doUpdateBaseInfo(ContractDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setCreateBy(data.getCreateBy() == null ? userId : data.getCreateBy());
        data.setUpdateTime(System.currentTimeMillis());
        data.setDeleted(Boolean.FALSE);
        data.setIdentifier(IDENTIFIER);
        ContractDo hisContract = contractRepository.selectById(data.getBid(), data.getIdentifier());
        if (hisContract != null) {
            data.setIsHideInApproval(hisContract.getIsHideInApproval());
        }
    }

    public String updateContractDo(ContractDo data) {
        doUpdateBaseInfo(data);
        log.info("timer updating contract>>>>>>>>>>>>{}", FastjsonUtil.toJson(data));
        contractRepository.updateById(data);
        return data.getBid();
    }

    public String onlyUpdate(ContractDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(tenantId);
        data.setUpdateBy(userId);
        data.setCreateBy(data.getCreateBy() == null ? userId : data.getCreateBy());
        data.setUpdateTime(System.currentTimeMillis());
        data.setDeleted(Boolean.FALSE);
        data.setIdentifier(IDENTIFIER);
        ContractDo hisContract = contractRepository.selectById(data.getBid(), data.getIdentifier());
        if (hisContract != null) {
            data.setIsHideInApproval(hisContract.getIsHideInApproval());
        }

        log.info("updating contract>>>>>>>>>>>>{}", FastjsonUtil.toJson(data));
        contractRepository.updateById(data);
        return data.getBid();
    }

    /**
     * 校验合同的开始、结束时间不与已有合同交叉
     *
     * @param data
     */
    private void checkContractDate(ContractDo data, String contractStatus) {
        if (FunUtil.isNull(data)) {
            return;
        }
        if (data.getSignType() != null && SignTypeEnum.CHANGE.getCode().equals(data.getSignType().getValue())) {
            return;
        }
        long cnt = contractRepository.countCrossDate(IDENTIFIER, data.getOwner().getEmpId(), data.getBid(), data.getStartDate(), data.getEndDate(), contractStatus);
        PreCheck.preCheckArgument(cnt > 0, LangUtil.getMsg(TIME_RANGE_CONFLICT));
    }

    /**
     * 合同编号校验
     *
     * @param data
     */
    private void checkContractNo(ContractDo data) {
        // 校验候选人合同中合同编号是否存在
        String empId = data.getOwner().getEmpId();
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .and(DataFilter.eq("empId", empId).or(DataFilter.eq("reentryId", empId)));
        PageResult<DataSimple> empResult = DataQuery.identifier("entity.onboarding.EmpWorkInfo").specifyLanguage().queryInvisible().decrypt()
                .filter(dataFilter, DataSimple.class);
        if (CollectionUtils.isEmpty(empResult.getItems())) {
            return;
        }
        SimplePropertyValue preEmpId = (SimplePropertyValue) empResult.getItems().get(0).getProperties().get("empId");
        PageResult<DataSimple> result = DataQuery.identifier("entity.onboarding.contract").specifyLanguage().queryInvisible().decrypt()
                .filter(DataFilter.eq("contractNo", data.getContractNo()).andNe("empId", preEmpId.getValue())
                        .andEq("tenantId", UserContext.getTenantId())
                        .andNe("deleted", Boolean.TRUE.toString()), DataSimple.class);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(result.getItems()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80008));
    }

    public PageResult<ContractDo> selectPage(ContractQueryDto dto) {
        ContractDo query = doContractQuery(dto);
        return contractRepository.selectPage(dto, query, dto.getKeyword(), null);
    }

    public PageResult<ContractDo> selectRecordPage(ContractQueryDto dto) {
        ContractDo query = doContractQuery(dto);
        return contractRepository.selectRecordPage(dto, query, dto.getKeyword(), null);
    }

    public List<ContractDo> getLinkContracts(List<String> ids) {
        return contractRepository.getLinkContracts(IDENTIFIER, ids);
    }

    public List<ContractDo> getLinkContracts(List<String> ids, List<String> approvalStatus) {
        return contractRepository.getLinkContracts(IDENTIFIER, ids, approvalStatus);
    }

    private ContractDo doContractQuery(ContractQueryDto dto) {
        ContractDo query = new ContractDo();
        query.setIdentifier(IDENTIFIER);
        query.setTenantId(UserContext.getTenantId());
        query.setOrganize(dto.getOrganize());
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(dto.getStatus());
        query.setApprovalStatus(enumSimple);
        return query;
    }

    public List<ContractDo> getEmpCurrentContract(List<String> empList, String approvalStatus) {
        return contractRepository.getEmpCurrentContract(IDENTIFIER, empList, approvalStatus);
    }

    public ContractDo getEmpLastContract(String empId, String approvalStatus) {
        return contractRepository.getEmpLastContract(IDENTIFIER, empId, approvalStatus);
    }

    public List<ContractDo> getEmpContractByStatus(String empId, String approvalStatus, List<String> contractStatus) {
        return contractRepository.getEmpContractByStatus(IDENTIFIER, empId, approvalStatus, contractStatus);
    }

    public List<ContractDo> getEmpHistoryContract(List<String> empList, String approvalStatus) {
        return contractRepository.getEmpHistoryContract(IDENTIFIER, empList, approvalStatus);
    }

    public List<ContractDo> getContractSignList(List<String> empList) {
        return contractRepository.getContractSignList(IDENTIFIER, empList);
    }

    public void deleteByEmpIds(List<String> empIds) {
        contractRepository.deleteByEmpIds(IDENTIFIER, empIds);
    }

    public PageResult<ContractDo> getList(String approvalStatus, ContractQueryDto queryDto) {
        return contractRepository.getList(IDENTIFIER, approvalStatus, queryDto);
    }

    public List<ContractDo> getInoperativeContract(List<String> empList, String approvalStatus, String contractStatus) {
        return contractRepository.getInoperativeContract(IDENTIFIER, empList, approvalStatus, contractStatus);
    }

    public List<ContractDo> getContractByContractNo(List<String> contractNos) {
        return contractRepository.getContractByContractNo(IDENTIFIER, contractNos);
    }

    public List<ContractDo> getContractByWorkNo(List<String> workNos) {
        return contractRepository.getContractByWorkNo(IDENTIFIER, workNos);
    }

    public List<ContractDo> getLatestList(List<String> empList) {
        return contractRepository.getLatestList(IDENTIFIER, empList);
    }

    public PageResult<ContractDo> getLatestList(int pageNo, int pageSize) {
        return contractRepository.getLatestPage(IDENTIFIER, pageNo, pageSize);
    }

    public PageResult<ContractDo> selectContractUser(ContractQueryDto dto) {
        ContractDo query = new ContractDo();
        query.setIdentifier(IDENTIFIER);
        query.setOrganize(dto.getOrganize());
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(dto.getStatus());
        query.setApprovalStatus(enumSimple);
        return contractRepository.selectContractUser(dto, query, dto.getKeyword(), null);
    }

    public long countSignTime(String empId) {
        return contractRepository.countSignTime(IDENTIFIER, empId);
    }

    public Map<String, Long> countSignTimes(List<String> empIds) {
        return contractRepository.countGroupSignTime(IDENTIFIER, empIds);
    }

    public PageResult<ContractDo> selectApprovalRecordPage(ContractQueryDto dto) {
        ContractDo query = doContractQuery(dto);
        return contractRepository.selectApprovalRecordPage(dto, query, dto.getKeyword(), null);
    }

    public void calcStatus() {
        // 合同状态
        EnumSimple contractStatus = this.contractStatus == null ? new EnumSimple() : this.contractStatus;
        // 对失效的合同，不计算合同状态
        if (CANCEL.getIndex().equals(contractStatus.getValue()) || null == startDate) {
            return;
        }
        try {
            Long current = DateUtil.getCurrentTimestamp();
            if (current < startDate) {
                // 当前时间<合同开始时间 (合同未生效)
                contractStatus.setValue(String.valueOf(ContractStatusEnum.IN_EFFECTIVE.getIndex()));
            } else if (current <= endDate) {
                // 合同结束时间>=当前时间>=合同开始时间 (合同生效中)
                contractStatus.setValue(String.valueOf(ContractStatusEnum.EFFECTIVE.getIndex()));
            } else {
                // 当前时间>合同开始时间 (合同失效)
                contractStatus.setValue(String.valueOf(ContractStatusEnum.INVALID.getIndex()));
            }

            // 合同终止时间不为null 且 当前时间 ≥ 终止时间 (合同已终止)
            if (terminationDate != null && current >= terminationDate) {
                contractStatus.setValue(String.valueOf(ContractStatusEnum.TERMINATED.getIndex()));
            }
            this.contractStatus = contractStatus;
        } catch (Exception e) {
            log.error("Troubleshoot the abnormal contract status.{}", e.getMessage(), e);
        }
    }

    public PageResult<ContractDo> getContractPage(ContractStatusQueryDto queryDto) {
        return contractRepository.getContractPage(IDENTIFIER, queryDto);
    }

    public void convertContractStatus() {
        if (null == contractStatus || null == contractStatus.getValue()) {
            return;
        }

        long nowTime = DateUtil.getCurrentTimestamp();
        // 如果是生效中的合同，但是当天零点日期已经大于合同结束日期，则显示合同已失效
        if (ContractStatusEnum.EFFECTIVE.getIndex().equals(contractStatus.getValue()) && null != endDate && nowTime > endDate) {
            String index = ContractStatusEnum.INVALID.getIndex(), textKey = ContractStatusEnum.INVALID.getName();
            contractStatus.setValue(index);
            contractStatus.setText(LangUtil.getMsg(Integer.valueOf(textKey)));
            return;
        }

        nowTime = nowTime + 24 * 60 * 60 * 1000;
        // 如果是已终止的合同，但是合同终止日期还未到，则显示生效中；
        boolean effective = ContractStatusEnum.TERMINATED.getIndex().equals(contractStatus.getValue()) && null != terminationDate && nowTime < terminationDate;
        effective = effective || (ContractStatusEnum.RELEASED.getIndex().equals(contractStatus.getValue()) && null != dissolveDate && nowTime < dissolveDate);
        // 或者如果是已解除，且解除日期还为到，则显示生效中
        if (effective) {
            String index = ContractStatusEnum.EFFECTIVE.getIndex(), textKey = ContractStatusEnum.EFFECTIVE.getName();
            contractStatus.setValue(index);
            contractStatus.setText(LangUtil.getMsg(Integer.valueOf(textKey)));
            return;
        }
    }

    public List<ContractDo> getContractByLastDay(Long lastDay) {
        return contractRepository.getContractByLastDay(IDENTIFIER, lastDay);
    }

    /**
     * 改签合同时，生效中的合同立即终止；未生效的合同只赋值生效日期
     */
    public void doLastContractChangeSign(Long tDate, String newSignType) {
        if (!SignTypeEnum.CHANGE.getCode().equals(newSignType)) {
            return;
        }

        boolean isEffect = ContractStatusEnum.EFFECTIVE.getIndex().equals(this.getContractStatus().getValue()),
                isNotEffect = ContractStatusEnum.IN_EFFECTIVE.getIndex().equals(this.getContractStatus().getValue());
        if (!isEffect && !isNotEffect) {
            return;
        }

        // 如果是生效中，则终止
        if (isEffect && tDate < com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.getMidnightTimestamp()) {
            EnumSimple simple = new EnumSimple();
            simple.setValue(String.valueOf(ContractStatusEnum.TERMINATED.getIndex()));
            this.setContractStatus(simple);
        }

        // 合同终止日期改完当前合同前一天
        this.setTerminationDate(tDate);
    }

    public List<ContractDo> getContractByInApproval(List<String> empList) {
        return contractRepository.getContractByInApproval(IDENTIFIER, empList);
    }

    public ContractDo selectByLoseEfficacy(ContractDo data) {
        return contractRepository.selectByLoseEfficacy(IDENTIFIER, data);
    }

    /**
     * 获取归档附件
     * @param page
     */
    public List<ContractDo> getArchiveData(BasePage page) {
        return contractRepository.getArchiveData(IDENTIFIER, page);
    }
}