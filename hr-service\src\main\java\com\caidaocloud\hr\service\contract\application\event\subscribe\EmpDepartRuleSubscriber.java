package com.caidaocloud.hr.service.contract.application.event.subscribe;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.temination.application.event.dto.ScheduleTaskMsg;
import com.caidaocloud.hr.service.temination.application.event.dto.TerminationTaskDetailDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Authot CI29616
 * @Date 2023/8/15 15:38
 * @Version 1.0
 **/
@Slf4j
@Component
public class EmpDepartRuleSubscriber {

//    @Resource
//    private EmpBasicInfoService empBasicInfoService;

    @Resource
    private UserFeignClient userFeignClient;

//    /**
//     * 接受 员工离职信息；
//     *
//     * @param message
//     */
//    @RabbitHandler
//    @RabbitListener(
//            bindings = @QueueBinding(
//                    value = @Queue(value = "caidaocloud.hr.emp.depart.queue", durable = "true"),
//                    exchange = @Exchange(value = "schedule.task.exchange.termination"),
//                    key = {"routingKey.termination.end.stop"}
//            )
//    )
//    public void departProcess(String message) {
//        log.info("Subscribe EmpDepartMessage={}", message);
//        try {
//            //转换消息类型
//            ScheduleTaskMsg msg = FastjsonUtil.toObject(message, ScheduleTaskMsg.class);
//
//            String tenantId = msg.getUserInfo().getTenantId();
//
//            //设置当前用户
//            userInfoSet(tenantId);
//
//            //配置 empId
//            String taskDetail = msg.getTaskDetail();
//            TerminationTaskDetailDto ttd = FastjsonUtil.toObject(taskDetail, TerminationTaskDetailDto.class);
//            //离职操作；
//            empBasicInfoService.stopUserRule(ttd.getEmpId(), ttd.getTerminationDate());
//        } catch (Exception ex) {
//            log.error("process EmpDepartMessage err,{}", ex.getMessage(), ex);
//        } finally {
//            SecurityUserUtil.removeSecurityUserInfo();
//            UserContext.remove();
//        }
//    }

    /**
     * 接受员工停用消息；
     *
     * @param message
     */
    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "caidaocloud.hr.emp.stop.queue", durable = "true"),
                    exchange = @Exchange(value = "schedule.task.exchange.stop"),
                    key = {"routingKey.schedule.task.stop"}
            )
    )
    public void stopProcess(String message) {
        log.info("Subscribe EmpStopMessage={}", message);
        try {
            //转换消息类型
            ScheduleTaskMsg msg = FastjsonUtil.toObject(message, ScheduleTaskMsg.class);

            String tenantId = msg.getUserInfo().getTenantId();

            //设置当前用户
            userInfoSet(tenantId);

            //配置 empId
            String taskDetail = msg.getTaskDetail();
            TerminationTaskDetailDto ttd = FastjsonUtil.toObject(taskDetail, TerminationTaskDetailDto.class);
            //停用操作；
            userFeignClient.stopUser(ttd.getEmpId());
        } catch (Exception ex) {
            log.error("process EmpStopMessage err,{}", ex.getMessage(), ex);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private void userInfoSet(String tenantId) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setEmpId(0L);
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        user.setStaffId(0L);
        user.setUserid(0);
        user.setEmpid(0);
        UserContext.setCurrentUser(user);
    }


}
