package com.caidaocloud.hr.service.contract.interfaces.dto;

import cn.hutool.core.lang.mutable.MutablePair;
import com.caidaocloud.hr.service.contract.application.enums.ConditionNodeRelation;
import com.caidaocloud.hr.service.contract.application.enums.ConditionNodeType;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.SimplePropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Slf4j
public class ConditionTreeDto {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("分组节点关系")
    private ConditionNodeRelation relation;
    @ApiModelProperty("分组类型")
    private ConditionNodeType type;
    @ApiModelProperty("分组子节点")
    private List<ConditionNodeDto> children;

    public MutablePair<List<Long>, List<Long>> filterEmpIds(ContractTypeSetDo contractTypeSet, List<Long> empIdList){
        // 排除已离职和已删除的员工
        DataFilter dataFilter = DataFilter.in("empType$dictValue", contractTypeSet.getEmpType())
                .andNe("empStatus", "1").andNe("deleted", Boolean.TRUE.toString());
        // 初始化复合条件的员工ID集合
        getEmpIdAll("entity.hr.EmpWorkInfo", dataFilter, "empId", empIdList);
        log.info("----init filter empIdList={}", FastjsonUtil.toJson(empIdList));
        List<Long> allTypeEmpIds = new ArrayList<>(empIdList);
        // 根据自定义条件再次过滤
        if (children != null && !children.isEmpty()) {
            List<Long> childrenResult = children.get(0).filterEmpIds(allTypeEmpIds);
            if("1712487408205_626".equals(id)){
                log.info("=====>"+ allTypeEmpIds.contains(1895834939644251l));
                log.info("=====>>"+ childrenResult.contains(1895834939644251l));
                log.info("=====>>>"+children.get(0).getId());
            }
            for (int i = 1; i < children.size(); i++) {
                val nodeResult = children.get(i).filterEmpIds(allTypeEmpIds);
                // and取该组条件交集，or取该组条件并集
                if("1712487408205_626".equals(id)){
                    log.info("=====>"+ allTypeEmpIds.contains(1895834939644251l));
                    log.info("=====>>"+ nodeResult.contains(1895834939644251l));
                    log.info("=====>>>"+children.get(i).getId());
                }
                if (ConditionNodeRelation.and.equals(relation)) {
                    childrenResult = (List<Long>) CollectionUtils.intersection(childrenResult, nodeResult);
                } else {
                    childrenResult = (List<Long>) CollectionUtils.union(childrenResult, nodeResult);
                }
            }
            allTypeEmpIds = (List<Long>) CollectionUtils.intersection(childrenResult, allTypeEmpIds);
        }
        if (CollectionUtils.isEmpty(allTypeEmpIds)) {
            return new MutablePair<>(allTypeEmpIds, null);
        } else {
            List<Long> filterEmpIds =  getEmpIdAll("entity.hr.LastContract",
                    DataFilter.eq("contractType$dictValue", contractTypeSet.getContractClass().getValue())
                            .andEq("deleted", Boolean.FALSE.toString()), "owner.empId", Lists.list(allTypeEmpIds));
            log.info("----contractType dictValue filterEmpIds={},allTypeEmpIds={}",
                    FastjsonUtil.toJson(filterEmpIds), FastjsonUtil.toJson(allTypeEmpIds));
            List<Long> otherEmpIds = allTypeEmpIds.stream().filter(it -> !filterEmpIds.contains(it)).collect(Collectors.toList());

            return new MutablePair<>(filterEmpIds, otherEmpIds);
        }
    }

    public static List<Long> getEmpIdAll(String identifier, DataFilter filter, String prop, List<Long> empIdList){
        long dataTime = System.currentTimeMillis();
        int i = 1;
        DataQuery dataQuery = DataQuery.identifier(identifier);
        Set<Long> set = new HashSet<>();
        do{
            dataQuery.limit(5000, i);
            val page = dataQuery.exp().filterProperties(filter, Lists.list(prop), dataTime);
            if(null == page || null == page.getItems()){
                break;
            }

            // Set<Long> set = new HashSet<>();
            page.getItems().forEach(map -> {
                if(null != map.get(prop)){
                    set.add(Long.valueOf(map.get(prop)));
                }
            });

            if(page.getTotal() / 5000 + (page.getTotal() % 5000 > 0 ? 1 : 0) <= i){
                break;
            }

            i++;
        } while(true);

        if (!empIdList.isEmpty()) {
            set.retainAll(empIdList);
        }
        empIdList.clear();
        empIdList.addAll(set);
        return empIdList;
    }

    public static List<DataSimple> queryAll(String identifier, DataFilter filter){
        int i = 1;
        DataQuery dataQuery = DataQuery.identifier(identifier);
        List<DataSimple> result = Lists.list();
        do{
            dataQuery.limit(5000, i);
            val page = dataQuery.filter(filter, DataSimple.class);
            result.addAll(page.getItems());
            if(page.getTotal()/5000 + (page.getTotal() % 5000 > 0 ? 1 : 0) <=i){
                break;
            }
            i++;
        }while(true);
        return result;
    }

    public static String fetchSimpleDataProperty(DataSimple dataSimple, String property){
        NestPropertyValue properties = dataSimple.getProperties();
        SimplePropertyValue simplePropertyValue =  (SimplePropertyValue) properties.get(property);
        return simplePropertyValue.getValue();
    }

    public static PropertyValue fetchDataProperty(DataSimple dataSimple, String property){
        NestPropertyValue properties = dataSimple.getProperties();
        return properties.get(property);
    }

    public List<Long> initEmpIdList(List<Long> empIdList, List<String> empType, String contractClass){
        if(null != empIdList && !empIdList.isEmpty()){
            return empIdList;
        }

        // 所有符合员工类型的empId
        getEmpIdAll("entity.hr.EmpWorkInfo",
                DataFilter.in("empType$dictValue", empType), "empId", empIdList);

        getEmpIdAll("entity.hr.LastContract",
                DataFilter.eq("contractType$dictValue", contractClass)
                        .andEq("deleted", Boolean.FALSE.toString()), "owner.empId", empIdList);

        return empIdList;
    }
}
