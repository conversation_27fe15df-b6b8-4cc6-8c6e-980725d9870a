package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.HRApplication;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeEmpMergeConfig;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 简历解析聚合排序功能测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-13
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = HRApplication.class)
public class EmpResumeServiceSortTest {

    @Autowired
    private EmpResumeService empResumeService;

    @Before
    public void setUp() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    /**
     * 测试基本的聚合排序功能
     */
    @Test
    public void testBasicMergeAndSort() throws Exception {
        // 准备测试数据
        Map<String, Object> transferDataMap = createTransferDataMap();
        Map<String, ResumeEmpMergeConfig> mergeConfigs = createBasicMergeConfigs();

        // 调用私有方法进行测试
        Map<String, Object> result = invokeProcessMergeConfigs(transferDataMap, mergeConfigs);

        // 验证结果
        Assert.assertNotNull("结果不能为空", result);
        Assert.assertTrue("应该包含SOCIAL_CAREER数据", result.containsKey("SOCIAL_CAREER"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> socialCareerList = (List<Map<String, Object>>) result.get("SOCIAL_CAREER");
        Assert.assertNotNull("SOCIAL_CAREER列表不能为空", socialCareerList);
        Assert.assertTrue("SOCIAL_CAREER列表应该有数据", socialCareerList.size() > 0);
        
        // 验证排序字段是否存在
        for (Map<String, Object> item : socialCareerList) {
            Assert.assertTrue("应该包含sort字段", item.containsKey("sort"));
        }
        
        log.info("基本聚合排序测试结果: {}", FastjsonUtil.toJson(result));
    }

    /**
     * 测试日期字段排序功能
     */
    @Test
    public void testDateFieldSort() throws Exception {
        // 准备带日期的测试数据
        Map<String, Object> transferDataMap = createDateTransferDataMap();
        Map<String, ResumeEmpMergeConfig> mergeConfigs = createDateMergeConfigs();

        // 调用私有方法进行测试
        Map<String, Object> result = invokeProcessMergeConfigs(transferDataMap, mergeConfigs);

        // 验证结果
        Assert.assertNotNull("结果不能为空", result);
        Assert.assertTrue("应该包含POST_CLC数据", result.containsKey("POST_CLC"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> postClcList = (List<Map<String, Object>>) result.get("POST_CLC");
        Assert.assertNotNull("POST_CLC列表不能为空", postClcList);
        Assert.assertTrue("POST_CLC列表应该有数据", postClcList.size() > 0);
        
        // 验证日期排序是否正确
        for (int i = 0; i < postClcList.size() - 1; i++) {
            Long currentSort = (Long) postClcList.get(i).get("sort");
            Long nextSort = (Long) postClcList.get(i + 1).get("sort");
            Assert.assertTrue("日期排序应该是升序", currentSort <= nextSort);
        }
        
        log.info("日期字段排序测试结果: {}", FastjsonUtil.toJson(result));
    }

    /**
     * 测试二次排序功能
     */
    @Test
    public void testSecondarySort() throws Exception {
        // 准备带二次排序的测试数据
        Map<String, Object> transferDataMap = createSecondarySortDataMap();
        Map<String, ResumeEmpMergeConfig> mergeConfigs = createSecondarySortConfigs();

        // 调用私有方法进行测试
        Map<String, Object> result = invokeProcessMergeConfigs(transferDataMap, mergeConfigs);

        // 验证结果
        Assert.assertNotNull("结果不能为空", result);
        Assert.assertTrue("应该包含RANK_POINTS数据", result.containsKey("RANK_POINTS"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rankPointsList = (List<Map<String, Object>>) result.get("RANK_POINTS");
        Assert.assertNotNull("RANK_POINTS列表不能为空", rankPointsList);
        Assert.assertTrue("RANK_POINTS列表应该有数据", rankPointsList.size() > 0);
        
        // 验证二次排序是否正确
        verifySortOrder(rankPointsList);
        
        log.info("二次排序测试结果: {}", FastjsonUtil.toJson(result));
    }

    /**
     * 测试日期字符串转时间戳功能
     */
    @Test
    public void testDateConversion() throws Exception {
        // 测试不同日期格式
        String[] dateStrings = {
            "2025-01-01",
            "2025/01/01",
            "2025-01-01 00:00:00",
            "2025/01/01 00:00:00"
        };

        for (String dateString : dateStrings) {
            long timestamp = invokeConvertDateToTimestamp(dateString);
            Assert.assertTrue("时间戳应该大于0", timestamp > 0);
            log.info("日期 {} 转换为时间戳: {}", dateString, timestamp);
        }
    }

    /**
     * 测试空数据边界情况
     */
    @Test
    public void testEmptyDataBoundary() throws Exception {
        // 测试空的transferDataMap
        Map<String, Object> result1 = invokeProcessMergeConfigs(new HashMap<>(), createBasicMergeConfigs());
        Assert.assertNotNull("空数据结果不能为null", result1);
        Assert.assertTrue("空数据结果应该为空", result1.isEmpty());

        // 测试空的mergeConfigs
        Map<String, Object> result2 = invokeProcessMergeConfigs(createTransferDataMap(), new HashMap<>());
        Assert.assertNotNull("空配置结果不能为null", result2);
        Assert.assertTrue("空配置结果应该为空", result2.isEmpty());

        // 测试null值
        Map<String, Object> result3 = invokeProcessMergeConfigs(null, null);
        Assert.assertNotNull("null值结果不能为null", result3);
        Assert.assertTrue("null值结果应该为空", result3.isEmpty());
    }

    // ========== 私有辅助方法 ==========

    /**
     * 调用私有方法 processMergeConfigs
     */
    private Map<String, Object> invokeProcessMergeConfigs(Map<String, Object> transferDataMap, 
                                                         Map<String, ResumeEmpMergeConfig> mergeConfigs) throws Exception {
        Method method = EmpResumeService.class.getDeclaredMethod("processMergeConfigs", Map.class, Map.class);
        method.setAccessible(true);
        return (Map<String, Object>) method.invoke(empResumeService, transferDataMap, mergeConfigs);
    }

    /**
     * 调用私有方法 convertDateToTimestamp
     */
    private long invokeConvertDateToTimestamp(String dateString) throws Exception {
        Method method = EmpResumeService.class.getDeclaredMethod("convertDateToTimestamp", String.class);
        method.setAccessible(true);
        return (Long) method.invoke(empResumeService, dateString);
    }

    /**
     * 创建基本测试数据
     */
    private Map<String, Object> createTransferDataMap() {
        Map<String, Object> transferDataMap = new HashMap<>();
        
        // 创建TRANSFER_1931312175077378数据
        List<Map<String, Object>> transfer1Data = new ArrayList<>();
        Map<String, Object> item1 = new HashMap<>();
        item1.put("effectiveDate$Transfer", "2025-01-01");
        item1.put("workplace_work$Transfer", "上海");
        item1.put("organize_work$Transfer", "技术部");
        item1.put("post_work$Transfer", "高级工程师");
        transfer1Data.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("effectiveDate$Transfer", "2025-02-01");
        item2.put("workplace_work$Transfer", "北京");
        item2.put("organize_work$Transfer", "产品部");
        item2.put("post_work$Transfer", "产品经理");
        transfer1Data.add(item2);

        transferDataMap.put("TRANSFER_1931312175077378", transfer1Data);
        
        return transferDataMap;
    }

    /**
     * 创建基本合并配置
     */
    private Map<String, ResumeEmpMergeConfig> createBasicMergeConfigs() {
        Map<String, ResumeEmpMergeConfig> mergeConfigs = new HashMap<>();
        
        ResumeEmpMergeConfig socialCareerConfig = new ResumeEmpMergeConfig();
        
        // 配置映射
        Map<String, List<Map<String, String>>> config = new HashMap<>();
        List<Map<String, String>> mappings = new ArrayList<>();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("a", "effectiveDate$Transfer");
        mapping.put("b", "workplace_work$Transfer");
        mapping.put("c", "organize_work$Transfer");
        mapping.put("d", "post_work$Transfer");
        mappings.add(mapping);
        config.put("TRANSFER_1931312175077378", mappings);
        socialCareerConfig.setConfig(config);
        
        // 配置主排序字段
        ResumeEmpMergeConfig.SortField mainSortField = new ResumeEmpMergeConfig.SortField();
        mainSortField.setField("a");
        mainSortField.setDataType("DATE_TXT");
        socialCareerConfig.setMainSortField(mainSortField);
        
        mergeConfigs.put("SOCIAL_CAREER", socialCareerConfig);
        
        return mergeConfigs;
    }

    /**
     * 创建带日期的测试数据
     */
    private Map<String, Object> createDateTransferDataMap() {
        Map<String, Object> transferDataMap = new HashMap<>();
        
        List<Map<String, Object>> transferData = new ArrayList<>();
        
        // 创建不同日期的数据
        Map<String, Object> item1 = new HashMap<>();
        item1.put("effectiveDate$Transfer", "2025-03-01");
        item1.put("workplace_work$Transfer", "深圳");
        transferData.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("effectiveDate$Transfer", "2025-01-01");
        item2.put("workplace_work$Transfer", "上海");
        transferData.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("effectiveDate$Transfer", "2025-02-01");
        item3.put("workplace_work$Transfer", "北京");
        transferData.add(item3);

        transferDataMap.put("TRANSFER_1979468587694080", transferData);
        
        return transferDataMap;
    }

    /**
     * 创建日期排序配置
     */
    private Map<String, ResumeEmpMergeConfig> createDateMergeConfigs() {
        Map<String, ResumeEmpMergeConfig> mergeConfigs = new HashMap<>();
        
        ResumeEmpMergeConfig postClcConfig = new ResumeEmpMergeConfig();
        
        // 配置映射
        Map<String, List<Map<String, String>>> config = new HashMap<>();
        List<Map<String, String>> mappings = new ArrayList<>();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("a", "effectiveDate$Transfer");
        mapping.put("b", "workplace_work$Transfer");
        mappings.add(mapping);
        config.put("TRANSFER_1979468587694080", mappings);
        postClcConfig.setConfig(config);
        
        // 配置主排序字段
        ResumeEmpMergeConfig.SortField mainSortField = new ResumeEmpMergeConfig.SortField();
        mainSortField.setField("a");
        mainSortField.setDataType("DATE_TXT");
        postClcConfig.setMainSortField(mainSortField);
        
        mergeConfigs.put("POST_CLC", postClcConfig);
        
        return mergeConfigs;
    }

    /**
     * 创建二次排序测试数据
     */
    private Map<String, Object> createSecondarySortDataMap() {
        Map<String, Object> transferDataMap = new HashMap<>();
        
        List<Map<String, Object>> transferData = new ArrayList<>();
        
        // 创建相同日期但不同类型的数据
        Map<String, Object> item1 = new HashMap<>();
        item1.put("effectiveDate$Transfer", "2025-01-01");
        item1.put("txt", "等级积分");
        transferData.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("effectiveDate$Transfer", "2025-01-01");
        item2.put("txt", "Rank");
        transferData.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("effectiveDate$Transfer", "2025-01-01");
        item3.put("txt", "CLC等级");
        transferData.add(item3);

        transferDataMap.put("TRANSFER_1979468587694080", transferData);
        
        return transferDataMap;
    }

    /**
     * 创建二次排序配置
     */
    private Map<String, ResumeEmpMergeConfig> createSecondarySortConfigs() {
        Map<String, ResumeEmpMergeConfig> mergeConfigs = new HashMap<>();
        
        ResumeEmpMergeConfig rankPointsConfig = new ResumeEmpMergeConfig();
        
        // 配置映射
        Map<String, List<Map<String, String>>> config = new HashMap<>();
        List<Map<String, String>> mappings = new ArrayList<>();
        Map<String, String> mapping = new HashMap<>();
        mapping.put("a", "effectiveDate$Transfer");
        mapping.put("b", "txt");
        mappings.add(mapping);
        config.put("TRANSFER_1979468587694080", mappings);
        rankPointsConfig.setConfig(config);
        
        // 配置主排序字段
        ResumeEmpMergeConfig.SortField mainSortField = new ResumeEmpMergeConfig.SortField();
        mainSortField.setField("a");
        mainSortField.setDataType("DATE_TXT");
        rankPointsConfig.setMainSortField(mainSortField);
        
        // 配置二次排序
        ResumeEmpMergeConfig.SortFieldVal secondSortList = new ResumeEmpMergeConfig.SortFieldVal();
        secondSortList.setField("b");
        
        List<ResumeEmpMergeConfig.FieldValSort> sortList = new ArrayList<>();
        
        ResumeEmpMergeConfig.FieldValSort sort1 = new ResumeEmpMergeConfig.FieldValSort();
        sort1.setVal("Rank");
        sort1.setSort(1);
        sortList.add(sort1);
        
        ResumeEmpMergeConfig.FieldValSort sort2 = new ResumeEmpMergeConfig.FieldValSort();
        sort2.setVal("等级积分");
        sort2.setSort(2);
        sortList.add(sort2);
        
        ResumeEmpMergeConfig.FieldValSort sort3 = new ResumeEmpMergeConfig.FieldValSort();
        sort3.setVal("CLC等级");
        sort3.setSort(3);
        sortList.add(sort3);
        
        secondSortList.setSortList(sortList);
        rankPointsConfig.setSecondSortList(secondSortList);
        
        mergeConfigs.put("RANK_POINTS", rankPointsConfig);
        
        return mergeConfigs;
    }

    /**
     * 验证排序顺序
     */
    private void verifySortOrder(List<Map<String, Object>> list) {
        // 验证主排序
        for (int i = 0; i < list.size() - 1; i++) {
            Long currentSort = (Long) list.get(i).get("sort");
            Long nextSort = (Long) list.get(i + 1).get("sort");
            Assert.assertTrue("主排序应该是升序", currentSort <= nextSort);
        }
        
        // 验证二次排序（相同日期的项目）
        Map<Long, List<Map<String, Object>>> groupedByDate = new HashMap<>();
        for (Map<String, Object> item : list) {
            Long sortValue = (Long) item.get("sort");
            groupedByDate.computeIfAbsent(sortValue, k -> new ArrayList<>()).add(item);
        }
        
        for (List<Map<String, Object>> sameDataGroup : groupedByDate.values()) {
            if (sameDataGroup.size() > 1) {
                // 验证二次排序顺序
                for (int i = 0; i < sameDataGroup.size() - 1; i++) {
                    String currentType = (String) sameDataGroup.get(i).get("b");
                    String nextType = (String) sameDataGroup.get(i + 1).get("b");
                    
                    int currentOrder = getSecondaryOrder(currentType);
                    int nextOrder = getSecondaryOrder(nextType);
                    
                    Assert.assertTrue("二次排序应该按配置顺序", currentOrder <= nextOrder);
                }
            }
        }
    }

    /**
     * 获取二次排序顺序
     */
    private int getSecondaryOrder(String type) {
        switch (type) {
            case "Rank":
                return 1;
            case "等级积分":
                return 2;
            case "CLC等级":
                return 3;
            default:
                return 999;
        }
    }
} 