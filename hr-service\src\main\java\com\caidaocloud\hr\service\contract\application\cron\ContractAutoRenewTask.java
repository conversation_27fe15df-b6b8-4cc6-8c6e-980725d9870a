package com.caidaocloud.hr.service.contract.application.cron;

import com.caidaocloud.hr.service.contract.application.service.ContractAutoRenewService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 自动发起合同续签任务 CAIDAOM-2131
 * created by: FoAng
 * create time: 29/11/2024 5:07 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractAutoRenewTask {

    private ContractAutoRenewService contractAutoRenewService;

    /**
     * 刷新自动续签匹配关系定时任务
     * @return
     */
    @XxlJob("contractRenewRelJobHandler")
    public ReturnT<String> ContractRenewRelJobHandler() {
        XxlJobHelper.log("[XxlJob] refresh contractRenewRel start at time: {}", DateUtil.formatTime());
        String[] tenantIds = fetchTenantByParams();
        if (tenantIds != null && tenantIds.length > 0) {
            XxlJobHelper.log("[XxlJob] contractRenewRel tenantIds: {}", String.join(",", tenantIds));
            for (String tenantId : tenantIds) {
                try {
                    SecurityUserInfo userInfo = new SecurityUserInfo();
                    userInfo.setTenantId(tenantId);
                    userInfo.setUserId(0L);
                    userInfo.setEmpId(0L);
                    SecurityUserUtil.setSecurityUserInfo(userInfo);
                    contractAutoRenewService.doRefreshTenantRuleRelAction(tenantId);
                } catch (Exception e) {
                    XxlJobHelper.log("[XxlJob] contractAutoRenew exec error, msg: {}", e.getMessage());
                } finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            }
        }
        XxlJobHelper.log("[XxlJob] contractAutoRenew end at time: {}", DateUtil.formatTime());
        return ReturnT.SUCCESS;
    }


    /**
     * 发起合同自动续签定时任务
     * @return
     */
    @XxlJob("contractAutoRenewJobHandler")
    public ReturnT<String> ContractAutoRenewJobHandler() {
        XxlJobHelper.log("[XxlJob] contractAutoRenew start at time: {}", DateUtil.formatTime());
        String[] tenantIds = fetchTenantByParams();
        if (tenantIds != null && tenantIds.length > 0) {
            XxlJobHelper.log("[XxlJob] contractAutoRenew tenantIds: {}", String.join(",", tenantIds));
            for (String tenantId : tenantIds) {
                try {
                    SecurityUserInfo userInfo = new SecurityUserInfo();
                    userInfo.setTenantId(tenantId);
                    userInfo.setUserId(0L);
                    userInfo.setEmpId(0L);
                    SecurityUserUtil.setSecurityUserInfo(userInfo);
                    contractAutoRenewService.doAutoRenewTask();
                } catch (Exception e) {
                    XxlJobHelper.log("[XxlJob] contractAutoRenew exec error, msg: {}", e.getMessage());
                } finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            }
        }
        XxlJobHelper.log("[XxlJob] contractAutoRenew end at time: {}", DateUtil.formatTime());
        return ReturnT.SUCCESS;
    }

    private String[] fetchTenantByParams() {
        String params = XxlJobHelper.getJobParam();
        if (StringUtil.isEmpty(params)) {
            XxlJobHelper.log("[XxlJob] tenant params is empty");
        } else {
            String[] tenantIds = params.split(",");
            XxlJobHelper.log("[XxlJob] tenantIds: {}", String.join(",", tenantIds));
            return tenantIds;
        }
        return null;
    }
}
