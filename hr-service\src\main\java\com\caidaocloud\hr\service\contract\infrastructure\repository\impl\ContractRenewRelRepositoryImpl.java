package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRelDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContractRenewRelRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 2/12/2024 10:25 上午
 */
@Slf4j
@Service
public class ContractRenewRelRepositoryImpl extends BaseRepositoryImpl<ContractRenewRelDo> implements IContractRenewRelRepository {

    @Override
    public List<ContractRenewRelDo> listByEmpId(String identifier, String empId) {
        DataFilter dataFilter = DataFilter.eq("empId", empId);
        return DataQuery.identifier(identifier).filter(dataFilter, ContractRenewRelDo.class).getItems();
    }

    @Override
    public Integer oneVersion(String identifier, String ruleId) {
        DataFilter dataFilter = DataFilter.eq("renewRuleId", ruleId);
        List<ContractRenewRelDo> renewRelDos = DataQuery.identifier(identifier).limit(1, 1)
                .filter(dataFilter, ContractRenewRelDo.class).getItems();
        return Optional.ofNullable(renewRelDos).filter(CollectionUtils::isNotEmpty).map(it -> it.get(0)).map(ContractRenewRelDo::getVersion)
                .orElse(0);
    }

    @Override
    public void batchInsert(String identifier, List<ContractRenewRelDo> relDoList) {
        DataInsert.identifier(identifier).batchInsert(relDoList);
    }

    @Override
    public void deleteRuleRel(String identifier, String version, String ruleId) {
        DataFilter dataFilter = DataFilter.eq("deleted", Boolean.FALSE.toString());
        if (StringUtil.isNotEmpty(version)) {
            dataFilter = dataFilter.andEq("version", version);
        }
        if (StringUtil.isNotEmpty(ruleId)) {
            dataFilter = dataFilter.andEq("renewRuleId", ruleId);
        }
        DataDelete.identifier(identifier).batchDelete(dataFilter);
    }
}
