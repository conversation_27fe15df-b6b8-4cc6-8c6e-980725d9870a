package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractStatusQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;
import java.util.Map;

/**
 * @Author: Aaron.Chen
 * @Date: 2022/4/26 14:25
 * @Description:
 **/
public interface IContractRepository extends BaseRepository<ContractDo> {
    List<ContractDo> getEmpCurrentContract(String identifier, List<String> empList, String approvalStatus);

    ContractDo getEmpLastContract(String identifier, String empId, String approvalStatus);

    List<ContractDo> getEmpContractByStatus(String identifier, String empId, String approvalStatus,List<String> contractStatus);

    List<ContractDo> getEmpHistoryContract(String identifier, List<String> empList, String approvalStatus);

    List<ContractDo> getContractSignList(String identifier, List<String> empList);

    void deleteByEmpIds(String identifier, List<String> empIds);

    PageResult<ContractDo> getList(String identifier, String approvalStatus, ContractQueryDto queryDto);

    List<ContractDo> getInoperativeContract(String identifier, List<String> empList, String approvalStatus,String contractStatus);

    List<ContractDo> getContractByContractNo(String identifier,List<String> contractNos);

    List<ContractDo> getContractByWorkNo(String identifier,List<String> workNos);

    List<ContractDo> getLatestList(String identifier, List<String> empList);

    PageResult<ContractDo> selectContractUser(BasePage page, ContractDo data, String keywords, Long dateTime);

    PageResult<ContractDo> getLatestPage(String identifier, int pageNo, int pageSize);

    long countSignTime(String identifier, String empId);

    Map<String, Long> countGroupSignTime(String identifier, List<String> empIds);

    List<ContractDo> getLinkContracts(String identifier, List<String> ids);

    List<ContractDo> getLinkContracts(String identifier, List<String> ids, List<String> approvalStatus);

    PageResult<ContractDo> selectRecordPage(BasePage page, ContractDo data, String keywords, Long dateTime);

    PageResult<ContractDo> selectApprovalRecordPage(ContractQueryDto page, ContractDo data, String keywords, Long dateTime);

    long countCrossDate(String identifier, String empId, String bid, Long startDate, Long endDate, String contractStatus);

    PageResult<ContractDo> getContractPage(String identifier, ContractStatusQueryDto queryDto);

    List<ContractDo> getContractByLastDay(String identifier, Long lastDay);

    List<ContractDo> getContractByInApproval(String identifier, List<String> empList);

    ContractDo selectByLoseEfficacy(String identifier, ContractDo data);

    List<ContractDo> getArchiveData(String identifier, BasePage page);
}
