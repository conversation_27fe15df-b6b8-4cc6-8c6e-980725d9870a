package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class AuthDataScopeFeignFallback implements AuthDataScopeFeignClient {
    @Override
    public Result<Map<String, String>> getDataScope(Long subjectId, String parentCode) {
        return Result.fail();
    }

    @Override
    public Result<List<AuthRoleScopeFilterDetail>> getScopeBySubject(String identifier, Long subjectId, String dymaicTarget) {
        return Result.fail();
    }

    @Override
    public Result<List<String>> getAuthCodes() {
        return Result.fail();
    }
}
