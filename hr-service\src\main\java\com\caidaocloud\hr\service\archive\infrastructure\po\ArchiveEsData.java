package com.caidaocloud.hr.service.archive.infrastructure.po;

import com.caidaocloud.hr.service.archive.annotation.DynamicIndex;
import lombok.Data;
import org.zxp.esclientrhl.annotation.ESID;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.annotation.ESMetaData;
import org.zxp.esclientrhl.enums.DataType;

import java.io.Serializable;

/**
 * 文件归档es实体类
 * created by: FoAng
 * create time: 6/6/2024 10:15 上午
 */
@Data
@DynamicIndex
@ESMetaData(indexName = "archive_file", number_of_shards = 5, number_of_replicas = 0, printLog = true)
public class ArchiveEsData implements Serializable, Cloneable {

    /**
     * 主键ID
     */
    @ESID
    private Long archiveId;

    /**
     * 员工工号
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String workno;

    /**
     * 员工Id
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String empId;

    /**
     * 员工empType
     */
    @ESMapping(datatype = DataType.integer_type)
    private int archiveEmpType;

    /**
     * 员工姓名
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String name;

    /**
     * 员工英文名
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String enName;

    /**
     * 入职日期
     */
    @ESMapping(datatype = DataType.long_type)
    private Long hireDate;

    /**
     * 离职日期
     */
    @ESMapping(datatype = DataType.long_type)
    private Long leaveDate;

    /**
     * 员工状态
     */
    @ESMapping(nested_class = EsEnumSimple.class)
    private EsEnumSimple empStatus;

    /**
     *  任职组织
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String organize;

    /**
     * 任职组织全路径
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String organizePath;

    /**
     * 任职组织名称
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String organizeTxt;

    /**
     * 岗位
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String post;

    /**
     * 岗位名称
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String postTxt;

    /**
     * 用工类型
     */
    @ESMapping(nested_class = EsDictSimple.class)
    private EsDictSimple empType;

    /**
     * 工作地ID
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String workplace;

    /**
     * 工作地名称
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String workplaceTxt;

    /**
     * 合同公司ID
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String company;

    /**
     * 合同公司名称
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String companyTxt;

    /**
     * 所属业务
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String businessLine;

    /**
     * 子业务
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String subBusinessLine;

    /**
     * 业务类型
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String businessType;

    /**
     * 业务流程ID
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String businessId;

    /**
     * 附件名称
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String fileName;

    /**
     * 附件url地址
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String fileUrl;

    /**
     * 文件扩展类型名
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String suffixType;

    /**
     * 电子签合同ID
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String openContractId;

    /**
     * 电子签签署平台
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String signPlatform;

    /**
     * 附件名称首字母
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String prefix;

    /**
     * 员工信息扩展字段
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String REDate;

    /**
     * 员工入职sapId
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String sapid;

    /**
     * 事件时间
     */
    @ESMapping(datatype = DataType.long_type)
    private Long eventTime;

    @Override
    public ArchiveEsData clone() {
        try {
            return (ArchiveEsData) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
