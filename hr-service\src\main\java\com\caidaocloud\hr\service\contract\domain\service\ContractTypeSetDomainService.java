package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.condition.tree.ComponentValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.contract.application.exception.ContinueMatchException;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueContractTemplateDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractTypeSetDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueContractTemplateRepository;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractTypeSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractSetQueryDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.contract.ContinueContractTemplateDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hrpaas.paas.match.ConditionExp;
import com.caidaocloud.hrpaas.paas.match.ConditionNameMapping;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.paas.match.ConditionNode;
import com.caidaocloud.hrpaas.paas.match.ConditionOperatorEnum;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ContractTypeSetDomainService extends BaseDomainServiceImpl<ContractTypeSetDo, ContractTypeSetQueryDto> {
    @Resource
    private ContractTypeSetDo contractTypeSetDo;
    @Resource
    private CompanyDo companyDo;
    @Resource
    private IContinueContractTemplateRepository contractTemplateRepository;

    @Override
    public BaseDomainDo getDoService() {
        return contractTypeSetDo;
    }

    public void updateStatus(ContractTypeSetDo data, BusinessEventTypeEnum eventTypeEnum) {
        contractTypeSetDo.updateStatus(data, eventTypeEnum);
    }

    public List<ContractTypeSetDo> getByCondition(ContractTypeSetDo data) {
        return contractTypeSetDo.getByCondition(data);
    }

    @Override
    public ContractTypeSetDo save(ContractTypeSetDo data) {
        setRequiredField(data, true);
        linkDataConvert(data);
        return contractTypeSetDo.save(data);
    }

    @Override
    public ContractTypeSetDo updateById(ContractTypeSetDo data) {
        setRequiredField(data, false);
        linkDataConvert(data);
        contractTypeSetDo.updateById(data);
        return data;
    }

    @Override
    protected void linkDataConvert(ContractTypeSetDo data) {
        convertCompany(data);
    }

    private void convertCompany(ContractTypeSetDo data) {
        List<CompanyDo> comList = companyDo.selectList();
        comList.add(0, CompanyDo.selectAllNode());
        HashSet<String> comSet = new HashSet<>(data.getCompany());
        // PreCheck.preCheckNotNull(companyDo, LangUtil.getMsg(INVALID_COMPANY_BID));
        data.setCompanyTxt(comList.stream()
                .filter(com -> comSet.contains(com.getBid()))
                .map(com -> {
                    ComponentValue cv = new ComponentValue();
                    cv.setDisplay(com.getCompanyName());
                    cv.setValue(com.getBid());
                    return cv;
                }).collect(Collectors.toList()));
    }

    public boolean filterCondition(ContractTypeSetDo data, String company, String signType) {
        if (data.getCompany() == null) {
            return false;
        }

        if (null == signType) {
            return filterCondition(data, company);
        }
        return data.getCompany().contains(company) && data.getSignType().contains(signType);
    }

    public boolean filterCondition(ContractTypeSetDo data, String company) {
        return data.getCompany().contains(company);
    }

    public List<ContractTypeSetDo> getListByRegex(String company, String signType) {
        List<ContractTypeSetDo> datas = contractTypeSetDo.selectListByRegex(company, signType);
        return datas;
    }

    /**
     * 保存或更新续签合同模版
     *
     * @param contractTemplateDo
     */
    @PaasTransactional
    public void saveOrUpdateContinueContract(ContinueContractTemplateDo contractTemplateDo) {
        if (contractTemplateDo == null) {
            return;
        }
        if (StringUtils.isBlank(contractTemplateDo.getBid())) {
            ContinueContractTemplateDo.save(contractTemplateDo);
        } else {
            ContinueContractTemplateDo.update(contractTemplateDo);
        }
    }

    /**
     * 获取续签意向模版详情
     *
     * @param bid
     * @return
     */
    public Optional<ContinueContractTemplateDto> getConditionContractByBid(String bid) {
        if (StringUtils.isBlank(bid)) {
            return Optional.empty();
        }
        var contractTemplateDo = new ContinueContractTemplateDo().getByBid(bid);
        if (contractTemplateDo == null) {
            return Optional.empty();
        }
        return Optional.of(contractTemplateDo.convertDto());
    }

    /**
     * 分页获取续签意向模版
     *
     * @param page
     * @return
     */
    public PageResult<ContinueContractTemplateDto> getPageOfConditionContract(ContinueContractSetQueryDto page) {
        var securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        var contractTemplateDo = new ContinueContractTemplateDo();
        contractTemplateDo.setTenantId(securityUserInfo.getTenantId());
        PageResult<ContinueContractTemplateDo> doPageResult = contractTemplateRepository.selectPage(page, contractTemplateDo);
        var result = new PageResult<ContinueContractTemplateDto>();
        BeanUtils.copyProperties(doPageResult, result, "items");
        if (!CollectionUtils.isEmpty(doPageResult.getItems())) {
            result.setItems(doPageResult.getItems().stream().map(e -> e.convertDto()).collect(Collectors.toList()));
        } else {
            result.setItems(Lists.newArrayList());
        }
        return result;
    }



    /**
     * 获取续签意向模版集合
     * @return
     */
    public List<ContinueContractTemplateDto> getContinueTemplateList() {
        var securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        var contractTemplateDo = new ContinueContractTemplateDo();
        contractTemplateDo.setTenantId(securityUserInfo.getTenantId());
        List<ContinueContractTemplateDo> list = contractTemplateRepository.selectList(contractTemplateDo);
        return Sequences.sequence(list).map(ContinueContractTemplateDo::convertDto).toList();
    }

    /**
     * 移除续签意向模版
     *
     * @param bid
     */
    @PaasTransactional
    public void removeConditionContractByBid(String bid) {
        if (StringUtils.isBlank(bid)) {
            return;
        }
        var contractTemplateDo = new ContinueContractTemplateDo();
        contractTemplateDo.setBid(bid);
        contractTemplateRepository.delete(contractTemplateDo);
    }
    public List<ContractTypeSetDo> getListCompanyByRegex(String signType) {
        return contractTypeSetDo.getListCompanyByRegex(signType);
    }

    public List<ContinueContractTemplateDto> findMatchedContinueTemplate(String empId, List<ContinueContractTemplateDto> templates,List<ConditionNameMapping> mappings) {
        List<ContinueContractTemplateDto> matched = new ArrayList<>();
        for (ContinueContractTemplateDto dto : templates) {
            ConditionTree condition = convertConditionTree(dto);
            if (condition == null || condition.match(empId, mappings)) {
                matched.add(dto);
            }
        }
        if (matched.isEmpty()) {
            throw ContinueMatchException.globalException(ErrorMessage.fromCode("caidao.exception.error_80013"));
        }
        return matched;
    }

    private ConditionTree convertConditionTree(ContinueContractTemplateDto dto) {
        ConditionTree condition = FastjsonUtil.convertObject(dto.getCondition(), ConditionTree.class);
        if (condition==null) {
            return null;
        }
        convertConditionValue(condition.getChildren());
        condition.setIdentifierEmpIdKey(Maps.map("entity.hr.LastContract", "owner$empId"));
        return condition;
    }

    private void convertConditionValue(List<ConditionNode> children) {
        if (children==null) {
            return;
        }
        for (ConditionNode node : children) {
            ConditionExp condition = node.getCondition();
            if (condition != null && condition.getSimpleValue() == null) {
                if (condition.getSymbol() == ConditionOperatorEnum.IN) {
                    condition.setSimpleValue(StringUtils.join(FastjsonUtil.toList(String.valueOf(condition.getValue()), String.class), ","));
                }
                else {
                    condition.setSimpleValue(String.valueOf(condition.getValue()));
                }
            }
            convertConditionValue(node.getChildren());
        }
    }
}
