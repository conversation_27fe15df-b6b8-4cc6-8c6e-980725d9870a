package com.caidaocloud.hr.service.employee.application.event.publish;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.employee.application.event.dto.PublishRefreshMsgDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 21/9/2022 11:25 上午
 */
@Slf4j
@Service
public class MsgTypeRefreshPublish {

    private final static String MSG_TEMPLATE_REFRESH_EXCHANGE = "caidaocloud.message.fac.direct.exchange";

    private final static String MSG_TEMPLATE_REFRESH_ROUTING_KEY = "routingKey.message.template.refresh";

    @Resource
    private MqMessageProducer<RabbitBaseMessage> producer;

    public void sendRefreshMsgTemplate(String empId) {
        RabbitBaseMessage message = new RabbitBaseMessage();
        UserInfo userInfo = UserContext.preCheckUser();
        PublishRefreshMsgDto refreshMsgDto = new PublishRefreshMsgDto();
        refreshMsgDto.setTenantId(userInfo.getTenantId());
        refreshMsgDto.setUserId(userInfo.getUserId().toString());
        refreshMsgDto.setEmpId(empId);
        String msgBody = FastjsonUtil.toJson(refreshMsgDto);
        message.setBody(msgBody);
        message.setExchange(MSG_TEMPLATE_REFRESH_EXCHANGE);
        message.setRoutingKey(MSG_TEMPLATE_REFRESH_ROUTING_KEY);
        log.info("message template RefreshMessagePublish time={} body={}",
                System.currentTimeMillis(), msgBody);
        producer.publish(message);
    }
}
