package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.dto.EmpTrainDto;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpTrainDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpTrainDomainService;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EmpTrainService {
    @Resource
    private EmpTrainDomainService empTrainDomainService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    public List<EmpTrainDo> getEmpTrainList(String empId) {
        return empTrainDomainService.getEmpTrainList(empId);
    }

    public EmpTrainDo getDetail(String bid) {
        return empTrainDomainService.getDetail(bid);
    }

    public String save(EmpTrainDto dto) {
        EmpTrainDo data = ObjectConverter.convert(dto, EmpTrainDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        return empTrainDomainService.save(data);
    }

    private void doConvert(EmpTrainDto source, EmpTrainDo target) {
        // 培训类型
        if (StringUtil.isNotEmpty(source.getTrainType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getTrainType());
            target.setTrainType(dictSimple);
        }
    }

    public void update(EmpTrainDto dto) {
        EmpTrainDo data = ObjectConverter.convert(dto, EmpTrainDo.class);
        doConvert(dto, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        empTrainDomainService.update(data);
    }

    public void delete(String bid) {
        empTrainDomainService.delete(bid);
    }
}
