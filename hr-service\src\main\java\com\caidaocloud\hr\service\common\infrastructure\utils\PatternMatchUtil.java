package com.caidaocloud.hr.service.common.infrastructure.utils;

import lombok.var;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class PatternMatchUtil {
    private final static Pattern INTEGER_PATTERN = Pattern.compile("[0-9]+");
    private final static Pattern NUMBER_PATTERN = Pattern.compile("[+-]?[0-9]+(\\.[0-9]{1,4})?");
    private final static Pattern HTML_PATTERN = Pattern.compile("<([^>]*)>");
    private final static Pattern FLOAT_PATTERN = Pattern.compile("^[-+]?\\d*\\.\\d+$");
    private final static Pattern LIST_PATTERN = Pattern.compile("\\[.*]");



    public static boolean isString(String str) {
        if (!isNumber(str)) {
            return true;
        }
        return false;
    }

    public static boolean isInteger(String str) {
        Matcher matcher = INTEGER_PATTERN.matcher(str);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public static boolean isNumber(String str) {
        Matcher matcher = NUMBER_PATTERN.matcher(str);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public static boolean isFloat(String str) {
        Matcher matcher = FLOAT_PATTERN.matcher(str);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public static boolean isJsonStr(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("{") && str.endsWith("}")) {
                result = true;
            } else if (str.startsWith("[") && str.endsWith("]")) {
                result = true;
            }
        }
        return result;
    }

    public static String filterHtml(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        Matcher matcher = HTML_PATTERN.matcher(str);
        var result = new StringBuffer();
        var b = matcher.find();
        while (b) {
            matcher.appendReplacement(result, "");
            b = matcher.find();
        }
        matcher.appendTail(result);
        return result.toString();
    }

    public static boolean isList(String str) {
        Matcher matcher = LIST_PATTERN.matcher(str);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }
}