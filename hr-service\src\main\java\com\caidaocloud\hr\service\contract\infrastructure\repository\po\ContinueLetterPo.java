package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContinueLetterRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Slf4j
@Data
@Service
public class ContinueLetterPo extends BaseDomainDoImpl<ContinueLetterPo> {
    /**
     * 续签意向模板id
     */
    private String templateId;

    /**
     * 续签意向字段
     */
    private String contractFields;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 合同签署人
     */
    private EmpSimple owner;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 员工状态
     */
    private EnumSimple empStatus;

    /**
     * 所属组织Id
     */
    private String organize;

    /**
     * 所属组织名称
     */
    private String organizeTxt;

    /**
     * 关联的职务ID
     */
    private String job;

    /**
     * 关联的职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;


    /**
     * 合同公司Id
     */
    private String company;

    /**
     * 所属公司名称
     */
    private String companyTxt;

    /**
     * 合同编号
     */
    private String contractNo;


    /**
     * 合同期（月）
     */
    private Integer contractPeriod;

    /**
     * 合同设置Bid
     */
    private String contractTypeSet;

    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;

    /**
     * 合同类型
     */
    private DictSimple contractSettingType;

    /**
     * 合同开始日期
     */
    private Long startDate;

    /**
     * 合同结束日期
     */
    private Long endDate;

    /**
     * 附件
     */
    private Attachment attachment;

    /**
     * 续签意向确认时间
     */
    private Long confirmedTime;

    /**
     * 续签意向状态
     */
    private EnumSimple continueStatus;

    /**
     * 续签意向反馈
     */
    private DictSimple feedback;

    public static ContinueLetterPo fromEntity(ContinueLetterDo entity){
        ContinueLetterPo po = ObjectConverter.convert(entity, ContinueLetterPo.class);
        po.setContractFields(FastjsonUtil.toJson(entity.getContractFields()));
        return po;
    }

    public ContinueLetterDo toEntity() {
        ContinueLetterDo entity = ObjectConverter.convert(this, ContinueLetterDo.class);
        entity.setContractFields(FastjsonUtil.toList(contractFields, MetadataPropertyDto.class));
        return entity;
    }

}
