package com.caidaocloud.hr.service.contract.infrastructure.repository.po;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CompletedContractImportPo {
    /**
     * 员工姓名
     */
    @Excel(name = "姓名", width = 15)
    private String name;

    /**
     * 员工工号
     */
    @Excel(name = "工号", width = 10, orderNum = "1")
    private String workno;

    /**
     * 签订类型
     */
    @Excel(name = "签订类型", width = 10, orderNum = "2")
    private String signTypeTxt;

    /**
     * 合同公司
     */
    @Excel(name = "合同公司", width = 10, orderNum = "3")
    private String companyTxt;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号", width = 10, orderNum = "4")
    private String contractNo;

    /**
     * 合同类型
     */
    @Excel(name = "合同类型", width = 10, orderNum = "5")
    private String contractTypeSetTxt;

    /**
     * 合同类型
     */
//    @Excel(name = "合同期限", width = 10, orderNum = "5")
    private String periodTypeTxt;

    /**
     * 合同开始日期
     */
    @Excel(name = "合同开始日期", width = 10, orderNum = "6", importFormat = "yyyy/MM/dd")
    private String startDateTxt;

    /**
     * 合同结束日期
     */
    @Excel(name = "合同结束日期", width = 10, orderNum = "7", importFormat = "yyyy/MM/dd")
    private String endDateTxt;

    @Excel(name = "错误信息", width = 15, orderNum = "8")
    private String checkEmptyTips;
    // 是否需要审批
    @Excel(name = "是否需要审批", width = 30)
    private String needApprove;
}
