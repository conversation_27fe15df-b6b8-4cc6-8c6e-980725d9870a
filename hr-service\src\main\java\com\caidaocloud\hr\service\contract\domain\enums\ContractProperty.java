package com.caidaocloud.hr.service.contract.domain.enums;

import com.caidaocloud.workflow.enums.WfFieldDataTypeEnum;

public enum ContractProperty {
    SIGN_TYPE("签订类型", "SIGN_TYPE",WfFieldDataTypeEnum.Text),
    COMPANY("合同公司", "COMPANY", WfFieldDataTypeEnum.Text),
    REGISTER_ADDRESS("注册地址", "REGISTER_ADDRESS",WfFieldDataTypeEnum.Text),
    CONTRACT_TYPE("合同类型", "CONTRACT_TYPE",WfFieldDataTypeEnum.Text),
    PERIOD_TYPE("合同期限", "PERIOD_TYPE", WfFieldDataTypeEnum.Text),
    SIGN_DATE("合同签订日期", "SIGN_DATE", WfFieldDataTypeEnum.Timestamp),
    START_DATE("合同开始日期", "START_DATE", WfFieldDataTypeEnum.Timestamp),
    END_DATE("合同结束日期", "END_DATE" ,WfFieldDataTypeEnum.Timestamp),
    WORK_PLACE("工作地", "WORK_PLACE", WfFieldDataTypeEnum.Text),
    WORK_HOUR("工时制", "WORK_HOUR", WfFieldDataTypeEnum.Text),
    OPEN_WORK_FLOW("是否启用工作流", "OPEN_WORK_FLOW", WfFieldDataTypeEnum.Text),
    REMARK("备注", "REMARK", WfFieldDataTypeEnum.Text),
    ATTACH_FILE("附件", "ATTACH_FILE", WfFieldDataTypeEnum.Text);

    private String name;
    
    private String code;

    private WfFieldDataTypeEnum type;

    ContractProperty(String name, String code, WfFieldDataTypeEnum type) {
        this.name = name;
        this.code = code;
        this.type = type;
    }

}
