package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgEmpTreeQueryDto extends BasePage {
    @ApiModelProperty("组织ID")
    private String deptId;

    @ApiModelProperty("生效日期")
    private Long dataStartTime;

    @ApiModelProperty("搜索关键词")
    private String keyword;

    /**
     * "0": 在职
     * "1": "离职"
     */
    @ApiModelProperty("员工状态")
    private Integer empStatus;

    @ApiModelProperty("组织架构类型")
    private String schemaType;
}

