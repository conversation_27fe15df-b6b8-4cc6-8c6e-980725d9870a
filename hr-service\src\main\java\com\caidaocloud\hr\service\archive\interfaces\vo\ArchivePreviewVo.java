package com.caidaocloud.hr.service.archive.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 27/6/2024 3:20 下午
 */
@Data
@ApiModel("归档文件预览")
public class ArchivePreviewVo implements Serializable {

    @ApiModelProperty("预览地址")
    private String filePath;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件后缀")
    private String suffixType;

    @ApiModelProperty("是否重定向")
    private boolean redirect;

}
