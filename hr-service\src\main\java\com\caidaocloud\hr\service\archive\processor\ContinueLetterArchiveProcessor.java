package com.caidaocloud.hr.service.archive.processor;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.archive.IArchiveProcessor;
import com.caidaocloud.hr.service.archive.beans.ArchiveData;
import com.caidaocloud.hr.service.archive.beans.ArchiveFile;
import com.caidaocloud.hr.service.contract.domain.entity.ContinueLetterDo;
import com.caidaocloud.hr.service.contract.domain.service.ContinueLetterDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * created by: FoAng
 * create time: 13/6/2024 4:15 下午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContinueLetterArchiveProcessor extends AbsArchiveProcessor {

    private ContinueLetterDomainService continueLetterDomainService;

    @Override
    public String businessLine() {
        return ArchiveStandardLine.CONTRACT_LETTER.name();
    }

    @Override
    public List<ArchiveData> fetchArchiveData(String businessId) {
        ContinueLetterDo contractDo = continueLetterDomainService.getById(businessId);
        if (contractDo == null) {
            log.error("[archive] fetch contract letter error, contractId:{}", businessId);
            return Lists.newArrayList();
        }
        return filterArchiveList(Lists.newArrayList(buildArchiveData(contractDo)));
    }

    @Override
    public List<ArchiveData> fetchPageArchiveData(BasePage page) {
        List<ContinueLetterDo> contractDos = continueLetterDomainService.getArchiveData(page);
        List<ArchiveData> archiveDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contractDos)) {
            for (ContinueLetterDo continueLetterDo: contractDos) {
                archiveDataList.add(buildArchiveData(continueLetterDo));
            }
        }
        return filterArchiveList(archiveDataList);
    }

    public ArchiveData buildArchiveData(ContinueLetterDo continueLetterDo) {
        ArchiveData archiveData = new ArchiveData();
        archiveData.setBusinessLine(ArchiveStandardLine.CONTRACT.getDesc());
        archiveData.setBusinessType(ArchiveStandardLine.CONTRACT_LETTER.getDesc());
        archiveData.setBusinessId(continueLetterDo.getBid());
        archiveData.setEmpId(continueLetterDo.getOwner().getEmpId());
        archiveData.setEventTime(continueLetterDo.getCreateTime());
        archiveData.setArchiveFiles(Lists.newArrayList(BeanUtil.convert(continueLetterDo.getAttachment(), ArchiveFile.class)));
        return archiveData;
    }
}
