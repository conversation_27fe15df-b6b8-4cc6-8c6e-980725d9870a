package com.caidaocloud.hr.service.enums.archive;

import com.caidaocloud.hr.service.handler.*;
import com.caidaocloud.util.SpringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * created by: FoAng
 * create time: 5/6/2024 4:23 下午
 */
@Slf4j
@Getter
public enum ArchiveStandardLine {

    // 入职
    ONBOARDING("入职", "文件上传,模板电子签,上传电子签,文件上传-PC,欢迎页附件", CommonResolveHandler.class),
    // 转正
    REGULARIZATION("转正", "员工申请,代员工申请", CommonResolveHandler.class),
    // 员工信息
    EMPLOYEE("员工", "证件信息,学历信息,学位信息,财务信息,其他信息", CommonResolveHandler.class),
    // 异动
    TRANSFER("异动", TransferResolveHandler.class),
    // 离职
    TERMINATE("离职", "TerminateType", DictResolveHandler.class),
    // 合同
    CONTRACT("合同", "合同新签,合同续签,合同改签,合同续签意向", CommonResolveHandler.class),
    // 合同续签意向
    CONTRACT_LETTER("合同续签意向"),
    // 电子签合同
    ESIGN_CONTRACT("电子签(合同/协议)"),
    // 合同电子签
    ESIGN_COMMON("电子签(入职/证明)"),
    // 电子签证明
    ESIGN_PROOF("证明", ProofResolveHandler.class),
    // 电子签签署线上
    ESIGN_ONLINE("线上");

    private final String desc;

    private String simpleValues;

    private Class<? extends ITypeResolveHandler> resolveHandler;

    ArchiveStandardLine(String desc, Class<? extends ITypeResolveHandler> resolveHandler) {
        this.desc = desc;
        this.resolveHandler = resolveHandler;
    }

    ArchiveStandardLine(String desc, String simpleValues, Class<? extends ITypeResolveHandler> resolveHandler) {
        this.desc = desc;
        this.simpleValues = simpleValues;
        this.resolveHandler = resolveHandler;
    }

    ArchiveStandardLine(String desc) {
        this.desc = desc;
    }

    public static ArchiveStandardLine indexDesc(String desc) {
        return Arrays.stream(ArchiveStandardLine.values()).filter(it -> it.getDesc().equals(desc))
                .findFirst().orElse(null);
    }

    /**
     *
     * @param desc 所属业务逗号分割
     * @return
     */
    public static List<String> getBusinessTypes(String desc) {
        List<String> result = new ArrayList<>();
        if (desc != null) {
            String[] split = desc.split(",");
            for (String st : split) {
                ArchiveStandardLine standardLine = indexDesc(st);
                if (standardLine != null) {
                    Class<? extends ITypeResolveHandler> handler = standardLine.getResolveHandler();
                    if (handler != null) {
                        try {
                            result.addAll(SpringUtil.getBean(handler).getTypes(standardLine.getSimpleValues()));
                        } catch (Exception e) {
                            log.error("[archive] fetch businessTypes error, desc:{},st:{},{}", desc, st, e);
                        }
                    }
                }
            }
        }
        return result;
    }

}
