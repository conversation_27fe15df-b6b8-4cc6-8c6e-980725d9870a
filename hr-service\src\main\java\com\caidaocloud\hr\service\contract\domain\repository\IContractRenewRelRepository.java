package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRelDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * created by: FoAng
 * create time: 2/12/2024 10:24 上午
 */
public interface IContractRenewRelRepository extends BaseRepository<ContractRenewRelDo> {

    List<ContractRenewRelDo> listByEmpId(String identifier, String empId);

    Integer oneVersion(String identifier, String ruleId);

    void batchInsert(String identifier, List<ContractRenewRelDo> relDoList);

    void deleteRuleRel(String identifier, String version, String ruleId);
}
