package com.caidaocloud.hr.service.employee.application.emp.fieldset.service;

import com.caidaocloud.hr.service.employee.domain.emp.fieldset.entity.EmpRegisterModuleDo;
import com.caidaocloud.hr.service.employee.domain.emp.fieldset.service.EmpRegisterModuleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/17
 */
@Slf4j
@Service
public class EmpRegisterModuleService {
    @Resource
    private EmpRegisterModuleDomainService empRegisterModuleDomainService;

    public String save(EmpRegisterModuleDo data) {
        return empRegisterModuleDomainService.save(data);
    }

    public void update(EmpRegisterModuleDo data) {
        empRegisterModuleDomainService.update(data);
    }

    public void updateSelective(EmpRegisterModuleDo data) {
        empRegisterModuleDomainService.updateSelective(data);
    }

    public void updateModuleStatus(String bid, Boolean show) {
        empRegisterModuleDomainService.updateModuleStatus(bid, show);
    }

    public void deleteById(EmpRegisterModuleDo data) {
        empRegisterModuleDomainService.deleteById(data);
    }

    public EmpRegisterModuleDo selectById(String bid) {
        return empRegisterModuleDomainService.selectById(bid);
    }

    public List<EmpRegisterModuleDo> selectList(Boolean show) {
        return empRegisterModuleDomainService.selectList(show);
    }
}
