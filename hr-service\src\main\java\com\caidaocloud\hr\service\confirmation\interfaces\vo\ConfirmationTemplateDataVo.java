package com.caidaocloud.hr.service.confirmation.interfaces.vo;

import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeField;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ConfirmationTemplateDataVo implements Serializable {
    @ApiModelProperty("转正模板定义")
    private ConfirmationConfig template;
    @ApiModelProperty("异动数据")
    private List<ConfirmationChangeField> data;
    @ApiModelProperty("表单valueId, 存在表单发生修改情况")
    private String formValueId;
    @ApiModelProperty("挂载的表单定义")
    private FormDefDto formDef;
    @ApiModelProperty("表单数据")
    private Map<String, Object> formData;
}