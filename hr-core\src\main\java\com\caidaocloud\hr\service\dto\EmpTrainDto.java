package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工培训经历DTO")
public class EmpTrainDto {
    @ApiModelProperty("员工培训经历ID")
    private String bid;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("培训类型")
    private String trainType;

    @ApiModelProperty("课程名称")
    private String className;

    @ApiModelProperty("培训单位")
    private String trainAgency;

    @ApiModelProperty("培训经费")
    private String trainFunds;

    @ApiModelProperty("培训地点")
    private String trainAddr;

    @ApiModelProperty("培训详情")
    private String trainDesc;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
