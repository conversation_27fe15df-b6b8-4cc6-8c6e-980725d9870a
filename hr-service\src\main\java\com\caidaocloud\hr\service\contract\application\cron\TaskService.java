package com.caidaocloud.hr.service.contract.application.cron;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaskService {

//    // 合同状态跑批
//    @XxlJob("contractStatusJobHandler")
//    public ReturnT<String> autoTemplatePackageJobHandler() {
//        XxlJobHelper.log("XxlJob autoTemplatePackageJobHandler start");
//        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());
//
//        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());
//        XxlJobHelper.log("XxlJob autoTemplatePackageJobHandler end");
//        return ReturnT.SUCCESS;
//    }

}
