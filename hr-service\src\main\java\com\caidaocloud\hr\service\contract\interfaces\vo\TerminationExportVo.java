package com.caidaocloud.hr.service.contract.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/3/15 下午4:27
 * @Version 1.0
 **/
@Data
public class TerminationExportVo {

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("英文名")
    private String enName;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("证件号")
    private String cardNo;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("个人电子邮箱")
    private String email;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("年龄")
    private String divisionAge;

    @ApiModelProperty("学历")
    private String background;

    @ApiModelProperty("任职组织")
    private String organizePathTxt;

    @ApiModelProperty("直接上级")
    private String leadEmpId;

    @ApiModelProperty("组织层级")
    private String orglevel;

    @ApiModelProperty("业务线")
    private String businessLine;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("入职日期")
    private String onboardingTime;

    @ApiModelProperty("用工类型")
    private String empTypeName;

    @ApiModelProperty("用工子类型")
    private String empsubtype;

    @ApiModelProperty("所属组织")
    private String orgName;

    @ApiModelProperty("岗位")
    private String postName;

    @ApiModelProperty("合同公司")
    private String companyName;

    @ApiModelProperty("工作地")
    private String workplaceName;

    @ApiModelProperty("离职时间")
    private String terminationTime;

    @ApiModelProperty("离职类型")
    private String type;

    @ApiModelProperty("离职原因（LM填写）")
    private String reason;

    @ApiModelProperty("离职原因（员工申请 ）")
    private String terminationReason;

    @ApiModelProperty("工龄（年）")
    private String divisionAgeToYear;

    @ApiModelProperty("是否签署竞业协议")
    private String signAgreement;

    @ApiModelProperty("CTA等级")
    private String ctalevel;

    @ApiModelProperty("是否黑名单")
    private String intoBlacklist;

    @ApiModelProperty("审批状态")
    private String status;

    @ApiModelProperty("内嵌表单字段")
    private Map<String, Object> fieldMap;


}

 
    
    
    
    