package com.caidaocloud.hr.service.vo.organization.post;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class PostVo {
    @ApiModelProperty("岗位ID")
    private String bid;

    @ApiModelProperty("岗位名称")
    private String name;

    @ApiModelProperty("岗位名称多语言")
    private Map<String, Object> i18nName;

    @ApiModelProperty("岗位编码")
    private String code;

    @ApiModelProperty("所属组织ID")
    private String orgId;

    @ApiModelProperty("所属组织名称")
    private String orgName;

    @ApiModelProperty("工作地ID")
    private String workplaceId;

    @ApiModelProperty("工作地名称")
    private String workplaceName;

    @ApiModelProperty("是否关键岗位")
    private Boolean keyPost;

    @ApiModelProperty("是否关键岗位txt")
    private String keyPostTxt;

    @ApiModelProperty("职级关联")
    private JobGradeRange jobGrade;

    @ApiModelProperty("关联职务或基准岗位，0：未配置，1：职务，2：基准岗位")
    private Integer relation;

    @ApiModelProperty("关联基准岗位ID")
    private String benchmarkPositionId;

    @ApiModelProperty("关联基准岗位名称")
    private String benchmarkPositionName;

    @ApiModelProperty("关联的职务ID")
    private String jobId;

    @ApiModelProperty("关联的职务名称")
    private String jobName;

    @ApiModelProperty("岗位说明")
    private String desc;

    @ApiModelProperty("岗位说明书")
    private Attachment jobDescFiles;

    @ApiModelProperty("关联基准岗位说明书")
    private Attachment linkBenchmarkDescFiles;

    @ApiModelProperty("关联职务说明书")
    private Attachment linkJobDescFiles;

    @ApiModelProperty("任职资格")
    private String qualification;

    @ApiModelProperty("是否更新该岗位下的员工工作地信息")
    private Boolean syncEmpPost;

    @ApiModelProperty("附件")
    private Attachment files;

    @ApiModelProperty("状态")
    private EnumSimple status;

    @ApiModelProperty("状态txt")
    private String statusTxt;

    @ApiModelProperty("生效日期（单位毫秒）")
    private long dataStartTime;

    @ApiModelProperty("职级")
    private String jobLevelName;

    @ApiModelProperty("职等")
    private String jobLevel;

    @ApiModelProperty("生效日期（单位毫秒）")
    private String dataStartTimeTxt;
}
