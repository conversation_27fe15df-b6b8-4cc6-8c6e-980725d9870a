package com.caidaocloud.hr.service.archive.beans;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * created by: FoAng
 * create time: 5/6/2024 1:53 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArchiveFile extends Attachment {

    /**
     * 电子签三方合同，不为空则优先展示电子签
     */
    private String openContractId;

    /**
     * 文件类型 .pdf .excel .png
     */
    private String suffixType;

    /**
     * 业务文件名称
     */
    private String fileName;

    /**
     * 电子签合同状态，如电子签作废或者撤销
     */
    private String contractStatus;

    /**
     * 电子签签署平台
     */
    private String signPlatform;

}
