package com.caidaocloud.hr.service.employee.application.dataimport.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.TmpEmpStatusImportPo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class TmpEmpStatusImportService {
    @Autowired
    private EmpWorkInfoService empWorkInfoService;
    @Autowired
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    public void updateStatus(MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        try {
            List<TmpEmpStatusImportPo> importData = ExcelImportUtil.importExcel(file.getInputStream(), TmpEmpStatusImportPo.class, params);
            Iterator<TmpEmpStatusImportPo> iterator = importData.iterator();
            List<String> worknos = new ArrayList<>(500);
            while (iterator.hasNext()) {
                TmpEmpStatusImportPo po = iterator.next();
                worknos.add(po.getWorkno());
                iterator.remove();

                if (worknos.size() >= 500 || !iterator.hasNext()) {
                    List<EmpWorkInfoDo> workInfoList = empWorkInfoService.getEmpListByWorkno(worknos);
                    for (EmpWorkInfoDo workInfo : workInfoList) {
                        // 更新员工状态为离职
                        EnumSimple enumSimple = new EnumSimple();
                        enumSimple.setValue("1");
                        workInfo.setEmpStatus(enumSimple);
                        empWorkInfoDomainService.update(workInfo);
                    }
                    worknos.clear();
                }
            }
        } catch (Exception e) {
            log.error("import emp info occur error ,msg = {}", e.getMessage(), e);
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_32014"));
        }
    }
}
