package com.caidaocloud.hr.service.contract.application.event.publish;

import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class ContractMessage extends RabbitBaseMessage {

    private String tenantId;
    private String msgConfig;
    private Long userId;
    private List<String> subjects;
    private int type = 0;
    private String mdc;
    private long createTime;
    /**
     * 其他字段信息
     */
    private Map<String, String> ext;
    /**
     * 消息来源
     * 用于追踪消息来源于某个业务或模块
     */
    private String msgFrom;
}
