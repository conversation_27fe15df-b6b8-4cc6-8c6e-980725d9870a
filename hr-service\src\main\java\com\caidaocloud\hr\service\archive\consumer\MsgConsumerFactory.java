package com.caidaocloud.hr.service.archive.consumer;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.application.ArchiveService;
import com.caidaocloud.hr.service.archive.constants.ArchiveConstant;
import com.caidaocloud.hr.service.archive.properties.ArchiveProperty;
import com.caidaocloud.mq.rabbitmq.ConsumerGenerate;
import com.caidaocloud.mq.rabbitmq.consumer.DynamicConsumer;
import com.caidaocloud.mq.rabbitmq.factory.ExchangeType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * created by: FoAng
 * create time: 20/6/2024 2:25 下午
 */
@Slf4j
@Component
@AllArgsConstructor
public class MsgConsumerFactory {

    public static final ConcurrentMap<String, ArchiveMsgConsumer> msgConsumers = new ConcurrentHashMap<>();

    private ArchiveProperty archiveProperty;

    private ConsumerGenerate consumerGenerate;

    private ArchiveService archiveService;

    @PostConstruct
    public void initMsgConsumer() {
        String[] tenantIds = archiveProperty.getTenantIds();
        if (tenantIds == null || tenantIds.length == 0) {
            log.info("[archive] tenantId is not config...");
            return;
        }
        for (String tenantId : tenantIds) {
            DynamicConsumer recordConsumer;
            try {
                recordConsumer = consumerGenerate.genConsumer(ExchangeType.DIRECT,
                        new ArchiveMsgConsumer(archiveService, tenantId),
                        ArchiveConstant.tenantExchangeKey(tenantId),
                        ArchiveConstant.tenantQueueKey(tenantId),
                        ArchiveConstant.tenantRoutingKey(tenantId),
                        false, true, true);
                recordConsumer.start();
            } catch (Exception e) {
                log.error("create dynamic consume error: {}", e.getMessage(), e);
            }
        }
    }

    public static ArchiveMsgConsumer getArchiveMsgConsumer(String tenantId) {
        return Optional.ofNullable(msgConsumers.getOrDefault(tenantId, null))
                .orElseThrow(() -> new ServerException("租户配置错误"));
    }

}
