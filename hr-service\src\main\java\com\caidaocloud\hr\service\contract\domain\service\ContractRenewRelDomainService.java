package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.condition.service.IRuleCondition;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRelDo;
import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRuleDo;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 3/12/2024 10:55 上午
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractRenewRelDomainService extends BaseDomainServiceImpl<ContractRenewRelDo, BasePage> {

    private ContractRenewRelDo contractRenewRelDo;

    private IRuleCondition ruleCondition;

    private Locker locker;

    @Override
    public BaseDomainDo<ContractRenewRelDo> getDoService() {
        return contractRenewRelDo;
    }

    public List<ContractRenewRelDo> listByEmpId(String empId) {
        return contractRenewRelDo.listByEmpId(empId);
    }

    public Integer versionByRule(String ruleId) {
        return contractRenewRelDo.versionByRule(ruleId);
    }

    @Override
    public void batchSave(List<ContractRenewRelDo> renewRelDos) {
        if (CollectionUtils.isNotEmpty(renewRelDos)) {
            for (ContractRenewRelDo it : renewRelDos) {
                setRequiredField(it, true);
                it.setDataEndTime(DateUtil.MAX_TIMESTAMP);
            }
            contractRenewRelDo.batchInsert(renewRelDos);
        }
    }

    public void deleteRuleRel(String version, String ruleId) {
        contractRenewRelDo.deleteRuleRel(version, ruleId);
    }

    /**
     * 刷新单个模板匹配关系
     */
    @Async
    public void doRefreshRuleRelAction(ContractRenewRuleDo ruleDo, String tenantId) {
        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            List<ContractRenewRelDo> relDoList = null;
            List<String> relEmpIds = ruleCondition.getEmpIdList(tenantId, ruleDo.getCondition());
            if (CollectionUtils.isNotEmpty(relEmpIds)) {
                relDoList = relEmpIds.stream().map(it -> new ContractRenewRelDo()
                                .setRenewRuleId(ruleDo.getBid())
                                .setRenewRuleName(ruleDo.getName())
                                .setEmpId(it))
                        .collect(Collectors.toList());
            }
            saveRelEmpData(relDoList, ruleDo);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    @PaasTransactional
    private void saveRelEmpData(List<ContractRenewRelDo> relDoList, ContractRenewRuleDo ruleDo) {
        String lockerKey = String.format("contract_renew_rel_locker_key_%s", ruleDo.getBid());
        Lock lock = locker.getLock(lockerKey);
        try {
            boolean locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (locked) {
                Integer version = this.versionByRule(ruleDo.getBid());
                if (CollectionUtils.isNotEmpty(relDoList)) {
                    relDoList.forEach(it -> it.setVersion(version + 1));
                    this.batchSave(relDoList);
                }
                removeVersionRule(version, ruleDo.getBid());
            }
        } catch (InterruptedException e) {
           log.error("[contractRenewRel] saveRelEmpData error, {}", e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 移除消息关联rel
     * @param version
     * @param ruleId
     */
    private void removeVersionRule(Integer version, String ruleId) {
        log.error("[contractRenewRel] remove rule emp rel data, version:{}, ruleId:{}", version, ruleId);
        this.deleteRuleRel(version == null ? null : Objects.toString(version), ruleId);
    }

}
