package com.caidaocloud.hr.service.employee.application.event.subscribe;

import javax.annotation.Resource;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.employee.application.event.dto.TenantInitDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.msg.configuration.MessageConsumerManager;
import com.caidaocloud.msg.configuration.RabbitMqConsumerManager;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/04/04
 */
@Slf4j
@Component
public class TenantInitSubscriber {
	@Resource
	private MessageConsumerManager messageConsumerManager;

	@RabbitHandler
	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "hr.tenant.init.queue", durable = "true"),
					exchange = @Exchange(value = "maintenance.tenant.init.fanout.exchange", type = ExchangeTypes.FANOUT),
					key = {"routingKey.maintenance.tenant.init"}
			)
	)
	public void process(String message) {
		log.info("Tenant init message={}", message);
		try {
			TenantInitDto dto = FastjsonUtil.toObject(message, TenantInitDto.class);
			log.info("Creating consumer,tenantId={}", dto.getTenantId());
			messageConsumerManager.createQueue(dto.getTenantId());
		} catch (Exception ex) {
			log.error("process tenantInitMessage err,{}", ex.getMessage(), ex);
		}
	}

}