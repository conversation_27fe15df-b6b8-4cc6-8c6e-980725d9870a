package com.caidaocloud.hr.service.util;

import cn.hutool.core.collection.CollectionUtil;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据反射排除非标准字段
 * 过滤出自定义字段
 */
@Slf4j
public abstract class DataSimpleUtil {
    public static List excludeField() {
        Field[] fields = AbstractData.class.getDeclaredFields();
        List<String> fieldList = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        return fieldList;
    }

    public static <T extends DataSimple> List<String> excludeDataSimpleField(Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<String> fieldList = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        fieldList.addAll(excludeField());
        return fieldList;
    }

    public static <T extends DataSimple> List<String> dataSimpleStandardField(T t) {
        List<String> cusList = excludeDataSimpleField(t.getClass());
        if (cusList.isEmpty()) {
            return new ArrayList<>(t.getProperties().keySet());
        }

        Set<String> props = t.getProperties().keySet();
        // 差集 = props - cusField
        List<String> standardField = props.stream().filter(item -> !cusList.contains(item)).collect(Collectors.toList());
        return standardField;
    }

    public static final Map<String, String> simpleMap = initMap();

    private static Map<String, String> initMap() {
        Map<String, String> baseMap = new HashMap<>();
        Field[] fields = AbstractData.class.getDeclaredFields();
        Arrays.stream(fields).map(Field::getName).forEach(field -> baseMap.put(field, field));
        return Collections.unmodifiableMap(baseMap);
    }

    public static <T> T convertBaseData(DataSimple dataSimple, T data, String prefix) {
        Field[] fields = data.getClass().getDeclaredFields();
        BeanUtil.copyProperties(dataSimple, data);
        List<String> fieldNames = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fieldNames) || !dataSimple.getProperties().isEmpty()) {
            NestPropertyValue properties = dataSimple.getProperties();
            Set<String> propsSet = properties.keySet();
            fieldNames.stream().filter(it -> propsSet.contains(it)
                    || propsSet.contains(String.join("$", prefix, it))).forEach(it -> {
                try {
                    Field field = data.getClass().getDeclaredField(it);
                    PropertyValue propValue = properties.get(it) == null ? properties.get(String.join("$", prefix, it)) : properties.get(it);
                    if (propValue == null) return;
                    field.setAccessible(true);
                    boolean propertyDataType = PropertyValue.class.isAssignableFrom(field.getType());
                    if (propertyDataType) {
                        Object objectValue = FastjsonUtil.toObject(FastjsonUtil.toJson(propValue), field.getType());
                        field.set(data, objectValue);
                    } else if (propValue instanceof SimplePropertyValue) {
                        String fieldValue = ((SimplePropertyValue) propValue).getValue();
                        field.set(data, ConvertUtils.convert(fieldValue, field.getType()));
                    } else {
                        field.set(data, ConvertUtils.convert(propValue, field.getType()));
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            });
        }
        return data;
    }

    public static String getString(PropertyValue pv){
        return getString(pv, "");
    }

    public static String getString(PropertyValue pv, String defaultValue){
        if(null == pv){
            return defaultValue;
        }

        String value = null;
        if(pv instanceof SimplePropertyValue && null != (value = ((SimplePropertyValue) pv).getValue())){
            return value;
        }

        return defaultValue;
    }

    public static String getEnumSimpleValue(PropertyValue pv, String defaultValue){
        return getEnumSimple(pv, defaultValue, true);
    }

    public static String getEnumSimple(PropertyValue pv, String defaultValue, boolean isValue){
        if(null == pv){
            return defaultValue;
        }

        String value = null;
        if(pv instanceof EnumSimple){
            value = isValue ? ((EnumSimple) pv).getValue() : ((EnumSimple) pv).getText();
            if(null != value){
                return value;
            }
        }

        return defaultValue;
    }

    public static PropertyValue getTxtFieldPropertyValue(PropertyDataType dataType, PropertyValue propertyValue) {
        boolean isSimple = propertyValue == null;
        isSimple = isSimple || (propertyValue instanceof SimplePropertyValue && StringUtil.isEmpty(((SimplePropertyValue) propertyValue).getValue()));
        if (isSimple) {
            return new SimplePropertyValue("");
        }

        String textValue;
        switch (dataType) {
            case Enum:
                EnumSimple enumSimple = (EnumSimple) propertyValue;
                textValue = enumSimple.getText();
                break;
            case Attachment:
                Attachment attachment = (Attachment) propertyValue;
                textValue = CollectionUtils.isNotEmpty(attachment.getNames()) ? attachment.getNames().get(0) : "";
                break;
            case Emp:
                EmpSimple empSimple = (EmpSimple) propertyValue;
                textValue = empSimple.getName();
                break;
            case Dict:
                DictSimple dictSimple = (DictSimple) propertyValue;
                textValue = dictSimple.getText();
                break;
            case Job_Grade_Range:
                JobGradeRange jobGradeRange = (JobGradeRange) propertyValue;
                textValue = jobGradeRange.getStartGradeName();
                break;
            case Timestamp:
                SimplePropertyValue timeProperty = (SimplePropertyValue) propertyValue;
                textValue = StringUtils.isNotBlank(timeProperty.getValue()) ?
                        DateUtil.formatDate(new Date(Long.parseLong(timeProperty.getValue()))) : null;
                break;
            case Boolean:
                SimplePropertyValue statusProperty = (SimplePropertyValue) propertyValue;
                textValue = Boolean.valueOf(statusProperty.getValue()) ? "是" : "否";
                break;
            case Address:
                try {
                    QueryInfoCache.init();
                    Address address = (Address) propertyValue;
                    address.setValue(String.join("/", Objects.toString(address.getProvince()),
                            Objects.toString(address.getCity()), Objects.toString(address.getArea())));
                    Address.doAddress(address);
                    textValue = address.getText();
                } catch (Exception e) {
                    log.error("getTxtFieldPropertyValue pv to address err,{}", e.getMessage(), e);
                    textValue = "";
                } finally {
                    QueryInfoCache.clear();
                }
                break;
            default:
                if (propertyValue instanceof SimplePropertyValue) {
                    SimplePropertyValue simpleValue = (SimplePropertyValue) propertyValue;
                    textValue = simpleValue.getValue() == null ? "" : Objects.toString(simpleValue.getValue());
                } else {
                    log.info("save convert property data error, def:{}, value:{}", dataType, FastjsonUtil.toJson(propertyValue));
                    textValue = "";
                }
                break;
        }
        return new SimplePropertyValue(textValue);
    }

    public static Set<String> getChangeDataProperty(DataSimple newObj, DataSimple oldObj) {
        Set<String> changeSet = Sets.newHashSet();
        if (Objects.isNull(newObj) || Objects.isNull(oldObj)) {
            return changeSet;
        }
        NestPropertyValue newObjProperties = newObj.getProperties();
        NestPropertyValue oldObjProperties = oldObj.getProperties();
        for (Map.Entry<String, PropertyValue> propertyValueEntry : newObjProperties.entrySet()) {
            if (!oldObjProperties.containsKey(propertyValueEntry.getKey())) {
                changeSet.add(propertyValueEntry.getKey());
                continue;
            }
            PropertyValue oldValue = oldObjProperties.get(propertyValueEntry.getKey());
            PropertyValue newValue = propertyValueEntry.getValue();
            if ((Objects.isNull(oldValue) && Objects.nonNull(newValue)) ||
                    (Objects.nonNull(oldValue) && Objects.isNull(newValue)) ||
                    (Objects.nonNull(oldValue) && Objects.nonNull(newValue) 
                            && !(newValue instanceof SimplePropertyValue) && !newValue.equals(oldValue)) ||
                    (Objects.nonNull(oldValue) && Objects.nonNull(newValue)
                            && newValue instanceof SimplePropertyValue && !newValue.toText().equals(oldValue.toText()))
               ) {
                changeSet.add(propertyValueEntry.getKey());
                continue;
            }
        }
        return changeSet;
    }
}
