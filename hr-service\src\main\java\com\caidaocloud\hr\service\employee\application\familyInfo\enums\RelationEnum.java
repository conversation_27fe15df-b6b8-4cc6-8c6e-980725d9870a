package com.caidaocloud.hr.service.employee.application.familyInfo.enums;

/**
 * <AUTHOR>
 */
public enum RelationEnum {
    FATHER(0, "父亲"),
    MOTHER(1, "母亲"),
    CHILDREN(2, "子女"),
    SPOUSE(3, "配偶"),
    BROTHER_SISTER(4, "兄弟姐妹"),
    OTHER(5, "其他");

    private Integer index;
    private String name;

    RelationEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(Integer index) {
        for (RelationEnum c : RelationEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
