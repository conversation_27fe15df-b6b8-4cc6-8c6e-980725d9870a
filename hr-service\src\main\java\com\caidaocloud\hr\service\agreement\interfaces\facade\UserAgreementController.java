package com.caidaocloud.hr.service.agreement.interfaces.facade;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.agreement.application.UserAgreementService;
import com.caidaocloud.hr.service.agreement.domain.entity.UserAgreementDo;
import com.caidaocloud.hr.service.agreement.interfaces.dto.AgreementUpDto;
import com.caidaocloud.hr.service.agreement.interfaces.dto.UserAgreementDto;
import com.caidaocloud.hr.service.agreement.interfaces.vo.UserAgreementVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * created by: FoAng
 * create time: 14/10/2024 4:23 下午
 */
@RestController
@RequestMapping("/api/hr/user/agreement/v1")
@Api(value = "用户协议", tags = "用户协议")
@AllArgsConstructor
public class UserAgreementController {

    private UserAgreementService userAgreementService;

    /**
     * 协议列表
     * @param page
     * @return
     */
    @PostMapping("/list")
    @ApiOperation("协议列表")
    public Result<PageResult<UserAgreementVo>> summaryList(@RequestBody BasePage page) {
        return Result.ok(userAgreementService.getUserAgreementVoList(page));
    }

    /**
     * 协议详情
     * @param bid
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation("协议详情")
    public Result<UserAgreementVo> detail(@RequestParam String bid) {
        return Result.ok(userAgreementService.detail(bid));
    }


    @GetMapping("/type/detail")
    @ApiOperation("获取制定业务类型协议")
    public Result<?> detailByType(@RequestParam String type) {
        return Result.ok(userAgreementService.detailByType(type));
    }

    /**
     * 保存更新协议
     * @param agreementDto
     * @return
     */
    @PostMapping("/saveUpdate")
    @ApiOperation("保存更新协议")
    @LogRecordAnnotation(menu = "管理中心-基础设置-安全协议", category = "编辑", success = "编辑了{{#agreementDto.type.desc}}")
    public Result<?> save(@RequestBody UserAgreementDto agreementDto) {
        userAgreementService.saveUpdate(agreementDto);
        return Result.ok(true);
    }

    /**
     * 协议状态变更
     * @param dto
     * @return
     */
    @PostMapping("/status")
    @ApiOperation("更新协议状态")
    @LogRecordAnnotation(menu = "管理中心-基础设置-安全协议", category = "{{#operator}}", fail = "更新协议状态失败",
            success = "{{#operator}}了{{#record.type.desc}}")
    public Result<?> toggleStatus(@RequestBody AgreementUpDto dto) {
        LogRecordContext.putVariable("operator", dto.getStatus() == 1 ? "启用" : "停用");
        UserAgreementDo userAgreementDo = userAgreementService.changeStatus(dto.getId(), dto.getStatus());
        LogRecordContext.putVariable("record", userAgreementDo);
        return Result.ok(true);
    }
}
