package com.caidaocloud.hr.service.contract.interfaces.facade.workflow;

import com.caidaocloud.hr.service.contract.application.service.workflow.WorkFlowSequenceService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 流程条件序列流
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/contract/workflow/sequence/v1")
@Api(value = "流程条件序列流", description = "流程条件序列流", tags = "v4.2")
public class WorkFlowSequenceController {
    @Resource
    private WorkFlowSequenceService workFlowSequenceService;

    /**
     * 根据序列流Code查询流程条件序列流数据
     *
     * @param businessId  业务id(业务主键)
     * @param initiatorId 发起人
     * @param applicantId 申请人
     * @param code        流程类型code
     * @return
     */
    @GetMapping("/data")
    @ApiOperation("根据序列流Code查询流程条件序列流数据")
    public Result<String> sequenceData(@RequestParam("businessId") String businessId,
                                       @RequestParam("initiatorId") String initiatorId,
                                       @RequestParam("applicantId") String applicantId,
                                       @RequestParam("code") String code) {
        return Result.ok(workFlowSequenceService.getSeqValue(businessId, initiatorId, applicantId, code));
    }
}
