package com.caidaocloud.hr.service.contract.interfaces.dto;

import com.caidaocloud.hr.service.contract.interfaces.dto.contract.DictDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6
 */
@Data
@ApiModel("合同设置DTO")
@Accessors(chain = true)
public class ContractTypeSetDto {
    @ApiModelProperty("合同设置bid")
    String bid;
    @ApiModelProperty("合同类型")
    String contractType;
    @ApiModelProperty("备注")
    String remark;
    @ApiModelProperty("合同类别")
    String contractClass;
    @ApiModelProperty("合同公司bid")
    List<String> company;
    @ApiModelProperty("签订类型")
    List<String> signType;
    @ApiModelProperty("员工类型")
    List<DictDto> empType;
    @ApiModelProperty("合同设置Bid")
    String contractTypeSet;
    @ApiModelProperty("分组条件树")
    private ConditionTreeDto conditionTree;
    @ApiModelProperty("合同期限类型")
    Boolean periodType;
    @ApiModelProperty("合同期限（月）")
    Integer contractPeriod;
    @ApiModelProperty("合同结束日期类型")
    String endDateType;
    @ApiModelProperty("有无试用期")
    Boolean probation;
    @ApiModelProperty("试用期（月）")
    Integer probationPeriod;
    @ApiModelProperty("是否根据预计毕业日期判断")
    Boolean baseExpectGraduateDate;
    @ApiModelProperty("试用期期限")
    EnumSimple probationDeadline;
    @ApiModelProperty("是否根据退休日期判断")
    Boolean baseRetireDate;
}
