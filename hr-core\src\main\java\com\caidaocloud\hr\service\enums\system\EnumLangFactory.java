package com.caidaocloud.hr.service.enums.system;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public enum EnumLangFactory {
    CN,
    EN;

    static Map<String, LanguageEnum> enumMap = new ConcurrentHashMap<>();

    public static void register(String enumType, LanguageEnum type){
        enumMap.put(enumType, type);
    }

    public static String getEnumTextByIndex(String enumType, String index, String lang) {
        lang = check(lang);

        LanguageEnum languageEnum = enumMap.get(enumType);
        if(null == languageEnum){
            return null;
        }

        return languageEnum.getEnumTextByIndexAndLang(index, lang);
    }

    private static String check(String lang){
        if(StringUtil.isEmpty(lang)){
            throw new ServerException("Language cannot be empty");
        }

        lang = lang.toUpperCase();
        if(!lang.equals(CN.toString()) && !lang.equals(EN.toString())){
            throw new ServerException("Current language=[" + lang + "] is not supported");
        }

        return lang;
    }
}
