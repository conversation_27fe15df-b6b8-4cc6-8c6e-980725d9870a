package com.caidaocloud.hr.service.contract.domain.repository;

import com.caidaocloud.hr.service.contract.domain.entity.ContractSetConditionDo;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2022/4/26 14:25
 * @Description:
 **/
public interface IContractSetConditionRepository extends BaseRepository<ContractSetConditionDo> {

    List<ContractSetConditionDo> selectByTypeSetId(String identifier, String typeSetBid);

    List<ContractSetConditionDo> selectByTypeSetIds(String identifier, List<String> typeSetIds);
}
