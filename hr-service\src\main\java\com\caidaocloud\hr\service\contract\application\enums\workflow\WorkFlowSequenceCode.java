package com.caidaocloud.hr.service.contract.application.enums.workflow;

/**
 * 流程条件序列流CODE
 * <AUTHOR>
 */
public enum WorkFlowSequenceCode {
    CONTRACT_TYPE_SET("CONTRACT_TYPE_SET", "合同类型"),
    APPLICANT_COMPETITION_AGREEMENT("APPLICANT_COMPETITION_AGREEMENT", "申请人竞业协议");

    private String value;
    private String name;

    WorkFlowSequenceCode(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(String value) {
        for (WorkFlowSequenceCode c : WorkFlowSequenceCode.values()) {
            if (c.getValue().equals(value)) {
                return c.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
