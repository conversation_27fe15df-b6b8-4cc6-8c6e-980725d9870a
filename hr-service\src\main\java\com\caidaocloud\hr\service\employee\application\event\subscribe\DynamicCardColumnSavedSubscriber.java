package com.caidaocloud.hr.service.employee.application.event.subscribe;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpDynamicRefreshService;
import com.caidaocloud.hrpaas.paas.common.event.DynamicColumnSaveEvent;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
@Component
public class DynamicCardColumnSavedSubscriber implements MessageHandler<DynamicColumnSaveEvent> {
	@Resource
	private EmpDynamicRefreshService empDynamicRefreshService;


	@Override
	public String topic() {
		return DynamicColumnSaveEvent.topic+"EMPLOYEECARD";
	}

	@Override
	public void handle(DynamicColumnSaveEvent message) throws Exception {
		String tenantId = message.getTenantId();
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId(tenantId);
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
		try {

			empDynamicRefreshService.refresh();
		}finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}
}
