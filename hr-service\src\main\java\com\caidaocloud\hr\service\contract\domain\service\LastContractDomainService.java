package com.caidaocloud.hr.service.contract.domain.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.domain.entity.LastContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDo;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainServiceImpl;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LastContractDomainService extends BaseDomainServiceImpl<LastContractDo, ContractQueryDto> {
    @Resource
    private LastContractDo lastContractDo;
    @Resource
    private EmpWorkInfoDo workInfoDo;

    @Override
    public BaseDomainDo<LastContractDo> getDoService() {
        return lastContractDo;
    }

    /**
     * 分页查询最新合同（与员工信息join）
     *
     * @param query
     * @return
     */
    public PageResult<ContractDto> getEmpContractPage(ContractQueryDto query) {
        PageResult<LastContractDo> page = lastContractDo.getPage(query);
        return buildContract(page);
    }

    /**
     * 分页查询新签合同
     *
     * @param query
     * @return
     */
    public PageResult<Map<String, String>> newlySignedList(ContractQueryDto query) {
        return workInfoDo.newlySignedList(query);
    }

    @NotNull
    private PageResult<ContractDto> buildContract(PageResult<LastContractDo> page) {
        var list = page.getItems().stream().map(data -> {
            ContractDto contract = ObjectConverter.convert(data, ContractDto.class);
            contract.setBid(data.getContractId());
            contract.setLastContractBid(data.getBid());
            return contract;
        }).collect(Collectors.toList());
        return new PageResult<ContractDto>(list, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    public List<ContractDo> getByEmp(List<String> empIds) {
        List<LastContractDo> lastContracts = lastContractDo.getByEmp(empIds);

        return lastContracts.stream()
                .map(lc -> {
                    ContractDo c = ObjectConverter.convert(lc, ContractDo.class);
                    c.setBid(lc.getContractId());
                    c.setIdentifier(ContractDo.IDENTIFIER);
                    return c;
                }).collect(Collectors.toList());
    }

    /**
     * 分页查询最新合同
     *
     * @param queryDto
     * @return
     */
    public PageResult<ContractDto> getContractPage(ContractQueryDto queryDto) {
        PageResult<LastContractDo> page = lastContractDo.getContractPage(queryDto);
        return buildContract(page);
    }

    /**
     * 分页查询最新合同
     *
     * @param queryDto
     * @return
     */
    public PageResult<LastContractDo> getLastContractPage(ContractQueryDto queryDto) {
        return lastContractDo.getContractPage(queryDto);
    }

    public LastContractDo getByBid(String bid) {
        return lastContractDo.getByBid(bid);
    }
}
