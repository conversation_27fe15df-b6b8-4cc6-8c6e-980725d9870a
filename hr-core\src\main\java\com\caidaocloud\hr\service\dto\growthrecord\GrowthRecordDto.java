package com.caidaocloud.hr.service.dto.growthrecord;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("成长记录Dto")
public class GrowthRecordDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 数据变化显示title
     */
    private String dataChangeTitle;

    /**
     * 数据变化来源id
     */
    private String dataChangeSourceId;

    /**
     * 数据变化原因
     */
    private String dataChangeReason;

    /**
     * 业务变化事件类型
     */
    private String businessEventType;

    /**
     * 字段变化列表
     */
    private List<DataItem> dataList;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 业务发生时间
     */
    private Long effectiveDate;

    /**
     * 数据开始时间
     */
//    private Long dataStartTime;

    /**
     * 数据结束时间
     */
//    private Long dataEndTime;

    /**
     * 数据结束时间
     */
    private Long dueDate;

    /**
     * 是否删除标识 默认（false）
     */
    private Boolean isDelete = false;

    /**
     * 数据项
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class DataItem {

        /**
         * 字段属性
         */
        private String prop;

        /**
         * 字段类型
         */
        private String dataType;

        /**
         * 字段名称
         */
        private String text;

        /**
         * 变前后的值
         */
        private String value;

        /**
         * 变化后的值
         */
        private String newValue;

        public DataItem(String prop) {
            this.prop = prop;
        }

        public DataItem(String prop, String value) {
            this.prop = prop;
            this.value = value;
        }

        public DataItem(String prop, String value, String newValue) {
            this.prop = prop;
            this.value = value;
            this.newValue = newValue;
        }
    }
}
