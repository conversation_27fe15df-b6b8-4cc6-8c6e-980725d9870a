package com.caidaocloud.hr.service.contract.application.cron;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.service.ContractDomainService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同签订次数定时任务
 *
 * <AUTHOR>
 * @date 2022/5/25
 */
@Component
@Slf4j
public class ContractSignTimeTask {
    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;
    @Resource
    private ContractService contractService;
    @Resource
    private ContractDomainService contractDomainService;

    @XxlJob("contractSignTimeJobHandler")
    public ReturnT<String> contractSignTimeJobHandler() {
        XxlJobHelper.log("XxlJob contractSignTimeJobHandler start");
        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());

        for (String tenantId : tenantList) {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            // 系统跑批userid默认为0
            userInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            contractService.countSignTime();
            SecurityUserUtil.removeSecurityUserInfo();
        }

        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob contractSignTimeJobHandler end");
        return ReturnT.SUCCESS;
    }

    /**
     * 合同状态定时任务
     * @return
     */
    @XxlJob("contractStatusJobHandler")
    public ReturnT<String> contractStatusJobHandler() {
        XxlJobHelper.log("XxlJob contractStatusJobHandler start");
        log.info("cronTask[Template package]------------------------start execution,time {}", System.currentTimeMillis());

        Long currentDay = DateUtil.getMidnightTimestamp();
        for (String tenantId : tenantList) {
            try {
                UserInfo user = new UserInfo();
                user.setTenantId(tenantId);
                user.setStaffId(0L);
                user.setUserid(0);
                user.setEmpid(0);

                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                // 系统跑批userid默认为0
                userInfo.setUserId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);

                // 合同结束日期小于今天且状态为生效中的变更为【已失效】
                contractService.invalidContractStatus(currentDay);
                // 合同开始日期等于今天且状态为未生效的变更为【生效中】
                contractService.effectiveContractStatus(currentDay);
                // 处理终止日期不为空，且终止日期已到期的合同 DEV-12792
                contractDomainService.doContractByLastDay(currentDay + 24 * 60 * 60 * 1000);
            } catch (Exception e) {
                log.error("XxlJob contractStatusJobHandler Exec failure,time {},tenantId {}", System.currentTimeMillis(), tenantId);
                log.error("错误详细信息", e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
                UserContext.remove();
            }
        }
        log.info("cronTask[Template package]------------------------stop execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob contractStatusJobHandler end");
        return ReturnT.SUCCESS;
    }

}
