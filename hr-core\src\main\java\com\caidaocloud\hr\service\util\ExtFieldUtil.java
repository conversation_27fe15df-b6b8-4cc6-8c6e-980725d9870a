package com.caidaocloud.hr.service.util;

import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.util.I18nUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class ExtFieldUtil {
    public static void doExtField(List<MetadataPropertyVo> cusPropList, Map<String, MetadataPropertyVo> fieldMap, DataSimple dataSimple, Map<String, Object> data) {
        cusPropList.forEach(prop -> {
            doExtField(fieldMap, dataSimple, data, prop);
        });
    }

    public static void doExtField(Map<String, MetadataPropertyVo> fieldMap, DataSimple dataSimple, Map<String, Object> data, MetadataPropertyVo prop) {
        MetadataPropertyVo propVal = fieldMap.get(prop.getProperty());
        if (null == propVal) {
            // 字段不存在
            return;
        }
        String property = prop.getProperty();
        Object propertyValue = dataSimple.getProperties().get(property);
        if (propertyValue == null) {
            data.put(property, null);
        } else {
            if (PropertyDataType.Emp.equals(prop.getDataType())) {
                EmpSimple empSimple = FastjsonUtil.toObject(FastjsonUtil.toJson(propertyValue), EmpSimple.class);
                empSimple = null == empSimple || StringUtil.isEmpty(empSimple.getEmpId()) ? null : empSimple;
                data.put(property, empSimple);
            } else if (PropertyDataType.Enum.equals(prop.getDataType()) || propertyValue instanceof EnumSimple) {
                EnumSimple enumSimple = FastjsonUtil.toObject(FastjsonUtil.toJson(propertyValue), EnumSimple.class);
                enumSimple = null == enumSimple || StringUtil.isEmpty(enumSimple.getValue()) ? null : enumSimple;
                handleEnumTxt(enumSimple, prop);
                data.put(property, enumSimple);
            } else if (PropertyDataType.Timestamp_Array.equals(prop.getDataType())) {
                if (propertyValue instanceof List) {
                    data.put(property, propertyValue);
                } else {
                    data.put(property, ((SimplePropertyValue) propertyValue).getArrayValues());
                }
            } else if (prop.getDataType().isComponent() || prop.getDataType().isArray()) {
                // 暂不支持组件和数组保存
                data.put(property, propertyValue);
            } else {
                SimplePropertyValue simplePropertyValue = FastjsonUtil.toObject(FastjsonUtil.toJson(propertyValue), SimplePropertyValue.class);
                data.put(property, simplePropertyValue.getValue());
            }
        }
    }

    private static void handleEnumTxt(EnumSimple enumSimple, MetadataPropertyVo prop) {
        if (Objects.isNull(enumSimple) || StringUtil.isBlank(enumSimple.getValue()) || StringUtils.isNoneBlank(enumSimple.getText()) || CollectionUtils.isEmpty(prop.getEnumDef())) {
            return;
        }
        Optional<PropertyEnumDefDto> optional = prop.getEnumDef().stream().filter(e -> e.getValue().equals(enumSimple.getValue())).findAny();
        if (!optional.isPresent()) {
            return;
        }
        val propertyEnumDef = optional.get();
        String txt = null;
        if (!CollectionUtils.isEmpty(propertyEnumDef.getI18nDisplay())) {
            txt = I18nUtil.lang(propertyEnumDef.getI18nDisplay());
        }
        enumSimple.setText(StringUtils.isNotBlank(txt) ? txt : StringUtils.defaultString(propertyEnumDef.getDisplay()));
    }

    public static void doExtObj(Map<String, Object> ext, Map<String, MetadataPropertyVo> fieldMap, Map<String, Object> data) {
        ext.forEach((k, v) -> {
            doComponentField(k, v, fieldMap, data);
        });
    }

    public static void doComponentField(String k, Object v, Map<String, MetadataPropertyVo> fieldMap, Map<String, Object> data) {
        MetadataPropertyVo prop = fieldMap.get(k);
        if (null == prop) {
            // 字段不存在
            return;
        }

        if (PropertyDataType.Attachment.equals(prop.getDataType())) {
            Attachment attachment = FastjsonUtil.convertObject(v, Attachment.class);
            data.put(k, null == v ? null : attachment);
            return;
        }
        if (PropertyDataType.Dict.equals(prop.getDataType())) {
            if (v instanceof DictSimple) {
                data.put(k, v);
                return;
            } else if (v instanceof Map) {
                Map v1 = (Map) v;
                v = v1.getOrDefault("value", "");
            }
            data.put(k, null == v ? null : DictSimple.doDictSimple(v.toString()));
            return;
        }
        /*if(PropertyDataType.Enum.equals(prop.getDataType())){
            EnumSimple enumSimple = FastjsonUtil.convertObject(v, EnumSimple.class);
            data.put(k, enumSimple);
            return;
        }*/
        if (PropertyDataType.Address.equals(prop.getDataType())) {
            Address address = FastjsonUtil.convertObject(v, Address.class);
            data.put(k, null == v ? null : address);
            return;
        }
        if (PropertyDataType.Phone.equals(prop.getDataType())) {
            PhoneSimple phone = FastjsonUtil.convertObject(v, PhoneSimple.class);
            data.put(k, null == v ? null : phone);
            return;
        }
        if (PropertyDataType.Emp.equals(prop.getDataType())) {
            EmpSimple empSimple = FastjsonUtil.convertObject(v, EmpSimple.class);
            data.put(k, empSimple);
            return;
        }
        if (PropertyDataType.Timestamp_Array.equals(prop.getDataType())) {
            SimplePropertyValue spv = null;
            if (v instanceof List) {
                spv = new SimplePropertyValue();
                spv.setType(PropertyDataType.Timestamp_Array);
                spv.setArrayValues(FastjsonUtil.toList(FastjsonUtil.toJson(v), String.class));
            } else {
                spv = FastjsonUtil.convertObject(v, SimplePropertyValue.class);
            }
            data.put(k, spv);
            return;
        }
        if (PropertyDataType.Enum.equals(prop.getDataType()) && Objects.nonNull(v) && (v instanceof EnumSimple)) {
            data.put(k, ((EnumSimple) v).getValue());
            return;
        }
        if (prop.getDataType().isComponent() && prop.getDataType().isArray()) {
            // 暂不支持组件和数组保存
            return;
        }
        if (v instanceof SimplePropertyValue) {
            v = ((SimplePropertyValue) v).getValue();
        }

        data.put(k, null == v ? null : v.toString());
    }

    public static void doProperties(Map<String, Object> data, DataSimple dataSimple) {
        NestPropertyValue properties = dataSimple.getProperties();
        data.forEach((k, v) -> {
            if (v instanceof String) {
                /*SimplePropertyValue spv = new SimplePropertyValue();
                spv.setType(PropertyDataType.String);
                spv.setValue((String) v);
                properties.add(k, spv);*/
                properties.add(k, (String) v);
                return;
            } else if (v instanceof PropertyValue) {
                properties.add(k, (PropertyValue) v);
            }
        });
    }
}
