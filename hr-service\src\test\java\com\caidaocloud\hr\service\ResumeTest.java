package com.caidaocloud.hr.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.application.feign.HrPaasFeignClient;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumeConfigDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.ResumeFormConfig;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.AbsFormResumeService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpResumeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = HRApplication.class)
public class ResumeTest {
    private static String str = "{\"configs\":[{\"alias\":\"a\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkInfo\",\"multiple\":false,\"timeline\":true,\"width\":120},{\"alias\":\"b\",\"height\":120,\"identifier\":\"entity.hr.EmpPrivateInfo\",\"multiple\":false,\"timeline\":false,\"width\":120},{\"alias\":\"edus\",\"height\":120,\"identifier\":\"entity.hr.EmpEduExp\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"ewes\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkExperience\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"c\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkOverview\",\"multiple\":false,\"postMethod\":\"calcWorkAge\",\"timeline\":false,\"width\":120},{\"alias\":\"d\",\"height\":120,\"identifier\":\"EmpCurrentContract\",\"multiple\":false,\"timeline\":false,\"width\":120}],\"fileName\":\"2.0履历模板_20250716.docx\",\"id\":0,\"mergeConfigs\":{\"SOCIAL_CAREER\":{\"config\":{\"TRANSFER_1979490510911488\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"workplace_work$Transfer\",\"c\":\"organize_work$Transfer\",\"d\":\"post_work$Transfer\",\"e\":\"leadEmpId_work$Transfer\"}],\"TRANSFER_1931312175077378\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"workplace_work$Transfer\",\"c\":\"organize_work$Transfer\",\"d\":\"post_work$Transfer\",\"e\":\"leadEmpId_work$Transfer\"}]},\"mainSortField\":{\"dataType\":\"DATE_TXT\",\"field\":\"a\"}},\"POST_CLC\":{\"config\":{\"TRANSFER_1979485446961152\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_jobGrade_work$Transfer\",\"d\":\"jobGrade_work$Transfer\",\"e\":\"\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_dengji_work$Transfer\",\"d\":\"dengji_work$Transfer\",\"e\":\"\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_CLC_work$Transfer\",\"d\":\"CLC_work$Transfer\",\"e\":\"\"}],\"TRANSFER_1979490510911488\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:岗位\",\"c\":\"old_post_work$Transfer\",\"d\":\"post_work$Transfer\",\"e\":\"\"}],\"TRANSFER_1979468587694080\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:岗位\",\"c\":\"old_post_work$Transfer\",\"d\":\"post_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:职位\",\"c\":\"old_job_work$Transfer\",\"d\":\"job_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_jobGrade_work$Transfer\",\"d\":\"jobGrade_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_CLC_work$Transfer\",\"d\":\"CLC_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:CLC等级\",\"c\":\"old_dengji_work$Transfer\",\"d\":\"dengji_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:职位\",\"c\":\"old_zhiwei_work$Transfer\",\"d\":\"zhiwei_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"}],\"TRANSFER_2218709452699944\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:岗位\",\"c\":\"old_post_work$Transfer\",\"d\":\"post_work$Transfer\",\"e\":\"Select_ke5adc$entity.form.1979498882856960\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:职位\",\"c\":\"old_job_work$Transfer\",\"d\":\"job_work$Transfer\",\"e\":\"Select_ke5adc$entity.form.1979498882856960\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:职位\",\"c\":\"old_zhiwei_work$Transfer\",\"d\":\"zhiwei_work$Transfer\",\"e\":\"Select_ke5adc$entity.form.1979498882856960\"}]},\"mainSortField\":{\"dataType\":\"DATE_TXT\",\"field\":\"a\"},\"secondSortList\":{\"field\":\"b\",\"sortList\":[{\"sort\":1,\"val\":\"岗位\"},{\"sort\":2,\"val\":\"职位\"},{\"sort\":3,\"val\":\"CLC等级\"}]}},\"RANK_POINTS\":{\"config\":{\"TRANSFER_2201106563586355\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:Rank\",\"c\":\"old_Rank_work$Transfer\",\"d\":\"Rank_work$Transfer\",\"e\":\"Textarea_aN0If1$entity.form.2201108290304472\"}],\"TRANSFER_1979485446961152\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:等级积分\",\"c\":\"old_dengjijifen_work$Transfer\",\"d\":\"dengjijifen_work$Transfer\",\"e\":\"\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:Rank\",\"c\":\"old_Rank_work$Transfer\",\"d\":\"Rank_work$Transfer\",\"e\":\"\"}],\"TRANSFER_1979468587694080\":[{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:Rank\",\"c\":\"old_Rank_work$Transfer\",\"d\":\"Rank_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"},{\"a\":\"effectiveDate$Transfer\",\"b\":\"txt:等级积分\",\"c\":\"old_dengjijifen_work$Transfer\",\"d\":\"dengjijifen_work$Transfer\",\"e\":\"Input_9CNHM$entity.form.1978125792327680\"}]},\"mainSortField\":{\"dataType\":\"DATE_TXT\",\"field\":\"a\"},\"secondSortList\":{\"field\":\"b\",\"sortList\":[{\"sort\":1,\"val\":\"Rank\"},{\"sort\":2,\"val\":\"等级积分\"}]}}},\"formConfigs\":{\"entity.form.1973145885014755\":{\"type\":\"DATETYPE_OF_LASTEST_DATA\",\"aliases\":\"ccc\",\"fieldList\":[{\"prop\":\"DatePicker_ueLB05\",\"sort\":1}],\"filterList\":[{\"prop\":\"Switch_u_p4wJ\",\"val\":\"是\"}],\"sourceDatePattern\":\"yyyy-MM-dd\",\"targetFormat\":\"%s\",\"sortProp\":\"DatePicker_ueLB05\"}},\"template\":\"8663t6g3b5c183d22684-2.0履历模板_20250716.docx\",\"tenantId\":\"32840\"}";
    private static String str2 = "{\"tenantId\":\"11\",\"configs\":[{\"alias\":\"a\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkInfo\",\"multiple\":false,\"timeline\":true,\"width\":120},{\"alias\":\"b\",\"height\":120,\"identifier\":\"entity.hr.EmpPrivateInfo\",\"multiple\":false,\"timeline\":false,\"width\":120},{\"alias\":\"edus\",\"height\":120,\"identifier\":\"entity.hr.EmpEduExp\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"ewes\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkExperience\",\"multiple\":true,\"timeline\":false,\"width\":120},{\"alias\":\"c\",\"height\":120,\"identifier\":\"entity.hr.EmpWorkOverview\",\"multiple\":false,\"postMethod\":\"calcWorkAge\",\"timeline\":false,\"width\":120},{\"alias\":\"d\",\"height\":120,\"identifier\":\"EmpCurrentContract\",\"multiple\":false,\"timeline\":false,\"width\":120},{\"alias\":\"e\",\"height\":120,\"identifier\":\"entity.certificate.certificateAndEmp\",\"multiple\":true,\"timeline\":false,\"width\":120}]}";
    @Resource
    private HrPaasFeignClient paasFeignClient;
    @Autowired
    private EmpResumeService empResumeService;

    @Before
    public void before() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0l);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void test1() {
        Result<PageResult<Map<String, Object>>> formPage = paasFeignClient.formPage("1973145885014755", 1, 100, "1973171391002624", true);
        List<Map<String, Object>> items = formPage.getData().getItems();
        EmpResumeConfigDto erc = EmpResumeConfigDto.convert(str);
        ConfigureBuilder cb = Configure.builder().useSpringEL(false);
        Map<String, Object> dataMap = Maps.newHashMap();
        Map<String, Object> formDataMap = Maps.newHashMap();
        formDataMap.put("entity_form_1973145885014755", items);
        if (MapUtils.isNotEmpty(erc.getFormConfigs())) {
            for (Map.Entry<String, ? extends ResumeFormConfig> entry : erc.getFormConfigs().entrySet()) {
                AbsFormResumeService.process(cb, dataMap, entry.getKey(), formDataMap, entry.getValue());
            }
        }
        System.out.println(dataMap);
    }

    @Test
    public void test2() {
        EmpResumeConfigDto convert = EmpResumeConfigDto.convert(str2);
        Map<String, Object> data = empResumeService.getEmpResumeData("1999215364208640", System.currentTimeMillis(), convert);
        System.out.println(data);
    }

    public static void main(String[] args) {
        EmpResumeConfigDto convert = EmpResumeConfigDto.convert(str2);
        System.out.println(convert);
    }
}
