package com.caidaocloud.hr.service.common.interfaces;

import com.caidaocloud.hr.service.common.application.service.ModelApplicationService;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService.CONTRACT_EMP_PROPERTY;

@Slf4j
@RestController
@RequestMapping("/api/hr/model/v1")
@Api(value = "/api/hr/model/v1", description = "模型", tags = "v1.7")
public class MetaController {
    @Autowired
    private ModelApplicationService modelApplicationService;

    @ApiOperation("最新合同记录模型定义")
    @GetMapping("last/contract")
    public Result<List<MetadataPropertyVo>> getContractModel() {
        var contractModel = modelApplicationService.getContractModel();
        contractModel = contractModel.stream().filter(e ->
                        CONTRACT_EMP_PROPERTY.contains(e.getProperty()))
                .collect(Collectors.toList());
        return Result.ok(contractModel);
    }
}