package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.hr.core.feign.DictFeignFallBack;
import com.caidaocloud.hr.service.organization.application.org.dto.DictKeyValue;
import com.caidaocloud.hr.service.temination.application.dto.bcc.SysParamDictDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "caidaocloud-business-config-center",
        fallback = DictFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "terminationDictFeignClient")
public interface DictFeignClient {

    @GetMapping("/api/bcc/dict/common/v1/info")
    Result<SysParamDictDto> getDictInfo(@RequestParam(value = "dictType") String dictType,
            @RequestParam(value = "dictCode") String dictCode);

    @GetMapping("/api/bcc/dict/common/v1/dict/detail")
    Result<SysParamDictDto> getDictDetail(@RequestParam("id") Long dictId);

    @GetMapping("/api/bcc/dict/common/v1/dict/getEnableDictList")
    Result<List<DictKeyValue>> getEnableDictList(@RequestParam(name = "typeCode") String typeCode,
                                                 @RequestParam(name = "belongModule", required = false) String belongModule);
}
