package com.caidaocloud.hr.service.dto.ruleset;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgSettingsDto {
    @ApiModelProperty("公司管理展示联系人信息")
    private Boolean showContacts;
    @ApiModelProperty("职务管理层级(范围：2～4)")
    private Integer jobLevel;
    @ApiModelProperty("岗位/职位管理体系:1.岗位 2.职位")
    private Integer manageSystem;
    @ApiModelProperty("公司管理展示税务信息")
    private boolean taxInfo;
}
