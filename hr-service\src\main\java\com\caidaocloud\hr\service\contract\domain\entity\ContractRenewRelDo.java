package com.caidaocloud.hr.service.contract.domain.entity;

import com.caidaocloud.hr.service.contract.domain.repository.IContractRenewRelRepository;
import com.caidaocloud.hr.service.employee.domain.base.entity.BaseDomainDoImpl;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 自动续签规则匹配
 * created by: FoAng
 * create time: 2/12/2024 10:17 上午
 */
@Slf4j
@Data
@Service
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ContractRenewRelDo extends BaseDomainDoImpl<ContractRenewRelDo> {

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("自动续签规则ID")
    private String renewRuleId;

    @ApiModelProperty("续签规则名称")
    private String renewRuleName;

    @ApiModelProperty("版本")
    private Integer version;

    private static final String IDENTIFIER = "entity.hr.ContractRenewRel";

    @Resource
    private IContractRenewRelRepository contractRenewRelRepository;

    @Override
    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public BaseRepository<ContractRenewRelDo> getRepository() {
        return contractRenewRelRepository;
    }

    public List<ContractRenewRelDo> listByEmpId(String empId) {
        return contractRenewRelRepository.listByEmpId(getDoIdentifier(), empId);
    }

    public Integer versionByRule(String ruleId) {
        return contractRenewRelRepository.oneVersion(getDoIdentifier(), ruleId);
    }

    public void batchInsert(List<ContractRenewRelDo> renewRelDos) {
        contractRenewRelRepository.batchInsert(getDoIdentifier(), renewRelDos);
    }

    public void deleteRuleRel(String version, String ruleId) {
        contractRenewRelRepository.deleteRuleRel(getDoIdentifier(), version, ruleId);
    }

}
