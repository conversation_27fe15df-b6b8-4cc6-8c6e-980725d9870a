package com.caidaocloud.hr.service.enums;

import com.caidaocloud.util.StringUtil;

/**
 * 薪资类型枚举
 *
 *
 **/
public enum EmpSalaryEnum {
    AnnualPaymentType("2", "年薪"),
    OtherSalaryType("3", "其它"),
    MonthlyPaymentType("1", "月薪"),
//    DaySalaryType("4", "日薪"),
    HourlyRateType("0", "时薪");

    private String code;
    private String name;


    EmpSalaryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        if (!StringUtil.isBlank(code)) {
            for (EmpSalaryEnum value : EmpSalaryEnum.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
        }
        return null;
    }
}
