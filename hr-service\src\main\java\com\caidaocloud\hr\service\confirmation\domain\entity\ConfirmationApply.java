package com.caidaocloud.hr.service.confirmation.domain.entity;

import java.util.List;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationType;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
public class ConfirmationApply extends DataSimple {
	// 主键Id
	private String id;
	// 员工
	private EmpSimple emp;
	// 配置Id
	private String defId;
	// 挂载表单ID
	private String formId;

	// 表单formValueId
	private String formValueId;

	private List<ConfirmationChangeField> data;

	// 审批状态
	private WfProcessStatusEnum approvalStatus;

	// 创建时间
	private long createTime;

	// 更新时间
	private long updateTime;

	public void revoke() {
		if (approvalStatus!= WfProcessStatusEnum.IN_PROCESS) {
			throw new ServerException("Failed to revoke confirmation apply");
		}
		approvalStatus = WfProcessStatusEnum.REVOKE;
		updateTime = System.currentTimeMillis();
	}
}
