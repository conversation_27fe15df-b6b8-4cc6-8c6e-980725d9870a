package com.caidaocloud.hr.service.employee.application.dataimport.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po.TmpEmpPostImportPo;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.application.post.service.PostService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.util.DateUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class TmpEmpPostImportService {
    @Autowired
    private EmpWorkInfoService empWorkInfoService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private PostService postService;
    @Autowired
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    public void updatePost(MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        try {
            List<TmpEmpPostImportPo> importData = ExcelImportUtil.importExcel(file.getInputStream(), TmpEmpPostImportPo.class, params);
            Iterator<TmpEmpPostImportPo> iterator = importData.iterator();
            List<TmpEmpPostImportPo> dataList = new ArrayList<>(500);
            while (iterator.hasNext()) {
                TmpEmpPostImportPo po = iterator.next();
                dataList.add(po);
                iterator.remove();

                if (dataList.size() >= 500 || !iterator.hasNext()) {
                    doUpdate(dataList);
                }
            }
        } catch (Exception e) {
            log.error("import emp info occur error ,msg = {}", e.getMessage(), e);
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_32014"));
        }
    }

    private void doUpdate(List<TmpEmpPostImportPo> dataList) {
        // 获取员工任职信息
        List<String> workNos = Sequences.sequence(dataList).map(TmpEmpPostImportPo::getWorkno).toList();
        List<EmpWorkInfoDo> workInfoList = empWorkInfoService.getEmpListByWorkno(workNos);
        Map<String, EmpWorkInfoDo> workInfoMap = workInfoList.stream()
                .collect(Collectors.toMap(EmpWorkInfoDo::getWorkno, workInfo -> workInfo, (a, b) -> a));

        // 获取组织信息
        List<String> orgCodeList = Sequences.sequence(dataList).map(TmpEmpPostImportPo::getOrgCode).toList();
        List<OrgDo> orgList = orgService.getOrgListByOrgCodes(orgCodeList, DateUtil.getCurrentTimestamp());
        Map<String, OrgDo> orgMap = orgList.stream()
                .collect(Collectors.toMap(OrgDo::getCode, org -> org, (a, b) -> a));

        // 获取岗位信息
        List<String> postCodeList = Sequences.sequence(dataList).map(TmpEmpPostImportPo::getPostCode).toList();
        List<PostDo> postList = postService.selectPostDoByCodes(postCodeList, DateUtil.getCurrentTimestamp());
        Map<String, PostDo> postMap = postList.stream()
                .collect(Collectors.toMap(PostDo::getCode, post -> post, (a, b) -> a));

        for (TmpEmpPostImportPo importPo : dataList) {
            EmpWorkInfoDo workInfo = workInfoMap.get(importPo.getWorkno());
            if (workInfo == null) {
                log.warn("update emp post failed，{}",importPo.getWorkno());
                continue;
            }

            OrgDo orgDo = orgMap.get(importPo.getOrgCode());
            if (orgDo == null) {
                log.warn("update emp post failed，{}",importPo.getWorkno());
                continue;
            }

            PostDo post = postMap.get(importPo.getPostCode());
            if (post == null) {
                log.warn("update emp post failed，{}",importPo.getWorkno());
                continue;
            }
            workInfo.setOrganize(orgDo.getBid());
            workInfo.setOrganizeTxt(orgDo.getName());
            workInfo.setPost(post.getBid());
            workInfo.setPostTxt(post.getName());
            empWorkInfoDomainService.update(workInfo);
        }
        dataList.clear();
    }
}
