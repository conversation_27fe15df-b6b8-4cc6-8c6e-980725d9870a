package com.caidaocloud.hr.service.common.application.feign;

import com.caidaocloud.hr.service.common.application.dto.ContractRevokeDto;
import com.caidaocloud.hr.service.temination.application.dto.esign.ContractSignDto;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignFallback;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@FeignClient(
		value = "caidaocloud-esign-service",
		fallback = FormFeignFallback.class,
		configuration = FeignConfiguration.class,
		contextId = "esignFeignClient"
)
public interface EsignFeignClient {
	/**
	 * 查看员工签署文件
	 *
	 * @param empId
	 * @param businessProcesses
	 * @return
	 */
	@GetMapping("/api/esign/contractSign/v1/emp")
	Result<List<ContractSignDto>> getContractByEmp(@RequestParam("empId") String empId, @RequestParam("businessProcesses") String businessProcesses);

	@GetMapping("/api/esign/common/v1/uploadByContractId")
	Result<Attachment> uploadByContractId(@RequestParam("openContractId") String openContractId);

	/**
	 * 预览合同url地址
	 * @param openContractId
	 * @return
	 */
	@GetMapping("/api/esign/common/v1/preview")
	Result<String> previewContract(@RequestParam("openContractId") String openContractId);

	@PostMapping("/api/esign/contractSign/v1/batchRevoke")
	Result batchRevoke(@RequestBody ContractRevokeDto dto);
}
