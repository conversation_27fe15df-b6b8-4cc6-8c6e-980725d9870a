package com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 工作流字段注册-合同续签
 *
 * <AUTHOR>
 */
public class ContractRenewFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return ContractCommonFormDef.formList();
    }
}
