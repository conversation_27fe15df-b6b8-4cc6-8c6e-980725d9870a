package com.caidaocloud.hr.service.contract.infrastructure.repository.impl;

import com.caidaocloud.hr.service.contract.domain.entity.ContractRenewRuleDo;
import com.caidaocloud.hr.service.contract.domain.repository.IContractRenewRuleRepository;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hrpaas.metadata.sdk.dto.GroupedIndicateResult;
import com.caidaocloud.hrpaas.metadata.sdk.enums.IndicateType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 29/11/2024 3:12 下午
 */
@Slf4j
@Service
public class ContractRenewRuleRepositoryImpl extends BaseRepositoryImpl<ContractRenewRuleDo> implements IContractRenewRuleRepository {

    @Override
    public List<Integer> listAdvanceDay(String identifier) {
        List<GroupedIndicateResult> indicateResults = DataQuery.identifier(identifier)
                .indicateByGroup(DataFilter.eq("deleted", Boolean.FALSE.toString()), "advance", IndicateType.UNIQUE, System.currentTimeMillis(),"bid");
        if (CollectionUtils.isNotEmpty(indicateResults)) {
            return indicateResults.stream().map(GroupedIndicateResult::getIndicate).map(it -> Double.valueOf(it).intValue())
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
