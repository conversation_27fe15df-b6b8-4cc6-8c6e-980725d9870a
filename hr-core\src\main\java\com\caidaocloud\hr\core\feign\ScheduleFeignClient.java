package com.caidaocloud.hr.core.feign;

import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/1/16
 */
@FeignClient(
	value = "caidaocloud-hr-paas-service",
	fallback = ScheduleFeignFallback.class,
	configuration = FeignConfiguration.class,
	contextId = "paasScheduleFeign"
)
public interface ScheduleFeignClient {

	@PostMapping("/api/hrpaas/v1/schedule/add")
	Result<Boolean> addSchedule(@RequestBody ScheduleTaskDto scheduleTaskDto);

	@DeleteMapping("/api/hrpaas/v1/schedule/delete")
	Result<Boolean> deletePage(@RequestParam("taskTopic") String taskTopic, @RequestParam("taskId") String taskId);
}
