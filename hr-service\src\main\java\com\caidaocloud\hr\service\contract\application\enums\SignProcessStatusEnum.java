package com.caidaocloud.hr.service.contract.application.enums;

import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;

/**
 * 文件签署流程状态
 *
 * <AUTHOR>
 */
public enum SignProcessStatusEnum {
    initiate(0, "待发起", "initiate"),
    signing(1, "签署中", "signing"),
    finish(2, "已完成", "finish"),
    canceling(3, "作废中", "canceling"),
    revoke(4, "已撤销", "revoke"),
    cancel(5, "已作废", "cancel"),
    expired(6, "已过期", "expired"),
    exception(7, "异常", "exception"),
    REFUSED(8, "拒签", "REFUSED");

    private Integer value;
    private String name;
    private String code;

    SignProcessStatusEnum() {
    }

    SignProcessStatusEnum(Integer value) {
        this.value = value;
    }

    SignProcessStatusEnum(Integer value, String name, String code) {
        this.value = value;
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public static SignProcessStatusEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (SignProcessStatusEnum status : SignProcessStatusEnum.values()) {
            if (String.valueOf(status.getValue()).equals(value)) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByValue(String value) {
        if (value == null) {
            return null;
        }
        for (SignProcessStatusEnum status : SignProcessStatusEnum.values()) {
            if (String.valueOf(status.getValue()).equals(value)) {
                return status.getName();
            }
        }
        return null;
    }

    public EnumSimple convertToEnumSimple() {
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(String.valueOf(this.value));
        return enumSimple;
    }
}
