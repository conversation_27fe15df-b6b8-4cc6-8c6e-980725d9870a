package com.caidaocloud.hr.service.contract.infrastructure.config.workflow.field;

import com.caidaocloud.workflow.annotation.WfMetaFunFormDef;
import com.caidaocloud.workflow.dto.WfMetaFunFormFieldDto;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 改签
 *
 * <AUTHOR>
 */
public class ContractAmendmentFormDef extends WfMetaFunFormDef {
    @NotNull
    @Override
    public List<WfMetaFunFormFieldDto> formList() {
        return ContractCommonFormDef.formList();
    }
}
