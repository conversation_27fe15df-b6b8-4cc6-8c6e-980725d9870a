package com.caidaocloud.hr.service.contract.interfaces.facade;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hr.service.contract.application.event.publish.ContractTypeSetPublish;
import com.caidaocloud.hr.service.contract.application.service.EmpContractTypeSetRelService;
import com.caidaocloud.hr.service.contract.domain.entity.EmpContractTypeSetRelDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.EmpContractTypeSetRelDto;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetRelListVo;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hr/contractset/emp/v1")
@Api(value = "/api/hr/contractset/emp/v1", description = "员工与合同设置关联关系", tags = "v1.5")
public class EmpContractTypeSetRelController {
    @Resource
    private EmpContractTypeSetRelService empContractTypeSetRelService;
    @Resource
    private ContractTypeSetPublish contractTypeSetPublish;
    @Resource
    private CacheService cacheService;

    @ApiOperation("获取当前员工所属的合同类型")
    @GetMapping("/list")
    public Result<List<ContractTypeSetRelListVo>> list(@RequestParam("empId") String empId,
                                                    String company,
                                                    String signType) {
        List<ContractTypeSetRelListVo> list = empContractTypeSetRelService.getEmpContractTypeList(empId, company, signType);
        return Result.ok(list);
    }

    @ApiOperation("根据员工和合同公司获取合同设置")
    @GetMapping("/selectList")
    public Result<List<KeyValue>> selectList(@RequestParam("empId") String empId, String company) {
        List<ContractTypeSetRelListVo> list = empContractTypeSetRelService.getEmpContractTypeList(empId, company, null);
        if(!CollectionUtils.isEmpty(list)){
            return Result.ok(list.stream().map(cts -> new KeyValue(cts.getContractTypeSetTxt(), cts.getBid())).collect(Collectors.toList()));
        }
        return Result.ok(Lists.newArrayList());
    }

    @ApiOperation("新增合同类型")
    @PostMapping("/save")
    public Result saveContract(@RequestBody EmpContractTypeSetRelDto dto) {
        empContractTypeSetRelService.save(ObjectConverter.convert(dto, EmpContractTypeSetRelDo.class));
        return Result.ok();
    }

    @ApiOperation("刷新合同类型")
    @GetMapping("/refresh")
    public Result refresh(@RequestParam(required = false, value = "bid") String bid) {
        String progress = UUID.randomUUID().toString().replaceAll("-", "");
        ImportExcelProcessVo processObj = new ImportExcelProcessVo(progress, 100, 0, 0, 0, 0);
        String tenantId = UserContext.getTenantId();
        String progressKey = String.format("contract_type_set_progress_%s_%s", tenantId, progress);
        cacheService.cacheValue(progressKey, FastjsonUtil.toJson(processObj), 300);
        contractTypeSetPublish.publishContractTypeSetRefresh(tenantId, bid, progress);
        return Result.ok(progress);
    }

    @ApiOperation("获取合同类型刷新进度")
    @GetMapping("/progress")
    public Result<ImportExcelProcessVo> progress(@RequestParam(required = false, value = "progress") String progress) {
        String tenantId = UserContext.getTenantId();
        String progressKey = String.format("contract_type_set_progress_%s_%s", tenantId, progress);
        String value = cacheService.getValue(progressKey);
        if(StringUtil.isEmpty(value)){
            return Result.ok(new ImportExcelProcessVo(progress, 100, 100, 0, 100, 0));
        }

        return Result.ok(FastjsonUtil.toObject(value, ImportExcelProcessVo.class));
    }

}
