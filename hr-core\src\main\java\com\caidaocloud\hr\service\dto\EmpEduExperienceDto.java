package com.caidaocloud.hr.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工教育经历DTO")
public class EmpEduExperienceDto {
    @ApiModelProperty("员工教育经历id")
    private String bid;

    /**
     * 员工ID
     */
    @ApiModelProperty("员工ID")
    private String empId;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private Long startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private Long endDate;

    /**
     * 毕业学校
     */
    @ApiModelProperty("毕业学校")
    private String school;

    /**
     * 专业
     */
    @ApiModelProperty("专业")
    private String major;

    /**
     * 学历
     */
    @ApiModelProperty("学历")
    private String background;

    /**
     * 学位
     */
    @ApiModelProperty("学位")
    private String degree;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("自定义字段")
    private Map ext = new LinkedHashMap();
}
