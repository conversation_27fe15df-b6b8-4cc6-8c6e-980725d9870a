package com.caidaocloud.hr.service.confirmation.interfaces.facade;

import com.caidaocloud.hr.service.confirmation.application.ConfirmationConfigService;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationConfigDto;
import com.caidaocloud.hr.service.confirmation.application.dto.ConfirmationConfigEnableDto;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.confirmation.domain.enums.ConfirmationAppliedBy;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/confirmation/config")
@Api(value = "/api/hr/v1/confirmation/config", description = "转正配置", tags = "转正配置")
public class ConfirmationConfigController {

    @Autowired
    private ConfirmationConfigService confirmationConfigService;

    @PostMapping
    public Result<String> create(@RequestBody ConfirmationConfigDto config) {
        return Result.ok(confirmationConfigService.create(config));
    }

    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody ConfirmationConfigDto config) {
        confirmationConfigService.update(config);
        return Result.ok();
    }

    @ApiOperation("转正管理名称修改")
    @PostMapping ("/name/update")
    public Result<Boolean> updateName(@RequestBody ConfirmationConfigDto config) {
        confirmationConfigService.updateName(config);
        return Result.ok();
    }


    @PutMapping("/enable")
    public Result<Boolean> enable(@RequestBody ConfirmationConfigEnableDto enable) {
        confirmationConfigService.enable(enable.getBid());
        return Result.ok();
    }

    @GetMapping("/list")
    public Result<List<ConfirmationConfig>> list(@RequestParam ConfirmationAppliedBy applyBy, @RequestParam(required = false) boolean showDisabled) {
        return Result.ok(confirmationConfigService.list(applyBy, showDisabled));
    }

    @GetMapping("/one")
    public Result<ConfirmationConfig> one(@RequestParam ConfirmationAppliedBy applyType) {
        return Result.ok(confirmationConfigService.one(applyType));
    }

    @GetMapping("/one/byConfigId")
    public Result<ConfirmationConfig> oneById(@RequestParam String configId) {
        return Result.ok(confirmationConfigService.one(configId));
    }

    @ApiOperation("转正管理配置修改")
    @PostMapping("/updateConfig")
    public Result updateConfig(@RequestBody ConfirmationConfigDto configId) {
        confirmationConfigService.updateConfig(configId.getBid());
        return Result.ok();
    }

}