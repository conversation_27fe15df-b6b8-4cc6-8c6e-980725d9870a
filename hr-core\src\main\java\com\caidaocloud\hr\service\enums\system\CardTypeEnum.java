package com.caidaocloud.hr.service.enums.system;

public enum CardTypeEnum {
    ID_CARD("0", "身份证"),
    PASSPORT("1", "护照"),
    TAIWAN_COMPATRIOT_SYNDROME("2", "台湾居民来往大陆通行证"),
    HK_MACAO_MAINLAND_PASS("8", "港澳居民来往内地通行证"),
    HK_MACAO_PERMANENT_IDCARD("3", "港澳永久身份证"),
    HK_MACAO_NON_PERMANENT_IDCARD("4", "港澳非永久身份证"),
    DRIVER_LICENSE("5", "驾驶证"),
    TAIWAN_IDCARD("6", "台湾身份证"),
    OTHER("7", "其他");

    private String index;
    private String name;

    CardTypeEnum(String index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(String index) {
        for (CardTypeEnum c : CardTypeEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
