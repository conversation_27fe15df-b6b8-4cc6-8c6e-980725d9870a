package com.caidaocloud.hr.service.contract.application.enums;

/**
 * 合同，续签意向状态
 * <AUTHOR>
 * @date 2023/3/18
 */
public enum ContinueStatus {
	// 未发起
	TODO,
	// 已发起
	INITIATED,
	// 已确认
	CONFIRMED,
	;

	public static ContinueStatus getByName(String name) {
		if (name == null) {
			return TODO;
		}
		for (ContinueStatus value : ContinueStatus.values()) {
			if (value.name().equals(name)) {
				return value;
			}
		}
		return TODO;
	}
}
