package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.caidaocloud.hr.service.employee.application.common.service.BaseServiceImpl;
import com.caidaocloud.hr.service.employee.domain.base.service.BaseDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPostRecordDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPostRecordDomainService;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.PostRecordListVo;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.PostPo;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EmpPostRecordService extends BaseServiceImpl {
    @Resource
    private EmpPostRecordDomainService empPostRecordDomainService;
    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    @Override
    protected BaseDomainService getDomainService() {
        return empPostRecordDomainService;
    }

    public EmpPostRecordDo updateById(EmpPostRecordDo empPostRecordDo) {
        DataUpdate.identifier(empPostRecordDo.getIdentifier()).update(ObjectConverter.convert(empPostRecordDo, EmpPostRecordDo.class));
        return empPostRecordDo;
    }


    public List<PostRecordListVo> selectListByEmpAndDate(String empId, Long dateTime) {
        List<EmpPostRecordDo> dos = empPostRecordDomainService.selectListByEmpAndLeDate(empId, dateTime);
        List<PostRecordListVo> vos = ObjectConverter.convertList(dos, PostRecordListVo.class);
        if (CollectionUtils.isNotEmpty(vos)) {
            PostRecordListVo vo = vos.get(0);
            if (vo.getPostDuration() == null) {
                vo.setPostDuration(empPostRecordDomainService.getDateSubtract(vo.getStartTime(), dateTime));
            }
        }
        vos.forEach(vo->{
            if(!"enabled".equals(postTxtShowCode)){
                String postTxt = vo.getPostTxt();
                if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    vo.setPostTxt(postTxt);
                }
            }
        });
        return vos;
    }

}
