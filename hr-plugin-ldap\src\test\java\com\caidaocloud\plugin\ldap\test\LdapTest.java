package com.caidaocloud.plugin.ldap.test;

import lombok.extern.slf4j.Slf4j;

import java.util.Hashtable;
import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;

@Slf4j
public class LdapTest {
    public static void main(String[] args) {
        connect("***************", "10389", "uid=admin,ou=system", "secret");

        //connect("***************", "10389", "caidao01", "secret");

        //connect("***************", "10389", "uid=caidao02,ou=system", "secret");

        lookup();
    }

    public static void connect(String host, String port, String username, String password) {
        DirContext ctx = null;
        Hashtable<String, String> hashEnv = new Hashtable();
        // LDAP访问安全级别(none,simple,strong)
        hashEnv.put(Context.SECURITY_AUTHENTICATION, "simple");
        //AD的用户名(需要加域名)
        hashEnv.put(Context.SECURITY_PRINCIPAL, username);
        //AD的密码
        hashEnv.put(Context.SECURITY_CREDENTIALS, password);
        // LDAP工厂类
        hashEnv.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        //连接超时设置为3秒
        hashEnv.put("com.sun.jndi.ldap.connect.timeout", "3000");
        // 修复乱码
        hashEnv.put("java.naming.ldap.attributes.binary", "objectGUID");
        // 默认端口389
        hashEnv.put(Context.PROVIDER_URL, "ldap://" + host + ":" + port);
        try {
            // 初始化上下文
            ctx = new InitialDirContext(hashEnv);
            log.info(username + "：身份验证成功!");
        } catch (AuthenticationException e) {
            log.error("身份验证失败!");
            e.printStackTrace();
        } catch (javax.naming.CommunicationException e) {
            log.error("AD域连接失败!");
            e.printStackTrace();
        } catch (Exception e) {
            log.error("身份验证未知异常!");
            e.printStackTrace();
        } finally {
            if (null != ctx) {
                try {
                    ctx.close();
                    ctx = null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void login(String username, String password) {
        log.info("开始执行 {} 登录", username);
        try { Hashtable env = new Hashtable();
            String ldapUrl = "ldap://***************:10389";
            env.put(Context.INITIAL_CONTEXT_FACTORY,"com.sun.jndi.ldap.LdapCtxFactory");
            env.put(Context.PROVIDER_URL, ldapUrl);
            env.put(Context.SECURITY_AUTHENTICATION, "simple");
            env.put(Context.SECURITY_PRINCIPAL, username);
            env.put(Context.SECURITY_CREDENTIALS, password);
            // 初始化上下文（拥有验证功能）
            InitialDirContext dc = new InitialDirContext(env);
            log.info("登陆成功 {}", username);
        } catch (javax.naming.AuthenticationException e) {
            log.error("{} 登录验证失败", username);
        } catch (Exception e) {
            log.error("{} 登录验证异常", username);
        }
    }

    public static String lookup() {
        System.out.println("------------------------lookup------");
        // 连接LDAP库
        Hashtable env = new Hashtable<>();
        String url = "ldap://***************:10389";
        String searchBase = "";
        String user = "uid=admin,ou=system";
        String password = "secret";
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory"); //LDAP工厂
        env.put(Context.SECURITY_AUTHENTICATION, "simple"); //LDAP访问安全级别
        env.put(Context.PROVIDER_URL, url);
        env.put(Context.SECURITY_PRINCIPAL, user);
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put("java.naming.ldap.attributes.binary", "objectSid objectGUID");
        LdapContext ctx = null;
        try {
            ctx = new InitialLdapContext(env, null);

            // 根据条件查询
            String cn = "";
            String filter = "(&(objectClass=top)(objectClass=organizationalPerson))";
            //String filter = "(&(objectClass=top)(objectClass=organizationalPerson))";
            SearchControls searchControls = new SearchControls();
            String[] attrNames = {"cn", "mail", "uid", "ou", "dc"};
            searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            //设置将被返回的Attribute
            searchControls.setReturningAttributes(attrNames);
            NamingEnumeration<SearchResult> search = ctx.search(searchBase, filter.toString(), searchControls);
            while (search.hasMore()) {
                SearchResult result = search.next();
                NamingEnumeration<? extends Attribute> attrs = result.getAttributes().getAll();
                while (attrs.hasMore()) {
                    Attribute attr = attrs.next();
                    System.out.println(attr.getID() + ":=====:" + attr.get());
                }
                System.out.println("===========");
            }
        } catch (NamingException e) {
            e.printStackTrace();
        } finally {
            if (ctx != null) {
                try {
                    ctx.close();
                } catch (NamingException e) {
                }
            }

        }
        System.out.println("---------------------end.");
        return "返回信息";
    }

}
