package com.caidaocloud.hr.service.enums;

import java.util.Optional;

/**
 * created by: FoAng
 * create time: 14/10/2024 3:54 下午
 */
public enum AgreementTypeEnum {

    ESIGN_H5("文件签署-移动端");

    final String desc;

    AgreementTypeEnum(String desc) {
        this.desc = desc;
    }

    public static AgreementTypeEnum of(String code) {
        return Optional.ofNullable(code).map(AgreementTypeEnum::valueOf).orElse(null);
    }
}
