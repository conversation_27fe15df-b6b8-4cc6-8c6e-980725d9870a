package com.caidaocloud.hr.service.employee.application.cron;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.hr.core.feign.ScheduleFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.TableGenerateUtil;
import com.caidaocloud.hr.service.dto.schedule.ScheduleTaskDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.message.sdk.dto.MsgConfigDto;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.enums.NotificationCycleTypeEnum;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.temporal.ChronoField;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.caidaocloud.message.sdk.enums.NotificationMethodEnum.MERGE;
import static com.caidaocloud.message.sdk.enums.NotificationMethodEnum.ONCE;

/**
 * 转正消息消息提醒触发
 * created by: FoAng
 * create time: 5/6/2023 2:36 下午
 */
@Slf4j
@Service
public class RegularizationNoticeTask {

    public static final String TOPIC = "regularization.notice";

    @Value("${caidaocloud.tenant:}")
    private List<String> tenantList;

    @Resource
    private MsgNoticeService msgNoticeService;

    @Resource
    private ScheduleFeignClient scheduleFeignClient;

    @Resource
    private EmpWorkInfoDo empWorkInfoDo;

    /**
     * 定时执行转正消息触发
     * @param params 执行器参数
     */
    @XxlJob("regularizationTaskHandler")
    public ReturnT<String> regularizationTaskHandler(String params) {
        log.info("regularizationTaskHandler with params: {}", params);
        log.info("cronTask[regularizationTaskHandler]------------------------start execution,time {}", System.currentTimeMillis());
        if (null == tenantList || tenantList.isEmpty()) {
            log.info("regularizationTaskHandler: tenantList is empty, execution end");
            return ReturnT.SUCCESS;
        }
        // 遍历租户，进行消息提醒
        for (String tenantId : tenantList) {
            handleTenantNoticeAction(tenantId);
        }
        log.info("cronTask[regularizationTaskHandler]------------------------end execution,time {}", System.currentTimeMillis());
        return ReturnT.SUCCESS;
    }

    private void initSecurityUser(String tenantId){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        UserInfo user = new UserInfo();
        user.setTenantId(tenantId);
        user.doSetUserId(userInfo.getUserId());
        UserContext.setCurrentUser(user);
    }

    public void handleTenantNoticeAction(String tenantId) {
        try {
            initSecurityUser(tenantId);
            List<MsgConfigDto> msgConfigDtoList = msgNoticeService.getMsgConfigList(NoticeType.EMP_PROBATION_END_REMIND);
            if(CollectionUtils.isEmpty(msgConfigDtoList)){
                log.info("cronTask[regularizationTaskHandler]-------message config is empty");
                return;
            }

            msgConfigDtoList.forEach(it -> {
                // 计算触发时间，查询试用期到期并且没有转正员工
                EmpPageQueryDto queryDto = new EmpPageQueryDto();
                queryDto.setPageNo(1);
                queryDto.setPageSize(500);
                Long [] ppArr = calculatePeriodPeriod(it);
                queryDto.setProbationPeriodRange(ppArr);
                queryDto.setFilters(buildPeriodFilters(queryDto));
                switch (it.getFunc()){
                    case MERGE:
                        // 合并发送
                        handleMergeNotice(it, queryDto);
                        break;
                    case CYCLE:
                        // 循环通知，每天都通知
                        handleNoticeWithEmp(it, queryDto);
                        break;
                    case ONCE:
                        // 单次通知
                        queryDto.setProbationPeriod(ppArr[0]);
                        queryDto.setFilters(buildPeriodFilters(queryDto));
                        handleNoticeWithEmp(it, queryDto);
                        break;
                }
            });
        } catch (Exception e) {
            log.error("regularizationTaskHandler query page err,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private List<FilterElement> buildPeriodFilters(EmpPageQueryDto dto) {
        Long[] periodRange = dto.getProbationPeriodRange();
        int periodLength = periodRange == null ? 0 : periodRange.length;
        if (periodLength == 0) {
            return Lists.newArrayList();
        }
        List<FilterElement> filterElements = Lists.newArrayList();
        if(null != dto.getProbationPeriod()){
            FilterElement filter = new FilterElement();
            filter.setOp(OpEnum.eq);
            filter.setProp("probationPeriodEndDate");
            filter.setValue(dto.getProbationPeriod());
            filterElements.add(filter);
        } else {
            if (periodLength == 1) {
                FilterElement filter = new FilterElement();
                filter.setOp(OpEnum.le);
                filter.setProp("probationPeriodEndDate");
                filter.setValue(periodRange[0]);
                filterElements.add(filter);
            } else {
                FilterElement geFilter = new FilterElement();
                geFilter.setOp(OpEnum.ge);
                geFilter.setProp("probationPeriodEndDate");
                geFilter.setValue(periodRange[0]);

                FilterElement leFilter = new FilterElement();
                leFilter.setOp(OpEnum.le);
                leFilter.setProp("probationPeriodEndDate");
                leFilter.setValue(periodRange[1]);

                filterElements.addAll(Lists.newArrayList(geFilter, leFilter));
            }
        }

        FilterElement statusFilter = new FilterElement();
        statusFilter.setOp(OpEnum.eq);
        statusFilter.setProp("confirmationStatus");
        statusFilter.setValue(1);
        filterElements.add(statusFilter);

        FilterElement empStatusFilter = new FilterElement();
        empStatusFilter.setOp(OpEnum.ne);
        empStatusFilter.setProp("empStatus");
        empStatusFilter.setValue(1);
        // 排除离职人员的提醒
        filterElements.add(empStatusFilter);
        return filterElements;
    }

    /**
     * 计算触发合同时间
     * @param msgConfigDto 消息配置
     */
    private Long[] calculatePeriodPeriod(MsgConfigDto msgConfigDto) {
        long currentTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).toEpochSecond(OffsetDateTime.now().getOffset())
                * 1000;
        NotificationCycleTypeEnum cycleType = NotificationCycleTypeEnum.getByName(msgConfigDto.getRound());
        if (cycleType != null) {
            switch (cycleType) {
                case ADVANCE:
                    currentTime += TimeUnit.DAYS.toMillis(msgConfigDto.getDay());
                    break;
                case DELAY:
                    currentTime -=  TimeUnit.DAYS.toMillis(msgConfigDto.getDay());
                    break;
                case TODAY:
                    break;
            }
            if (msgConfigDto.getFunc() == ONCE || msgConfigDto.getFunc() == MERGE) {
                if (msgConfigDto.getFunc() == ONCE) {
                    return new Long[]{currentTime};
                } else {
                    long endTime = currentTime + TimeUnit.DAYS.toMillis(msgConfigDto.getMergeRule()
                            .getMergePeriod());
                    return new Long[] {currentTime, endTime};
                }
            } else {
                log.error("unSupport notice func type");
            }
        }
        return new Long[] {currentTime};
    }

    public void handleMergeNotice(MsgConfigDto msgConfigDto, EmpPageQueryDto queryDto) {
        // 计算合并时间点
        Integer day = msgConfigDto.getMergeRule().getMergeDay();
        LocalDateTime now = LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis()), OffsetDateTime.now()
                .getOffset());
        day = Math.min(day, (int) now.range(ChronoField.DAY_OF_MONTH).getMaximum());
        LocalDateTime target = now.withDayOfMonth(day);
        if (now.equals(target)) {
            PageResult<EmpWorkInfoDo> pageResult;
            List<EmpWorkInfoDo> allEmpInfo = Lists.newArrayList();
            do {
                pageResult = empWorkInfoDo.selectPage(queryDto);
                queryDto.setPageNo(queryDto.getPageNo() + 1);
                allEmpInfo.addAll(CollectionUtils.isNotEmpty(pageResult.getItems()) ? pageResult.getItems() : Lists.newArrayList());
            } while (CollectionUtils.isNotEmpty(pageResult.getItems()));
            doSendNotice(msgConfigDto, allEmpInfo.get(0), buildNoticeParams(allEmpInfo));
        } else {
            log.info("handleMergeNotice but merge event date is not suit");
        }
    }

    private Map<String, String> buildNoticeParams(List<EmpWorkInfoDo> allEmpInfo) {
        Map<String, String> extParams = Maps.newHashMap();
        List<String> summaryData = allEmpInfo.stream()
                .map(it -> StringUtils.join(Arrays.asList(it.getName(), it.getWorkno(),
                        it.getOrganizeTxt(),
                        it.getPostTxt(), calculateDiffDays(it.getProbationPeriodEndDate()), DateUtil.formatDate(it.getProbationPeriodEndDate())), "#"))
                .collect(Collectors.toList());
        String summaryContent = TableGenerateUtil.buildHtmlContent(TableGenerateUtil.TableStyle.CONFIRM_SUMMARY, summaryData);
        extParams.put("empWorkInfo.confirm.summary", summaryContent);
        extParams.put("empWorkInfo.probationPeriodEndDay", String.valueOf(calculateDiffDays(allEmpInfo.get(0)
                .getProbationPeriodEndDate())));
        return extParams;
    }
    
    private long calculateDiffDays(long timestamp) {
        long currentTime = DateUtil.getCurrentTimestamp();
        long diff = timestamp - currentTime;
        return TimeUnit.MILLISECONDS.toDays(diff);
    }

    private void doSendNotice(MsgConfigDto msgConfigDto, EmpWorkInfoDo info, Map<String, String> extMap) {
        // 发送触发消息
        long eventTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).toEpochSecond(OffsetDateTime.now().getOffset()) * 1000
                + (msgConfigDto.getSendTime() != null ? msgConfigDto.getSendTime() : 0);
        long currentTime = System.currentTimeMillis();
        if (eventTime <= currentTime) {
            msgNoticeService.sendMsgNoticeEvent(msgConfigDto.getBid(), Lists.newArrayList(info.getEmpId()), extMap, "hr", 0);
        } else {
            // 定时任务触发
            // 创建定时任务
            scheduleFeignClient.addSchedule(new ScheduleTaskDto(SecurityUserUtil.getSecurityUserInfo()
                    .getTenantId(), TOPIC, String.format("%s_%s", msgConfigDto.getBid(), info.getEmpId()), FastjsonUtil.toJson(extMap), eventTime));
        }
    }

    public void handleNoticeWithEmp(MsgConfigDto msgConfigDto, EmpPageQueryDto queryDto) {
        PageResult<EmpWorkInfoDo> page = empWorkInfoDo.selectPage(queryDto);
        if (CollectionUtils.isNotEmpty(page.getItems())) {
            page.getItems().forEach(it -> {
                doSendNotice(msgConfigDto, it, buildNoticeParams(Lists.newArrayList(it)));
            });
            if(page.getTotal() < 500){
                return;
            }
            queryDto.setPageNo(queryDto.getPageNo() + 1);
            handleNoticeWithEmp(msgConfigDto, queryDto);
        } else {
            log.info("regularizationTaskHandler handleNoticeWithEmp query emp is empty, page: {}", queryDto.getPageNo());
        }
    }
}
