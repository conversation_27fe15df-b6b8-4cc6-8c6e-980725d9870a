package com.caidaocloud.hr.service.employee.application.emp.manage.service;

import com.alibaba.spring.util.ObjectUtils;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.EmpPrivateInfoDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.common.tool.LogChangeDataUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.system.CardTypeEnum;
import com.caidaocloud.hr.service.growthrecord.domain.utils.JodaTimeUtils;
import com.caidaocloud.hr.service.util.IdCardUtil;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class EmpPrivateInfoService {
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    private void preCheck(EmpPrivateInfoDto dto) {
        // 证件类型选择【身份证】，证件号码需要做15和18位位数校验
        if (null != dto.getCardType() &&
                CardTypeEnum.ID_CARD.getIndex().equals(dto.getCardType()) && StringUtils.isNotBlank(dto.getCardNo())) {
            String cardNo = dto.getCardNo().trim();
            dto.setCardNo(cardNo);
            PreCheck.preCheckArgument(!(is15IdCard(cardNo) || is18IdCard(cardNo)),
                    LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32001));
        }
    }

    /**
     * 15位身份证号码的基本数字和位数验校
     *
     * @param idCard
     * @return
     */
    public boolean is15IdCard(String idCard) {
        return Pattern.matches("^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$",idCard);
    }

    /**
     * 18位身份证号码的基本数字和位数验校
     *
     * @param idCard
     * @return
     */
    public boolean is18IdCard(String idCard) {
        return Pattern.matches("^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([\\d|x|X]{1})$",idCard);
    }

    public EmpPrivateInfoDo save(EmpPrivateInfoDto dto) {
        preCheck(dto);
        EmpPrivateInfoDo data = ObjectConverter.convert(dto, EmpPrivateInfoDo.class);
        doConverter(dto, data);

        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        String bid = empPrivateInfoDomainService.save(data);
        data.setBid(bid);
        return data;
    }

    public void update(EmpPrivateInfoDto dto) {
        preCheck(dto);
        EmpPrivateInfoDo data = ObjectConverter.convert(dto, EmpPrivateInfoDo.class);
        doConverter(dto, data);

        EmpWorkInfoDo workInfo = empWorkInfoDomainService.getEmpWorkInfo(dto.getEmpId(), System.currentTimeMillis());
        EmpPrivateInfoDo oldSimple = empPrivateInfoDomainService.getByEmpId(dto.getEmpId());
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        // 个人信息修改
        empPrivateInfoDomainService.update(data);
        EmpPrivateInfoDo newSimple = empPrivateInfoDomainService.getByEmpId(dto.getEmpId());
        LogRecordContext.putVariable("name", oldSimple.getName());
        LogRecordContext.putVariable("workno",workInfo.getWorkno());
        LogRecordContext.putVariable("change", LogChangeDataUtil.getChangeInfo(oldSimple, newSimple));
    }

    private void doConverter(EmpPrivateInfoDto source, EmpPrivateInfoDo target) {
        // 字典值
        // 性别
        if (StringUtil.isNotEmpty(source.getSex())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getSex());
            target.setSex(dictSimple);
        }
        // 国籍
        if (StringUtil.isNotEmpty(source.getNationality())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getNationality());
            target.setNationality(dictSimple);
        }
        // 民族
        if (StringUtil.isNotEmpty(source.getNation())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getNation());
            target.setNation(dictSimple);
        }
        // 户口类型
        if (StringUtil.isNotEmpty(source.getFamilyType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getFamilyType());
            target.setFamilyType(dictSimple);
        }
        //残疾人证等级 
        if (StringUtil.isNotEmpty(source.getDisabilityLevelType())) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setValue(source.getDisabilityLevelType());
            target.setDisabilityLevelType(dictSimple);
        }
        // 枚举值
        // 婚姻状态
        if (StringUtil.isNotEmpty(source.getMaritalStatus())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getMaritalStatus());
            target.setMaritalStatus(enumSimple);
        }

        // 生育状态
        if (StringUtil.isNotEmpty(source.getFertilityStatus())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getFertilityStatus());
            target.setFertilityStatus(enumSimple);
        }

        // 政治面貌
        if (StringUtil.isNotEmpty(source.getPoliticalOutlook())) {
            DictSimple enumSimple = new DictSimple();
            enumSimple.setValue(source.getPoliticalOutlook());
            target.setPoliticalOutlook(enumSimple);
        }

        // 证件类型
        if (StringUtil.isNotEmpty(source.getCardType())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getCardType());
            target.setCardType(enumSimple);
        }

        // 个人邮箱前后空格
        if (target.getEmail() != null) {
            target.setEmail(target.getEmail().trim());
        }
        // 监护人邮箱前后空格
        if (target.getGuardianEmail() != null) {
            target.setGuardianEmail(target.getGuardianEmail().trim());
        }

        // 身份证号不为空自动计算出生日期、性别、年龄
        if (null != source.getCardType() &&
                CardTypeEnum.ID_CARD.getIndex().equals(source.getCardType()) && StringUtils.isNotBlank(source.getCardNo())) {
            target.setDivisionAge(IdCardUtil.getAgeByIdCard(source.getCardNo()));
            target.setSex(IdCardUtil.getGenderByIdCard(source.getCardNo()));
            target.setBirthDate(IdCardUtil.getBirthByIdCard(source.getCardNo()));
        }else if ( StringUtils.isBlank(source.getCardNo())&& ObjectUtil.isNotEmpty(source.getBirthDate())) {
            LocalDate birthDate = JodaTimeUtils.convertTimeStampToLocalDate(source.getBirthDate());
            LocalDate currentDate = LocalDate.now();
            long age = currentDate.getYear() - birthDate.getYear();
            if (currentDate.getMonthValue() < birthDate.getMonthValue() ||
                (currentDate.getMonthValue() == birthDate.getMonthValue() && currentDate.getDayOfMonth() < birthDate.getDayOfMonth())) {
            age--;
              }
            target.setDivisionAge(Math.toIntExact(age));
        }
    }

    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoListByEmpId(List<String> empIds) {
        return empPrivateInfoDomainService.getAllEmpPrivateInfoListByEmpId(empIds);
    }

    public EmpPrivateInfoDo getByEmpId(String empId) {
        return empPrivateInfoDomainService.getByEmpId(empId);
    }

    public PageResult<EmpPrivateInfoDo> selectHasGuardian(BasePage page) {
        return empPrivateInfoDomainService.selectHasGuardian(page);
    }

    public List<EmpPrivateInfoDo> selectAllHasGuardian() {
        int pageNo = 1;
        int pageSize = 500;
        int index = pageNo * pageSize;
        BasePage page = new BasePage();
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        List<EmpPrivateInfoDo> list = new ArrayList<>();
        PageResult<EmpPrivateInfoDo> pageInfo = empPrivateInfoDomainService.selectHasGuardian(page);
        if (null != pageInfo && null != pageInfo.getItems() && pageInfo.getItems().size() > 0) {
            list.addAll(pageInfo.getItems());
        }
        if (pageInfo.getTotal() > index) {
            while (pageInfo.getTotal() > index) {
                page.setPageNo(++pageNo);
                pageInfo = empPrivateInfoDomainService.selectHasGuardian(page);
                if (null != pageInfo && null != pageInfo.getItems() && pageInfo.getItems().size() > 0) {
                    list.addAll(pageInfo.getItems());
                }
            }
        }
        return list;
    }

    public List<EmpPrivateInfoVo> getAllEmpPrivateData() {
        BasePage page = new BasePage();
        int pageNo = 1;
        int pageSize = 500;
        int total = pageSize;
        List<EmpPrivateInfoVo> list = new ArrayList<>();
        boolean flag = true;
        while (flag){
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            PageResult<EmpPrivateInfoDo> temp = empPrivateInfoDomainService.selectPage(page);
            list.addAll(ObjectConverter.convertList(temp.getItems(), EmpPrivateInfoVo.class));
            if(temp.getTotal()<=total){
                flag = false;
            }
            total += pageSize;
            ++pageNo;
        }
        return list;
    }
}
